import dotenv from 'dotenv';
dotenv.config();

import { pool } from './db';
import { addDiscountTypeToOrderRevisions } from './migrations/add_discount_type_to_order_revisions';

/**
 * Script para adicionar a coluna discount_type à tabela order_revisions
 */
async function main() {
  try {
    console.log('Iniciando processo de migração para adicionar discount_type na tabela order_revisions...');
    
    // Executar a migração
    await addDiscountTypeToOrderRevisions.up();
    
    console.log('Migração concluída com sucesso!');
    process.exit(0);
  } catch (error) {
    console.error('Erro durante a migração:', error);
    process.exit(1);
  }
}

// Executar a função principal
main();
