import { useEffect, useState } from 'react';
import { useLocation } from 'wouter';
import AdminLayout from '@/components/admin/AdminLayout';
import { useTranslation } from '@/hooks/useTranslation';
import { useStore } from '@/context/StoreContext';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { formatCurrency, formatPhoneWithCountryCode, formatDate, cn } from '@/lib/utils';
import { generateOrderPdf } from '@/lib/pdfUtils';
import { useForm, Controller } from 'react-hook-form';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import PaymentStatusCard from '@/components/admin/PaymentStatusCard';
import PaymentsList from '@/components/admin/PaymentsList';
import PaymentDialog from '@/components/admin/PaymentDialog';
import {
  useOrderPayments,
  useCreatePayment,
  useDeletePayment,
  useRecalculatePaymentStatus,
  calculateTotalReceived
} from '@/hooks/useOrderPayments';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
// Removendo importação duplicada - já existe abaixo
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { Package, User, ShoppingBag, Calendar, Info as InfoIcon, MapPin, Truck, Store, Clock, FileEdit, History, CheckCircle, XCircle, Pencil, Plus, Minus, Trash, Trash2, Save, CreditCard, AlertCircle, Check, Ban, ArrowLeft, ChevronRight, Search, Printer, X, Receipt, Tag, Ticket } from 'lucide-react';

// Status badge component
const OrderStatusBadge = ({ status }: { status: string }) => {
  const { t } = useTranslation();
  let bgColor = 'bg-primary';
  let textColor = 'text-primary';

  switch (status) {
    case 'pending':
      bgColor = 'bg-yellow-100';
      textColor = 'text-yellow-800';
      break;
    case 'confirmed':
      bgColor = 'bg-blue-100';
      textColor = 'text-blue-800';
      break;
    case 'delivered':
      bgColor = 'bg-green-100';
      textColor = 'text-green-800';
      break;
    case 'cancelled':
      bgColor = 'bg-red-100';
      textColor = 'text-red-800';
      break;
    // Support legacy statuses temporarily
    case 'processing':
    case 'completed':
      bgColor = 'bg-blue-100';
      textColor = 'text-blue-800';
      break;
  }

  return (
    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${bgColor} ${textColor}`}>
      {t(`dashboard.${status}`)}
    </span>
  );
};

type OrderDetailsProps = {
  id: number;
};

// Tipos para os formulários de edição
interface EditOrderInfoFormData {
  receivingMethod: string;
  receivingDate: string;
  receivingTime: string;
  status: string;
  paymentMethod: string;
  notes: string;
  deliveryAddress?: {
    street: string;
    number: string;
    neighborhood: string;
    cityState: string;
    reference: string;
  };
}

interface OrderRevisionItemEdit {
  id: number;
  quantity: number;
  unitPrice: number;
  productName: string;
  productImage?: string;
  selectedVariations: any[];
  observation: string | null;
}

export default function OrderDetailsPage({ id }: OrderDetailsProps) {
  const { t } = useTranslation();
  const [location, setLocation] = useLocation();
  const { store, isLoading: isStoreLoading } = useStore();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isCreatingRevision, setIsCreatingRevision] = useState(false);
  const [selectedRevisionId, setSelectedRevisionId] = useState<number | null>(null);
  const [activeRevision, setActiveRevision] = useState<any>(null);

  // Estados para edição de revisões
  const [isEditingInfo, setIsEditingInfo] = useState(false);
  const [isEditingItems, setIsEditingItems] = useState(false);
  const [initialized, setInitialized] = useState(false);



  // Estados para taxa de entrega durante a edição
  const [deliveryFee, setDeliveryFee] = useState<number>(0);
  const [isEditingDeliveryFee, setIsEditingDeliveryFee] = useState<boolean>(false);
  const [tempDeliveryFeeText, setTempDeliveryFeeText] = useState<string>('');

  // Estados para desconto durante a edição
  const [isEditingDiscount, setIsEditingDiscount] = useState<boolean>(false);
  const [discountValue, setDiscountValue] = useState<number>(0);
  const [discountType, setDiscountType] = useState<'fixed' | 'percentage'>('fixed');
  const [tempDiscountText, setTempDiscountText] = useState<string>('');

  // Estados para formulários de edição
  const [orderInfoForm, setOrderInfoForm] = useState<EditOrderInfoFormData | null>(null);
  const [editingItems, setEditingItems] = useState<OrderRevisionItemEdit[]>([]);

  // Estado para controlar o modal de pagamento
  const [isPaymentDialogOpen, setIsPaymentDialogOpen] = useState(false);

  // Verificar se viemos da página de detalhes do cliente usando localStorage
  const referer = localStorage.getItem('orderReferer');
  const customerId = localStorage.getItem('orderRefererId');

  console.log('Referer Info:', { referer, customerId });

  // Redirect to settings if no store exists
  useEffect(() => {
    if (!isStoreLoading && !store) {
      setLocation('/admin/settings');
    }
  }, [store, isStoreLoading, setLocation]);

  // Fetch order details
  const orderQuery = useQuery({
    queryKey: [`/api/orders/${id}`],
    enabled: !!store && !!id,
    staleTime: 0, // Sempre busque dados novos quando for invalidado
    refetchOnWindowFocus: false,
    onSuccess: (data) => {
      console.log("Dados do pedido recebidos:", data);

      // Log específico para os dados do cupom
      console.log("Dados do cupom:", {
        discount: data?.discount,
        couponId: data?.couponId,
        couponCode: data?.couponCode,
        couponType: data?.couponType
      });

      if (data && data.items) {
        console.log("Itens do pedido:", data.items);
        data.items.forEach((item, index) => {
          console.log(`Item ${index + 1}:`, {
            id: item.id,
            productId: item.productId,
            productName: item.productName,
            name: item.name,
            isCustomProduct: item.isCustomProduct,
            observation: item.observation
          });
        });
      }
    }
  });
  const { data: order, isLoading, error } = orderQuery;

  // Buscar revisões do pedido
  const revisionListQuery = useQuery({
    queryKey: [`/api/orders/${id}/revisions`],
    enabled: !!store && !!id,
    staleTime: 0, // Sempre busque dados novos quando for invalidado
    refetchOnWindowFocus: false,
    onSuccess: (data) => {
      console.log("Revisões recebidas:", data);
    }
  });
  const { data: revisions = [], isLoading: isLoadingRevisions, refetch: refetchRevisions } = revisionListQuery;

  // Buscar detalhes da revisão selecionada
  const revisionQuery = useQuery({
    queryKey: [`/api/orders/revisions/${selectedRevisionId}`],
    enabled: !!selectedRevisionId,
    refetchOnWindowFocus: false,
    staleTime: 0, // Sempre busque dados novos quando for invalidado
    onSuccess: (data) => {
      console.log("Detalhes da revisão recebidos:", data);

      // Log específico para os dados do cupom na revisão
      console.log("Dados do cupom na revisão:", {
        discount: data?.discount,
        couponId: data?.couponId,
        couponCode: data?.couponCode,
        couponType: data?.couponType
      });

      if (data && data.items) {
        console.log("Itens da revisão:", data.items);
        data.items.forEach((item, index) => {
          console.log(`Item da revisão ${index + 1}:`, {
            id: item.id,
            productId: item.productId,
            productName: item.productName,
            name: item.name,
            isCustomProduct: item.isCustomProduct,
            observation: item.observation
          });
        });
      }
    }
  });
  const { data: revisionDetails, refetch: refetchRevisionDetails } = revisionQuery;

  // Hooks para gerenciar recebimentos
  const { data: payments = [], isLoading: isLoadingPayments } = useOrderPayments(Number(id));
  const createPaymentMutation = useCreatePayment(Number(id));
  const deletePaymentMutation = useDeletePayment(Number(id));
  const recalculateStatusMutation = useRecalculatePaymentStatus(Number(id));

  // Efeito para atualizar o activeRevision quando a revisão selecionada muda ou os detalhes são atualizados
  useEffect(() => {
    console.log('🔄 useEffect para activeRevision executado:', {
      selectedRevisionId,
      hasRevisionDetails: !!revisionDetails,
      revisionsLength: revisions?.length || 0,
      currentRevisionInList: revisions?.find(rev => rev.isCurrent)?.id || null
    });

    // Verificar se a lista de revisões está vazia - mostrar o pedido original
    if (revisions && revisions.length === 0) {
      console.log("📋 Lista de revisões vazia, exibindo pedido original");
      setActiveRevision(null);
      setSelectedRevisionId(null);
      return;
    }

    // Situação 1: Temos um ID selecionado e os detalhes foram buscados
    if (selectedRevisionId && revisionDetails) {
      console.log("✅ Atualizando activeRevision com revisionDetails:", {
        revisionId: revisionDetails.id,
        paymentStatus: revisionDetails.paymentStatus,
        isCurrent: revisionDetails.isCurrent
      });
      setActiveRevision(revisionDetails);
    }
    // Situação 2: Inicialização da página - selecionar a revisão atual
    else if (selectedRevisionId === undefined && revisions?.length > 0) {
      // Quando a página carrega inicialmente (selectedRevisionId é undefined),
      // procuramos a revisão atual
      const current = revisions.find(rev => rev.isCurrent);
      if (current) {
        console.log("🚀 Inicializando com a revisão atual:", {
          revisionId: current.id,
          paymentStatus: current.paymentStatus,
          isCurrent: current.isCurrent
        });
        setActiveRevision(current);
        setSelectedRevisionId(current.id);
      } else {
        console.log("⚠️ Nenhuma revisão atual encontrada na inicialização");
        setActiveRevision(null);
      }
    }
    // Situação 3: Se não há revisão selecionada mas há revisões, usar a revisão atual da lista
    else if (selectedRevisionId === null && revisions?.length > 0) {
      const current = revisions.find(rev => rev.isCurrent);
      if (current) {
        console.log("🔄 Atualizando activeRevision com revisão atual da lista:", {
          revisionId: current.id,
          paymentStatus: current.paymentStatus,
          isCurrent: current.isCurrent
        });
        setActiveRevision(current);
      }
    }
    // Situação 4: Temos um ID selecionado mas não temos detalhes ainda
    else if (selectedRevisionId && !revisionDetails) {
      console.log("⏳ Aguardando detalhes da revisão selecionada:", selectedRevisionId);
    }
    // Não fazemos nada quando selectedRevisionId é null explicitamente (usuário selecionou o pedido original)
    else {
      console.log("ℹ️ Nenhuma ação necessária no useEffect activeRevision");
    }
  }, [selectedRevisionId, revisionDetails, revisions]);

  // Efeito para monitorar mudanças no paymentStatus
  useEffect(() => {
    if (activeRevision) {
      console.log("🔄 PaymentStatus da activeRevision atualizado:", activeRevision.paymentStatus);
    }
    if (order) {
      console.log("🔄 PaymentStatus do order atualizado:", order.paymentStatus);
    }
  }, [activeRevision?.paymentStatus, order?.paymentStatus]);

  // Efeito para verificar e buscar atualizações quando retornamos da edição de item
  useEffect(() => {
    // Verificar se há informações no localStorage que indiquem que viemos da edição de item
    const storedRevisionId = localStorage.getItem('currentRevisionId');
    const storedItemId = localStorage.getItem('currentItemId');

    if (storedRevisionId && storedItemId && selectedRevisionId && selectedRevisionId.toString() === storedRevisionId) {
      console.log("Detectada volta da página de edição de item, recarregando dados...");

      // Invalidar consultas para forçar a atualização dos dados
      queryClient.invalidateQueries({ queryKey: [`/api/orders/revisions/${selectedRevisionId}`] });
      queryClient.invalidateQueries({ queryKey: ['/api/orders/revisions', selectedRevisionId, 'items'] });

      // Refetch explícito
      refetchRevisionDetails();

      // Limpar o localStorage para não repetir este processo desnecessariamente
      localStorage.removeItem('currentItemId');
    }
  }, [selectedRevisionId, queryClient, refetchRevisionDetails]);

  // Estado para preservar a revisão selecionada durante recálculos
  const [preserveSelectedRevision, setPreserveSelectedRevision] = useState<number | null>(null);

  // Efeito para escutar evento de preservação de estado da revisão
  useEffect(() => {
    const handlePreserveRevision = () => {
      if (selectedRevisionId) {
        console.log('🔒 Preservando revisão selecionada:', selectedRevisionId);
        setPreserveSelectedRevision(selectedRevisionId);
      }
    };

    window.addEventListener('preserve-revision-state', handlePreserveRevision);
    return () => {
      window.removeEventListener('preserve-revision-state', handlePreserveRevision);
    };
  }, [selectedRevisionId]);

  // Efeito para selecionar automaticamente a revisão atual quando a página carrega
  useEffect(() => {
    // Se temos uma revisão preservada, usá-la em vez da inicialização padrão
    if (preserveSelectedRevision && revisions && revisions.length > 0) {
      const preservedRevision = revisions.find(rev => rev.id === preserveSelectedRevision);
      if (preservedRevision) {
        console.log("🔄 Restaurando revisão preservada:", preservedRevision.id);
        setSelectedRevisionId(preservedRevision.id);
        setActiveRevision(preservedRevision);
        setPreserveSelectedRevision(null); // Limpar após restaurar
        return;
      }
    }

    // Verificar se já inicializamos e se os dados necessários estão carregados
    if (!initialized && !isLoadingRevisions && revisions && revisions.length > 0) {
      // Procurar pela revisão marcada como atual
      const current = revisions.find(rev => rev.isCurrent);
      if (current) {
        console.log("Inicializando e selecionando automaticamente a revisão atual:", current.id);
        // Definir os dois estados de uma vez para evitar renders intermediários
        setSelectedRevisionId(current.id);
        setActiveRevision(current);
      }
      // Marcar como inicializado para evitar executar novamente mesmo após recarregamentos
      setInitialized(true);
    }
  }, [initialized, isLoadingRevisions, revisions, preserveSelectedRevision]);

  // Função para definir uma revisão como atual
  const setCurrentRevisionMutation = useMutation({
    mutationFn: (revisionId: number) =>
      apiRequest('PATCH', `/api/orders/revisions/${revisionId}/set-current`, {}),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/orders/${id}/revisions`] });
      queryClient.invalidateQueries({ queryKey: [`/api/orders/${id}`] });
      toast({
        title: "Revisão definida como atual",
        description: "A revisão foi definida como a versão atual do pedido",
        variant: "success"
      });
    },
    onError: (error) => {
      toast({
        title: "Erro ao definir revisão como atual",
        description: error instanceof Error ? error.message : String(error),
        variant: "destructive"
      });
    }
  });

  // Função para definir o pedido original como atual (limpar revisões atuais)
  const setOriginalAsCurrentMutation = useMutation({
    mutationFn: (orderId: number) =>
      apiRequest('PATCH', `/api/orders/${orderId}/set-original-as-current`, {}),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/orders/${id}/revisions`] });
      queryClient.invalidateQueries({ queryKey: [`/api/orders/${id}`] });
      toast({
        title: "Pedido original definido como atual",
        description: "O pedido original foi definido como a versão atual",
        variant: "success"
      });
    },
    onError: (error) => {
      toast({
        title: "Erro ao definir pedido original como atual",
        description: error instanceof Error ? error.message : String(error),
        variant: "destructive"
      });
    }
  });

  // Função para excluir uma revisão
  const deleteRevisionMutation = useMutation({
    mutationFn: (revisionId: number) =>
      apiRequest('DELETE', `/api/orders/revisions/${revisionId}`, {}),
    onSuccess: (data) => {
      // Invalidar todas as consultas relevantes
      queryClient.invalidateQueries({ queryKey: [`/api/orders/${id}/revisions`] });
      queryClient.invalidateQueries({ queryKey: [`/api/orders/${id}`] });

      // Sempre limpar o estado de seleção e revisão ativa quando uma revisão é excluída
      setSelectedRevisionId(null);
      setActiveRevision(null);

      // Forçar um refetch para atualizar os dados
      revisionQuery.refetch();
      orderQuery.refetch();

      // Adicionar um pequeno atraso para garantir que os estados estejam limpos
      setTimeout(() => {
        refetchRevisions();
      }, 100);

      toast({
        title: "Revisão excluída",
        description: "A revisão foi excluída com sucesso",
        variant: "success"
      });
    },
    onError: (error) => {
      toast({
        title: "Erro ao excluir revisão",
        description: error instanceof Error ? error.message : String(error),
        variant: "destructive"
      });
    }
  });

  // Create order revision mutation
  const createRevisionMutation = useMutation({
    mutationFn: () => apiRequest('POST', `/api/orders/${id}/revisions`, {
      // Enviamos os dados do pedido atual/original como base para a nova revisão
      receivingMethod: order.receivingMethod,
      receivingDate: order.receivingDate,
      receivingTime: order.receivingTime || null,
      deliveryAddress: order.deliveryAddress || null,
      paymentMethod: order.paymentMethod,
      subtotal: order.subtotal,
      deliveryFee: order.deliveryFee || 0,
      total: order.total,
      notes: order.notes || ''
    }),
    onSuccess: (data) => {
      console.log("Revisão criada com sucesso:", data);
      setIsCreatingRevision(false);
      queryClient.invalidateQueries({ queryKey: [`/api/orders/${id}`] });
      queryClient.invalidateQueries({ queryKey: [`/api/orders/${id}/revisions`] });
      toast({
        title: "Revisão criada com sucesso",
        description: `Nova revisão #${data.revisionNumber} criada para o pedido #${id}`,
        variant: "success"
      });

      // Selecionar a nova revisão automaticamente
      if (data && data.id) {
        setSelectedRevisionId(data.id);
      }
    },
    onError: (error: any) => {
      console.error("Erro ao criar revisão:", error);
      setIsCreatingRevision(false);
      toast({
        title: "Erro ao criar revisão",
        description: error.message || "Ocorreu um erro ao criar a revisão do pedido",
        variant: "destructive",
      });
    }
  });

  // Update order status mutation
  const updateStatusMutation = useMutation({
    mutationFn: ({ status }: { status: string }) =>
      apiRequest('PATCH', `/api/orders/${id}/status`, { status }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/orders/${id}`] });
      toast({
        title: t('orders.statusUpdated'),
        variant: "success",
      });
    },
    onError: (error) => {
      toast({
        title: t('common.error'),
        description: error instanceof Error ? error.message : String(error),
        variant: "destructive",
      });
    }
  });

  // Mutation para atualizar informações da revisão
  const updateRevisionInfoMutation = useMutation({
    mutationFn: (data: EditOrderInfoFormData) =>
      apiRequest('PATCH', `/api/orders/revisions/${selectedRevisionId}`, data),
    onSuccess: () => {
      // Invalidar as consultas
      queryClient.invalidateQueries({ queryKey: [`/api/orders/revisions/${selectedRevisionId}`] });
      queryClient.invalidateQueries({ queryKey: [`/api/orders/${id}/revisions`] });

      // Forçar atualizações manuais
      refetchRevisionDetails();
      refetchRevisions();

      setIsEditingInfo(false);
      toast({
        title: "Revisão atualizada",
        description: "As informações da revisão foram atualizadas com sucesso",
        variant: "success"
      });
    },
    onError: (error) => {
      toast({
        title: "Erro ao atualizar revisão",
        description: error instanceof Error ? error.message : String(error),
        variant: "destructive"
      });
    }
  });

  // Mutation para atualizar itens da revisão
  const updateRevisionItemsMutation = useMutation({
    mutationFn: (items: OrderRevisionItemEdit[]) =>
      apiRequest('PATCH', `/api/orders/revisions/${selectedRevisionId}/items`, { items }),
    onSuccess: (data) => {
      // Se o desconto for do tipo percentual, precisamos recalcular o valor do desconto após a atualização do subtotal
      if (activeRevision && activeRevision.discountType === 'percentage') {
        console.log('Recalculando desconto percentual após atualização do subtotal');

        // Obtém o novo subtotal da resposta
        const newSubtotal = data.subtotal || 0;

        // Recalcula o desconto usando a porcentagem original (se existir)
        // Prioridade: originalPercentage > lastOriginalPercentage (salvo em estado) > discount atual
        const percentage =
          activeRevision.originalPercentage ||
          activeRevision.lastOriginalPercentage ||
          activeRevision.discount || 0;

        if (percentage > 0) {
          console.log(`Aplicando percentual de desconto: ${percentage}% ao novo subtotal: ${newSubtotal}`);
          console.log(`Valor estimado de desconto: ${(percentage / 100) * newSubtotal}`);

          // Atualiza o desconto sem alterar o tipo ou porcentagem
          // Passamos sempre o percentual original, não o valor calculado
          updateDiscountMutation.mutate({
            discount: percentage,
            discountType: 'percentage'
          });
        }
      }

      // Invalidar as consultas
      queryClient.invalidateQueries({ queryKey: [`/api/orders/revisions/${selectedRevisionId}`] });
      queryClient.invalidateQueries({ queryKey: [`/api/orders/${id}/revisions`] });

      // Forçar atualizações manuais
      refetchRevisionDetails();
      refetchRevisions();

      setIsEditingItems(false);
      toast({
        title: "Itens atualizados",
        description: "Os itens da revisão foram atualizados com sucesso",
        variant: "success"
      });
    },
    onError: (error) => {
      toast({
        title: "Erro ao atualizar itens",
        description: error instanceof Error ? error.message : String(error),
        variant: "destructive"
      });
    }
  });

  // Mutation para remover um item da revisão
  const deleteRevisionItemMutation = useMutation({
    mutationFn: ({ revisionId, itemId }: { revisionId: number, itemId: number }) => {
      console.log('Enviando requisição para remover item da revisão:', {
        endpoint: `/api/orders/revisions/${revisionId}/items/${itemId}`,
        revisionId,
        itemId
      });

      return apiRequest('DELETE', `/api/orders/revisions/${revisionId}/items/${itemId}`, {});
    },
    onSuccess: (data) => {
      console.log('Item removido com sucesso da revisão. Resposta:', data);

      // Se o desconto for do tipo percentual, precisamos recalcular o valor do desconto após a atualização do subtotal
      if (activeRevision && activeRevision.discountType === 'percentage') {
        console.log('Recalculando desconto percentual após remoção de item');

        // Obtém o novo subtotal da resposta
        const newSubtotal = data.subtotal || 0;

        // Recalcula o desconto usando a porcentagem original (se existir)
        const percentage =
          activeRevision.originalPercentage ||
          activeRevision.lastOriginalPercentage ||
          activeRevision.discount || 0;

        if (percentage > 0) {
          console.log(`Aplicando percentual de desconto: ${percentage}% ao novo subtotal: ${newSubtotal}`);
          console.log(`Valor estimado de desconto: ${(percentage / 100) * newSubtotal}`);

          // Atualiza o desconto sem alterar o tipo ou porcentagem
          updateDiscountMutation.mutate({
            discount: percentage,
            discountType: 'percentage'
          });
        }
      }

      // Invalidar as consultas
      queryClient.invalidateQueries({ queryKey: [`/api/orders/revisions/${selectedRevisionId}`] });
      queryClient.invalidateQueries({ queryKey: [`/api/orders/${id}/revisions`] });

      // Forçar atualizações manuais
      refetchRevisionDetails();
      refetchRevisions();

      toast({
        title: t('orders.itemRemoved') || "Item removido",
        description: t('orders.itemRemovedFromRevision') || "O item foi removido desta revisão",
        variant: "success"
      });
    },
    onError: (error) => {
      console.error('Erro ao remover item da revisão:', error);
      toast({
        title: t('orders.errorRemovingItem') || "Erro ao remover item",
        description: error instanceof Error ? error.message : String(error),
        variant: "destructive"
      });
    }
  });

  // Mutation para remover cupom da revisão
  const removeCouponMutation = useMutation({
    mutationFn: (revisionId: number) => {
      console.log('Enviando requisição para remover cupom da revisão:', {
        endpoint: `/api/orders/revisions/${revisionId}/coupon`,
        revisionId
      });

      return apiRequest('DELETE', `/api/orders/revisions/${revisionId}/coupon`, {});
    },
    onSuccess: () => {
      console.log('Cupom removido com sucesso da revisão');

      // Invalidar as consultas
      queryClient.invalidateQueries({ queryKey: [`/api/orders/revisions/${selectedRevisionId}`] });
      queryClient.invalidateQueries({ queryKey: [`/api/orders/${id}/revisions`] });

      // Forçar atualizações manuais
      refetchRevisionDetails();
      refetchRevisions();

      toast({
        title: t('coupons.removed') || "Cupom removido",
        description: t('coupons.removedSuccess') || "O cupom foi removido com sucesso da revisão",
        variant: "success"
      });
    },
    onError: (error) => {
      console.error('Erro ao remover cupom da revisão:', error);
      toast({
        title: t('coupons.errorRemoving') || "Erro ao remover cupom",
        description: error instanceof Error ? error.message : String(error),
        variant: "destructive"
      });
    }
  });

  // Função para remover um item da revisão
  const handleRemoveRevisionItem = (itemId: number) => {
    if (!selectedRevisionId) return;

    // Confirmar antes de remover
    if (confirm(t('orders.confirmRemoveItem') || "Tem certeza que deseja remover este item da revisão?")) {
      deleteRevisionItemMutation.mutate({ revisionId: selectedRevisionId, itemId });
    }
  };



  // Função para remover o cupom da revisão
  const removeCouponFromRevision = (revisionId: number) => {
    removeCouponMutation.mutate(revisionId);
  };

  // Mutation para atualizar o desconto
  const updateDiscountMutation = useMutation({
    mutationFn: async ({ discount, discountType }: { discount: number, discountType: 'fixed' | 'percentage' }) => {
      console.log('=== INICIANDO ATUALIZAÇÃO DE DESCONTO ===');
      console.log('Dados da requisição:', {
        endpoint: `/api/orders/revisions/${selectedRevisionId}/discount`,
        payload: { discount, discountType },
        revisionId: selectedRevisionId,
        isPercentage: discountType === 'percentage',
        valorNumerico: discount,
        valorFormatado: discount.toFixed(2),
        tipoValor: typeof discount,
        tipoDiscountType: typeof discountType
      });

      try {
        const response = await apiRequest('PATCH', `/api/orders/revisions/${selectedRevisionId}/discount`, { discount, discountType });
        console.log('=== RESPOSTA DA API RECEBIDA ===');
        const data = await response.json();
        console.log('Dados da resposta:', data);
        return data;
      } catch (error) {
        console.error('=== ERRO NA REQUISIÇÃO ===');
        console.error('Erro capturado:', error);
        console.error('Tipo do erro:', typeof error);
        console.error('Stack trace:', error instanceof Error ? error.stack : 'N/A');
        throw error;
      }
    },
    onSuccess: (data) => {
      console.log('Desconto atualizado com sucesso. Resposta:', data);

      // Log adicional para depuração
      if (data._debug) {
        console.log('Informações de depuração do servidor:', data._debug);
      }

      // Atualizar o estado local com os novos valores
      if (data) {
        // Se for desconto percentual, manter a porcentagem original no state
        if (data.discount_type === 'percentage' && data.original_percentage !== undefined) {
          console.log(`Atualizando estado local com percentual: ${data.original_percentage}%`);
          setDiscountValue(data.original_percentage);
        } else {
          console.log(`Atualizando estado local com valor fixo: ${data.discount}`);
          setDiscountValue(data.discount || 0);
        }
        setDiscountType(data.discount_type || 'fixed');
      }

      // Invalidar as consultas
      queryClient.invalidateQueries({ queryKey: [`/api/orders/revisions/${selectedRevisionId}`] });
      queryClient.invalidateQueries({ queryKey: [`/api/orders/${id}/revisions`] });

      // Forçar atualizações manuais
      refetchRevisionDetails();
      refetchRevisions();

      setIsEditingDiscount(false);
      toast({
        title: t('orders.editDiscount') || "Desconto atualizado",
        description: t('orders.discountUpdated') || "O desconto foi atualizado com sucesso",
        variant: "success"
      });
    },
    onError: (error) => {
      console.error('Erro ao atualizar desconto:', error);
      toast({
        title: t('orders.errorUpdatingDiscount') || "Erro ao atualizar desconto",
        description: error instanceof Error ? error.message : String(error),
        variant: "destructive"
      });
    }
  });

  // Mutation para atualizar a taxa de entrega
  const updateDeliveryFeeMutation = useMutation({
    mutationFn: (deliveryFee: number) => {
      console.log('Enviando requisição para atualizar taxa de entrega:', {
        endpoint: `/api/orders/revisions/${selectedRevisionId}/delivery-fee`,
        payload: { deliveryFee },
        revisionId: selectedRevisionId
      });

      return apiRequest('PATCH', `/api/orders/revisions/${selectedRevisionId}/delivery-fee`, { deliveryFee });
    },
    onSuccess: (data) => {
      console.log('Taxa de entrega atualizada com sucesso. Resposta:', data);

      // Invalidar as consultas
      queryClient.invalidateQueries({ queryKey: [`/api/orders/revisions/${selectedRevisionId}`] });
      queryClient.invalidateQueries({ queryKey: [`/api/orders/${id}/revisions`] });

      // Forçar atualizações manuais
      refetchRevisionDetails();
      refetchRevisions();

      setIsEditingDeliveryFee(false);
      toast({
        title: t('orders.editDeliveryFee'),
        description: t('orders.deliveryFeeUpdated'),
        variant: "success"
      });
    },
    onError: (error) => {
      console.error('Erro ao atualizar taxa de entrega:', error);
      toast({
        title: t('orders.errorUpdatingDeliveryFee'),
        description: error instanceof Error ? error.message : String(error),
        variant: "destructive"
      });
    }
  });





  // Inicializa a taxa de entrega e desconto quando a revisão é carregada
  useEffect(() => {
    if (activeRevision) {
      // Inicializar taxa de entrega
      setDeliveryFee(activeRevision.deliveryFee || 0);
      setTempDeliveryFeeText(String((activeRevision.deliveryFee || 0).toFixed(2)).replace('.', ','));

      // Inicializar desconto
      const discountType = activeRevision.discount_type || activeRevision.discountType || 'fixed';
      setDiscountType(discountType);

      console.log('Inicializando desconto com base na revisão:', {
        discount_type: activeRevision.discount_type,
        discountType: activeRevision.discountType,
        original_percentage: activeRevision.original_percentage,
        originalPercentage: activeRevision.originalPercentage,
        discount: activeRevision.discount,
        tipoFinal: discountType
      });

      const isPercentage = discountType === 'percentage';
      const percentValue =
        activeRevision.original_percentage !== undefined && activeRevision.original_percentage !== null
          ? activeRevision.original_percentage
          : (activeRevision.originalPercentage !== undefined && activeRevision.originalPercentage !== null
              ? activeRevision.originalPercentage
              : 0);

      if (isPercentage && percentValue > 0) {
        // Se for desconto percentual, usar o valor percentual original
        console.log(`Usando valor percentual: ${percentValue}%`);
        setDiscountValue(percentValue);
        setTempDiscountText(String(percentValue.toFixed(2)).replace('.', ','));
      } else {
        // Se for desconto fixo, usar o valor monetário
        console.log(`Usando valor fixo: ${activeRevision.discount}`);
        setDiscountValue(activeRevision.discount || 0);
        setTempDiscountText(String((activeRevision.discount || 0).toFixed(2)).replace('.', ','));
      }
    }
  }, [activeRevision]);

  // Função para calcular o subtotal dos itens em edição
  const calculateSubtotal = (): number => {
    if (!editingItems.length) return 0;
    return editingItems.reduce((total, item) => total + calculateItemTotal(item), 0);
  };

  // Função para calcular o subtotal da revisão com base nos valores totais de cada item
  const calculateRevisionSubtotal = (revision: any): number => {
    if (!revision || !revision.items || !Array.isArray(revision.items) || revision.items.length === 0) {
      return 0;
    }

    // Calcular o subtotal somando o valor total de cada item
    return revision.items.reduce((total, item) => total + calculateItemTotal(item), 0);
  };

  // Função auxiliar para calcular o valor do desconto com base no tipo e no subtotal
  const calculateRevisionDiscount = (revision: any): number => {
    if (!revision) return 0;

    const subtotal = calculateRevisionSubtotal(revision);

    console.log('Dados da revisão para cálculo de desconto:', {
      discount_type: revision.discount_type,
      discountType: revision.discountType,
      original_percentage: revision.original_percentage,
      originalPercentage: revision.originalPercentage,
      discount: revision.discount,
      subtotal
    });

    // Se for desconto percentual, calcular o valor com base no percentual e no subtotal
    const isPercentage =
      (revision.discount_type === 'percentage') ||
      (revision.discountType === 'percentage');

    const percentValue =
      revision.original_percentage !== undefined && revision.original_percentage !== null
        ? revision.original_percentage
        : (revision.originalPercentage !== undefined && revision.originalPercentage !== null
            ? revision.originalPercentage
            : 0);

    if (isPercentage && percentValue > 0) {
      const discountValue = (percentValue / 100) * subtotal;

      console.log('Calculando desconto percentual na interface:', {
        percentValue,
        subtotal,
        calculoExplicito: `${percentValue}% de ${subtotal} = ${discountValue}`,
        finalDiscount: Math.min(discountValue, subtotal)
      });

      // Garantir que o desconto não seja maior que o subtotal
      return Math.min(parseFloat(discountValue.toFixed(2)), subtotal);
    } else {
      // Se for desconto fixo, usar o valor diretamente
      return revision.discount || 0;
    }
  };

  // Função para calcular o total do pedido em edição
  const calculateTotal = (): number => {
    const subtotal = calculateSubtotal();
    let discountAmount = 0;

    // Calcular o valor do desconto com base no tipo
    if (discountType === 'fixed') {
      // Desconto em valor fixo
      discountAmount = discountValue;
      console.log(`Calculando desconto fixo: ${discountValue}`);
    } else if (discountType === 'percentage') {
      // Desconto em percentual - usar a porcentagem original se disponível
      const percentageValue = activeRevision?.original_percentage || discountValue;
      discountAmount = (percentageValue / 100) * subtotal;
      console.log(`Calculando desconto percentual: ${percentageValue}% de ${subtotal} = ${discountAmount}`);
    }

    // Garantir que o desconto não seja maior que o subtotal
    discountAmount = Math.min(discountAmount, subtotal);

    // Calcular o total: subtotal - desconto + taxa de entrega
    const total = Math.max(0, subtotal - discountAmount + deliveryFee);

    console.log('Cálculo do total:', {
      subtotal,
      discountType,
      discountValue,
      discountAmount,
      deliveryFee,
      total
    });

    return total;
  };

  // Função para calcular o preço total de um item (preço base + variações)
  const calculateItemTotal = (item: any): number => {
    // Verificar se o item existe
    if (!item) return 0;

    // Obter a quantidade do item
    const quantity = item.quantity || 1;

    // Determinar o preço base
    const baseUnitPrice = item.price || item.unitPrice || 0;
    const basePrice = baseUnitPrice * quantity;

    // Adicionar preço das variações
    let variationsTotal = 0;

    // Verificar se temos variações no formato selectedOptions
    if (item.selectedOptions && Array.isArray(item.selectedOptions) && item.selectedOptions.length > 0) {
      // Para cada variação, calcular o preço total
      variationsTotal = item.selectedOptions.reduce(
        (total: number, variation: any) => {
          const variationPrice = parseFloat(variation.price) || 0;
          const variationQuantity = variation.quantity || 1;

          // Verificar se é uma variação do tipo "outros"
          const isCustomVariation = variation.variationName?.toLowerCase() === 'outros' ||
                                   variation.optionName?.toLowerCase() === 'outros';

          // Para variações do tipo "outros", não multiplicar pela quantidade do produto
          if (isCustomVariation) {
            return total + (variationPrice * variationQuantity);
          } else {
            // Para variações normais, multiplicar pela quantidade do produto
            return total + (variationPrice * variationQuantity * quantity);
          }
        },
        0
      );

      // Log para depuração
      console.log('Cálculo do valor total do item (selectedOptions):', {
        nome: item.name || item.productName,
        precoBase: baseUnitPrice,
        quantidade: quantity,
        precoBaseTotal: basePrice,
        variacoes: item.selectedOptions,
        totalVariacoes: variationsTotal,
        total: basePrice + variationsTotal
      });
    }
    // Verificar se temos variações no formato selectedVariations
    else if (item.selectedVariations && Array.isArray(item.selectedVariations) && item.selectedVariations.length > 0) {
      // Para cada variação, calcular o preço total
      variationsTotal = item.selectedVariations.reduce(
        (total: number, variation: any) => {
          const variationPrice = parseFloat(variation.price) || 0;
          const variationQuantity = variation.quantity || 1;

          // Verificar se é uma variação do tipo "outros"
          const isCustomVariation = variation.variationName?.toLowerCase() === 'outros' ||
                                   variation.optionName?.toLowerCase() === 'outros';

          // Para variações do tipo "outros", não multiplicar pela quantidade do produto
          if (isCustomVariation) {
            return total + (variationPrice * variationQuantity);
          } else {
            // Para variações normais, multiplicar pela quantidade do produto
            return total + (variationPrice * variationQuantity * quantity);
          }
        },
        0
      );

      // Log para depuração
      console.log('Cálculo do valor total do item (selectedVariations):', {
        nome: item.name || item.productName,
        precoBase: baseUnitPrice,
        quantidade: quantity,
        precoBaseTotal: basePrice,
        variacoes: item.selectedVariations,
        totalVariacoes: variationsTotal,
        total: basePrice + variationsTotal
      });
    }

    return basePrice + variationsTotal;
  };



  // Função para remover um item durante a edição
  const handleRemoveItem = (itemId: number) => {
    setEditingItems(items => items.filter(item => item.id !== itemId));
  };

  // Handle status change
  const handleStatusChange = (status: string) => {
    if (order) {
      updateStatusMutation.mutate({ status });
    }
  };

  // Manipuladores para edição de revisão
  const handleEditInfo = () => {
    if (selectedRevisionId && activeRevision) {
      // Formatar a data para o formato esperado pelo input type="date" (YYYY-MM-DD)
      let formattedDate = '';
      if (activeRevision.receivingDate) {
        const date = new Date(activeRevision.receivingDate);
        formattedDate = date.toISOString().split('T')[0];
      }

      // Preencher o formulário com os dados da revisão atual
      setOrderInfoForm({
        receivingMethod: activeRevision.receivingMethod || 'pickup',
        receivingDate: formattedDate,
        receivingTime: activeRevision.receivingTime || '',
        status: activeRevision.status || 'pending',
        paymentMethod: activeRevision.paymentMethod || '',
        notes: activeRevision.notes || '',
        deliveryAddress: activeRevision.deliveryAddress || {
          street: '',
          number: '',
          neighborhood: '',
          cityState: '',
          reference: ''
        }
      });
      setIsEditingInfo(true);
    }
  };

  const handleEditItems = (item?: any) => {
    if (!activeRevision || !selectedRevisionId) return;

    // Armazenar informações no localStorage para uso nas páginas de edição
    localStorage.setItem('currentRevisionId', String(selectedRevisionId));
    localStorage.setItem('currentOrderId', String(id));

    if (item && item.id) {
      // Se um item específico foi clicado, vamos para a página de detalhes do produto
      localStorage.setItem('currentItemId', String(item.id));

      // Invalidar explicitamente o cache para forçar o recarregamento dos dados
      queryClient.invalidateQueries({ queryKey: ['/api/orders/revisions', selectedRevisionId, 'items', item.id] });
      queryClient.invalidateQueries({ queryKey: ['/api/orders/revisions', selectedRevisionId] });

      // Verificar se é um produto customizado (sem ID de produto, com ID negativo, ou produto que não está no catálogo)
      const productIdNumber = typeof item.productId === 'string' ? parseInt(item.productId) : item.productId;
      const isCustomProduct = !item.productId || productIdNumber <= 0 || productIdNumber === -1;

      console.log('Verificando tipo de produto antes da edição:', {
        itemId: item.id,
        productId: item.productId,
        isCustomProduct: isCustomProduct
      });

      if (isCustomProduct) {
        // É um produto customizado, vamos para a página de edição de produto customizado
        console.log('Editando produto customizado');
        localStorage.setItem('isCustomProduct', 'true');
        localStorage.setItem('editingRevisionId', String(selectedRevisionId));

        // Armazenar informações do produto customizado
        if (item.productName) localStorage.setItem('customProductName', item.productName);
        if (item.unitPrice) localStorage.setItem('customProductPrice', String(item.unitPrice));
        if (item.quantity) localStorage.setItem('customProductQuantity', String(item.quantity));
        if (item.observation || item.productDescription) {
          localStorage.setItem('customProductDescription', item.observation || item.productDescription || '');
        }
        if (item.selectedVariations && Array.isArray(item.selectedVariations)) {
          localStorage.setItem('customProductVariations', JSON.stringify(item.selectedVariations));
        }

        // Navegar para a página de edição de produto customizado
        setLocation('/admin/orders/custom-product');
      } else {
        // É um produto do catálogo, vamos para a página de detalhes do produto
        console.log('Editando produto do catálogo, ID:', item.productId);
        localStorage.setItem('isCustomProduct', 'false');

        // Flag para adicionar a uma revisão
        localStorage.setItem('addingToRevision', 'true');

        // Verificar se temos um ID de produto válido
        if (item.productId && typeof item.productId === 'number' && item.productId > 0) {
          console.log('Navegando para detalhes do produto ID:', item.productId);
          // Navegar para a página de detalhes do produto
          setLocation(`/admin/orders/product-details/${item.productId}`);
        } else {
          console.error('ID de produto inválido:', item.productId);
          toast({
            title: "Erro ao editar produto",
            description: "O ID do produto não é válido. Tente novamente.",
            variant: "destructive",
          });
          // Voltar para a página de detalhes do pedido como fallback
          setLocation(`/admin/orders/${orderId}`);
        }
      }
    } else {
      // Se o botão geral de editar itens foi clicado, vamos para a página de edição de itens
      setLocation(`/admin/orders/edit-items/${selectedRevisionId}`);
    }
  };

  const handleSaveInfo = (data: EditOrderInfoFormData) => {
    if (selectedRevisionId) {
      updateRevisionInfoMutation.mutate(data);
    }
  };

  const handleSaveItems = () => {
    if (selectedRevisionId) {
      updateRevisionItemsMutation.mutate(editingItems);
    }
  };

  const handleCancelEdit = () => {
    setIsEditingInfo(false);
    setIsEditingItems(false);
    setOrderInfoForm(null);
    setEditingItems([]);
  };

  // Função para troca de cliente - redirecionando para a página dedicada
  const handleChangeCustomer = () => {
    if (!selectedRevisionId) return;

    // Armazenar os IDs no localStorage para uso na página de seleção
    localStorage.setItem('currentOrderId', String(id));
    localStorage.setItem('currentRevisionId', String(selectedRevisionId));

    // Navegar para a página de seleção de cliente
    setLocation('/admin/orders/select-customer');
  };

  // Função para adicionar produto à revisão
  const handleAddProduct = () => {
    if (!selectedRevisionId) return;

    // Armazenar os IDs no localStorage para uso na página de seleção de produtos
    localStorage.setItem('currentOrderId', String(id));
    localStorage.setItem('currentRevisionId', String(selectedRevisionId));
    localStorage.setItem('addingToRevision', 'true');

    // Navegar para a página de seleção de produtos
    setLocation('/admin/orders/select-products');
  };

  // Função para gerar PDF do pedido - removida por falta da biblioteca necessária
  // const handleGeneratePdf = () => {...};

  // Funções para edição da taxa de entrega
  const handleStartEditingDeliveryFee = () => {
    if (activeRevision) {
      // Inicializa o campo de texto com o valor atual formatado
      setTempDeliveryFeeText(String((activeRevision.deliveryFee || 0).toFixed(2)).replace('.', ','));
      setIsEditingDeliveryFee(true);
    }
  };

  const handleSaveDeliveryFee = () => {
    if (!selectedRevisionId) return;

    // Converter o texto para número, tratando vírgula como separador decimal
    let numericValue = parseFloat(tempDeliveryFeeText.replace(',', '.')) || 0;

    // Garantir que o valor seja não-negativo
    numericValue = Math.max(0, numericValue);

    console.log('Salvando taxa de entrega:', {
      revisionId: selectedRevisionId,
      tempDeliveryFeeText,
      numericValue,
      parsedValue: parseFloat(tempDeliveryFeeText.replace(',', '.'))
    });

    // Atualizar o estado local
    setDeliveryFee(numericValue);

    // Chamar a mutation para atualizar no servidor
    updateDeliveryFeeMutation.mutate(numericValue);
  };

  // Funções para manipular o desconto
  const handleStartEditingDiscount = () => {
    if (activeRevision) {
      const discountType = activeRevision.discount_type || activeRevision.discountType || 'fixed';
      setDiscountType(discountType);

      console.log('Iniciando edição de desconto:', {
        discount_type: activeRevision.discount_type,
        discountType: activeRevision.discountType,
        original_percentage: activeRevision.original_percentage,
        originalPercentage: activeRevision.originalPercentage,
        discount: activeRevision.discount,
        tipoFinal: discountType
      });

      const isPercentage = discountType === 'percentage';
      const percentValue =
        activeRevision.original_percentage !== undefined && activeRevision.original_percentage !== null
          ? activeRevision.original_percentage
          : (activeRevision.originalPercentage !== undefined && activeRevision.originalPercentage !== null
              ? activeRevision.originalPercentage
              : 0);

      if (isPercentage && percentValue > 0) {
        // Se for desconto percentual, usar o valor percentual original
        console.log(`Usando valor percentual para edição: ${percentValue}%`);
        setTempDiscountText(String(percentValue.toFixed(2)).replace('.', ','));
      } else {
        // Se for desconto fixo, usar o valor monetário
        console.log(`Usando valor fixo para edição: ${activeRevision.discount}`);
        setTempDiscountText(String((activeRevision.discount || 0).toFixed(2)).replace('.', ','));
      }
    }
    setIsEditingDiscount(true);
  };

  const handleCancelEditingDiscount = () => {
    // Restaurar o valor original
    if (activeRevision) {
      const discountType = activeRevision.discount_type || activeRevision.discountType || 'fixed';
      setDiscountType(discountType);

      console.log('Cancelando edição de desconto, restaurando valores originais:', {
        discount_type: activeRevision.discount_type,
        discountType: activeRevision.discountType,
        original_percentage: activeRevision.original_percentage,
        originalPercentage: activeRevision.originalPercentage,
        discount: activeRevision.discount,
        tipoFinal: discountType
      });

      const isPercentage = discountType === 'percentage';
      const percentValue =
        activeRevision.original_percentage !== undefined && activeRevision.original_percentage !== null
          ? activeRevision.original_percentage
          : (activeRevision.originalPercentage !== undefined && activeRevision.originalPercentage !== null
              ? activeRevision.originalPercentage
              : 0);

      if (isPercentage && percentValue > 0) {
        // Se for desconto percentual, usar o valor percentual original
        console.log(`Restaurando valor percentual: ${percentValue}%`);
        setTempDiscountText(String(percentValue.toFixed(2)).replace('.', ','));
      } else {
        // Se for desconto fixo, usar o valor monetário
        console.log(`Restaurando valor fixo: ${activeRevision.discount}`);
        setTempDiscountText(String((activeRevision.discount || 0).toFixed(2)).replace('.', ','));
      }
    }
    setIsEditingDiscount(false);
  };

  const handleSaveDiscount = () => {
    if (!selectedRevisionId) return;

    // Converter o texto para número, tratando vírgula como separador decimal
    let numericValue = parseFloat(tempDiscountText.replace(',', '.')) || 0;

    // Garantir que o valor seja não-negativo
    numericValue = Math.max(0, numericValue);

    // Para desconto percentual, limitar a 100%
    if (discountType === 'percentage') {
      numericValue = Math.min(numericValue, 100);
    }

    console.log('Salvando desconto:', {
      revisionId: selectedRevisionId,
      tempDiscountText,
      numericValue,
      discountType,
      parsedValue: parseFloat(tempDiscountText.replace(',', '.'))
    });

    // Atualizar o estado local
    setDiscountValue(numericValue);

    // Chamar a mutation para atualizar no servidor
    updateDiscountMutation.mutate({ discount: numericValue, discountType });
  };

  const handleCancelEditingDeliveryFee = () => {
    // Restaurar o valor original e sair do modo de edição
    if (activeRevision) {
      setTempDeliveryFeeText(String((activeRevision.deliveryFee || 0).toFixed(2)).replace('.', ','));
    }
    setIsEditingDeliveryFee(false);
  };

  // Funções para gerenciar recebimentos
  const handleRegisterPayment = () => {
    setIsPaymentDialogOpen(true);
  };

  const handleCreatePayment = (paymentData: any) => {
    createPaymentMutation.mutate(paymentData, {
      onSuccess: () => {
        setIsPaymentDialogOpen(false);
      }
    });
  };

  const handleRecalculateStatus = () => {
    recalculateStatusMutation.mutate();
  };

  const handleDeletePayment = (paymentId: number) => {
    deletePaymentMutation.mutate(paymentId);
  };

  if (error) {
    return (
      <AdminLayout title={t('orders.orderDetails')}>
        <div className="py-6 text-center">
          <h2 className="text-xl font-bold mb-2">{t('common.error')}</h2>
          <p className="text-muted-foreground">{t('orders.failedToLoadOrder')}</p>
          <Button
            variant="outline"
            className="mt-4"
            onClick={() => {
              // Se veio da página de detalhes do cliente, voltar para lá
              if (referer === 'customer' && customerId) {
                // Limpar o localStorage após usar
                localStorage.removeItem('orderReferer');
                localStorage.removeItem('orderRefererId');
                setLocation(`/admin/customers/${customerId}`);
              } else {
                // Caso contrário, voltar para a lista de pedidos
                setLocation('/admin/orders');
              }
            }}
          >
            {t('common.goBack')}
          </Button>
        </div>
      </AdminLayout>
    );
  }

  // Componente para edição de informações do pedido (integrado na página)
  const EditOrderInfoForm = () => {
    const form = useForm<EditOrderInfoFormData>({
      defaultValues: orderInfoForm || {
        receivingMethod: '',
        receivingDate: '',
        receivingTime: '',
        status: '',
        paymentMethod: '',
        notes: '',
        deliveryAddress: {
          street: '',
          number: '',
          neighborhood: '',
          cityState: '',
          reference: ''
        }
      }
    });

    useEffect(() => {
      if (orderInfoForm) {
        form.reset(orderInfoForm);
      }
    }, [orderInfoForm, form]);

    const onSubmit = (data: EditOrderInfoFormData) => {
      handleSaveInfo(data);
    };

    return (
      <Card className="mb-6">
        <CardHeader className="pb-2">
          <CardTitle className="text-xl flex justify-between items-center">
            <div className="flex items-center gap-2">
              <FileEdit className="h-5 w-5" />
              Editar Informações da Revisão
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid md:grid-cols-2 gap-4">
              {/* Método de recebimento */}
              <div className="space-y-2">
                <Label htmlFor="receivingMethod">Método de recebimento</Label>
                <Select
                  value={form.watch('receivingMethod') || ''}
                  onValueChange={(value) => form.setValue('receivingMethod', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione..." />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pickup">Retirada</SelectItem>
                    <SelectItem value="delivery">Entrega</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Status */}
              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select
                  value={form.watch('status') || ''}
                  onValueChange={(value) => form.setValue('status', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione..." />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pending">Pendente</SelectItem>
                    <SelectItem value="confirmed">Confirmado</SelectItem>
                    <SelectItem value="delivered">Entregue</SelectItem>
                    <SelectItem value="cancelled">Cancelado</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Data de recebimento */}
              <div className="space-y-2">
                <Label htmlFor="receivingDate">Data de recebimento</Label>
                <Input
                  type="date"
                  id="receivingDate"
                  {...form.register('receivingDate')}
                />
              </div>

              {/* Hora de recebimento */}
              <div className="space-y-2">
                <Label htmlFor="receivingTime">Hora de recebimento</Label>
                <Input
                  type="time"
                  id="receivingTime"
                  {...form.register('receivingTime')}
                />
              </div>

              {/* Método de pagamento */}
              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="paymentMethod">Método de pagamento</Label>
                <div className="flex items-center gap-2 w-full">
                  <CreditCard className="text-muted-foreground h-5 w-5" />
                  <Controller
                    control={form.control}
                    name="paymentMethod"
                    render={({ field }) => (
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Selecione o método de pagamento" />
                        </SelectTrigger>
                        <SelectContent>
                          {/* Métodos padrão */}
                          {store?.paymentMethods?.pix && (
                            <SelectItem value="pix">{t('storefront.pix')}</SelectItem>
                          )}
                          {store?.paymentMethods?.cash && (
                            <SelectItem value="cash">{t('storefront.cash')}</SelectItem>
                          )}
                          {store?.paymentMethods?.creditCard && (
                            <SelectItem value="creditCard">{t('storefront.creditCard')}</SelectItem>
                          )}
                          {store?.paymentMethods?.debitCard && (
                            <SelectItem value="debitCard">{t('storefront.debitCard')}</SelectItem>
                          )}
                          {store?.paymentMethods?.bankTransfer && (
                            <SelectItem value="bankTransfer">{t('storefront.bankTransfer')}</SelectItem>
                          )}

                          {/* Métodos personalizados */}
                          {store?.paymentMethods?.customMethods?.map((method, index) => (
                            <SelectItem key={index} value={`custom_${index}`}>
                              {method}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    )}
                  />
                </div>
              </div>

              {/* Observações */}
              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="notes">Observações</Label>
                <div className="flex items-start gap-2">
                  <AlertCircle className="text-muted-foreground h-5 w-5 mt-2" />
                  <Textarea
                    id="notes"
                    rows={3}
                    {...form.register('notes')}
                    className="w-full"
                  />
                </div>
              </div>

              {/* Endereço de entrega (condicional) */}
              {form.watch('receivingMethod') === 'delivery' && (
                <div className="space-y-3 border rounded-md p-4 md:col-span-2">
                  <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                    <MapPin className="h-4 w-4" />
                    Endereço de entrega
                  </h4>

                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="street">Rua</Label>
                      <Input
                        id="street"
                        {...form.register('deliveryAddress.street')}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="number">Número</Label>
                      <Input
                        id="number"
                        {...form.register('deliveryAddress.number')}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="neighborhood">Bairro</Label>
                    <Input
                      id="neighborhood"
                      {...form.register('deliveryAddress.neighborhood')}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="cityState">Cidade/Estado</Label>
                    <Input
                      id="cityState"
                      {...form.register('deliveryAddress.cityState')}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="reference">Referência</Label>
                    <Input
                      id="reference"
                      {...form.register('deliveryAddress.reference')}
                    />
                  </div>
                </div>
              )}
            </div>

            <div className="flex justify-end gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={handleCancelEdit}
              >
                <XCircle className="h-4 w-4 mr-2" />
                Cancelar
              </Button>
              <Button
                type="submit"
                disabled={updateRevisionInfoMutation.isPending}
              >
                {updateRevisionInfoMutation.isPending ? (
                  <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                Salvar Alterações
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    );
  };

  // Diálogo para edição de itens do pedido
  const EditOrderItemsDialog = () => {
    const handleUpdateAndCalculateSubtotal = (id: number, quantity: number, unitPrice: number) => {
      // Atualizar quantidade e preço
      setEditingItems(items => items.map(item => {
        if (item.id === id) {
          const updatedItem = { ...item, quantity, unitPrice };
          // Calcular o subtotal usando a função calculateItemTotal
          const subtotal = calculateItemTotal(updatedItem);
          return { ...updatedItem, subtotal };
        }
        return item;
      }));
    };

    return (
      <Dialog open={isEditingItems} onOpenChange={(open) => !open && handleCancelEdit()}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Editar Itens da Revisão</DialogTitle>
            <DialogDescription>
              Atualize os itens desta revisão do pedido.
            </DialogDescription>
          </DialogHeader>

          <div className="my-4">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[320px]">Produto</TableHead>
                  <TableHead>Quantidade</TableHead>
                  <TableHead>Preço Unitário</TableHead>
                  <TableHead>Subtotal</TableHead>
                  <TableHead>Observação</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {editingItems.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell className="flex items-center">
                      {item.productImage && (
                        <img
                          src={item.productImage}
                          alt={item.productName}
                          className="w-10 h-10 object-cover rounded-md mr-2"
                        />
                      )}
                      <div>
                        <div className="font-medium">{item.productName}</div>
                        {item.selectedVariations && item.selectedVariations.length > 0 && (
                          <div className="text-xs text-muted-foreground mt-1">
                            {item.selectedVariations.map((v: any, i: number) => (
                              <span key={`${v.variationId}-${v.optionId}`}>
                                {v.variationName}: {v.optionName}
                                {i < item.selectedVariations.length - 1 ? ', ' : ''}
                              </span>
                            ))}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8 w-8 p-0"
                          onClick={() => handleUpdateAndCalculateSubtotal(item.id, Math.max(1, item.quantity - 1), item.unitPrice)}
                        >
                          <Minus className="h-3 w-3" />
                        </Button>
                        <Input
                          type="number"
                          value={item.quantity}
                          onChange={(e) => handleUpdateAndCalculateSubtotal(item.id, parseInt(e.target.value) || 1, item.unitPrice)}
                          className="w-14 h-8 text-center"
                          min="1"
                        />
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8 w-8 p-0"
                          onClick={() => handleUpdateAndCalculateSubtotal(item.id, item.quantity + 1, item.unitPrice)}
                        >
                          <Plus className="h-3 w-3" />
                        </Button>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Input
                        type="number"
                        value={item.unitPrice}
                        onChange={(e) => handleUpdateAndCalculateSubtotal(item.id, item.quantity, parseFloat(e.target.value) || 0)}
                        className="w-20 h-8"
                        min="0"
                        step="0.01"
                      />
                    </TableCell>
                    <TableCell>
                      {formatCurrency(calculateItemTotal(item), store?.currency || '$')}
                    </TableCell>
                    <TableCell>
                      <Textarea
                        value={item.observation || ''}
                        onChange={(e) => handleUpdateItemObservation(item.id, e.target.value)}
                        className="min-h-[60px] text-sm"
                        placeholder="Observação"
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
              <TableFooter>
                <TableRow>
                  <TableCell colSpan={3} className="text-right font-medium">Total:</TableCell>
                  <TableCell className="font-bold">
                    {formatCurrency(
                      editingItems.reduce((sum, item) => sum + calculateItemTotal(item), 0),
                      store?.currency || '$'
                    )}
                  </TableCell>
                  <TableCell></TableCell>
                </TableRow>
              </TableFooter>
            </Table>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleCancelEdit}
            >
              Cancelar
            </Button>
            <Button
              onClick={handleSaveItems}
              disabled={updateRevisionItemsMutation.isPending}
            >
              {updateRevisionItemsMutation.isPending ? (
                <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              Salvar Alterações
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  };

  return (
    <AdminLayout title={`${t('orders.orderDetails')} #${id}`}>
      {/* Diálogos de edição */}
      <EditOrderItemsDialog />

      <div className="mb-6 flex justify-between items-center">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => {
              // Se veio da página de detalhes do cliente, voltar para lá
              if (referer === 'customer' && customerId) {
                // Limpar o localStorage após usar
                localStorage.removeItem('orderReferer');
                localStorage.removeItem('orderRefererId');
                setLocation(`/admin/customers/${customerId}`);
              } else {
                // Caso contrário, voltar para a lista de pedidos
                setLocation('/admin/orders');
              }
            }}
            size="sm"
            className="text-sm"
          >
            {t('common.goBack')}
          </Button>

          {/* Botão de preview */}
          {order && (
            <Button
              variant="outline"
              size="sm"
              className="text-sm flex items-center"
              onClick={() => setLocation(`/admin/orders/preview/${id}`)}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4 mr-2">
                <polyline points="6 9 6 2 18 2 18 9"></polyline>
                <path d="M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2"></path>
                <rect x="6" y="14" width="12" height="8"></rect>
              </svg>
              {t('orders.preview') || "Visualizar Pedido"}
            </Button>
          )}
        </div>

        {/* Botão de criar revisão - só exibir se não houver revisões */}
        {!(revisions && revisions.length > 0) && (
          <Button
            variant="outline"
            size="sm"
            className="text-sm flex items-center"
            onClick={() => {
              setIsCreatingRevision(true);
              createRevisionMutation.mutate();
            }}
            disabled={isCreatingRevision || createRevisionMutation.isPending}
          >
            {isCreatingRevision || createRevisionMutation.isPending ? (
              <div className="h-4 w-4 border-2 border-primary border-t-transparent rounded-full animate-spin mr-2" />
            ) : (
              <FileEdit className="h-4 w-4 mr-2" />
            )}
            Criar Revisão
          </Button>
        )}
      </div>

      {isLoading ? (
        <div className="space-y-6">
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-40 w-full" />
          <Skeleton className="h-60 w-full" />
        </div>
      ) : order ? (
        <div className="space-y-6">
          {/* Order Status */}
          <div className="flex items-center justify-between bg-card p-4 rounded-lg border">
            <div className="flex items-center">
              <Package className="mr-2 h-5 w-5 text-muted-foreground" />
              <span className="font-medium">{t('orders.orderStatus')}:</span>
            </div>
            <div className="flex items-center space-x-3">
              <OrderStatusBadge status={order.status} />
              <Select
                defaultValue={order.status}
                onValueChange={handleStatusChange}
                disabled={updateStatusMutation.isPending}
              >
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder={t('orders.updateStatus')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pending">{t('dashboard.pending')}</SelectItem>
                  <SelectItem value="confirmed">Confirmado</SelectItem>
                  <SelectItem value="delivered">Entregue</SelectItem>
                  <SelectItem value="cancelled">{t('dashboard.cancelled')}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Card de Revisões do Pedido */}
          {isLoadingRevisions ? (
            <Card className="p-4 text-center">
              <div className="h-5 w-5 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto" />
              <p className="text-sm text-muted-foreground mt-2">Carregando revisões...</p>
            </Card>
          ) : revisions && revisions.length > 0 ? (
            <Card className="p-0 overflow-hidden">
              <div className="bg-muted p-4 border-b flex justify-between items-center">
                <h3 className="text-md font-medium flex items-center">
                  <History className="h-4 w-4 mr-2" />
                  Revisões do Pedido
                </h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => refetchRevisions()}
                  className="text-xs flex items-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1"><path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/><path d="M21 3v5h-5"/><path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/><path d="M3 21v-5h5"/></svg>
                  Atualizar
                </Button>
              </div>
              <div className="p-4">


                <div className="grid gap-2 max-h-[200px] overflow-y-auto p-2 mt-3">
                  {/* Botão para mostrar o pedido original */}
                  <div
                    className={`relative flex items-center p-3 rounded-md border ${!selectedRevisionId
                      ? 'bg-accent/20 border-accent shadow-sm'
                      : 'border-input hover:bg-accent/10'} cursor-pointer`}
                    onClick={() => {
                      setSelectedRevisionId(null);
                      setActiveRevision(null);
                    }}
                  >
                    <div className="flex-1">
                      <div className="font-medium flex items-center">
                        <ShoppingBag className="h-4 w-4 mr-2 text-muted-foreground" />
                        Pedido Original
                        {!revisions.some(rev => rev.isCurrent) && (
                          <Badge className="ml-2 text-xs bg-success/20 text-success border-success/20">Atual</Badge>
                        )}
                      </div>
                      <div className="text-xs text-muted-foreground">Criado em {formatDate(order.createdAt)}</div>
                    </div>
                    <div className="flex flex-col items-end gap-2">
                      {!selectedRevisionId && (
                        <Badge variant="outline" className="text-xs text-accent-foreground border-accent mb-1">Visualizando</Badge>
                      )}

                      {/* O botão para definir o pedido original como atual foi removido */}
                    </div>
                  </div>

                  {/* Lista das revisões */}
                  {revisions.map((revision) => (
                    <div
                      key={revision.id}
                      className={`relative flex items-center p-3 rounded-md border ${selectedRevisionId === revision.id
                        ? 'bg-accent/20 border-accent shadow-sm'
                        : 'border-input hover:bg-accent/10'} cursor-pointer`}
                      onClick={() => {
                        setSelectedRevisionId(revision.id);
                        setActiveRevision(revision); // Defina a revisão ativa imediatamente para atualização rápida da UI
                      }}
                    >
                      <div className="flex-1">
                        <div className="font-medium flex items-center">
                          <FileEdit className="h-4 w-4 mr-2 text-muted-foreground" />
                          Revisão #{revision.revisionNumber}
                          {revision.isCurrent && (
                            <Badge className="ml-2 text-xs bg-success/20 text-success border-success/20">Atual</Badge>
                          )}
                        </div>
                        <div className="text-xs text-muted-foreground">Criada em {formatDate(revision.createdAt)}</div>
                      </div>

                      <div className="flex flex-col items-end gap-2">
                        {selectedRevisionId === revision.id && (
                          <Badge variant="outline" className="text-xs text-accent-foreground border-accent mb-1">Visualizando</Badge>
                        )}

                        {/* O botão para definir revisão como atual foi removido */}

                        {/* Botão para excluir revisão */}
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8 text-xs text-destructive border-destructive/30 hover:bg-destructive/10"
                          onClick={(e) => {
                            e.stopPropagation();
                            if (window.confirm(`Tem certeza que deseja excluir a Revisão #${revision.revisionNumber}? Esta ação não pode ser desfeita.`)) {
                              deleteRevisionMutation.mutate(revision.id);
                            }
                          }}
                          disabled={deleteRevisionMutation.isPending}
                        >
                          {deleteRevisionMutation.isPending ? (
                            <div className="h-3 w-3 border-2 border-destructive border-t-transparent rounded-full animate-spin mr-1" />
                          ) : (
                            <Trash2 className="h-3 w-3 mr-1" />
                          )}
                          Excluir
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </Card>
          ) : null}

          {/* Formulário de edição de informações - mostrado apenas durante a edição */}
          {isEditingInfo && selectedRevisionId && <EditOrderInfoForm />}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Order Info */}
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    <h3 className="text-lg font-medium">
                      {t('orders.orderInfo')}
                      {/* Badge de revisão apenas visível quando uma revisão estiver ativa */}
                      {activeRevision && (
                        <Badge className="ml-2 text-xs" variant="outline">
                          Revisão #{activeRevision.revisionNumber}
                        </Badge>
                      )}
                    </h3>
                  </div>

                  {/* Botões de edição apenas visíveis quando uma revisão estiver selecionada */}
                  {activeRevision && selectedRevisionId && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="text-xs flex items-center"
                      onClick={handleEditInfo}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1"><path d="M12 20h9"></path><path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path></svg>
                      Editar Informações
                    </Button>
                  )}
                </div>

                <div className="space-y-4">
                  <div className="flex items-start">
                    <Calendar className="h-5 w-5 mr-2 text-muted-foreground mt-0.5" />
                    <div>
                      <div className="text-sm text-muted-foreground">{t('orders.dateCreated')}</div>
                      <div>{formatDate(order.createdAt)}</div>
                    </div>
                  </div>

                  {/* Dados de Entrega/Retirada */}
                  <div className="bg-gray-50 p-3 rounded-md border-l-4"
                      style={{ borderLeftColor: store?.colors?.primary || '#4361EE' }}>
                    <h4 className="text-sm font-semibold mb-2">
                      {order.receivingMethod === 'delivery'
                        ? t('storefront.deliveryDetails') || 'Detalhes da Entrega'
                        : t('storefront.pickupDetails') || 'Detalhes da Retirada'}
                    </h4>

                    {/* Método (Entrega ou Retirada) */}
                    <div className="mb-2 flex items-center">
                      {/* Usar os dados da revisão ativa se estiver disponível, senão usar os dados originais */}
                      {(activeRevision?.receivingMethod || order.receivingMethod) === 'delivery' ? (
                        <Truck className="h-4 w-4 mr-2" style={{ color: store?.colors?.primary || '#4361EE' }} />
                      ) : (
                        <Store className="h-4 w-4 mr-2" style={{ color: store?.colors?.primary || '#4361EE' }} />
                      )}
                      <span className="text-sm font-medium">{t('storefront.receivingMethod') || 'Método de recebimento'}:</span>
                      <span className="ml-1">
                        {(activeRevision?.receivingMethod || order.receivingMethod) === 'delivery'
                          ? t('storefront.delivery') || 'Entrega'
                          : t('storefront.pickup') || 'Retirada'}
                      </span>
                    </div>

                    {/* Data e Horário */}
                    {(activeRevision?.receivingDate || order.receivingDate) && (
                      <div className="mb-2 flex">
                        <Calendar className="h-4 w-4 mr-2 mt-0.5" style={{ color: store?.colors?.primary || '#4361EE' }} />
                        <div>
                          <div className="flex items-center">
                            <span className="text-sm font-medium">{t('storefront.date') || 'Data'}:</span>
                            <span className="ml-1">{formatDate(activeRevision?.receivingDate || order.receivingDate)}</span>
                          </div>
                          {(activeRevision?.receivingTime || order.receivingTime) && (
                            <div className="flex items-center mt-1">
                              <Clock className="h-4 w-4 mr-2" style={{ color: store?.colors?.primary || '#4361EE' }} />
                              <span className="text-sm font-medium">{t('storefront.time') || 'Horário'}:</span>
                              <span className="ml-1">{activeRevision?.receivingTime || order.receivingTime}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Endereço de Entrega */}
                    {(activeRevision?.receivingMethod || order.receivingMethod) === 'delivery' &&
                     (activeRevision?.deliveryAddress || order.deliveryAddress) && (
                      <div className="mt-2 flex">
                        <MapPin className="h-4 w-4 mr-2 mt-0.5" style={{ color: store?.colors?.primary || '#4361EE' }} />
                        <div>
                          <span className="text-sm font-medium block mb-1">
                            {t('storefront.deliveryAddressTitle') || 'Endereço de entrega'}:
                          </span>
                          <div className="text-sm text-muted-foreground pl-2 border-l-2 border-muted">
                            {/* Usar o endereço da revisão ativa se disponível, senão usar o endereço original */}
                            {(activeRevision?.deliveryAddress || order.deliveryAddress)?.street &&
                             (activeRevision?.deliveryAddress || order.deliveryAddress)?.number && (
                              <p>
                                {(activeRevision?.deliveryAddress || order.deliveryAddress).street},
                                {(activeRevision?.deliveryAddress || order.deliveryAddress).number}
                              </p>
                            )}
                            {(activeRevision?.deliveryAddress || order.deliveryAddress)?.neighborhood &&
                             (activeRevision?.deliveryAddress || order.deliveryAddress)?.cityState && (
                              <p>
                                {(activeRevision?.deliveryAddress || order.deliveryAddress).neighborhood},
                                {(activeRevision?.deliveryAddress || order.deliveryAddress).cityState}
                              </p>
                            )}
                            {(activeRevision?.deliveryAddress || order.deliveryAddress)?.reference && (
                              <p className="italic mt-1">
                                {t('storefront.reference') || 'Referência'}:
                                {(activeRevision?.deliveryAddress || order.deliveryAddress).reference}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="flex items-start">
                    <Package className="h-5 w-5 mr-2 text-muted-foreground mt-0.5" />
                    <div>
                      <div className="text-sm text-muted-foreground">{t('orders.paymentMethod')}</div>
                      <div>
                        {(() => {
                          // Converter o ID do método de pagamento para o nome descritivo
                          // Usar o método de pagamento da revisão ativa se disponível, senão usar o original
                          const paymentMethodId = activeRevision?.paymentMethod || order.paymentMethod;
                          let displayName = paymentMethodId;

                          if (paymentMethodId && paymentMethodId.startsWith('custom_')) {
                            // Para métodos personalizados, obter o índice e buscar o texto correspondente
                            const customIndex = parseInt(paymentMethodId.replace('custom_', ''), 10);
                            if (!isNaN(customIndex) &&
                                store?.paymentMethods?.customMethods &&
                                Array.isArray(store.paymentMethods.customMethods) &&
                                customIndex < store.paymentMethods.customMethods.length) {
                              displayName = store.paymentMethods.customMethods[customIndex];
                            }
                          } else {
                            // Para métodos padrão, buscar o nome traduzido
                            switch(paymentMethodId) {
                              case 'cash':
                                displayName = t('storefront.cash');
                                break;
                              case 'creditCard':
                                displayName = t('storefront.creditCard');
                                break;
                              case 'debitCard':
                                displayName = t('storefront.debitCard');
                                break;
                              case 'pix':
                                displayName = t('storefront.pix');
                                break;
                              case 'bankTransfer':
                                displayName = t('storefront.bankTransfer');
                                break;
                            }
                          }

                          return displayName;
                        })()}
                      </div>
                    </div>
                  </div>

                  {/* Mostrar as observações da revisão ativa se disponível, senão mostrar as originais */}
                  {(activeRevision?.notes || order.notes) && (
                    <div className="flex items-start">
                      <InfoIcon className="h-5 w-5 mr-2 text-muted-foreground mt-0.5" />
                      <div>
                        <div className="text-sm text-muted-foreground">{t('orders.notes')}</div>
                        <div>{activeRevision?.notes || order.notes}</div>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Customer Info */}
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium flex items-center">
                    <User className="mr-2 h-5 w-5 text-muted-foreground" />
                    {t('orders.customerInfo')}
                  </h3>

                  {/* Botão de troca de cliente - apenas visível quando uma revisão estiver selecionada */}
                  {activeRevision && selectedRevisionId && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="text-xs flex items-center"
                      onClick={handleChangeCustomer}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg>
                      {t('orders.changeCustomer')}
                    </Button>
                  )}
                </div>

                <div className="space-y-4">
                  {/* Mostrar informações do cliente da revisão ativa se disponível, senão mostrar as do pedido original */}
                  {(activeRevision?.customer || order.customer) ? (
                    <>
                      <div>
                        <div className="text-sm text-muted-foreground">{t('customers.name')}</div>
                        <div>{activeRevision?.customer?.name || order.customer.name}</div>
                      </div>

                      <div>
                        <div className="text-sm text-muted-foreground">{t('customers.email')}</div>
                        <div>{activeRevision?.customer?.email || order.customer.email}</div>
                      </div>

                      {(activeRevision?.customer?.phone || order.customer.phone) && (
                        <div>
                          <div className="text-sm text-muted-foreground">{t('customers.phone')}</div>
                          <div className="flex items-center space-x-2">
                            <span>{formatPhoneWithCountryCode(
                              activeRevision?.customer?.phone || order.customer.phone,
                              activeRevision?.customer?.countryCode || order.customer.countryCode || store?.countryCode
                            )}</span>
                            <Badge
                              className="bg-green-600 hover:bg-green-700 cursor-pointer"
                              onClick={() => {
                                const customer = activeRevision?.customer || order.customer;
                                const countryCode = customer.countryCode || store?.countryCode || "+55";
                                const phoneNumber = customer.phone.replace(/\D/g, '');
                                const formattedNumber = `${countryCode.replace('+', '')}${phoneNumber}`;
                                window.open(`https://wa.me/${formattedNumber}`, '_blank');
                              }}
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="12"
                                height="12"
                                viewBox="0 0 24 24"
                                fill="white"
                                stroke="currentColor"
                                strokeWidth="0"
                                className="mr-1"
                              >
                                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347z" />
                              </svg>
                              WhatsApp
                            </Badge>
                          </div>
                        </div>
                      )}

                      <div className="flex justify-end">
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-sm"
                          onClick={() => {
                            // Salvar a origem para poder voltar
                            localStorage.setItem('orderReferer', 'order');
                            localStorage.setItem('orderRefererId', id.toString());
                            const customerId = activeRevision?.customer?.id || order.customer.id;
                            setLocation(`/admin/customers/${customerId}`);
                          }}
                        >
                          {t('common.viewDetails')}
                        </Button>
                      </div>
                    </>
                  ) : (
                    <div className="text-center py-4 text-muted-foreground">
                      {t('customers.noInformation')}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Order Items */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <ShoppingBag className="mr-2 h-5 w-5 text-muted-foreground" />
                  <h3 className="text-lg font-medium">
                    {t('orders.orderItems')}
                    {/* Badge de revisão apenas visível quando uma revisão estiver ativa */}
                    {activeRevision && (
                      <Badge className="ml-2 text-xs" variant="outline">
                        Revisão #{activeRevision.revisionNumber}
                      </Badge>
                    )}
                  </h3>
                </div>

                {/* Botão de adicionar produto apenas visível quando uma revisão estiver selecionada */}
                {activeRevision && selectedRevisionId && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-xs flex items-center"
                    onClick={handleAddProduct}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1"><path d="M12 5v14M5 12h14"></path></svg>
                    {t('orders.addProduct') || 'Adicionar Produto'}
                  </Button>
                )}
              </div>

              {activeRevision ? (
                // Renderizar itens da revisão ativa
                activeRevision.items && activeRevision.items.length > 0 ? (
                  <div className="pb-4">


                      {/* Layout mobile otimizado para iOS */}
                      <div className="md:hidden space-y-6">
                          {/* Card para lista de itens do pedido */}
                          <Card className="overflow-hidden shadow-sm">
                            <CardHeader className="bg-gradient-to-r from-blue-50 to-blue-100/50 px-4 py-3 border-b">
                              <CardTitle className="text-base flex items-center">
                                <ShoppingBag className="h-4 w-4 mr-2 text-blue-600" />
                                {t('orders.orderItems') || "Itens do Pedido"}
                              </CardTitle>
                            </CardHeader>
                            <CardContent className="p-4">
                              <div className="space-y-4">
                                {activeRevision.items.map((item: any) => (
                                  <div
                                    key={item.id}
                                    className="border rounded-lg overflow-hidden shadow-sm relative"
                                    onClick={() => activeRevision && selectedRevisionId ? handleEditItems(item) : undefined}
                                    style={activeRevision && selectedRevisionId ? {cursor: 'pointer'} : {}}
                                  >
                                    {/* Indicador visual de item editável */}
                                    {activeRevision && selectedRevisionId && (
                                      <div className="absolute top-2 right-2 text-primary bg-white/80 rounded-full p-1 shadow-sm">
                                        <Pencil className="h-3.5 w-3.5" />
                                      </div>
                                    )}
                                    <div className="p-3 flex items-start gap-3">
                                      {item.productImage ? (
                                        <div className="w-20 h-20 rounded-md overflow-hidden flex-shrink-0">
                                          <img
                                            src={item.productImage}
                                            alt={item.productName}
                                            className="w-full h-full object-cover"
                                          />
                                        </div>
                                      ) : (
                                        <div className="w-20 h-20 bg-muted rounded-md flex items-center justify-center flex-shrink-0">
                                          <ShoppingBag className="h-8 w-8 text-muted-foreground" />
                                        </div>
                                      )}

                                      <div className="flex-1 min-w-0">
                                        <div className="flex items-center justify-between mb-1">
                                          <h3 className="font-medium text-base line-clamp-2 pr-2">{item.product?.name || item.name || item.productName || "Produto Personalizado"}</h3>
                                          {activeRevision && selectedRevisionId && (
                                            <Pencil className="h-3.5 w-3.5 text-muted-foreground flex-shrink-0" />
                                          )}
                                        </div>

                                        <div className="grid grid-cols-2 gap-x-2 gap-y-1 mt-2 text-sm">
                                          <span className="text-muted-foreground">{t('products.quantity') || "Qtd"}:</span>
                                          <span className="font-medium text-right">{item.quantity}</span>

                                          <span className="text-muted-foreground">{t('products.unitPrice') || "Preço Unit."}:</span>
                                          <span className="font-medium text-right">{formatCurrency(item.unitPrice)}</span>
                                        </div>
                                      </div>
                                    </div>

                                    {/* Variações e observações */}
                                    {((Array.isArray(item.selectedVariations) && item.selectedVariations.length > 0) || item.observation) && (
                                      <div className="px-3 pb-3 pt-0">
                                        {item.selectedVariations && Array.isArray(item.selectedVariations) && item.selectedVariations.length > 0 && (
                                          <div className="bg-accent/5 p-2 rounded-md mb-2">
                                            <div className="text-xs font-medium mb-1 text-muted-foreground">
                                              {t('orders.selectedOptions') || 'Opções selecionadas'}:
                                            </div>
                                            <div className="space-y-1">
                                              {Array.isArray(item.selectedVariations) ? item.selectedVariations.map((option: any, index: number) => (
                                                <div key={index} className="flex items-center justify-between text-xs">
                                                  <div>
                                                    <span className="font-medium">{option.variationName}:</span>
                                                    <span className="ml-1">{option.optionName}</span>
                                                    {option.quantity > 1 && (
                                                      <span className="ml-1">x{option.quantity}</span>
                                                    )}
                                                  </div>
                                                  {option.price > 0 && (
                                                    <span className="text-primary">
                                                      (+{formatCurrency(option.price * (option.quantity || 1))}
                                                      {/* Mostrar a quantidade do produto apenas para variações que não são do tipo "outros" */}
                                                      {item.quantity > 1 && !(option.variationName?.toLowerCase() === 'outros' || option.optionName?.toLowerCase() === 'outros') &&
                                                        ` (${item.quantity}x)`
                                                      })
                                                    </span>
                                                  )}
                                                </div>
                                              )) : (
                                                <div className="text-xs text-muted-foreground">Sem opções selecionadas</div>
                                              )}
                                            </div>
                                          </div>
                                        )}

                                        {item.observation && (
                                          <div className="text-xs">
                                            <span className="font-medium text-muted-foreground">{t('orders.observation') || "Observação"}:</span>
                                            <p className="mt-1 bg-muted/30 p-2 rounded italic">{item.observation}</p>
                                          </div>
                                        )}
                                      </div>
                                    )}

                                    {/* Total do item - movido para abaixo das variações */}
                                    <div className="mt-3 pt-2 border-t flex justify-between items-center p-3">
                                      <div className="flex items-center gap-2">
                                        <span className="text-sm font-medium">{t('products.total') || "Total"}:</span>
                                        {/* Botão de remover item (apenas visível quando uma revisão estiver selecionada) */}
                                        {activeRevision && selectedRevisionId && (
                                          <Button
                                            variant="ghost"
                                            size="icon"
                                            className="h-7 w-7 rounded-full text-red-600 hover:bg-red-50 hover:text-red-700"
                                            onClick={(e) => {
                                              e.stopPropagation(); // Impedir que o clique propague para o card
                                              handleRemoveRevisionItem(item.id);
                                            }}
                                          >
                                            <Trash2 className="h-4 w-4" />
                                          </Button>
                                        )}
                                      </div>
                                      <span className="text-sm font-bold text-primary">{formatCurrency(calculateItemTotal(item))}</span>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </CardContent>
                          </Card>

                          {/* Card separado para o resumo financeiro (mobile) */}
                          <Card className="mt-8 overflow-hidden shadow-sm">
                            <CardHeader className="bg-gradient-to-r from-primary-50 to-primary-100/50 px-4 py-3 border-b">
                              <CardTitle className="text-base flex items-center">
                                <Receipt className="h-4 w-4 mr-2 text-primary" />
                                {t('orders.orderSummary') || "Resumo Financeiro"}
                              </CardTitle>
                            </CardHeader>
                            <CardContent className="p-4 space-y-4 bg-white">
                              {/* Itens e valores */}
                              <div className="grid grid-cols-1 gap-3">
                                {/* Subtotal dos produtos */}
                                <div className="flex justify-between items-center py-2">
                                  <span className="text-sm font-medium">
                                    {t('orders.subtotal')}:
                                  </span>
                                  <span className="font-medium">{formatCurrency(calculateRevisionSubtotal(activeRevision), store?.currency)}</span>
                                </div>

                                {/* Desconto - Com visual melhorado */}
                                <div className="py-2 border-t border-dashed border-muted/50">
                                  {!isEditingDiscount ? (
                                    <div className="flex justify-between items-center">
                                      <span className={`text-sm flex items-center ${activeRevision.discount > 0 ? "text-green-600" : "text-muted-foreground"}`}>
                                        <Tag className="h-3.5 w-3.5 mr-1.5" />
                                        {((activeRevision.discount_type === 'percentage' || activeRevision.discountType === 'percentage') &&
                                          (activeRevision.original_percentage > 0 || activeRevision.originalPercentage > 0))
                                          ? (t('storefront.discountPercentage', {
                                              value: activeRevision.original_percentage || activeRevision.originalPercentage
                                            }) || `Desconto (${activeRevision.original_percentage || activeRevision.originalPercentage}%):`)
                                          : (t('storefront.discountFixed') || "Desconto (Fixo):")}
                                      </span>
                                      <div className="flex items-center gap-2">
                                        {activeRevision && selectedRevisionId && (
                                        <Button
                                          variant="ghost"
                                          size="icon"
                                          className="h-6 w-6 rounded-full mr-1"
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            handleStartEditingDiscount();
                                          }}
                                        >
                                          <Pencil className="h-3 w-3 text-muted-foreground" />
                                        </Button>
                                      )}
                                      <span className={calculateRevisionDiscount(activeRevision) > 0 ? "font-medium text-green-600" : "font-medium"}>
                                        {calculateRevisionDiscount(activeRevision) > 0 ? `-${formatCurrency(calculateRevisionDiscount(activeRevision), store?.currency)}` : formatCurrency(0, store?.currency)}
                                      </span>
                                      </div>
                                    </div>
                                  ) : (
                                    <div className="space-y-2">
                                      <div className="flex justify-between items-center">
                                        <span className="text-sm text-muted-foreground flex items-center">
                                          <Tag className="h-3.5 w-3.5 mr-1.5" />
                                          {t('storefront.discount') || "Desconto"}:
                                        </span>
                                        <div className="flex flex-wrap items-center gap-2 justify-end">
                                          <Select
                                            value={discountType}
                                            onValueChange={(value) => {
                                              const newType = value as 'fixed' | 'percentage';
                                              setDiscountType(newType);

                                              // Atualizar o campo de texto com base no novo tipo
                                              const percentValue =
                                                activeRevision.original_percentage !== undefined && activeRevision.original_percentage !== null
                                                  ? activeRevision.original_percentage
                                                  : (activeRevision.originalPercentage !== undefined && activeRevision.originalPercentage !== null
                                                      ? activeRevision.originalPercentage
                                                      : 0);

                                              if (newType === 'percentage' && percentValue > 0) {
                                                // Se mudar para percentual, mostrar o valor percentual
                                                console.log(`Mudando para percentual: ${percentValue}%`);
                                                setTempDiscountText(String(percentValue.toFixed(2)).replace('.', ','));
                                              } else if (newType === 'fixed') {
                                                // Se mudar para fixo, mostrar o valor monetário
                                                console.log(`Mudando para fixo: ${activeRevision.discount}`);
                                                setTempDiscountText(String((activeRevision.discount || 0).toFixed(2)).replace('.', ','));
                                              }
                                            }}
                                          >
                                            <SelectTrigger className="w-24 h-8 text-xs">
                                              <SelectValue placeholder="Tipo" />
                                            </SelectTrigger>
                                            <SelectContent>
                                              <SelectItem value="fixed">{t('coupons.fixedValue') || "Valor Fixo"}</SelectItem>
                                              <SelectItem value="percentage">{t('coupons.percentage') || "Percentual"}</SelectItem>
                                            </SelectContent>
                                          </Select>
                                          <div className="relative">
                                            <span className="absolute left-2 top-1/2 -translate-y-1/2 text-muted-foreground text-xs">
                                              {discountType === 'fixed' ? (store?.currency || 'R$') : '%'}
                                            </span>
                                            <Input
                                              type="text"
                                              value={tempDiscountText}
                                              onChange={(e) => {
                                                const inputValue = e.target.value;
                                                const regex = /^[0-9]*[.,]?[0-9]*$/;
                                                if (regex.test(inputValue) || inputValue === '') {
                                                  setTempDiscountText(inputValue);
                                                }
                                              }}
                                              className="w-24 h-8 pl-8 text-sm"
                                              placeholder="0,00"
                                            />
                                          </div>
                                          <div className="flex gap-1">
                                            <Button
                                              variant="ghost"
                                              size="icon"
                                              className="h-7 w-7 rounded-full bg-green-50 text-green-600 hover:bg-green-100 hover:text-green-700"
                                              onClick={(e) => {
                                                e.stopPropagation();
                                                handleSaveDiscount();
                                              }}
                                              disabled={updateDiscountMutation.isPending}
                                            >
                                              {updateDiscountMutation.isPending ? (
                                                <div className="h-3 w-3 border-2 border-green-600 border-t-transparent rounded-full animate-spin" />
                                              ) : (
                                                <Check className="h-3 w-3" />
                                              )}
                                            </Button>
                                            <Button
                                              variant="ghost"
                                              size="icon"
                                              className="h-7 w-7 rounded-full bg-red-50 text-red-600 hover:bg-red-100 hover:text-red-700"
                                              onClick={(e) => {
                                                e.stopPropagation();
                                                handleCancelEditingDiscount();
                                              }}
                                            >
                                              <XCircle className="h-3 w-3" />
                                            </Button>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  )}
                                </div>
                              </div>

                              {/* Informações do cupom (se houver) - Com visual melhorado */}
                              {activeRevision.discount > 0 && activeRevision.couponCode && (
                                <div className="p-2 bg-green-50 rounded-md mt-2">
                                  <div className="flex flex-wrap justify-between items-center gap-2">
                                    <div className="flex-1 min-w-0 flex items-start">
                                      <Ticket className="h-3.5 w-3.5 mr-1.5 mt-0.5 text-green-600" />
                                      <div>
                                        <span className="text-xs text-green-700 block font-medium">{t('storefront.couponCode') || "Código do cupom"}:</span>
                                        <span className="text-xs font-medium block truncate text-green-600">
                                          {activeRevision.couponCode}
                                        </span>
                                      </div>
                                    </div>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => {
                                        if (confirm(t('coupons.confirmRemove') || "Tem certeza que deseja remover o cupom desta revisão?")) {
                                          removeCouponFromRevision(activeRevision.id);
                                        }
                                      }}
                                      className="h-7 text-red-600 hover:text-red-700 hover:bg-red-50"
                                    >
                                      <X className="h-3 w-3 mr-1" />
                                      {t('storefront.remove') || "Remover"}
                                    </Button>
                                  </div>
                                </div>
                              )}

                              {/* Taxa de Entrega - Com visual melhorado */}
                              <div className="py-2 border-t border-dashed border-muted/50 mt-2">
                                {!isEditingDeliveryFee ? (
                                  <div className="flex justify-between items-center">
                                    <span className="text-sm text-muted-foreground flex items-center">
                                      <Truck className="h-3.5 w-3.5 mr-1.5 text-muted-foreground/80" />
                                      {t('orders.deliveryFee')}:
                                    </span>
                                    <div className="flex items-center gap-2">
                                      {activeRevision && selectedRevisionId && (
                                        <Button
                                          variant="ghost"
                                          size="icon"
                                          className="h-6 w-6 rounded-full hover:bg-muted/50"
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            handleStartEditingDeliveryFee();
                                          }}
                                        >
                                          <Pencil className="h-3 w-3 text-muted-foreground" />
                                        </Button>
                                      )}
                                      <span className="font-medium">{formatCurrency(activeRevision.deliveryFee || 0, store?.currency)}</span>
                                    </div>
                                  </div>
                                ) : (
                                  <div className="space-y-2">
                                    <div className="flex justify-between items-center">
                                      <span className="text-sm text-muted-foreground flex items-center">
                                        <Truck className="h-3.5 w-3.5 mr-1.5 text-muted-foreground/80" />
                                        {t('orders.deliveryFee')}:
                                      </span>
                                      <div className="flex items-center gap-2">
                                        <div className="relative">
                                          <span className="absolute left-2 top-1/2 -translate-y-1/2 text-muted-foreground text-xs">
                                            {store?.currency || 'R$'}
                                          </span>
                                          <Input
                                            type="text"
                                            value={tempDeliveryFeeText}
                                            onChange={(e) => {
                                              const inputValue = e.target.value;
                                              const regex = /^[0-9]*[.,]?[0-9]*$/;
                                              if (regex.test(inputValue) || inputValue === '') {
                                                setTempDeliveryFeeText(inputValue);
                                              }
                                            }}
                                            className="w-24 h-8 pl-8 text-sm"
                                            placeholder="0,00"
                                          />
                                        </div>
                                        <div className="flex gap-1">
                                          <Button
                                            variant="ghost"
                                            size="icon"
                                            className="h-7 w-7 rounded-full bg-green-50 text-green-600 hover:bg-green-100 hover:text-green-700"
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              handleSaveDeliveryFee();
                                            }}
                                            disabled={updateDeliveryFeeMutation.isPending}
                                          >
                                            {updateDeliveryFeeMutation.isPending ? (
                                              <div className="h-3 w-3 border-2 border-green-600 border-t-transparent rounded-full animate-spin" />
                                            ) : (
                                              <Check className="h-3 w-3" />
                                            )}
                                          </Button>
                                          <Button
                                            variant="ghost"
                                            size="icon"
                                            className="h-7 w-7 rounded-full bg-red-50 text-red-600 hover:bg-red-100 hover:text-red-700"
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              handleCancelEditingDeliveryFee();
                                            }}
                                          >
                                            <XCircle className="h-3 w-3" />
                                          </Button>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                )}
                              </div>

                              {/* Total - Com visual melhorado */}
                              <div className="mt-5 pt-5 border-t-2 border-primary/20 bg-primary/5 -mx-4 -mb-4 p-4 rounded-b-lg">
                                <div className="flex justify-between items-center">
                                  <span className="font-medium flex items-center">
                                    <CreditCard className="h-4 w-4 mr-2 text-primary/80" />
                                    {t('orders.orderTotal')}:
                                  </span>
                                  <span className="font-bold text-xl text-primary">
                                    {formatCurrency(calculateRevisionSubtotal(activeRevision) - calculateRevisionDiscount(activeRevision) + (activeRevision.deliveryFee || 0), store?.currency)}
                                  </span>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        </div>

                        {/* Layout desktop (tabela) */}
                        <div className="hidden md:block space-y-6">
                          {/* Card para lista de itens do pedido */}
                          <Card className="overflow-hidden shadow-sm">
                            <CardHeader className="bg-gradient-to-r from-blue-50 to-blue-100/50 px-6 py-4 border-b">
                              <CardTitle className="text-lg flex items-center">
                                <ShoppingBag className="h-5 w-5 mr-2 text-blue-600" />
                                {t('orders.orderItems') || "Itens do Pedido"}
                              </CardTitle>
                            </CardHeader>
                            <CardContent className="p-6">
                              <Table>
                                <TableHeader>
                                  <TableRow>
                                    <TableHead>{t('products.product') || "Produto"}</TableHead>
                                    <TableHead className="text-right">
                                      {t('products.unitPrice') || "Preço Unitário"}
                                    </TableHead>
                                    <TableHead className="text-center">
                                      {t('products.quantity') || "Qtd"}
                                    </TableHead>
                                    <TableHead className="text-right">
                                      {t('products.total') || "Total"}
                                    </TableHead>
                                    {/* Coluna adicional para ações quando uma revisão estiver selecionada */}
                                    {activeRevision && selectedRevisionId && (
                                      <TableHead className="w-[50px]"></TableHead>
                                    )}
                                  </TableRow>
                                </TableHeader>
                                <TableBody>
                                  {activeRevision.items.map((item: any) => (
                                    <TableRow
                                      key={item.id}
                                      className={activeRevision && selectedRevisionId ? "cursor-pointer hover:bg-muted/40" : ""}
                                      onClick={() => activeRevision && selectedRevisionId ? handleEditItems(item) : undefined}
                                    >
                                      <TableCell>
                                        <div className="flex items-center gap-3">
                                          {item.productImage && (
                                            <div className="w-12 h-12 overflow-hidden rounded">
                                              <img
                                                src={item.productImage}
                                                alt={item.productName}
                                                className="w-full h-full object-cover"
                                              />
                                            </div>
                                          )}
                                          <div>
                                            <div className="font-medium">{item.product?.name || item.name || item.productName || "Produto Personalizado"}</div>

                                            {/* Exibição das variações escolhidas */}
                                            {item.selectedVariations && Array.isArray(item.selectedVariations) && item.selectedVariations.length > 0 && (
                                              <div className="text-xs text-muted-foreground mt-1 space-y-0.5">
                                                <span className="font-medium">{t('orders.selectedOptions') || 'Opções selecionadas'}:</span>
                                                <ul className="ml-2">
                                                  {Array.isArray(item.selectedVariations) ? item.selectedVariations.map((option: any, index: number) => (
                                                    <li key={index} className="flex items-center">
                                                      <span className="font-medium">{option.variationName}:</span>
                                                      <span className="ml-1">{option.optionName}</span>
                                                      {option.quantity > 1 && (
                                                        <span className="ml-1">x{option.quantity}</span>
                                                      )}
                                                      {option.price > 0 && (
                                                        <span className="ml-1 text-primary">
                                                          (+{formatCurrency(option.price * (option.quantity || 1), store?.currency)}
                                                          {/* Mostrar a quantidade do produto apenas para variações que não são do tipo "outros" */}
                                                          {item.quantity > 1 && !(option.variationName?.toLowerCase() === 'outros' || option.optionName?.toLowerCase() === 'outros') &&
                                                            ` (${item.quantity}x)`
                                                          })
                                                        </span>
                                                      )}
                                                    </li>
                                                  )) : (
                                                    <li className="text-xs text-muted-foreground">Sem opções selecionadas</li>
                                                  )}
                                                </ul>
                                              </div>
                                            )}

                                            {item.observation && (
                                              <div className="text-xs text-muted-foreground mt-1">
                                                <span className="font-medium">{t('orders.observation')}:</span> {item.observation}
                                              </div>
                                            )}
                                          </div>
                                        </div>
                                      </TableCell>
                                      <TableCell className="text-right">
                                        {formatCurrency(item.unitPrice, store?.currency)}
                                      </TableCell>
                                      <TableCell className="text-center">
                                        {item.quantity}
                                      </TableCell>
                                      <TableCell className="text-right font-medium">
                                        {formatCurrency(calculateItemTotal(item), store?.currency)}
                                      </TableCell>
                                      {/* Coluna de ações para remover o item */}
                                      {activeRevision && selectedRevisionId && (
                                        <TableCell className="text-right">
                                          <Button
                                            variant="ghost"
                                            size="icon"
                                            className="h-7 w-7 rounded-full text-red-600 hover:bg-red-50 hover:text-red-700"
                                            onClick={(e) => {
                                              e.stopPropagation(); // Impedir que o clique propague para a linha
                                              handleRemoveRevisionItem(item.id);
                                            }}
                                          >
                                            <Trash2 className="h-4 w-4" />
                                          </Button>
                                        </TableCell>
                                      )}
                                    </TableRow>
                                  ))}
                                </TableBody>
                              </Table>
                            </CardContent>
                          </Card>

                          {/* Card separado para o resumo financeiro (desktop) */}
                          <Card className="overflow-hidden shadow-sm">
                            <CardHeader className="bg-gradient-to-r from-primary-50 to-primary-100/50 px-6 py-4 border-b">
                              <CardTitle className="text-lg flex items-center">
                                <Receipt className="h-5 w-5 mr-2 text-primary" />
                                {t('orders.orderSummary') || "Resumo Financeiro"}
                              </CardTitle>
                            </CardHeader>
                            <CardContent className="p-6">
                              <Table>
                                <TableFooter>
                                  <TableRow>
                                    <TableCell colSpan={2}></TableCell>
                                    <TableCell className="text-right font-medium">
                                      {t('orders.subtotal')}:
                                    </TableCell>
                                    <TableCell className="text-right w-[160px]">
                                      {formatCurrency(calculateRevisionSubtotal(activeRevision), store?.currency)}
                                    </TableCell>
                                  </TableRow>
                                  {/* Desconto - Versão Desktop */}
                                  <TableRow>
                                    <TableCell colSpan={2}></TableCell>
                                    <TableCell className="text-right font-medium">
                                      <span className={activeRevision.discount > 0 ? "text-green-600" : ""}>
                                        {((activeRevision.discount_type === 'percentage' || activeRevision.discountType === 'percentage') &&
                                          (activeRevision.original_percentage > 0 || activeRevision.originalPercentage > 0))
                                          ? (t('storefront.discountPercentage', {
                                              value: activeRevision.original_percentage || activeRevision.originalPercentage
                                            }) || `Desconto (${activeRevision.original_percentage || activeRevision.originalPercentage}%):`)
                                          : (t('storefront.discountFixed') || "Desconto (Fixo):")}
                                      </span>
                                    </TableCell>
                                    <TableCell className="text-right w-[160px]">
                                      {!isEditingDiscount ? (
                                        <div className="flex items-center justify-end gap-2">
                                          {activeRevision && selectedRevisionId && (
                                            <Button
                                              variant="ghost"
                                              size="icon"
                                              className="h-6 w-6 rounded-full"
                                              onClick={(e) => {
                                                e.stopPropagation();
                                                handleStartEditingDiscount();
                                              }}
                                            >
                                              <Pencil className="h-3 w-3 text-muted-foreground" />
                                            </Button>
                                          )}
                                          <span className={calculateRevisionDiscount(activeRevision) > 0 ? "text-green-600" : ""}>
                                            {calculateRevisionDiscount(activeRevision) > 0 ? `-${formatCurrency(calculateRevisionDiscount(activeRevision), store?.currency)}` : formatCurrency(0, store?.currency)}
                                          </span>
                                        </div>
                                      ) : (
                                        <div className="flex items-center justify-end gap-2">
                                          <Select
                                            value={discountType}
                                            onValueChange={(value) => {
                                              const newType = value as 'fixed' | 'percentage';
                                              setDiscountType(newType);

                                              // Atualizar o campo de texto com base no novo tipo
                                              const percentValue =
                                                activeRevision.original_percentage !== undefined && activeRevision.original_percentage !== null
                                                  ? activeRevision.original_percentage
                                                  : (activeRevision.originalPercentage !== undefined && activeRevision.originalPercentage !== null
                                                      ? activeRevision.originalPercentage
                                                      : 0);

                                              if (newType === 'percentage' && percentValue > 0) {
                                                // Se mudar para percentual, mostrar o valor percentual
                                                console.log(`Mudando para percentual (desktop): ${percentValue}%`);
                                                setTempDiscountText(String(percentValue.toFixed(2)).replace('.', ','));
                                              } else if (newType === 'fixed') {
                                                // Se mudar para fixo, mostrar o valor monetário
                                                console.log(`Mudando para fixo (desktop): ${activeRevision.discount}`);
                                                setTempDiscountText(String((activeRevision.discount || 0).toFixed(2)).replace('.', ','));
                                              }
                                            }}
                                          >
                                            <SelectTrigger className="w-24 h-8 text-xs">
                                              <SelectValue placeholder="Tipo" />
                                            </SelectTrigger>
                                            <SelectContent>
                                              <SelectItem value="fixed">{t('coupons.fixedValue') || "Valor Fixo"}</SelectItem>
                                              <SelectItem value="percentage">{t('coupons.percentage') || "Percentual"}</SelectItem>
                                            </SelectContent>
                                          </Select>
                                          <div className="relative">
                                            <span className="absolute left-2 top-1/2 -translate-y-1/2 text-muted-foreground text-xs">
                                              {discountType === 'fixed' ? (store?.currency || 'R$') : '%'}
                                            </span>
                                            <Input
                                              type="text"
                                              value={tempDiscountText}
                                              onChange={(e) => {
                                                // Armazena o valor como texto, permitindo vírgula e ponto
                                                const inputValue = e.target.value;
                                                // Permite apenas dígitos, vírgula e ponto (no máximo um de cada)
                                                const regex = /^[0-9]*[.,]?[0-9]*$/;
                                                if (regex.test(inputValue) || inputValue === '') {
                                                  setTempDiscountText(inputValue);
                                                }
                                              }}
                                              className="w-24 h-8 pl-8 text-sm"
                                              placeholder="0,00"
                                            />
                                          </div>
                                          <Button
                                            variant="ghost"
                                            size="icon"
                                            className="h-7 w-7 rounded-full bg-green-50 text-green-600 hover:bg-green-100 hover:text-green-700"
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              handleSaveDiscount();
                                            }}
                                            disabled={updateDiscountMutation.isPending}
                                          >
                                            {updateDiscountMutation.isPending ? (
                                              <div className="h-3 w-3 border-2 border-green-600 border-t-transparent rounded-full animate-spin" />
                                            ) : (
                                              <Check className="h-3 w-3" />
                                            )}
                                          </Button>
                                          <Button
                                            variant="ghost"
                                            size="icon"
                                            className="h-7 w-7 rounded-full bg-red-50 text-red-600 hover:bg-red-100 hover:text-red-700"
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              handleCancelEditingDiscount();
                                            }}
                                          >
                                            <XCircle className="h-3 w-3" />
                                          </Button>
                                    </div>
                                  )}
                                </TableCell>
                              </TableRow>

                              {/* Informações do cupom (se houver) */}
                              {activeRevision.discount > 0 && activeRevision.couponCode && (
                                <TableRow>
                                  <TableCell colSpan={2}></TableCell>
                                  <TableCell className="text-right text-xs text-muted-foreground">
                                    {t('storefront.couponCode') || "Código do cupom"}:
                                  </TableCell>
                                  <TableCell className="text-right text-xs text-muted-foreground w-[160px]">
                                    <div className="flex items-center justify-end">
                                      <span className="mr-2">
                                        {activeRevision.couponCode}
                                      </span>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => {
                                          if (confirm(t('coupons.confirmRemove') || "Tem certeza que deseja remover o cupom desta revisão?")) {
                                            removeCouponFromRevision(activeRevision.id);
                                          }
                                        }}
                                        className="h-6 text-error hover:text-error-dark hover:bg-background"
                                      >
                                        <X className="h-3 w-3 mr-1" />
                                        {t('storefront.remove') || "Remover"}
                                      </Button>
                                    </div>
                                  </TableCell>
                                </TableRow>
                              )}
                              <TableRow>
                                <TableCell colSpan={2}></TableCell>
                                <TableCell className="text-right font-medium">
                                  {t('orders.deliveryFee')}:
                                </TableCell>
                                <TableCell className="text-right w-[160px]">
                                    {!isEditingDeliveryFee ? (
                                      <div className="flex items-center justify-end gap-1">
                                        {activeRevision && selectedRevisionId && (
                                          <Button
                                            variant="ghost"
                                            size="icon"
                                            className="h-6 w-6 rounded-full mr-1"
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              handleStartEditingDeliveryFee();
                                            }}
                                          >
                                            <Pencil className="h-3 w-3 text-muted-foreground" />
                                          </Button>
                                        )}
                                        {formatCurrency(activeRevision.deliveryFee || 0, store?.currency)}
                                      </div>
                                  ) : (
                                    <div className="flex items-center justify-end gap-2">
                                      <div className="relative">
                                        <span className="absolute left-2 top-1/2 -translate-y-1/2 text-muted-foreground text-xs">
                                          {store?.currency || 'R$'}
                                        </span>
                                        <Input
                                          type="text"
                                          value={tempDeliveryFeeText}
                                          onChange={(e) => {
                                            // Armazena o valor como texto, permitindo vírgula e ponto
                                            const inputValue = e.target.value;

                                            // Permite apenas dígitos, vírgula e ponto (no máximo um de cada)
                                            // Regex que permite apenas dígitos e no máximo uma vírgula ou ponto
                                            const regex = /^[0-9]*[.,]?[0-9]*$/;

                                            if (regex.test(inputValue) || inputValue === '') {
                                              setTempDeliveryFeeText(inputValue);
                                            }
                                          }}
                                          className="w-24 h-8 pl-8 text-sm"
                                          placeholder="0,00"
                                        />
                                      </div>
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        className="h-7 w-7 rounded-full bg-green-50 text-green-600 hover:bg-green-100 hover:text-green-700"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleSaveDeliveryFee();
                                        }}
                                        disabled={updateDeliveryFeeMutation.isPending}
                                      >
                                        {updateDeliveryFeeMutation.isPending ? (
                                          <div className="h-3 w-3 border-2 border-green-600 border-t-transparent rounded-full animate-spin" />
                                        ) : (
                                          <Check className="h-3 w-3" />
                                        )}
                                      </Button>
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        className="h-7 w-7 rounded-full bg-red-50 text-red-600 hover:bg-red-100 hover:text-red-700"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleCancelEditingDeliveryFee();
                                        }}
                                      >
                                        <XCircle className="h-3 w-3" />
                                      </Button>
                                    </div>
                                  )}
                                </TableCell>
                              </TableRow>
                              <TableRow>
                                <TableCell colSpan={2}></TableCell>
                                <TableCell className="text-right font-medium w-[180px]">
                                  {t('orders.orderTotal')}:
                                </TableCell>
                                <TableCell className="text-right font-bold w-[160px]">
                                  {formatCurrency(calculateRevisionSubtotal(activeRevision) - calculateRevisionDiscount(activeRevision) + (activeRevision.deliveryFee || 0), store?.currency)}
                                </TableCell>
                              </TableRow>
                                </TableFooter>
                              </Table>
                            </CardContent>
                          </Card>
                        </div>
                      </div>
                ) : (
                  <div className="text-center py-6 text-muted-foreground">
                    {t('orders.noItems') || "Nenhum item encontrado"}
                  </div>
                )
              ) : (
                // Renderizar itens originais do pedido
                order.items && order.items.length > 0 ? (
                  <div className="border rounded-md p-4 bg-white w-full">

                    {/* Layout mobile otimizado para iOS */}
                        <div className="md:hidden space-y-6">
                          {order.items.map((item: any) => (
                            <div key={item.id} className="border rounded-lg overflow-hidden shadow-sm">
                              <div className="p-3 flex items-start gap-3">
                                {item.product?.images && item.product.images[0] ? (
                                  <div className="w-20 h-20 rounded-md overflow-hidden flex-shrink-0">
                                    <img
                                      src={item.product.images[0]}
                                      alt={item.product?.name}
                                      className="w-full h-full object-cover"
                                    />
                                  </div>
                                ) : (
                                  <div className="w-20 h-20 bg-muted rounded-md flex items-center justify-center flex-shrink-0">
                                    <ShoppingBag className="h-8 w-8 text-muted-foreground" />
                                  </div>
                                )}

                                <div className="flex-1 min-w-0">
                                  <div className="flex items-center justify-between mb-1">
                                    <h3 className="font-medium text-base line-clamp-2 pr-2">
                                      {/* Usar o nome do produto do item se o produto não existir no banco */}
                                      {item.product?.name || item.name || item.productName || "Produto Personalizado"}
                                    </h3>
                                  </div>

                                  {/* Exibir a descrição do produto personalizado se disponível */}
                                  {!item.product && item.productDescription && (
                                    <p className="text-sm text-muted-foreground mb-2 italic">
                                      {item.productDescription}
                                    </p>
                                  )}

                                  <div className="grid grid-cols-2 gap-x-2 gap-y-1 mt-2 text-sm">
                                    <span className="text-muted-foreground">{t('products.quantity') || "Qtd"}:</span>
                                    <span className="font-medium text-right">{item.quantity}</span>

                                    <span className="text-muted-foreground">{t('products.unitPrice') || "Preço Unit."}:</span>
                                    <span className="font-medium text-right">{formatCurrency(item.price)}</span>
                                  </div>
                                </div>
                              </div>

                              {/* Variações e observações */}
                              {((Array.isArray(item.selectedOptions) && item.selectedOptions.length > 0) || item.observation) && (
                                <div className="px-3 pb-3 pt-0">
                                  {item.selectedOptions && Array.isArray(item.selectedOptions) && item.selectedOptions.length > 0 && (
                                    <div className="bg-accent/5 p-2 rounded-md mb-2">
                                      <div className="text-xs font-medium mb-1 text-muted-foreground">
                                        {t('orders.selectedOptions') || 'Opções selecionadas'}:
                                      </div>
                                      <div className="space-y-1">
                                        {Array.isArray(item.selectedOptions) ? item.selectedOptions.map((option: any, index: number) => (
                                          <div key={index} className="flex items-center justify-between text-xs">
                                            <div>
                                              <span className="font-medium">{option.variationName}:</span>
                                              <span className="ml-1">{option.optionName}</span>
                                              {option.quantity > 1 && (
                                                <span className="ml-1">x{option.quantity}</span>
                                              )}
                                            </div>
                                            {option.price > 0 && (
                                              <span className="text-primary">
                                                (+{formatCurrency(option.price * (option.quantity || 1))})
                                              </span>
                                            )}
                                          </div>
                                        )) : (
                                          <div className="text-xs text-muted-foreground">Sem opções selecionadas</div>
                                        )}
                                      </div>
                                    </div>
                                  )}

                                  {item.observation && (
                                    <div className="text-xs">
                                      <span className="font-medium text-muted-foreground">{t('orders.observation') || "Observação"}:</span>
                                      <p className="mt-1 bg-muted/30 p-2 rounded italic">{item.observation}</p>
                                    </div>
                                  )}
                                </div>
                              )}

                              {/* Total do item - movido para abaixo das variações */}
                              <div className="mt-3 pt-2 border-t flex justify-between items-center">
                                <span className="text-sm font-medium">{t('products.total') || "Total"}:</span>
                                <span className="text-sm font-bold text-primary">{formatCurrency(calculateItemTotal(item))}</span>
                              </div>
                            </div>
                          ))}

                          {/* Resumo do pedido (mobile) - Card com layout melhorado */}
                          <div className="mt-6 rounded-lg border shadow-sm overflow-hidden">
                            <div className="bg-muted/30 px-4 py-3 border-b">
                              <h3 className="font-medium text-base">{t('orders.orderSummary') || "Resumo do Pedido"}</h3>
                            </div>

                            <div className="p-4 space-y-3 bg-white">
                              {/* Subtotal - Mostra apenas o valor dos itens */}
                              <div>
                                <div className="flex justify-between items-center">
                                  <span className="text-sm text-muted-foreground">{t('orders.subtotal') || "Subtotal"}:</span>
                                  <span className="font-medium">{formatCurrency(order.subtotal, store?.currency)}</span>
                                </div>
                              </div>

                              {/* Desconto */}
                              {order.discount > 0 && (
                                <div className="border-t pt-3">
                                  <div className="flex justify-between items-center">
                                    <span className="text-sm text-green-600">
                                      {order.discount_type === 'percentage' && order.original_percentage
                                        ? (t('storefront.discountPercentage', { value: order.original_percentage }) || `Desconto (${order.original_percentage}%):`)
                                        : (t('storefront.discountFixed') || "Desconto (Fixo):")}
                                    </span>
                                    <span className="font-medium text-green-600">-{formatCurrency(order.discount, store?.currency)}</span>
                                  </div>

                                  {/* Informações do cupom */}
                                  {order.couponCode && (
                                    <div className="mt-2">
                                      <div className="flex flex-wrap justify-between items-center gap-1">
                                        <div className="flex-1 min-w-0">
                                          <span className="text-xs text-muted-foreground block">{t('storefront.couponCode') || "Código do cupom"}:</span>
                                          <span className="text-xs font-medium block truncate">
                                            {order.couponCode}
                                          </span>
                                        </div>
                                      </div>
                                    </div>
                                  )}
                                </div>
                              )}

                              {/* Taxa de Entrega */}
                              <div className="border-t pt-3">
                                <div className="flex justify-between items-center">
                                  <span className="text-sm text-muted-foreground">{t('orders.deliveryFee') || "Taxa de Entrega"}:</span>
                                  <span className="font-medium">{formatCurrency(order.deliveryFee || 0, store?.currency)}</span>
                                </div>
                              </div>

                              {/* Total */}
                              <div className="mt-4 pt-3 border-t border-primary/20 bg-primary/5 -mx-4 -mb-4 p-4 rounded-b-lg">
                                <div className="flex justify-between items-center">
                                  <span className="font-medium">{t('orders.orderTotal') || "Total do Pedido"}:</span>
                                  <span className="font-bold text-lg">{formatCurrency(order.subtotal - (order.discount || 0) + (order.deliveryFee || 0), store?.currency)}</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Layout desktop (tabela) */}
                        <div className="hidden md:block">
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>{t('products.product') || "Produto"}</TableHead>
                                <TableHead className="text-right">
                                  {t('products.unitPrice') || "Preço Unitário"}
                                </TableHead>
                                <TableHead className="text-center">
                                  {t('products.quantity') || "Qtd"}
                                </TableHead>
                                <TableHead className="text-right">
                                  {t('products.total') || "Total"}
                                </TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {order.items.map((item: any) => (
                                <TableRow key={item.id}>
                                  <TableCell>
                                    <div className="flex items-center gap-3">
                                      {item.product?.images && item.product.images[0] && (
                                        <div className="w-12 h-12 overflow-hidden rounded">
                                          <img
                                            src={item.product.images[0]}
                                            alt={item.product.name}
                                            className="w-full h-full object-cover"
                                          />
                                        </div>
                                      )}
                                      <div>
                                        <div className="font-medium">
                                          {/* Usar o nome do produto do item se o produto não existir no banco */}
                                          {item.product?.name || item.name || item.productName || "Produto Personalizado"}
                                        </div>

                                        {/* Exibir a descrição do produto personalizado se disponível */}
                                        {!item.product && item.productDescription && (
                                          <p className="text-sm text-muted-foreground mt-1 italic">
                                            {item.productDescription}
                                          </p>
                                        )}

                                        {/* Exibição das variações escolhidas */}
                                        {item.selectedOptions && Array.isArray(item.selectedOptions) && item.selectedOptions.length > 0 && (
                                          <div className="text-xs text-muted-foreground mt-1 space-y-0.5">
                                            <span className="font-medium">{t('orders.selectedOptions') || 'Opções selecionadas'}:</span>
                                            <ul className="ml-2">
                                              {Array.isArray(item.selectedOptions) ? item.selectedOptions.map((option: any, index: number) => (
                                                <li key={index} className="flex items-center">
                                                  <span className="font-medium">{option.variationName}:</span>
                                                  <span className="ml-1">{option.optionName}</span>
                                                  {option.quantity > 1 && (
                                                    <span className="ml-1">x{option.quantity}</span>
                                                  )}
                                                  {option.price > 0 && (
                                                    <span className="ml-1 text-primary">
                                                      (+{formatCurrency(option.price * (option.quantity || 1))})
                                                    </span>
                                                  )}
                                                </li>
                                              )) : (
                                                <li className="text-xs text-muted-foreground">Sem opções selecionadas</li>
                                              )}
                                            </ul>
                                          </div>
                                        )}

                                        {item.observation && (
                                          <div className="text-xs text-muted-foreground mt-1">
                                            <span className="font-medium">{t('orders.observation')}:</span> {item.observation}
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </TableCell>
                                  <TableCell className="text-right">
                                    {formatCurrency(item.price, store?.currency)}
                                  </TableCell>
                                  <TableCell className="text-center">
                                    {item.quantity}
                                  </TableCell>
                                  <TableCell className="text-right font-medium">
                                    {formatCurrency(calculateItemTotal(item), store?.currency)}
                                  </TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                            <TableFooter>
                              <TableRow>
                                <TableCell colSpan={2}></TableCell>
                                <TableCell className="text-right font-medium">
                                  {t('orders.subtotal') || "Subtotal"}:
                                </TableCell>
                                <TableCell className="text-right w-[160px]">
                                  {formatCurrency(order.subtotal, store?.currency)}
                                </TableCell>
                              </TableRow>
                              {order.discount > 0 && (
                                <>
                                  <TableRow>
                                    <TableCell colSpan={2}></TableCell>
                                    <TableCell className="text-right font-medium text-green-600">
                                      {order.discount_type === 'percentage' && order.original_percentage
                                        ? (t('storefront.discountPercentage', { value: order.original_percentage }) || `Desconto (${order.original_percentage}%):`)
                                        : (t('storefront.discountFixed') || "Desconto (Fixo):")}
                                    </TableCell>
                                    <TableCell className="text-right text-green-600 w-[160px]">
                                      -{formatCurrency(order.discount, store?.currency)}
                                    </TableCell>
                                  </TableRow>
                                  {order.couponCode && (
                                    <TableRow>
                                      <TableCell colSpan={2}></TableCell>
                                      <TableCell className="text-right text-xs text-muted-foreground">
                                        {t('storefront.couponCode') || "Código do cupom"}:
                                      </TableCell>
                                      <TableCell className="text-right text-xs text-muted-foreground w-[160px]">
                                        {order.couponCode}
                                      </TableCell>
                                    </TableRow>
                                  )}
                                </>
                              )}
                              {/* Taxa de Entrega */}
                              <TableRow>
                                <TableCell colSpan={2}></TableCell>
                                <TableCell className="text-right font-medium">
                                  {t('orders.deliveryFee') || "Taxa de Entrega"}:
                                </TableCell>
                                <TableCell className="text-right w-[160px]">
                                  {formatCurrency(order.deliveryFee || 0, store?.currency)}
                                </TableCell>
                              </TableRow>
                              <TableRow>
                                <TableCell colSpan={2}></TableCell>
                                <TableCell className="text-right font-medium w-[180px]">
                                  {t('orders.orderTotal')}:
                                </TableCell>
                                <TableCell className="text-right font-bold w-[160px]">
                                  {formatCurrency(order.subtotal - (order.discount || 0) + (order.deliveryFee || 0), store?.currency)}
                                </TableCell>
                              </TableRow>
                            </TableFooter>
                          </Table>
                        </div>
                  </div>
                ) : (
                  <div className="text-center py-6 text-muted-foreground">
                    {t('orders.noItems') || "Nenhum item encontrado"}
                  </div>
                )
              )}
            </CardContent>
          </Card>

          {/* Seção de Recebimentos */}
          <div className="space-y-6">
            {(() => {
              const currentPaymentStatus = activeRevision?.paymentStatus || order.paymentStatus || 'pendente';
              const currentOrderTotal = activeRevision?.total || order.total;

              console.log('🔄 Renderizando PaymentStatusCard com:', {
                activeRevisionStatus: activeRevision?.paymentStatus,
                orderStatus: order.paymentStatus,
                finalStatus: currentPaymentStatus,
                activeRevisionTotal: activeRevision?.total,
                orderTotal: order.total,
                finalTotal: currentOrderTotal,
                hasActiveRevision: !!activeRevision,
                paymentsCount: payments.length
              });

              return (
                <PaymentStatusCard
                  orderTotal={currentOrderTotal}
                  paymentStatus={currentPaymentStatus}
                  payments={payments}
                  currency={store?.currency}
                  onRegisterPayment={handleRegisterPayment}
                  onRecalculateStatus={handleRecalculateStatus}
                  isRecalculating={recalculateStatusMutation.isPending}
                />
              );
            })()}

            <PaymentsList
              payments={payments}
              currency={store?.currency}
              onDeletePayment={handleDeletePayment}
              isDeleting={deletePaymentMutation.isPending}
            />
          </div>



          {/* Modal de Registro de Pagamento */}
          <PaymentDialog
            open={isPaymentDialogOpen}
            onOpenChange={setIsPaymentDialogOpen}
            onSubmit={handleCreatePayment}
            isSubmitting={createPaymentMutation.isPending}
            orderTotal={activeRevision?.total || order.total}
            totalReceived={calculateTotalReceived(payments)}
            currency={store?.currency}
          />
        </div>
      ) : (
        <div className="py-6 text-center">
          <p className="text-muted-foreground">{t('orders.orderNotFound')}</p>
        </div>
      )}


    </AdminLayout>
  );
}

// This is a workaround for wouter params
OrderDetailsPage.getLayout = function getLayout() {
  return { title: 'Order Details' };
};