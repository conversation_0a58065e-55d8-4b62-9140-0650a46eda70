-- Script de migração para adicionar o campo de cliente às revisões de pedidos
-- Execute este script diretamente no seu ambiente de banco de dados

-- 1. Adicionar a coluna customer_id à tabela order_revisions
ALTER TABLE order_revisions ADD COLUMN IF NOT EXISTS customer_id INTEGER REFERENCES customers(id);

-- 2. Popular a coluna customer_id com os dados dos pedidos originais
UPDATE order_revisions r
SET customer_id = o.customer_id
FROM orders o
WHERE r.order_id = o.id AND r.customer_id IS NULL;

-- 3. Atualizar a função create_order_revision (se existir) para incluir o campo customer_id
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'create_order_revision') THEN
    DROP FUNCTION IF EXISTS create_order_revision;
    
    CREATE OR REPLACE FUNCTION create_order_revision(order_id_param INTEGER)
    RETURNS INTEGER AS $$
    DECLARE
      new_revision_id INTEGER;
      new_revision_number INTEGER;
      order_record RECORD;
    BEGIN
      -- Get the next revision number for this order
      SELECT COALESCE(MAX(revision_number), 0) + 1 INTO new_revision_number
      FROM order_revisions
      WHERE order_id = order_id_param;
      
      -- Get the order data
      SELECT * INTO order_record
      FROM orders
      WHERE id = order_id_param;
      
      -- Create the new revision
      INSERT INTO order_revisions (
        order_id,
        revision_number,
        status,
        receiving_method,
        receiving_date,
        receiving_time,
        delivery_address,
        payment_method,
        subtotal,
        delivery_fee,
        total,
        notes,
        is_current,
        customer_id
      ) VALUES (
        order_id_param,
        new_revision_number,
        order_record.status,
        order_record.receiving_method,
        order_record.receiving_date,
        order_record.receiving_time,
        order_record.delivery_address,
        order_record.payment_method,
        order_record.subtotal,
        order_record.delivery_fee,
        order_record.total,
        order_record.notes,
        FALSE, -- New revision is not current by default
        order_record.customer_id -- Copia o ID do cliente do pedido original
      ) RETURNING id INTO new_revision_id;
      
      -- Copy order items to the new revision with complete product information
      INSERT INTO order_revision_items (
        revision_id,
        product_id,
        product_name,
        product_description,
        product_image,
        quantity,
        unit_price,
        subtotal,
        selected_variations,
        observation
      )
      SELECT
        new_revision_id,
        oi.product_id,
        p.name,
        p.description,
        (CASE WHEN p.images IS NOT NULL AND jsonb_array_length(p.images) > 0 
              THEN p.images ->> 0 
              ELSE NULL 
         END),
        oi.quantity,
        oi.price,
        oi.quantity * oi.price,
        oi.selected_options,
        oi.observation
      FROM order_items oi
      LEFT JOIN products p ON oi.product_id = p.id
      WHERE oi.order_id = order_id_param;
      
      RETURN new_revision_id;
    END;
    $$ LANGUAGE plpgsql;
  END IF;
END$$;