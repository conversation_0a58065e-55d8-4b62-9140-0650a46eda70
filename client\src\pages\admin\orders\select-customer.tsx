import { useState, useEffect } from 'react';
import { useLocation } from 'wouter';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useTranslation } from '@/hooks/useTranslation';
import { useStore } from '@/context/StoreContext';
import { useAdminOrder } from '@/context/AdminOrderContext';
import { useToast } from '@/hooks/use-toast';
import { formatPhoneWithCountryCode, cn } from '@/lib/utils';
import AdminLayout from '@/components/admin/AdminLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft, Search, Check, User, Phone, Mail } from 'lucide-react';

export default function SelectCustomerPage() {
  const { t } = useTranslation();
  const [location, setLocation] = useLocation();
  const { store } = useStore();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { setCustomer } = useAdminOrder();

  // Estados
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [selectedCustomerId, setSelectedCustomerId] = useState<number | null>(null);
  const [orderId, setOrderId] = useState<string | null>(null);
  const [revisionId, setRevisionId] = useState<number | null>(null);
  const [isNewOrder, setIsNewOrder] = useState<boolean>(false);

  // Recuperar os IDs do localStorage
  useEffect(() => {
    const storedOrderId = localStorage.getItem('currentOrderId');
    const storedRevisionId = localStorage.getItem('currentRevisionId');
    const storedIsNewOrder = localStorage.getItem('adminOrderCreation');

    if (storedOrderId) {
      setOrderId(storedOrderId);
    }

    if (storedRevisionId) {
      setRevisionId(parseInt(storedRevisionId));
    }

    if (storedIsNewOrder === 'true') {
      setIsNewOrder(true);
    }
  }, []);

  // Query para buscar clientes com filtro
  const customersQuery = useQuery({
    queryKey: ['/api/customers', searchQuery],
    queryFn: async () => {
      const endpoint = searchQuery
        ? `/api/customers?search=${encodeURIComponent(searchQuery)}`
        : '/api/customers';
      console.log('Buscando clientes no endpoint:', endpoint);

      try {
        // Verificar se temos o Firebase inicializado e o usuário está logado
        const firebaseModule = await import('@/lib/firebase');
        const { auth } = firebaseModule;

        const currentUser = auth.currentUser;
        console.log('Firebase currentUser:', currentUser ? `UID: ${currentUser.uid}` : 'Não autenticado');

        if (currentUser && currentUser.uid) {
          // Adicionar o UID como parâmetro de consulta manualmente
          const separator = endpoint.includes('?') ? '&' : '?';
          const finalUrl = `${endpoint}${separator}uid=${currentUser.uid}`;
          console.log('URL com UID adicionado manualmente:', finalUrl);

          const response = await fetch(finalUrl, {
            method: 'GET',
            credentials: 'include'
          });

          console.log('Status da resposta:', response.status);

          if (!response.ok) {
            throw new Error(`Erro na requisição: ${response.status} ${response.statusText}`);
          }

          const data = await response.json();
          console.log('Dados da resposta:', data);
          return data;
        } else {
          console.error('Usuário não autenticado!');
        }
      } catch (error) {
        console.error('Erro ao buscar clientes:', error);
        throw error;
      }

      // Fallback para o método padrão
      const response = await apiRequest('GET', endpoint);
      console.log('Resposta da busca de clientes (método padrão):', response);

      // Se a resposta for um objeto Response, extrair o JSON
      if (response instanceof Response) {
        const data = await response.json();
        console.log('Dados extraídos da resposta:', data);
        return data;
      }

      return response;
    },
  });

  // Mutation para atualizar o cliente da revisão
  const updateCustomerMutation = useMutation({
    mutationFn: (customerId: number) => {
      if (!revisionId) {
        throw new Error('ID da revisão não encontrado');
      }

      console.log('Enviando requisição para atualizar cliente da revisão:', {
        endpoint: `/api/orders/revisions/${revisionId}/customer`,
        payload: { customerId },
        revisionId
      });

      return apiRequest('PATCH', `/api/orders/revisions/${revisionId}/customer`, { customerId });
    },
    onSuccess: (data) => {
      console.log('Cliente da revisão atualizado com sucesso. Resposta:', data);

      // Invalidar as consultas
      if (revisionId) {
        queryClient.invalidateQueries({ queryKey: [`/api/orders/revisions/${revisionId}`] });
      }

      if (orderId) {
        queryClient.invalidateQueries({ queryKey: [`/api/orders/${orderId}/revisions`] });
      }

      toast({
        title: t('orders.changeCustomer'),
        description: t('orders.customerUpdated'),
        variant: "default"
      });

      // Voltar para a página de detalhes do pedido
      if (orderId) {
        setLocation(`/admin/orders/${orderId}`);
      } else {
        setLocation('/admin/orders');
      }
    },
    onError: (error) => {
      console.error('Erro ao atualizar cliente da revisão:', error);
      toast({
        title: t('orders.errorUpdatingCustomer'),
        description: error instanceof Error ? error.message : String(error),
        variant: "destructive"
      });
    }
  });

  const handleSelectCustomer = (customerId: number) => {
    setSelectedCustomerId(customerId);
  };

  const handleSaveCustomer = () => {
    if (!selectedCustomerId) return;

    // Se estiver criando um novo pedido
    if (isNewOrder) {
      // Encontrar o cliente selecionado nos dados
      const selectedCustomer = customersQuery.data?.find((c: any) => c.id === selectedCustomerId);

      if (selectedCustomer) {
        // Atualizar o contexto do pedido com o cliente selecionado
        setCustomer({
          id: selectedCustomer.id,
          name: selectedCustomer.name,
          email: selectedCustomer.email,
          phone: selectedCustomer.phone
        });

        // Limpar o localStorage
        localStorage.removeItem('adminOrderCreation');

        // Navegar de volta para a página de criação de pedido
        setLocation('/admin/orders/new');

        toast({
          title: t('orders.customerSelected') || 'Cliente selecionado',
          description: t('orders.customerSelectedDescription') || 'O cliente foi selecionado para o pedido',
          variant: "default"
        });

        return;
      }
    }

    // Caso contrário, atualizar a revisão (fluxo original)
    if (!revisionId) {
      toast({
        title: "Erro",
        description: "ID da revisão não encontrado",
        variant: "destructive"
      });
      return;
    }

    updateCustomerMutation.mutate(selectedCustomerId);
  };

  const handleGoBack = () => {
    // Se estiver criando um novo pedido, voltar para a página de criação
    if (isNewOrder) {
      localStorage.removeItem('adminOrderCreation');
      setLocation('/admin/orders/new');
      return;
    }

    // Caso contrário, seguir o fluxo original
    if (orderId) {
      setLocation(`/admin/orders/${orderId}`);
    } else {
      setLocation('/admin/orders');
    }
  };

  return (
    <AdminLayout title={t('orders.selectCustomerButton')}>
      <div className="container px-4 sm:px-6 py-4 sm:py-6 max-w-4xl">
        {/* Cabeçalho com botões - layout responsivo */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 mb-4 sm:mb-6">
          <Button
            variant="outline"
            onClick={handleGoBack}
            className="w-full sm:w-auto"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t('common.goBack')}
          </Button>

          <Button
            onClick={handleSaveCustomer}
            disabled={!selectedCustomerId || updateCustomerMutation.isPending}
            className="w-full sm:w-auto"
          >
            {updateCustomerMutation.isPending ? (
              <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
            ) : null}
            {t('common.save')}
          </Button>
        </div>

        {/* Campo de busca fora do card para maior destaque em mobile */}
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder={t('orders.searchCustomers')}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 h-12 text-base"
          />
        </div>

        {/* Título da seção */}
        <h2 className="text-lg font-semibold mb-3">{t('orders.selectCustomerButton')}</h2>

        {/* Lista de clientes - sem card para melhor uso do espaço em mobile */}
        <div className="border rounded-lg overflow-hidden bg-card">
          {customersQuery.isLoading ? (
            <div className="flex flex-col items-center justify-center p-8 space-y-2">
              <div className="h-8 w-8 border-2 border-primary border-t-transparent rounded-full animate-spin" />
              <p className="text-sm text-muted-foreground">{t('common.loading')}</p>
            </div>
          ) : customersQuery.data?.length > 0 ? (
            <div className="divide-y">
              {customersQuery.data.map((customer: any) => (
                <div
                  key={customer.id}
                  className={cn(
                    "p-4 cursor-pointer hover:bg-muted active:bg-muted/80 transition-colors",
                    selectedCustomerId === customer.id ? "bg-muted border-l-4 border-l-primary" : ""
                  )}
                  onClick={() => handleSelectCustomer(customer.id)}
                >
                  <div className="flex items-start justify-between">
                    <div className="space-y-2 flex-1 min-w-0">
                      <div className="flex items-center">
                        <User className="h-5 w-5 mr-2 flex-shrink-0 text-muted-foreground" />
                        <div className="font-medium truncate">{customer.name}</div>
                      </div>

                      {customer.email && (
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Mail className="h-4 w-4 mr-2 flex-shrink-0" />
                          <span className="truncate">{customer.email}</span>
                        </div>
                      )}

                      {customer.phone && (
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Phone className="h-4 w-4 mr-2 flex-shrink-0" />
                          <span className="mr-2">{formatPhoneWithCountryCode(customer.phone, customer.countryCode || store?.countryCode)}</span>
                          <button
                            type="button"
                            className="inline-flex items-center justify-center rounded-full bg-green-600 p-1.5 text-white hover:bg-green-700 transition-colors"
                            onClick={(e) => {
                              e.stopPropagation(); // Evitar que o clique selecione o cliente
                              const countryCode = customer.countryCode || store?.countryCode || "+55";
                              const phoneNumber = customer.phone.replace(/\D/g, '');
                              const formattedNumber = `${countryCode.replace('+', '')}${phoneNumber}`;
                              window.open(`https://wa.me/${formattedNumber}`, '_blank');
                            }}
                            aria-label="WhatsApp"
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="14"
                              height="14"
                              viewBox="0 0 24 24"
                              fill="white"
                              stroke="currentColor"
                              strokeWidth="0"
                            >
                              <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347z" />
                            </svg>
                          </button>
                        </div>
                      )}
                    </div>

                    {selectedCustomerId === customer.id && (
                      <div className="ml-2 flex-shrink-0">
                        <div className="bg-primary text-primary-foreground rounded-full p-1">
                          <Check className="h-4 w-4" />
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="p-8 flex flex-col items-center justify-center text-center space-y-3">
              <div className="rounded-full bg-muted p-3">
                <User className="h-6 w-6 text-muted-foreground" />
              </div>
              <div>
                <p className="text-muted-foreground font-medium">{t('orders.noCustomersFound')}</p>
                <p className="text-sm text-muted-foreground mt-1">
                  {searchQuery
                    ? t('orders.tryAnotherSearch') || "Tente outra busca"
                    : t('orders.searchToFindCustomers') || "Digite para buscar clientes"}
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Botão flutuante para salvar em mobile */}
        {selectedCustomerId && (
          <div className="fixed bottom-6 right-6 sm:hidden z-10">
            <Button
              onClick={handleSaveCustomer}
              disabled={updateCustomerMutation.isPending}
              size="lg"
              className="rounded-full shadow-lg h-14 w-14 p-0"
            >
              {updateCustomerMutation.isPending ? (
                <div className="h-6 w-6 border-2 border-white border-t-transparent rounded-full animate-spin" />
              ) : (
                <Check className="h-6 w-6" />
              )}
            </Button>
          </div>
        )}
      </div>
    </AdminLayout>
  );
}

// This is a workaround for wouter params
SelectCustomerPage.getLayout = function getLayout() {
  return { title: 'Select Customer' };
};
