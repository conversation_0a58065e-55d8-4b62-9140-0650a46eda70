import React from 'react';
import { ShoppingCart, ChevronRight } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { Category, Product, Store } from '@shared/schema';
import { formatCurrency } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import { Input } from '@/components/ui/input';

interface CategorySectionsLayoutProps {
  categories: Category[];
  products: Product[];
  store: Store;
  onProductClick: (product: Product) => void;
  onAddToCart: (product: Product) => void;
}

const CategorySectionsLayout: React.FC<CategorySectionsLayoutProps> = ({
  categories,
  products,
  store,
  onProductClick,
  onAddToCart
}) => {
  const { t } = useTranslation();

  // Group products by category
  const productsByCategory: Record<number, Product[]> = {};
  const uncategorizedProducts: Product[] = [];
  
  // Initialize categories
  categories.forEach(category => {
    productsByCategory[category.id] = [];
  });
  
  // Fill products into categories
  products.forEach(product => {
    if (product.categoryId) {
      if (productsByCategory[product.categoryId]) {
        productsByCategory[product.categoryId].push(product);
      }
    } else {
      uncategorizedProducts.push(product);
    }
  });

  return (
    <div className="flex flex-col min-h-screen bg-background">
      {/* Header */}
      <header className="sticky top-0 z-10 bg-background border-b p-4 shadow-sm">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            {store?.logo && (
              <img 
                src={store.logo} 
                alt={store?.name || "Store"} 
                className="h-10 w-10 object-contain mr-2 rounded-md"
              />
            )}
            <h1 className="text-xl font-bold">{store?.name}</h1>
          </div>
          <Button variant="outline" size="icon">
            <ShoppingCart className="h-5 w-5" />
          </Button>
        </div>
        
        {/* Search input */}
        <div className="mt-4">
          <Input
            placeholder={t('storefront.searchProducts')}
            className="w-full"
          />
        </div>
      </header>

      {/* Main content with category sections */}
      <main className="flex-1 p-4">
        {/* Hero section with featured category or product could go here */}
        <div className="rounded-lg bg-muted mb-6 p-6 text-center">
          <h2 className="text-2xl font-bold">{store?.name}</h2>
          <p className="mt-2 text-muted-foreground">{store?.description}</p>
        </div>
        
        {/* Category sections */}
        <div className="space-y-10">
          {categories.map((category) => (
            productsByCategory[category.id].length > 0 && (
              <section key={category.id} className="space-y-4">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-semibold">
                    {category.name}
                  </h2>
                  <Button variant="ghost" size="sm" className="text-sm flex items-center">
                    {t('common.view')} <ChevronRight className="ml-1 h-4 w-4" />
                  </Button>
                </div>
                
                <ScrollArea className="w-full">
                  <div className="flex space-x-4 pb-4">
                    {productsByCategory[category.id].map((product) => (
                      <div 
                        key={product.id} 
                        className="border rounded-lg overflow-hidden flex-shrink-0 w-[160px] bg-white shadow-sm flex flex-col"
                        onClick={() => onProductClick(product)}
                      >
                        <div className="h-32 relative">
                          {product.images && Array.isArray(product.images) && product.images.length > 0 ? (
                            <img 
                              src={product.images[0] as string} 
                              alt={product.name} 
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="w-full h-full bg-muted flex items-center justify-center">
                              <span className="text-muted-foreground">No image</span>
                            </div>
                          )}
                          
                          {product.hasVariations && (
                            <Badge 
                              className="absolute top-2 right-2 bg-primary/70 hover:bg-primary/70"
                              variant="secondary"
                            >
                              {t('products.hasVariationsShort')}
                            </Badge>
                          )}
                          
                          {!product.inStock && (
                            <div className="absolute inset-0 bg-background/60 flex items-center justify-center">
                              <Badge variant="destructive">
                                {t('storefront.outOfStock')}
                              </Badge>
                            </div>
                          )}
                        </div>
                        
                        <div className="p-3 flex-1 flex flex-col">
                          <h3 className="font-medium line-clamp-2 text-sm">{product.name}</h3>
                          <p className="text-primary font-bold mt-2">
                            {formatCurrency(product.price, store?.currency || 'R$')}
                          </p>
                        </div>
                        
                        {product.inStock && (
                          <div className="px-3 pb-3">
                            <Button 
                              variant="outline" 
                              size="sm" 
                              className="w-full"
                              onClick={(e) => {
                                e.stopPropagation();
                                onAddToCart(product);
                              }}
                            >
                              {t('storefront.addToCart')}
                            </Button>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                  <ScrollBar orientation="horizontal" />
                </ScrollArea>
              </section>
            )
          ))}
          
          {/* Uncategorized products section */}
          {uncategorizedProducts.length > 0 && (
            <section className="space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold">
                  {t('products.uncategorized')}
                </h2>
                <Button variant="ghost" size="sm" className="text-sm flex items-center">
                  {t('common.view')} <ChevronRight className="ml-1 h-4 w-4" />
                </Button>
              </div>
              
              <ScrollArea className="w-full">
                <div className="flex space-x-4 pb-4">
                  {uncategorizedProducts.map((product) => (
                    <div 
                      key={product.id} 
                      className="border rounded-lg overflow-hidden flex-shrink-0 w-[160px] bg-white shadow-sm flex flex-col"
                      onClick={() => onProductClick(product)}
                    >
                      <div className="h-32 relative">
                        {product.images && Array.isArray(product.images) && product.images.length > 0 ? (
                          <img 
                            src={product.images[0] as string} 
                            alt={product.name} 
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full bg-muted flex items-center justify-center">
                            <span className="text-muted-foreground">No image</span>
                          </div>
                        )}
                        
                        {product.hasVariations && (
                          <Badge 
                            className="absolute top-2 right-2 bg-primary/70 hover:bg-primary/70"
                            variant="secondary"
                          >
                            {t('products.hasVariationsShort')}
                          </Badge>
                        )}
                        
                        {!product.inStock && (
                          <div className="absolute inset-0 bg-background/60 flex items-center justify-center">
                            <Badge variant="destructive">
                              {t('storefront.outOfStock')}
                            </Badge>
                          </div>
                        )}
                      </div>
                      
                      <div className="p-3 flex-1 flex flex-col">
                        <h3 className="font-medium line-clamp-2 text-sm">{product.name}</h3>
                        <p className="text-primary font-bold mt-2">
                          {formatCurrency(product.price, store?.currency)}
                        </p>
                      </div>
                      
                      {product.inStock && (
                        <div className="px-3 pb-3">
                          <Button 
                            variant="outline" 
                            size="sm" 
                            className="w-full"
                            onClick={(e) => {
                              e.stopPropagation();
                              onAddToCart(product);
                            }}
                          >
                            {t('storefront.addToCart')}
                          </Button>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
                <ScrollBar orientation="horizontal" />
              </ScrollArea>
            </section>
          )}
        </div>
      </main>
      
      {/* Footer with cart button */}
      <footer className="sticky bottom-0 left-0 right-0 p-4 bg-background border-t z-10">
        <Button className="w-full" size="lg">
          <ShoppingCart className="mr-2 h-5 w-5" />
          {t('storefront.cart')}
        </Button>
      </footer>
    </div>
  );
};

export default CategorySectionsLayout;