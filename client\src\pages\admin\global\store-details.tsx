import { useEffect, useState } from 'react';
import { useLocation } from 'wouter';
import { useQueryClient } from '@tanstack/react-query';
import GlobalAdminLayout from '@/components/global-admin/GlobalAdminLayout';
import {
  useGlobalAdminGuard,
  useGlobalStoreDetails,
  updateSubscriptionPlan,
  updateSubscriptionStatus
} from '@/hooks/useGlobalAdmin';
import { useTranslation } from '@/hooks/useTranslation';
import { useToast } from '@/hooks/use-toast';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  ArrowLeft,
  Store,
  User,
  Calendar,
  Crown,
  ShoppingBag,
  ShoppingCart,
  Users,
  DollarSign,
  ExternalLink,
  Mail,
  Phone,
  Edit,
  CreditCard
} from 'lucide-react';

interface StoreDetailsPageProps {
  id: number;
}

export default function StoreDetailsPage({ id }: StoreDetailsPageProps) {
  const [, setLocation] = useLocation();
  const { t } = useTranslation();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Estados para gerenciamento de assinatura
  const [showSubscriptionDialog, setShowSubscriptionDialog] = useState(false);
  const [subscriptionAction, setSubscriptionAction] = useState<'plan' | 'status'>('plan');
  const [newValue, setNewValue] = useState('');
  const [isUpdating, setIsUpdating] = useState(false);

  const { data: adminUser, isLoading: isLoadingUser } = useGlobalAdminGuard();
  const { data: store, isLoading: isLoadingStore, error } = useGlobalStoreDetails(id);

  // Redirecionar se não for admin global
  useEffect(() => {
    if (!isLoadingUser && (!adminUser || !adminUser.isGlobalAdmin)) {
      setLocation('/admin');
    }
  }, [adminUser, isLoadingUser, setLocation]);

  // Função para abrir dialog de gerenciamento de assinatura
  const openSubscriptionDialog = (action: 'plan' | 'status') => {
    if (!store?.subscription) return;

    setSubscriptionAction(action);
    setNewValue(action === 'plan' ? store.subscription.planType : store.subscription.status);
    setShowSubscriptionDialog(true);
  };

  // Função para calcular preview das datas
  const calculateDatePreview = (planType: 'free' | 'premium') => {
    const now = new Date();
    const currentPeriodStart = new Date(now);

    if (planType === 'premium') {
      const currentPeriodEnd = new Date(now);
      currentPeriodEnd.setDate(currentPeriodEnd.getDate() + 30);
      return {
        currentPeriodStart,
        currentPeriodEnd,
        hasExpiration: true
      };
    }

    return {
      currentPeriodStart,
      currentPeriodEnd: null,
      hasExpiration: false
    };
  };

  // Função para atualizar assinatura
  const handleUpdateSubscription = async () => {
    if (!store?.subscription || !newValue) return;

    setIsUpdating(true);
    try {
      if (subscriptionAction === 'plan') {
        await updateSubscriptionPlan(store.subscription.id, newValue as 'free' | 'premium');
      } else {
        await updateSubscriptionStatus(store.subscription.id, newValue as any);
      }

      // Invalidar queries para atualizar dados
      queryClient.invalidateQueries({ queryKey: [`/api/admin/global/stores/${id}`] });
      queryClient.invalidateQueries({ queryKey: ['/api/admin/global/stores'] });
      queryClient.invalidateQueries({ queryKey: ['/api/admin/global/analytics'] });

      toast({
        title: t('common.success'),
        description: subscriptionAction === 'plan'
          ? 'Plano da assinatura atualizado com sucesso'
          : 'Status da assinatura atualizado com sucesso',
      });
    } catch (error) {
      toast({
        title: t('common.error'),
        description: 'Erro ao atualizar assinatura',
        variant: 'destructive',
      });
    } finally {
      setIsUpdating(false);
      setShowSubscriptionDialog(false);
      setNewValue('');
    }
  };

  // Mostrar loading enquanto verifica permissões
  if (isLoadingUser) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Crown className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
          <p className="text-gray-600">{t('common.loading')}</p>
        </div>
      </div>
    );
  }

  // Mostrar erro se não conseguir verificar usuário
  if (!adminUser?.isGlobalAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Alert className="max-w-md">
          <AlertDescription>
            Acesso negado. Permissões de super-administrador necessárias.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const formatDate = (dateString: string | Date) => {
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
    return date.toLocaleDateString('pt-BR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatDateShort = (dateString: string | Date) => {
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
    return date.toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const isSubscriptionExpired = (endDate: string | Date | null) => {
    if (!endDate) return false;
    const dateObj = typeof endDate === 'string' ? new Date(endDate) : endDate;
    return dateObj < new Date();
  };

  const getDaysUntilExpiration = (endDate: string | Date | null) => {
    if (!endDate) return null;
    const dateObj = typeof endDate === 'string' ? new Date(endDate) : endDate;
    const now = new Date();
    const diffTime = dateObj.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const getPlanBadgeColor = (planType: string) => {
    return planType === 'premium' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800';
  };

  const getStatusBadgeColor = (isActive: boolean) => {
    return isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
  };

  const getSubscriptionStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 border-green-200';
      case 'past_due': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'canceled': return 'bg-red-100 text-red-800 border-red-200';
      case 'unpaid': return 'bg-red-100 text-red-800 border-red-200';
      case 'incomplete': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <GlobalAdminLayout
      title={store ? `${store.name}` : t('globalAdmin.stores.details.title')}
      description={store ? `Detalhes da loja /${store.slug}` : ''}
    >
      {/* Botão voltar */}
      <div className="mb-6">
        <Button
          variant="outline"
          onClick={() => setLocation('/admin/global/stores')}
          className="flex items-center space-x-2"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Voltar para Lojas</span>
        </Button>
      </div>

      {isLoadingStore ? (
        <div className="space-y-6">
          {/* Header skeleton */}
          <Card>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="space-y-2">
                  <Skeleton className="h-8 w-64" />
                  <Skeleton className="h-4 w-32" />
                </div>
                <Skeleton className="h-16 w-16 rounded-lg" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Skeleton className="h-20" />
                <Skeleton className="h-20" />
                <Skeleton className="h-20" />
              </div>
            </CardContent>
          </Card>

          {/* Metrics skeleton */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <Card key={i}>
                <CardHeader>
                  <Skeleton className="h-4 w-24" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-8 w-16" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      ) : error ? (
        <Alert>
          <AlertDescription>
            Erro ao carregar detalhes da loja: {error.message}
          </AlertDescription>
        </Alert>
      ) : !store ? (
        <Alert>
          <AlertDescription>
            Loja não encontrada.
          </AlertDescription>
        </Alert>
      ) : (
        <div className="space-y-6">
          {/* Informações principais da loja */}
          <Card>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-2xl flex items-center space-x-3">
                    <Store className="h-6 w-6" />
                    <span>{store.name}</span>
                  </CardTitle>
                  <p className="text-muted-foreground mt-1">/{store.slug}</p>
                  {store.description && (
                    <p className="text-sm text-muted-foreground mt-2">{store.description}</p>
                  )}
                </div>
                {store.logo && (
                  <img 
                    src={store.logo} 
                    alt={store.name}
                    className="w-16 h-16 rounded-lg object-cover"
                  />
                )}
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Status e Plano */}
                <div className="space-y-3">
                  <h4 className="font-medium">Status e Plano</h4>
                  <div className="space-y-2">
                    <Badge className={getStatusBadgeColor(store.isActive)}>
                      {store.isActive ? 'Ativa' : 'Inativa'}
                    </Badge>
                    {store.subscription && (
                      <div className="space-y-2">
                        <Badge className={getPlanBadgeColor(store.subscription.planType)}>
                          {store.subscription.planType === 'premium' ? (
                            <Crown className="h-3 w-3 mr-1" />
                          ) : null}
                          {store.subscription.planType === 'premium' ? 'Premium' : 'Gratuito'}
                        </Badge>
                        <Badge className={getSubscriptionStatusBadgeColor(store.subscription.status)}>
                          {store.subscription.status}
                        </Badge>

                        {/* Datas de validade */}
                        {store.subscription.currentPeriodEnd && (
                          <div className="text-xs space-y-1">
                            {isSubscriptionExpired(store.subscription.currentPeriodEnd) ? (
                              <p className="text-red-600 font-medium">
                                ⚠️ Expirou em {formatDateShort(store.subscription.currentPeriodEnd)}
                              </p>
                            ) : (
                              <>
                                <p className="text-green-600">
                                  ✅ Válida até {formatDateShort(store.subscription.currentPeriodEnd)}
                                </p>
                                {getDaysUntilExpiration(store.subscription.currentPeriodEnd) !== null && (
                                  <p className="text-muted-foreground">
                                    {getDaysUntilExpiration(store.subscription.currentPeriodEnd)} dias restantes
                                  </p>
                                )}
                              </>
                            )}
                          </div>
                        )}

                        {store.subscription.planType === 'free' && (
                          <p className="text-xs text-muted-foreground">
                            📅 Plano gratuito - sem data de expiração
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                </div>

                {/* Proprietário */}
                <div className="space-y-3">
                  <h4 className="font-medium flex items-center space-x-2">
                    <User className="h-4 w-4" />
                    <span>{t('globalAdmin.stores.details.owner')}</span>
                  </h4>
                  {store.user ? (
                    <div className="space-y-1">
                      <p className="text-sm">{store.user.fullName || 'Sem nome'}</p>
                      <p className="text-sm text-muted-foreground flex items-center space-x-1">
                        <Mail className="h-3 w-3" />
                        <span>{store.user.email}</span>
                      </p>
                      <p className="text-xs text-muted-foreground">
                        Membro desde {formatDate(store.user.createdAt)}
                      </p>
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground">Proprietário não encontrado</p>
                  )}
                </div>

                {/* Datas */}
                <div className="space-y-3">
                  <h4 className="font-medium flex items-center space-x-2">
                    <Calendar className="h-4 w-4" />
                    <span>Informações</span>
                  </h4>
                  <div className="space-y-1">
                    <p className="text-sm">
                      <span className="text-muted-foreground">Criada:</span> {formatDate(store.createdAt)}
                    </p>
                    {store.updatedAt && (
                      <p className="text-sm">
                        <span className="text-muted-foreground">Atualizada:</span> {formatDate(store.updatedAt)}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* Ações */}
              <div className="flex flex-wrap gap-3 mt-6 pt-6 border-t">
                <Button
                  variant="outline"
                  onClick={() => window.open(`/${store.slug}`, '_blank')}
                  className="flex items-center space-x-2"
                >
                  <ExternalLink className="h-4 w-4" />
                  <span>Visitar Loja</span>
                </Button>

                {store.subscription && (
                  <>
                    <Button
                      variant="outline"
                      onClick={() => openSubscriptionDialog('plan')}
                      className="flex items-center space-x-2"
                    >
                      <Edit className="h-4 w-4" />
                      <span>Alterar Plano</span>
                    </Button>

                    <Button
                      variant="outline"
                      onClick={() => openSubscriptionDialog('status')}
                      className="flex items-center space-x-2"
                    >
                      <CreditCard className="h-4 w-4" />
                      <span>Alterar Status</span>
                    </Button>
                  </>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Métricas da loja */}
          {store.metrics && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    {t('globalAdmin.stores.details.totalProducts')}
                  </CardTitle>
                  <ShoppingBag className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{store.metrics.totalProducts}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    {t('globalAdmin.stores.details.totalOrders')}
                  </CardTitle>
                  <ShoppingCart className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{store.metrics.totalOrders}</div>
                  <p className="text-xs text-muted-foreground">
                    {store.metrics.recentOrders} nos últimos 30 dias
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    {t('globalAdmin.stores.details.totalCustomers')}
                  </CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{store.metrics.totalCustomers}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    {t('globalAdmin.stores.details.recentRevenue')}
                  </CardTitle>
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {formatCurrency(store.metrics.recentRevenue)}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Ticket médio: {formatCurrency(store.metrics.avgOrderValue)}
                  </p>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Informações da assinatura */}
          {store.subscription && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Crown className="h-5 w-5" />
                  <span>Detalhes da Assinatura</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Plano</p>
                    <div className="flex items-center space-x-2 mt-1">
                      {store.subscription.planType === 'premium' && <Crown className="h-4 w-4 text-yellow-500" />}
                      <p className="text-lg font-semibold">
                        {store.subscription.planType === 'premium' ? 'Premium' : 'Gratuito'}
                      </p>
                    </div>
                  </div>

                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Status</p>
                    <Badge className={`mt-1 ${getSubscriptionStatusBadgeColor(store.subscription.status)}`}>
                      {store.subscription.status}
                    </Badge>
                  </div>

                  {store.subscription.currentPeriodStart && (
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Início do Período</p>
                      <p className="text-lg">{formatDateShort(store.subscription.currentPeriodStart)}</p>
                    </div>
                  )}

                  {store.subscription.currentPeriodEnd ? (
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        {isSubscriptionExpired(store.subscription.currentPeriodEnd) ? 'Expirou em' : 'Válida até'}
                      </p>
                      <div className="mt-1">
                        <p className={`text-lg font-semibold ${
                          isSubscriptionExpired(store.subscription.currentPeriodEnd)
                            ? 'text-red-600'
                            : 'text-green-600'
                        }`}>
                          {formatDateShort(store.subscription.currentPeriodEnd)}
                        </p>
                        {!isSubscriptionExpired(store.subscription.currentPeriodEnd) &&
                         getDaysUntilExpiration(store.subscription.currentPeriodEnd) !== null && (
                          <p className="text-xs text-muted-foreground">
                            {getDaysUntilExpiration(store.subscription.currentPeriodEnd)} dias restantes
                          </p>
                        )}
                      </div>
                    </div>
                  ) : (
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Validade</p>
                      <p className="text-lg text-gray-600">Sem expiração</p>
                    </div>
                  )}
                </div>

                {/* Informações adicionais */}
                <div className="mt-6 pt-6 border-t">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-muted-foreground">Criada em:</p>
                      <p>{formatDate(store.subscription.createdAt)}</p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">Última atualização:</p>
                      <p>{formatDate(store.subscription.updatedAt)}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Dialog de Gerenciamento de Assinatura */}
      <Dialog open={showSubscriptionDialog} onOpenChange={setShowSubscriptionDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {subscriptionAction === 'plan' ? 'Alterar Plano' : 'Alterar Status da Assinatura'}
            </DialogTitle>
            <DialogDescription>
              {subscriptionAction === 'plan'
                ? 'Selecione o novo plano para esta loja.'
                : 'Selecione o novo status para a assinatura desta loja.'
              }
            </DialogDescription>
          </DialogHeader>

          <div className="py-4 space-y-4">
            <div>
              <label className="text-sm font-medium mb-2 block">
                {subscriptionAction === 'plan' ? 'Novo Plano' : 'Novo Status'}
              </label>
              <Select value={newValue} onValueChange={setNewValue}>
                <SelectTrigger>
                  <SelectValue placeholder={`Selecione ${subscriptionAction === 'plan' ? 'o plano' : 'o status'}`} />
                </SelectTrigger>
                <SelectContent>
                  {subscriptionAction === 'plan' ? (
                    <>
                      <SelectItem value="free">Gratuito</SelectItem>
                      <SelectItem value="premium">Premium</SelectItem>
                    </>
                  ) : (
                    <>
                      <SelectItem value="active">Ativa</SelectItem>
                      <SelectItem value="past_due">Em Atraso</SelectItem>
                      <SelectItem value="canceled">Cancelada</SelectItem>
                      <SelectItem value="unpaid">Não Paga</SelectItem>
                      <SelectItem value="incomplete">Incompleta</SelectItem>
                    </>
                  )}
                </SelectContent>
              </Select>
            </div>

            {/* Preview das mudanças de data para alteração de plano */}
            {subscriptionAction === 'plan' && newValue && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 mb-2">📅 Preview das Datas</h4>
                {(() => {
                  const preview = calculateDatePreview(newValue as 'free' | 'premium');
                  return (
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-blue-700">Início do período:</span>
                        <span className="font-medium">{formatDateShort(preview.currentPeriodStart)}</span>
                      </div>
                      {preview.hasExpiration ? (
                        <div className="flex justify-between">
                          <span className="text-blue-700">Válida até:</span>
                          <span className="font-medium text-green-600">
                            {formatDateShort(preview.currentPeriodEnd!)}
                          </span>
                        </div>
                      ) : (
                        <div className="flex justify-between">
                          <span className="text-blue-700">Validade:</span>
                          <span className="font-medium text-gray-600">Sem expiração</span>
                        </div>
                      )}
                      {preview.hasExpiration && (
                        <p className="text-xs text-blue-600 mt-2">
                          ⏰ A assinatura será válida por 30 dias a partir de hoje
                        </p>
                      )}
                    </div>
                  );
                })()}
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowSubscriptionDialog(false)}>
              Cancelar
            </Button>
            <Button onClick={handleUpdateSubscription} disabled={isUpdating || !newValue}>
              {isUpdating ? 'Atualizando...' : 'Atualizar'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </GlobalAdminLayout>
  );
}
