import { useEffect, useState } from 'react';
import { Switch, Route, Router, useLocation } from 'wouter';
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { useAuth } from "@/context/FirebaseAuthContext";
import NotFound from "@/pages/not-found";
import LandingPage from "@/pages/landing";

// Auth pages
import Login from "@/pages/auth/login";
import Register from "@/pages/auth/register";

// Admin pages
import AdminDashboard from "@/pages/admin/index";
import ProductsPage from "@/pages/admin/products";
import EditProductPage from "@/pages/admin/products/edit";
import NewProductPage from "@/pages/admin/products/new";

// Global Admin pages
import GlobalDashboard from "@/pages/admin/global/index";
import GlobalStoresPage from "@/pages/admin/global/stores";
import StoreDetailsPage from "@/pages/admin/global/store-details";
import GlobalAdminGuard from "@/components/global-admin/GlobalAdminGuard";
import OrdersPage from "@/pages/admin/orders";
import NewOrderPage from "@/pages/admin/orders/new";
import OrderDetailsPage from "@/pages/admin/orders/[id]";
import OrderProductDetailsPage from "@/pages/admin/orders/product-details/[id]";
import EditOrderItemsPage from "@/pages/admin/orders/edit-items/[id]";
import OrderPreviewPage from "@/pages/admin/orders/preview/[id]";
// Importar páginas de seleção
import SelectCustomerPage from "@/pages/admin/orders/select-customer";
import SelectProductsPage from "@/pages/admin/orders/select-products";
import CustomProductPage from "@/pages/admin/orders/custom-product";
import CustomersPage from "@/pages/admin/customers";
import CustomerDetailPage from "@/pages/admin/customers/[id]";
import NewCustomerPage from "@/pages/admin/customers/new";
import SettingsPage from "@/pages/admin/settings";

// Category pages
import CategoriesList from "@/pages/admin/categories/index";
import NewCategoryPage from "@/pages/admin/categories/new";
import EditCategoryPage from "@/pages/admin/categories/[id]/edit";

// Coupon pages
import CouponsPage from "@/pages/admin/coupons/index";
import NewCouponPage from "@/pages/admin/coupons/new";
import EditCouponPage from "@/pages/admin/coupons/[id]/edit";

// Store pages
import ProductDetailPage from '@/pages/store/product/[id]';
import CartPage from '@/pages/store/CartPage';
import StoreInfoPage from '@/pages/store/info/[slug]';
import StorePage from "@/pages/store/[slug]";
import TestIOSLayout from "@/pages/test-ios-layout";

// Context providers
import { CartProvider } from "@/context/CartContext";
import { StoreProvider } from "@/context/StoreContext";
import { FirebaseAuthProvider } from "@/context/FirebaseAuthContext";
import { AdminOrderProvider } from "@/context/AdminOrderContext";
import { SubscriptionProvider } from "@/context/SubscriptionContext";

// Function to inject fonts
function injectFonts() {
  const fontLink = document.createElement('link');
  fontLink.href = 'https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=Nunito:wght@400;600&display=swap';
  fontLink.rel = 'stylesheet';
  document.head.appendChild(fontLink);

  const iconLink = document.createElement('link');
  iconLink.href = 'https://fonts.googleapis.com/icon?family=Material+Icons';
  iconLink.rel = 'stylesheet';
  document.head.appendChild(iconLink);

  return { fontLink, iconLink };
}

// Protected route component
function ProtectedRoute({ children }: { children: React.ReactNode }) {
  const [location, setLocation] = useLocation();
  const { user, loading } = useAuth();

  useEffect(() => {
    if (!loading && !user) {
      setLocation("/login");
    }
  }, [user, loading, setLocation]);

  if (loading) {
    return <div className="flex items-center justify-center min-h-screen">Loading...</div>;
  }

  return user ? <>{children}</> : null;
}

function App() {
  // Inject fonts on app mount
  useEffect(() => {
    const { fontLink, iconLink } = injectFonts();

    // Clean up on unmount
    return () => {
      if (document.head.contains(fontLink)) {
        document.head.removeChild(fontLink);
      }
      if (document.head.contains(iconLink)) {
        document.head.removeChild(iconLink);
      }
    };
  }, []);

  return (
    <FirebaseAuthProvider>
      <StoreProvider>
        <SubscriptionProvider>
          <CartProvider>
            <AdminOrderProvider>
              <TooltipProvider>
                <Toaster />
                <Router>
              <Switch>
                {/* Auth routes */}
                <Route path="/login" component={Login} />
                <Route path="/register" component={Register} />

                {/* Global Admin routes - MUST come before /admin route */}
                <Route path="/admin/global">
                  <ProtectedRoute>
                    <GlobalAdminGuard>
                      <GlobalDashboard />
                    </GlobalAdminGuard>
                  </ProtectedRoute>
                </Route>
                <Route path="/admin/global/stores">
                  <ProtectedRoute>
                    <GlobalAdminGuard>
                      <GlobalStoresPage />
                    </GlobalAdminGuard>
                  </ProtectedRoute>
                </Route>
                <Route path="/admin/global/stores/:id">
                  {(params) => {
                    const storeId = parseInt(params.id);
                    if (isNaN(storeId)) {
                      return <ProtectedRoute>
                        <GlobalAdminGuard>
                          <div className="p-6">
                            <h2 className="text-xl font-semibold mb-4">Error</h2>
                            <p>ID da loja inválido. Por favor, volte e tente novamente.</p>
                          </div>
                        </GlobalAdminGuard>
                      </ProtectedRoute>;
                    }
                    return (
                      <ProtectedRoute>
                        <GlobalAdminGuard>
                          <StoreDetailsPage id={storeId} />
                        </GlobalAdminGuard>
                      </ProtectedRoute>
                    );
                  }}
                </Route>

                {/* Admin routes - protected */}
                <Route path="/admin">
                  <ProtectedRoute>
                    <AdminDashboard />
                  </ProtectedRoute>
                </Route>
                <Route path="/admin/dashboard">
                  <ProtectedRoute>
                    <AdminDashboard />
                  </ProtectedRoute>
                </Route>
                <Route path="/admin/products">
                  <ProtectedRoute>
                    <ProductsPage />
                  </ProtectedRoute>
                </Route>
                <Route path="/admin/products/edit/:id">
                  {(params) => {
                    const productId = parseInt(params.id);
                    if (isNaN(productId)) {
                      console.error("Invalid product ID:", params.id);
                      return <ProtectedRoute>
                        <div className="p-6">
                          <h2 className="text-xl font-semibold mb-4">Error</h2>
                          <p>Invalid product ID. Please go back and try again.</p>
                        </div>
                      </ProtectedRoute>;
                    }
                    return (
                      <ProtectedRoute>
                        <EditProductPage id={productId} />
                      </ProtectedRoute>
                    );
                  }}
                </Route>
                <Route path="/admin/products/new">
                  <ProtectedRoute>
                    <NewProductPage />
                  </ProtectedRoute>
                </Route>
                <Route path="/admin/orders">
                  <ProtectedRoute>
                    <OrdersPage />
                  </ProtectedRoute>
                </Route>
                <Route path="/admin/orders/new">
                  <ProtectedRoute>
                    <NewOrderPage />
                  </ProtectedRoute>
                </Route>
                <Route path="/admin/orders/product-details/:id">
                  {(params) => (
                    <ProtectedRoute>
                      <OrderProductDetailsPage />
                    </ProtectedRoute>
                  )}
                </Route>
                <Route path="/admin/orders/edit-items/:id">
                  {(params) => (
                    <ProtectedRoute>
                      <EditOrderItemsPage />
                    </ProtectedRoute>
                  )}
                </Route>
                <Route path="/admin/orders/preview/:id">
                  {(params) => {
                    const orderId = parseInt(params.id);
                    if (isNaN(orderId)) {
                      return <ProtectedRoute>
                        <div className="p-6">
                          <h2 className="text-xl font-semibold mb-4">Error</h2>
                          <p>Invalid order ID. Please go back and try again.</p>
                        </div>
                      </ProtectedRoute>;
                    }
                    return (
                      <ProtectedRoute>
                        {/* @ts-ignore - id is expected by the component */}
                        <OrderPreviewPage id={orderId} />
                      </ProtectedRoute>
                    );
                  }}
                </Route>
                <Route path="/admin/orders/select-customer">
                  <ProtectedRoute>
                    <SelectCustomerPage />
                  </ProtectedRoute>
                </Route>
                <Route path="/admin/orders/select-products">
                  <ProtectedRoute>
                    <SelectProductsPage />
                  </ProtectedRoute>
                </Route>
                <Route path="/admin/orders/custom-product">
                  <ProtectedRoute>
                    <CustomProductPage />
                  </ProtectedRoute>
                </Route>
                <Route path="/admin/orders/:id">
                  {(params) => {
                    const orderId = parseInt(params.id);
                    if (isNaN(orderId)) {
                      return <ProtectedRoute>
                        <div className="p-6">
                          <h2 className="text-xl font-semibold mb-4">Error</h2>
                          <p>Invalid order ID. Please go back and try again.</p>
                        </div>
                      </ProtectedRoute>;
                    }
                    return (
                      <ProtectedRoute>
                        {/* @ts-ignore - id is expected by the component */}
                        <OrderDetailsPage id={orderId} />
                      </ProtectedRoute>
                    );
                  }}
                </Route>
                <Route path="/admin/customers">
                  <ProtectedRoute>
                    <CustomersPage />
                  </ProtectedRoute>
                </Route>
                <Route path="/admin/customers/new">
                  <ProtectedRoute>
                    <NewCustomerPage />
                  </ProtectedRoute>
                </Route>
                <Route path="/admin/customers/:id">
                  {(params) => {
                    // Skip if the path is for new customer
                    if (params.id === 'new') {
                      return null;
                    }

                    const customerId = parseInt(params.id);
                    if (isNaN(customerId)) {
                      return <ProtectedRoute>
                        <div className="p-6">
                          <h2 className="text-xl font-semibold mb-4">Error</h2>
                          <p>Invalid customer ID. Please go back and try again.</p>
                        </div>
                      </ProtectedRoute>;
                    }
                    return (
                      <ProtectedRoute>
                        {/* @ts-ignore - id is expected by the component */}
                        <CustomerDetailPage id={customerId} />
                      </ProtectedRoute>
                    );
                  }}
                </Route>
                <Route path="/admin/settings">
                  <ProtectedRoute>
                    <SettingsPage />
                  </ProtectedRoute>
                </Route>
                <Route path="/admin/categories/new">
                  <ProtectedRoute>
                    <NewCategoryPage />
                  </ProtectedRoute>
                </Route>

                <Route path="/admin/categories/:id/edit">
                  {(params) => {
                    const categoryId = parseInt(params.id);
                    if (isNaN(categoryId)) {
                      return <ProtectedRoute>
                        <div className="p-6">
                          <h2 className="text-xl font-semibold mb-4">Error</h2>
                          <p>Invalid category ID. Please go back and try again.</p>
                        </div>
                      </ProtectedRoute>;
                    }
                    return (
                      <ProtectedRoute>
                        <EditCategoryPage />
                      </ProtectedRoute>
                    );
                  }}
                </Route>

                <Route path="/admin/categories">
                  <ProtectedRoute>
                    <CategoriesList />
                  </ProtectedRoute>
                </Route>

                <Route path="/admin/coupons">
                  <ProtectedRoute>
                    <CouponsPage />
                  </ProtectedRoute>
                </Route>

                <Route path="/admin/coupons/new">
                  <ProtectedRoute>
                    <NewCouponPage />
                  </ProtectedRoute>
                </Route>

                <Route path="/admin/coupons/:id/edit">
                  {(params) => {
                    const couponId = parseInt(params.id);
                    if (isNaN(couponId)) {
                      return <ProtectedRoute>
                        <div className="p-6">
                          <h2 className="text-xl font-semibold mb-4">Error</h2>
                          <p>ID de cupom inválido. Por favor, volte e tente novamente.</p>
                        </div>
                      </ProtectedRoute>;
                    }
                    return (
                      <ProtectedRoute>
                        <EditCouponPage />
                      </ProtectedRoute>
                    );
                  }}
                </Route>



                {/* Store front routes - support both formats */}
                {/* New direct URL format */}
                <Route path="/:slug/product/:id">
                  {(params) => <ProductDetailPage />}
                </Route>

                <Route path="/:slug/cart">
                  {(params) => <CartPage />}
                </Route>

                <Route path="/:slug/info">
                  {(params) => <StoreInfoPage />}
                </Route>

                <Route path="/:slug">
                  {(params) => {
                    // Skip if the path is an admin or auth route
                    if (['admin', 'login', 'register'].includes(params.slug)) {
                      return null;
                    }
                    return <StorePage slug={params.slug} />;
                  }}
                </Route>

                {/* Legacy store URL format (for compatibility) */}
                <Route path="/store/:slug/product/:id">
                  {(params) => <ProductDetailPage />}
                </Route>

                <Route path="/store/:slug">
                  {(params) => <StorePage slug={params.slug} />}
                </Route>

                {/* Test iOS Layout page */}
                <Route path="/test-ios-layout">
                  <TestIOSLayout />
                </Route>

                {/* Landing page as the home route */}
                <Route path="/">
                  <LandingPage />
                </Route>

                {/* 404 route */}
                <Route component={NotFound} />
              </Switch>
            </Router>
            </TooltipProvider>
          </AdminOrderProvider>
        </CartProvider>
        </SubscriptionProvider>
      </StoreProvider>
    </FirebaseAuthProvider>
  );
}

export default App;