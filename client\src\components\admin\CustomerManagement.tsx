import { useState, useEffect, useMemo } from "react";
import { useQuery, useQueryClient, useMutation } from "@tanstack/react-query";
import { useLocation } from "wouter";
import {
  Card,
  CardContent
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "@/hooks/useTranslation";
import { Search, Mail, Phone, Calendar, Edit, ExternalLink, ArrowDown, ArrowUp, SortAsc, SortDesc, ChevronLeft, ChevronRight, User, UserPlus } from "lucide-react";
import { formatPhoneWithCountryCode, formatDate } from "@/lib/utils";
import { apiRequest } from "@/lib/queryClient";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

// Order status badge component
const OrderStatusBadge = ({ status }: { status: string }) => {
  const { t } = useTranslation();

  const getVariant = () => {
    switch (status) {
      case 'pending':
        return "warning";
      case 'processing':
        return "info";
      case 'shipped':
        return "default";
      case 'completed':
        return "success";
      case 'cancelled':
        return "destructive";
      default:
        return "default";
    }
  };

  return (
    <Badge variant={getVariant() as any}>
      {t(`dashboard.${status}`)}
    </Badge>
  );
};

export function CustomerManagement() {
  const { t } = useTranslation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [, navigate] = useLocation();
  const [searchQuery, setSearchQuery] = useState("");
  const [sortField, setSortField] = useState<"name" | "createdAt">("createdAt");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  const [isMobile, setIsMobile] = useState(false);

  // Function to toggle sort direction or change sort field
  const handleSort = (field: "name" | "createdAt") => {
    if (sortField === field) {
      // Toggle direction if same field
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      // Set new field with default direction
      setSortField(field);
      setSortDirection(field === "name" ? "asc" : "desc"); // Default: name=asc, date=desc
    }
  };

  // Format WhatsApp number for link
  const getWhatsAppLink = (customer: any) => {
    if (!customer?.phone) return null;

    const countryCode = customer.countryCode || "+55";
    const phoneNumber = customer.phone.replace(/\D/g, '');
    const formattedNumber = `${countryCode.replace('+', '')}${phoneNumber}`;

    return `https://wa.me/${formattedNumber}`;
  };

  // Detectar se é mobile
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Verificar inicialmente
    checkIfMobile();

    // Adicionar listener para redimensionamento
    window.addEventListener('resize', checkIfMobile);

    // Forçar verificação após um curto período
    const timer = setTimeout(checkIfMobile, 500);

    // Cleanup
    return () => {
      window.removeEventListener('resize', checkIfMobile);
      clearTimeout(timer);
    };
  }, []);

  // Fetch customers
  const { data: customers, isLoading } = useQuery({
    queryKey: ['/api/customers'],
  });

  // Navigate to customer details page
  const handleViewDetails = (customer: any) => {
    navigate(`/admin/customers/${customer.id}`);
  };

  // Open edit dialog (keeping for backward compatibility)
  const handleEditCustomer = (customer: any) => {
    navigate(`/admin/customers/${customer.id}`);
  };

  // Filter and sort customers
  const filteredAndSortedCustomers = useMemo(() => {
    return customers
      ? customers
          .filter((customer: any) =>
            customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            (customer.email && customer.email.toLowerCase().includes(searchQuery.toLowerCase()))
          )
          .sort((a: any, b: any) => {
            // Sort by the selected field
            if (sortField === "name") {
              const nameA = a.name.toLowerCase();
              const nameB = b.name.toLowerCase();
              return sortDirection === "asc"
                ? nameA.localeCompare(nameB)
                : nameB.localeCompare(nameA);
            } else {
              // Sort by createdAt date
              const dateA = new Date(a.createdAt).getTime();
              const dateB = new Date(b.createdAt).getTime();
              return sortDirection === "asc"
                ? dateA - dateB
                : dateB - dateA;
            }
          })
      : [];
  }, [customers, searchQuery, sortField, sortDirection]);

  // Calculate total pages
  const totalPages = Math.ceil(filteredAndSortedCustomers.length / itemsPerPage);

  // Get paginated customers
  const paginatedCustomers = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredAndSortedCustomers.slice(startIndex, endIndex);
  }, [filteredAndSortedCustomers, currentPage, itemsPerPage]);

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Reset to first page when search query or sort changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery, sortField, sortDirection]);

  return (
    <div>
      {/* Header with Search Bar and Add Button */}
      <div className="flex flex-col md:flex-row gap-4 mb-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder={`${t('common.search')} ${t('customers.title').toLowerCase()}...`}
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Button
          onClick={() => navigate('/admin/customers/new')}
          className="whitespace-nowrap"
        >
          <UserPlus className="h-4 w-4 mr-2" />
          {t('customers.newCustomer') || 'Novo Cliente'}
        </Button>
      </div>

      {/* Sort Buttons */}
      <div className="flex gap-2 mb-4">
        <Button
          variant={sortField === "name" ? "default" : "outline"}
          size="sm"
          onClick={() => handleSort("name")}
          className="flex items-center"
        >
          {t('customers.name')}
          {sortField === "name" && (
            sortDirection === "asc" ? <ArrowUp className="ml-2 h-4 w-4" /> : <ArrowDown className="ml-2 h-4 w-4" />
          )}
        </Button>
        <Button
          variant={sortField === "createdAt" ? "default" : "outline"}
          size="sm"
          onClick={() => handleSort("createdAt")}
          className="flex items-center"
        >
          {t('customers.createdAt')}
          {sortField === "createdAt" && (
            sortDirection === "asc" ? <ArrowUp className="ml-2 h-4 w-4" /> : <ArrowDown className="ml-2 h-4 w-4" />
          )}
        </Button>
      </div>

      {/* Renderiza um card para cada cliente (versão mobile) */}
      {isMobile ? (
        <div className="space-y-4">
          {isLoading ? (
            // Mobile loading skeletons
            Array(3).fill(0).map((_, index) => (
              <div key={index} className="bg-white rounded-lg shadow p-4 mb-4">
                <div className="flex justify-between items-center mb-3">
                  <Skeleton className="h-5 w-32" />
                  <Skeleton className="h-8 w-16" />
                </div>
                <div className="space-y-2 mb-3">
                  <div className="flex items-center">
                    <Mail className="h-4 w-4 mr-2 text-muted-foreground" />
                    <Skeleton className="h-4 w-40" />
                  </div>
                  <div className="flex items-center">
                    <Phone className="h-4 w-4 mr-2 text-muted-foreground" />
                    <Skeleton className="h-4 w-24" />
                  </div>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                    <Skeleton className="h-4 w-24" />
                  </div>
                </div>
              </div>
            ))
          ) : filteredAndSortedCustomers.length > 0 ? (
            // Mobile customer cards
            paginatedCustomers.map((customer: any) => (
              <div
                key={customer.id}
                className="bg-white rounded-lg shadow p-4 mb-4 cursor-pointer hover:bg-gray-50 transition-colors"
                onClick={() => handleViewDetails(customer)}
              >
                <div className="flex justify-between items-center mb-3">
                  <h3 className="font-medium text-lg">{customer.name}</h3>
                  <div className="flex gap-1">
                    {customer.phone && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 text-green-600 hover:text-green-700 hover:bg-green-50"
                        onClick={(e) => {
                          e.stopPropagation();
                          const whatsappLink = getWhatsAppLink(customer);
                          if (whatsappLink) window.open(whatsappLink, '_blank');
                        }}
                      >
                        <ExternalLink className="h-4 w-4" />
                        <span className="sr-only">{t('customers.contactViaWhatsApp')}</span>
                      </Button>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleEditCustomer(customer);
                      }}
                    >
                      <Edit className="h-4 w-4" />
                      <span className="sr-only">{t('common.edit')}</span>
                    </Button>
                  </div>
                </div>

                <div className="space-y-2 text-sm">
                  {customer.email && (
                    <div className="flex items-center text-muted-foreground">
                      <Mail className="h-4 w-4 mr-2" />
                      {customer.email}
                    </div>
                  )}

                  {customer.phone && (
                    <div className="flex items-center text-muted-foreground">
                      <Phone className="h-4 w-4 mr-2" />
                      {customer.countryCode ? `${customer.countryCode} ${customer.phone}` : formatPhoneWithCountryCode(customer.phone, "br")}
                    </div>
                  )}

                  <div className="flex items-center text-muted-foreground">
                    <Calendar className="h-4 w-4 mr-2" />
                    {formatDate(customer.createdAt)}
                  </div>
                </div>
              </div>
            ))
          ) : (
            // No customers found (mobile)
            <div className="bg-white rounded-lg shadow p-6 text-center">
              <p className="text-muted-foreground">
                {searchQuery
                  ? t('customers.noMatchingCustomers', { query: searchQuery })
                  : t('customers.noCustomers')
                }
              </p>
            </div>
          )}

          {/* Pagination for mobile */}
          {filteredAndSortedCustomers.length > itemsPerPage && (
            <div className="mt-6">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                      className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
                    />
                  </PaginationItem>

                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    // Show first page, last page, current page, and pages around current
                    let pageToShow: number | null = null;

                    if (totalPages <= 5) {
                      // If 5 or fewer pages, show all
                      pageToShow = i + 1;
                    } else if (i === 0) {
                      // First button is always page 1
                      pageToShow = 1;
                    } else if (i === 4) {
                      // Last button is always the last page
                      pageToShow = totalPages;
                    } else if (currentPage <= 2) {
                      // Near the start
                      pageToShow = i + 1;
                    } else if (currentPage >= totalPages - 1) {
                      // Near the end
                      pageToShow = totalPages - 4 + i;
                    } else {
                      // In the middle
                      pageToShow = currentPage - 1 + i;
                    }

                    // Show ellipsis instead of page number in certain cases
                    if (totalPages > 5) {
                      if ((i === 1 && pageToShow > 2) || (i === 3 && pageToShow < totalPages - 1)) {
                        return (
                          <PaginationItem key={i}>
                            <PaginationEllipsis />
                          </PaginationItem>
                        );
                      }
                    }

                    return (
                      <PaginationItem key={i}>
                        <PaginationLink
                          isActive={currentPage === pageToShow}
                          onClick={() => handlePageChange(pageToShow!)}
                        >
                          {pageToShow}
                        </PaginationLink>
                      </PaginationItem>
                    );
                  })}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                      className={currentPage === totalPages ? "pointer-events-none opacity-50" : ""}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </div>
      ) : (
        // Desktop view - Table
        <Card>
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort("name")}
                  >
                    <div className="flex items-center">
                      {t('customers.name')}
                      {sortField === "name" ? (
                        sortDirection === "asc" ?
                          <ArrowUp className="ml-2 h-4 w-4" /> :
                          <ArrowDown className="ml-2 h-4 w-4" />
                      ) : null}
                    </div>
                  </TableHead>
                  <TableHead>{t('customers.email')}</TableHead>
                  <TableHead>{t('customers.phone')}</TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort("createdAt")}
                  >
                    <div className="flex items-center">
                      {t('customers.createdAt')}
                      {sortField === "createdAt" ? (
                        sortDirection === "asc" ?
                          <ArrowUp className="ml-2 h-4 w-4" /> :
                          <ArrowDown className="ml-2 h-4 w-4" />
                      ) : null}
                    </div>
                  </TableHead>
                  <TableHead className="text-right">{t('common.actions')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  // Loading skeletons
                  Array(5).fill(0).map((_, index) => (
                    <TableRow key={index}>
                      <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-40" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                      <TableCell className="text-right"><Skeleton className="h-4 w-16 ml-auto" /></TableCell>
                    </TableRow>
                  ))
                ) : filteredAndSortedCustomers.length > 0 ? (
                  paginatedCustomers.map((customer: any) => (
                    <TableRow
                      key={customer.id}
                      className="cursor-pointer hover:bg-gray-50"
                      onClick={() => handleViewDetails(customer)}
                    >
                      <TableCell className="font-medium">{customer.name}</TableCell>
                      <TableCell>{customer.email || "-"}</TableCell>
                      <TableCell>{customer.phone ? (customer.countryCode ? `${customer.countryCode} ${customer.phone}` : formatPhoneWithCountryCode(customer.phone, "br")) : "-"}</TableCell>
                      <TableCell>{formatDate(customer.createdAt)}</TableCell>
                      <TableCell className="text-right flex justify-end gap-2">
                        {customer.phone && (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-green-600 hover:text-green-700 hover:bg-green-50"
                            onClick={(e) => {
                              e.stopPropagation();
                              const whatsappLink = getWhatsAppLink(customer);
                              if (whatsappLink) window.open(whatsappLink, '_blank');
                            }}
                          >
                            <ExternalLink className="h-4 w-4 mr-1" />
                            {t('customers.contactViaWhatsApp')}
                          </Button>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEditCustomer(customer);
                          }}
                        >
                          <Edit className="h-4 w-4 mr-1" />
                          {t('common.edit')}
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-6">
                      {searchQuery
                        ? t('customers.noMatchingCustomers', { query: searchQuery })
                        : t('customers.noCustomers')
                      }
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </CardContent>

          {/* Pagination for desktop */}
          {filteredAndSortedCustomers.length > itemsPerPage && (
            <div className="py-4 border-t">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                      className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
                    />
                  </PaginationItem>

                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    // Show first page, last page, current page, and pages around current
                    let pageToShow: number | null = null;

                    if (totalPages <= 5) {
                      // If 5 or fewer pages, show all
                      pageToShow = i + 1;
                    } else if (i === 0) {
                      // First button is always page 1
                      pageToShow = 1;
                    } else if (i === 4) {
                      // Last button is always the last page
                      pageToShow = totalPages;
                    } else if (currentPage <= 2) {
                      // Near the start
                      pageToShow = i + 1;
                    } else if (currentPage >= totalPages - 1) {
                      // Near the end
                      pageToShow = totalPages - 4 + i;
                    } else {
                      // In the middle
                      pageToShow = currentPage - 1 + i;
                    }

                    // Show ellipsis instead of page number in certain cases
                    if (totalPages > 5) {
                      if ((i === 1 && pageToShow > 2) || (i === 3 && pageToShow < totalPages - 1)) {
                        return (
                          <PaginationItem key={i}>
                            <PaginationEllipsis />
                          </PaginationItem>
                        );
                      }
                    }

                    return (
                      <PaginationItem key={i}>
                        <PaginationLink
                          isActive={currentPage === pageToShow}
                          onClick={() => handlePageChange(pageToShow!)}
                        >
                          {pageToShow}
                        </PaginationLink>
                      </PaginationItem>
                    );
                  })}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                      className={currentPage === totalPages ? "pointer-events-none opacity-50" : ""}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </Card>
      )}

      {/* Items per page selector */}
      {filteredAndSortedCustomers.length > 10 && (
        <div className="mt-4 flex justify-end items-center gap-2 text-sm text-muted-foreground">
          <span>{t('common.itemsPerPage', 'Items per page')}:</span>
          <select
            className="bg-transparent border rounded px-2 py-1"
            value={itemsPerPage}
            onChange={(e) => {
              setItemsPerPage(Number(e.target.value));
              setCurrentPage(1); // Reset to first page when changing items per page
            }}
          >
            <option value="5">5</option>
            <option value="10">10</option>
            <option value="20">20</option>
            <option value="50">50</option>
          </select>
        </div>
      )}
    </div>
  );
}

export default CustomerManagement;
