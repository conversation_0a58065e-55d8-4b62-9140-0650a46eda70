import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useMutation } from '@tanstack/react-query';
import { countryCodes } from '@/lib/countryCodes';
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetDescription,
  SheetFooter,
  SheetClose
} from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { useCart } from '@/context/CartContext';
import { useTranslation } from '@/hooks/useTranslation';
import { formatCurrency, formatStoreCurrency } from '@/lib/utils';
import { apiRequest } from '@/lib/queryClient';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogAction,
} from '@/components/ui/alert-dialog';
import { X, Minus, Plus, ShoppingBag, Check } from 'lucide-react';
import { useStore } from '@/context/StoreContext';

interface CartProps {
  isOpen: boolean;
  onClose: () => void;
}

// Checkout form schema
const checkoutSchema = z.object({
  customer: z.object({
    name: z.string().min(1, { message: "Name is required" }),
    email: z.string().email({ message: "Invalid email address" }),
    countryCode: z.string().default("+55"),
    whatsapp: z.string()
      .min(8, { message: "WhatsApp number must be at least 8 digits" })
      .max(15, { message: "WhatsApp number is too long" })
      .regex(/^[0-9]+$/, { message: "WhatsApp must contain only numbers" })
      .refine(val => val.length >= 8, {
        message: "WhatsApp number must be at least 8 digits"
      }),
  }),
  paymentMethod: z.string().min(1, { message: "Payment method is required" }),
  notes: z.string().optional(),
});

type CheckoutFormValues = z.infer<typeof checkoutSchema>;

export function Cart({ isOpen, onClose }: CartProps) {
  const { t } = useTranslation();
  const { toast } = useToast();
  const { store } = useStore();
  const { items, addItem, removeItem, updateQuantity, clearCart, subtotal } = useCart();

  // Calcula o subtotal de um item incluindo o preço base e as variações
  const calculateItemSubtotal = (item: any) => {
    let itemSubtotal = item.price * item.quantity; // Preço base × quantidade

    // Adicionar preço das variações
    if (item.selectedVariations && item.selectedVariations.length > 0) {
      const variationsTotal = item.selectedVariations.reduce(
        (total: number, variation: any) => total + (variation.price || 0),
        0
      );
      itemSubtotal += variationsTotal * item.quantity;
    }

    return itemSubtotal;
  };

  // Calcula o preço total das variações de um item
  const calculateVariationsTotal = (item: any) => {
    if (!item.selectedVariations || item.selectedVariations.length === 0) {
      return 0;
    }

    return item.selectedVariations.reduce(
      (total: number, variation: any) => total + (variation.price || 0),
      0
    ) * item.quantity;
  };
  const [isCheckingOut, setIsCheckingOut] = useState(false);
  const [isOrderSuccess, setIsOrderSuccess] = useState(false);
  const [orderNumber, setOrderNumber] = useState<number | null>(null);

  // Set up checkout form
  const form = useForm<CheckoutFormValues>({
    resolver: zodResolver(checkoutSchema),
    defaultValues: {
      customer: {
        name: '',
        email: '',
        countryCode: '+55',
        whatsapp: ''
      },
      paymentMethod: '',
      notes: ''
    }
  });

  // Place order mutation
  const orderMutation = useMutation({
    mutationFn: (data: CheckoutFormValues) => {
      const orderData = {
        ...data,
        receivingMethod: 'delivery', // Método padrão de recebimento
        receivingDate: new Date(), // Data atual como padrão
        subtotal: subtotal, // Total do pedido
        items: items.map(item => ({
          productId: item.productId,
          quantity: item.quantity,
          price: item.price,
          selectedVariations: item.selectedVariations || [],
          observation: item.observation || undefined
        }))
      };
      return apiRequest('POST', `/api/public/stores/${store?.slug}/orders`, orderData);
    },
    onSuccess: async (response) => {
      const data = await response.json();
      setOrderNumber(data.orderId);
      setIsOrderSuccess(true);
      clearCart();
    },
    onError: (error) => {
      toast({
        title: t('storefront.orderError'),
        description: error instanceof Error ? error.message : String(error),
        variant: "destructive",
      });
    }
  });

  // Handle checkout form submission
  const onSubmit = (values: CheckoutFormValues) => {
    if (items.length === 0) {
      toast({
        title: t('common.error'),
        description: t('storefront.emptyCart'),
        variant: "destructive",
      });
      return;
    }

    orderMutation.mutate(values);
  };

  // Start checkout process
  const handleCheckout = () => {
    if (items.length === 0) {
      toast({
        description: t('storefront.emptyCart'),
        variant: "destructive",
      });
      return;
    }
    setIsCheckingOut(true);
  };

  // Go back to cart from checkout
  const handleBackToCart = () => {
    setIsCheckingOut(false);
  };

  // Close cart and reset states
  const handleClose = () => {
    onClose();
    if (isCheckingOut) {
      setTimeout(() => {
        setIsCheckingOut(false);
        setIsOrderSuccess(false);
      }, 300);
    }
  };

  // Get available payment methods from store
  const getAvailablePaymentMethods = () => {
    if (!store?.paymentMethods) return [];

    const methods = [];
    // Adicionar métodos padrão
    if (store.paymentMethods.cash) methods.push({ id: 'cash', name: t('storefront.cash') });
    if (store.paymentMethods.creditCard) methods.push({ id: 'creditCard', name: t('storefront.creditCard') });
    if (store.paymentMethods.debitCard) methods.push({ id: 'debitCard', name: t('storefront.debitCard') });
    if (store.paymentMethods.pix) methods.push({ id: 'pix', name: t('storefront.pix') });
    if (store.paymentMethods.bankTransfer) methods.push({ id: 'bankTransfer', name: t('storefront.bankTransfer') });

    // Adicionar métodos personalizados
    if (store.paymentMethods.customMethods && Array.isArray(store.paymentMethods.customMethods)) {
      store.paymentMethods.customMethods.forEach((method, index) => {
        methods.push({
          id: `custom_${index}`,
          name: method
        });
      });
    }

    console.log('Available payment methods:', methods);
    return methods;
  };

  return (
    <>
      <Sheet open={isOpen} onOpenChange={handleClose}>
        <SheetContent className="w-full sm:max-w-md overflow-y-auto">
          <SheetHeader>
            <SheetTitle>{t('storefront.cart')}</SheetTitle>
            <SheetDescription>
              {items.length === 0 ? t('storefront.emptyCart') : ''}
            </SheetDescription>
          </SheetHeader>

          {!isCheckingOut ? (
            // Cart View
            <>
              {items.length > 0 ? (
                <div className="mt-8 flex-1 overflow-y-auto">
                  <ul className="divide-y divide-border">
                    {items.map((item) => (
                      <li key={item.id} className="py-6 flex">
                        {item.image && (
                          <div className="flex-shrink-0 w-6 h-6 rounded-md overflow-hidden">
                            <img src={item.image} alt={item.name} className="w-6 h-6 object-cover" />
                          </div>
                        )}
                        <div className="ml-4 flex-1 flex flex-col">
                          <div>
                            <div className="flex justify-between text-base font-medium text-neutral-dark">
                              <h3 className="line-clamp-1">{item.name}</h3>
                              <p className="ml-4">{formatStoreCurrency(item.price, store?.currency)}</p>
                            </div>
                            {item.selectedVariations && item.selectedVariations.length > 0 && (
                              <div className="mt-1 text-sm text-muted-foreground">
                                {item.selectedVariations.map((variation, i) => (
                                  <p key={i} className="line-clamp-1">
                                    {variation.variationName}: {variation.optionName}
                                    {variation.price > 0 && ` (+${formatStoreCurrency(variation.price * item.quantity, store?.currency)})`}
                                  </p>
                                ))}

                              </div>
                            )}
                            {item.observation && (
                              <div className="mt-1 text-sm text-muted-foreground">
                                <p className="line-clamp-2">{item.observation}</p>
                              </div>
                            )}
                          </div>
                          <div className="flex-1 flex items-end justify-between text-sm">
                            <div className="flex flex-col items-start">
                              <div className="flex items-center">
                                <button
                                  type="button"
                                  className="text-neutral-dark p-1"
                                  onClick={() => updateQuantity(item.id, item.quantity - 1)}
                                >
                                  <Minus className="h-4 w-4" />
                                </button>
                                <span className="mx-2 text-neutral-dark">{item.quantity}</span>
                                <button
                                  type="button"
                                  className="text-neutral-dark p-1"
                                  onClick={() => updateQuantity(item.id, item.quantity + 1)}
                                >
                                  <Plus className="h-4 w-4" />
                                </button>
                              </div>
                              <div className="text-sm font-medium text-primary mt-1">
                                {t('storefront.itemTotal')}: {formatStoreCurrency(calculateItemSubtotal(item), store?.currency)}
                              </div>
                            </div>
                            <div className="flex">
                              <button
                                type="button"
                                className="font-medium text-error hover:text-error-dark"
                                onClick={() => removeItem(item.id)}
                              >
                                {t('storefront.removeItem')}
                              </button>
                            </div>
                          </div>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              ) : (
                <div className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <ShoppingBag className="mx-auto h-12 w-12 text-muted-foreground" />
                    <h3 className="mt-2 text-sm font-medium text-neutral-dark">
                      {t('storefront.emptyCart')}
                    </h3>
                  </div>
                </div>
              )}

              {items.length > 0 && (
                <div className="border-t border-border py-6">
                  <div className="flex justify-between text-base font-medium text-neutral-dark">
                    <p>{t('storefront.subtotal')}</p>
                    <p>{formatStoreCurrency(subtotal, store?.currency)}</p>
                  </div>
                  <p className="mt-0.5 text-sm text-neutral-dark">
                    {t('storefront.shippingCalc')}
                  </p>
                  <div className="mt-6">
                    <Button
                      className="w-full bg-primary text-white hover:bg-primary/90 hover:text-white"
                      onClick={handleCheckout}
                    >
                      {t('storefront.checkout')}
                    </Button>
                  </div>
                  <div className="mt-6 flex justify-center text-sm text-center text-neutral-dark">
                    <SheetClose asChild>
                      <Button variant="link">
                        {t('storefront.continueShopping')}
                      </Button>
                    </SheetClose>
                  </div>
                </div>
              )}
            </>
          ) : (
            // Checkout Form
            <div className="py-6">
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium">{t('storefront.customerInfo')}</h3>
                    <div className="mt-4 space-y-4">
                      <FormField
                        control={form.control}
                        name="customer.name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('storefront.name')}</FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="customer.email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('storefront.email')}</FormLabel>
                            <FormControl>
                              <Input type="email" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      {/* Código de país */}
                      <FormField
                        control={form.control}
                        name="customer.countryCode"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('storefront.countryCode')}</FormLabel>
                            <FormControl>
                              <Select
                                value={field.value}
                                onValueChange={field.onChange}
                              >
                                <SelectTrigger className="w-full">
                                  <SelectValue placeholder={t('storefront.selectCountryCode')} />
                                </SelectTrigger>
                                <SelectContent>
                                  {countryCodes.map((country) => (
                                    <SelectItem key={country.id} value={country.id}>
                                      <div className="flex items-center">
                                        <span className="mr-2">{country.flag}</span>
                                        <span>{country.name}</span>
                                        <span className="ml-1 text-muted-foreground">{country.code}</span>
                                      </div>
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {/* Whatsapp */}
                      <FormField
                        control={form.control}
                        name="customer.whatsapp"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('storefront.whatsapp')}</FormLabel>
                            <FormControl>
                              <Input {...field} placeholder={t('storefront.whatsappPlaceholder')} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  <Separator />

                  <div>
                    <h3 className="text-lg font-medium">{t('storefront.paymentMethod')}</h3>
                    <div className="mt-4">
                      <FormField
                        control={form.control}
                        name="paymentMethod"
                        render={({ field }) => (
                          <FormItem>
                            <Select
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder={t('storefront.paymentMethod')} />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {getAvailablePaymentMethods().map(method => (
                                  <SelectItem key={method.id} value={method.id}>
                                    {method.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  <div>
                    <FormField
                      control={form.control}
                      name="notes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t('storefront.notes')}</FormLabel>
                          <FormControl>
                            <Textarea
                              {...field}
                              placeholder={t('storefront.notes')}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <Separator />

                  <div className="pt-2">
                    <div className="flex justify-between text-base font-medium text-neutral-dark">
                      <p>{t('storefront.total')}</p>
                      <p>{formatStoreCurrency(subtotal, store?.currency)}</p>
                    </div>
                  </div>

                  <div className="flex gap-4">
                    <Button
                      type="button"
                      variant="outline"
                      className="flex-1"
                      onClick={handleBackToCart}
                    >
                      {t('common.back')}
                    </Button>
                    <Button
                      type="submit"
                      className="flex-1 bg-primary text-white hover:bg-primary/90 hover:text-white"
                      disabled={orderMutation.isPending}
                    >
                      {orderMutation.isPending
                        ? t('common.loading')
                        : t('storefront.placeOrder')}
                    </Button>
                  </div>
                </form>
              </Form>
            </div>
          )}
        </SheetContent>
      </Sheet>

      {/* Order Success Dialog */}
      <AlertDialog open={isOrderSuccess} onOpenChange={setIsOrderSuccess}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('storefront.orderSuccess')}</AlertDialogTitle>
            <AlertDialogDescription>
              <div className="flex flex-col items-center justify-center py-4">
                <div className="rounded-full bg-success bg-opacity-20 p-3 mb-4">
                  <Check className="h-6 w-6 text-success" />
                </div>
                <p>{t('storefront.orderNumber')}: #{orderNumber}</p>
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction onClick={handleClose} className="bg-primary text-white hover:bg-primary/90 hover:text-white">
              {t('storefront.returnToStore')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}

export default Cart;