# Controle de Status de Recebimento - Exemplos de Uso

## Visão Geral

O sistema de controle de recebimentos permite rastrear pagamentos de pedidos e atualizar automaticamente o status de pagamento baseado nos recebimentos registrados.

## Estrutura de Dados

### Tipo `Recebimento`
```typescript
type Recebimento = {
  valor: number;
  data: Date;
  metodo: 'Pix' | 'Dinheiro' | 'Cartão' | 'Transferência' | 'Outro';
  observacao?: string;
};
```

### Tipo `Order` (estendido)
```typescript
type Order = {
  // ... campos existentes
  paymentStatus: 'pendente' | 'parcialmente_recebido' | 'recebido' | 'em_disputa' | 'estornado' | 'cancelado';
  recebimentos?: OrderPayment[]; // Relação com recebimentos
};
```

## Status de Pagamento

### Estados Possíveis
- **`pendente`**: Nenhum recebimento registrado
- **`parcialmente_recebido`**: Valor recebido menor que o total do pedido
- **`recebido`**: Valor recebido igual ou maior que o total do pedido
- **`em_disputa`**: Recebimento marcado como disputa (observação contém "disputa")
- **`estornado`**: Valor total recebido é negativo ou zero (devido a estornos)
- **`cancelado`**: Recebimento marcado como cancelado (observação contém "cancelado")

## Função `atualizarStatusPagamento()`

### Uso
```typescript
import { atualizarStatusPagamento } from '@shared/schema';

const totalPedido = 100.00;
const recebimentos = [
  { valor: 50.00, data: new Date(), metodo: 'Pix', observacao: null },
  { valor: 50.00, data: new Date(), metodo: 'Dinheiro', observacao: null }
];

const status = atualizarStatusPagamento(totalPedido, recebimentos);
console.log(status); // 'recebido'
```

### Exemplos de Cenários

#### 1. Pedido Pendente
```typescript
const status = atualizarStatusPagamento(100.00, []);
// Resultado: 'pendente'
```

#### 2. Pagamento Parcial
```typescript
const recebimentos = [
  { valor: 30.00, data: new Date(), metodo: 'Pix', observacao: null }
];
const status = atualizarStatusPagamento(100.00, recebimentos);
// Resultado: 'parcialmente_recebido'
```

#### 3. Pagamento Completo
```typescript
const recebimentos = [
  { valor: 100.00, data: new Date(), metodo: 'Cartão', observacao: null }
];
const status = atualizarStatusPagamento(100.00, recebimentos);
// Resultado: 'recebido'
```

#### 4. Pagamento em Disputa
```typescript
const recebimentos = [
  { valor: 100.00, data: new Date(), metodo: 'Outro', observacao: 'Pagamento em disputa com o banco' }
];
const status = atualizarStatusPagamento(100.00, recebimentos);
// Resultado: 'em_disputa'
```

#### 5. Pagamento Estornado
```typescript
const recebimentos = [
  { valor: 100.00, data: new Date(), metodo: 'Cartão', observacao: null },
  { valor: -100.00, data: new Date(), metodo: 'Outro', observacao: 'Estorno do cartão' }
];
const status = atualizarStatusPagamento(100.00, recebimentos);
// Resultado: 'estornado'
```

#### 6. Pagamento Cancelado
```typescript
const recebimentos = [
  { valor: 0.00, data: new Date(), metodo: 'Outro', observacao: 'Pedido cancelado pelo cliente' }
];
const status = atualizarStatusPagamento(100.00, recebimentos);
// Resultado: 'cancelado'
```

## API Endpoints

### Criar Recebimento
```http
POST /api/orders/:orderId/payments
Content-Type: application/json
Authorization: Bearer <token>

{
  "valor": 50.00,
  "data": "2024-01-15T10:30:00Z",
  "metodo": "Pix",
  "observacao": "Pagamento via PIX"
}
```

### Listar Recebimentos
```http
GET /api/orders/:orderId/payments
Authorization: Bearer <token>
```

### Recalcular Status
```http
POST /api/orders/:orderId/payments/recalculate
Authorization: Bearer <token>
```

## Comportamento Automático

O sistema atualiza automaticamente o `paymentStatus` do pedido sempre que:
1. Um novo recebimento é criado
2. O método `atualizarStatusPagamentoAutomatico()` é chamado

### Lógica de Prioridade
1. **Cancelado** - tem prioridade máxima
2. **Estornado** - quando total recebido ≤ 0
3. **Em Disputa** - quando há observação com "disputa"
4. **Recebido** - quando total recebido ≥ total do pedido
5. **Parcialmente Recebido** - quando total recebido > 0 mas < total do pedido
6. **Pendente** - quando não há recebimentos

### Tolerância
O sistema usa uma tolerância de R$ 0,01 para considerar um pagamento como "recebido", evitando problemas com diferenças de centavos.

## Integração com Frontend

### Exemplo de Uso no React
```typescript
// Hook para buscar recebimentos
const useOrderPayments = (orderId: number) => {
  return useQuery({
    queryKey: ['orderPayments', orderId],
    queryFn: () => apiRequest(`/api/orders/${orderId}/payments`)
  });
};

// Componente para exibir status
const PaymentStatus = ({ order }: { order: Order }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'recebido': return 'text-green-600';
      case 'parcialmente_recebido': return 'text-yellow-600';
      case 'em_disputa': return 'text-red-600';
      case 'estornado': return 'text-red-800';
      case 'cancelado': return 'text-gray-600';
      default: return 'text-blue-600';
    }
  };

  return (
    <span className={getStatusColor(order.paymentStatus)}>
      {order.paymentStatus.replace('_', ' ').toUpperCase()}
    </span>
  );
};
```
