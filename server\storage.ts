import {
  type User, type InsertUser, type UpsertUser, type Store, type InsertStore, type Category, type InsertCategory,
  type Product, type InsertProduct, type Customer, type InsertCustomer, type Order, type InsertOrder,
  type OrderItem, type InsertOrderItem, type StoreVisit, type InsertStoreVisit,
  type ProductVariation, type InsertProductVariation, type VariationOption, type InsertVariationOption,
  type CartItem, type InsertCartItem, type OrderRevision, type InsertOrderRevision,
  type OrderRevisionItem, type InsertOrderRevisionItem, type Coupon, type InsertCoupon,
  type CouponUsage, type InsertCouponUsage, type OrderPayment, type InsertOrderPayment,
  type Subscription, type InsertSubscription
} from '@shared/schema';

export interface IStorage {
  // User methods - updated for Firebase Auth
  getUser(id: number): Promise<User | undefined>;
  getUserById(id: number): Promise<User | undefined>; // Alias para getUser
  getUserByEmail(email: string): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getUserByFirebaseUid(firebaseUid: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  createUserWithFirebaseUid(userData: Partial<User> & { fullName?: string }): Promise<User>;
  upsertUser(user: UpsertUser): Promise<User>;
  updateUser(id: number, userData: Partial<User>): Promise<User | undefined>;
  updateUserByFirebaseUid(firebaseUid: string, userData: Partial<User>): Promise<User | undefined>;

  // Store methods
  getStore(id: number): Promise<Store | undefined>;
  getStoreById(id: number): Promise<Store | undefined>; // Alias para getStore
  getStoreBySlug(slug: string): Promise<Store | undefined>;
  getStoreByUserId(userId: number): Promise<Store | undefined>;
  getStoreByFirebaseUid(firebaseUid: string): Promise<Store | undefined>;
  getAllStores(): Promise<Store[]>; // Para dashboard global
  createStore(store: InsertStore): Promise<Store>;
  updateStore(id: number, store: Partial<Store>): Promise<Store | undefined>;

  // Product methods
  getProduct(id: number): Promise<Product | undefined>;
  getProductsByStoreId(storeId: number): Promise<Product[]>;
  getProductsByCategoryId(categoryId: number): Promise<Product[]>;
  createProduct(product: InsertProduct): Promise<Product>;
  updateProduct(id: number, product: Partial<Product>): Promise<Product | undefined>;
  deleteProduct(id: number): Promise<boolean>;

  // Product Variation methods
  getProductVariationsByProductId(productId: number): Promise<ProductVariation[]>;
  createProductVariation(variation: InsertProductVariation): Promise<ProductVariation>;
  updateProductVariation(id: number, variation: Partial<ProductVariation>): Promise<ProductVariation | undefined>;
  deleteProductVariation(id: number): Promise<boolean>;

  // Variation Option methods
  getVariationOptionsByVariationId(variationId: number): Promise<VariationOption[]>;
  createVariationOption(option: InsertVariationOption): Promise<VariationOption>;
  updateVariationOption(id: number, option: Partial<VariationOption>): Promise<VariationOption | undefined>;
  deleteVariationOption(id: number): Promise<boolean>;

  // Category methods
  getCategory(id: number): Promise<Category | undefined>;
  getCategoriesByStoreId(storeId: number): Promise<Category[]>;
  createCategory(category: InsertCategory): Promise<Category>;
  updateCategory(id: number, category: Partial<Category>): Promise<Category | undefined>;
  deleteCategory(id: number): Promise<boolean>;

  // Customer methods
  getCustomer(id: number): Promise<Customer | undefined>;
  getCustomersByStoreId(storeId: number): Promise<Customer[]>;
  getCustomerByEmail(storeId: number, email: string): Promise<Customer | undefined>;
  getCustomerByPhone(storeId: number, phone: string): Promise<Customer | undefined>;
  createCustomer(customer: InsertCustomer): Promise<Customer>;
  updateCustomer(id: number, customer: Partial<Customer>): Promise<Customer | undefined>;

  // Order methods
  getOrder(id: number): Promise<Order | undefined>;
  getOrdersByStoreId(storeId: number): Promise<Order[]>;
  getOrdersByCustomerId(customerId: number): Promise<Order[]>;
  getOrdersByDateRange(startDate: Date, endDate: Date): Promise<Order[]>; // Para analytics globais
  createOrder(order: InsertOrder): Promise<Order>;
  updateOrder(id: number, order: Partial<Order>): Promise<Order | undefined>;
  updateOrderStatus(id: number, status: string): Promise<Order | undefined>;
  updateOrderPaymentStatus(orderId: number, paymentStatus: string): Promise<Order | undefined>;

  // Order item methods
  getOrderItemsByOrderId(orderId: number): Promise<OrderItem[]>;
  createOrderItem(orderItem: InsertOrderItem): Promise<OrderItem>;

  // Order Payment methods
  createOrderPayment(payment: InsertOrderPayment): Promise<OrderPayment>;
  getOrderPaymentsByOrderId(orderId: number): Promise<OrderPayment[]>;
  atualizarStatusPagamentoAutomatico(orderId: number): Promise<void>;

  // Analytics methods
  recordStoreVisit(storeVisit: InsertStoreVisit): Promise<StoreVisit>;
  getStoreVisitsByStoreId(storeId: number, startDate?: Date, endDate?: Date): Promise<StoreVisit[]>;
  getMonthlyVisitCount(storeId: number): Promise<number>;
  getMonthlyOrderCount(storeId: number): Promise<number>;

  // Cart item methods
  getCartItemsBySessionId(storeId: number, sessionId: string): Promise<CartItem[]>;
  getCartItemsByUserId(storeId: number, userId: string): Promise<CartItem[]>;
  createCartItem(cartItem: InsertCartItem): Promise<CartItem>;
  updateCartItem(id: number, cartItem: Partial<CartItem>): Promise<CartItem | undefined>;
  deleteCartItem(id: number): Promise<boolean>;
  clearCartItems(storeId: number, sessionId: string): Promise<boolean>;

  // Order Revision methods
  getOrderRevision(id: number): Promise<OrderRevision | undefined>;
  getOrderRevisionsByOrderId(orderId: number): Promise<OrderRevision[]>;
  getOrderRevisionItems(revisionId: number): Promise<OrderRevisionItem[]>;
  createOrderRevision(revision: InsertOrderRevision): Promise<OrderRevision>;
  createOrderRevisionItem(item: InsertOrderRevisionItem): Promise<OrderRevisionItem>;
  getLatestRevisionNumber(orderId: number): Promise<number>;
  updateOrderRevisionStatus(id: number, status: string): Promise<OrderRevision | undefined>;
  updateOrderRevision(id: number, data: Partial<OrderRevision>): Promise<OrderRevision | undefined>;
  setCurrentRevision(revisionId: number, orderId: number): Promise<void>;
  deleteOrderRevision(id: number): Promise<boolean>;
  deleteOrderRevisionItem(id: number): Promise<boolean>;

  // Coupon methods
  getCoupon(id: number): Promise<Coupon | undefined>;
  getCouponsByStoreId(storeId: number): Promise<Coupon[]>;
  getCouponByCode(storeId: number, code: string): Promise<Coupon | undefined>;
  createCoupon(coupon: InsertCoupon): Promise<Coupon>;
  updateCoupon(id: number, coupon: Partial<Coupon>): Promise<Coupon | undefined>;
  updateCouponStatus(id: number, active: boolean): Promise<Coupon | undefined>;

  // Coupon Usage methods
  createCouponUsage(usage: InsertCouponUsage): Promise<CouponUsage>;
  getCouponUsagesByCouponId(couponId: number): Promise<CouponUsage[]>;
  getCouponUsagesByCustomerId(customerId: number): Promise<CouponUsage[]>;

  // Subscription methods
  getSubscription(id: number): Promise<Subscription | undefined>;
  getSubscriptionsByStoreId(storeId: number): Promise<Subscription[]>;
  getSubscriptionByStripeId(stripeSubscriptionId: string): Promise<Subscription | undefined>;
  getActiveSubscription(storeId: number): Promise<Subscription | undefined>;
  getAllSubscriptions(): Promise<Subscription[]>; // Para dashboard global
  createSubscription(subscription: InsertSubscription): Promise<Subscription>;
  updateSubscription(id: number, subscription: Partial<Subscription>): Promise<Subscription | undefined>;
  deleteSubscription(id: number): Promise<boolean>;
}



// Importar storage do Supabase
import { SupabaseStorage } from './storage.supabase';

// Usar SupabaseStorage para armazenamento com Supabase
export const storage = new SupabaseStorage();
