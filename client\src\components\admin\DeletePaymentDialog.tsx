import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { formatCurrency, formatDate } from '@/lib/utils';
import { useTranslation } from '@/hooks/useTranslation';
import { type OrderPayment } from '@/hooks/useOrderPayments';

interface DeletePaymentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  payment: OrderPayment | null;
  onConfirm: () => void;
  isDeleting?: boolean;
  currency?: string;
}

export default function DeletePaymentDialog({
  open,
  onOpenChange,
  payment,
  onConfirm,
  isDeleting = false,
  currency = 'R$'
}: DeletePaymentDialogProps) {
  const { t } = useTranslation();

  if (!payment) return null;

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            {t('payments.deletePayment') || 'Excluir Recebimento'}
          </AlertDialogTitle>
          <AlertDialogDescription className="space-y-3">
            <p>
              {t('payments.confirmDeleteMessage') || 'Tem certeza que deseja excluir este recebimento? Esta ação não pode ser desfeita.'}
            </p>
            
            {/* Detalhes do recebimento */}
            <div className="bg-gray-50 p-3 rounded-lg border">
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="font-medium text-gray-600">
                    {t('payments.amount') || 'Valor'}:
                  </span>
                  <span className="font-bold text-green-600">
                    {payment.valor < 0 ? '-' : '+'}{formatCurrency(Math.abs(payment.valor), currency)}
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span className="font-medium text-gray-600">
                    {t('payments.date') || 'Data'}:
                  </span>
                  <span>{formatDate(payment.data)}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="font-medium text-gray-600">
                    {t('payments.method') || 'Método'}:
                  </span>
                  <span>{payment.metodo}</span>
                </div>
                
                {payment.observacao && (
                  <div className="flex justify-between">
                    <span className="font-medium text-gray-600">
                      {t('payments.observation') || 'Observação'}:
                    </span>
                    <span className="italic text-gray-500">{payment.observacao}</span>
                  </div>
                )}
              </div>
            </div>

            <p className="text-sm text-amber-600 bg-amber-50 p-2 rounded border border-amber-200">
              <strong>{t('payments.warning') || 'Atenção'}:</strong> {t('payments.deleteWarning') || 'O status de pagamento do pedido será recalculado automaticamente após a exclusão.'}
            </p>
          </AlertDialogDescription>
        </AlertDialogHeader>
        
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>
            {t('common.cancel') || 'Cancelar'}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={onConfirm}
            disabled={isDeleting}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isDeleting ? (
              <div className="flex items-center">
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                {t('common.deleting') || 'Excluindo...'}
              </div>
            ) : (
              t('common.delete') || 'Excluir'
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
