import { z } from "zod";

// Schema para validação de categoria no frontend
export const categoryFormSchema = z.object({
  name: z.string().min(1, "Nome é obrigatório"),
  description: z.string().optional(),
  displayOrder: z.number().min(0, "Ordem deve ser um número positivo").default(0),
});

export type CategoryFormValues = z.infer<typeof categoryFormSchema>;

// Schema para inserção de categoria (compatível com o backend)
export const insertCategorySchema = z.object({
  name: z.string().min(1, "Nome é obrigatório"),
  description: z.string().optional(),
  displayOrder: z.number().min(0, "Ordem deve ser um número positivo").default(0),
});

export type InsertCategoryData = z.infer<typeof insertCategorySchema>;
