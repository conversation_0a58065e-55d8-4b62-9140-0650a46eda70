/**
 * Este arquivo contém as correções para o problema das variações de produtos.
 * 
 * Problema:
 * - <PERSON> esque<PERSON> define o campo como 'price'
 * - O banco de dados usa 'additional_price'
 * - O código deve usar o campo correto do banco de dados para funcionar
 */

import { supabaseAdmin } from './db';
import { VariationOption, InsertVariationOption } from '@shared/schema';

// Funções corrigidas para interagir com a tabela variation_options

export async function getVariationOptionsByVariationId(variationId: number): Promise<VariationOption[]> {
  try {
    const { data, error } = await supabaseAdmin
      .from('variation_options')
      .select('*')
      .eq('variation_id', variationId);
      
    if (error) {
      console.error('Error fetching variation options:', error);
      return [];
    }
    
    return data.map(option => ({
      id: option.id,
      variationId: option.variation_id,
      name: option.name,
      price: option.additional_price
    }));
  } catch (error) {
    console.error('Unexpected error in getVariationOptionsByVariationId:', error);
    return [];
  }
}

export async function createVariationOption(insertOption: InsertVariationOption): Promise<VariationOption> {
  try {
    console.log('Creating variation option with data:', insertOption);
    
    const { data, error } = await supabaseAdmin
      .from('variation_options')
      .insert([{
        variation_id: insertOption.variationId,
        name: insertOption.name,
        additional_price: insertOption.price || 0
      }])
      .select()
      .single();
      
    if (error) {
      console.error('Error creating variation option:', error);
      throw new Error(`Failed to create variation option: ${error.message}`);
    }
    
    console.log('Variation option created successfully:', data);
    
    return {
      id: data.id,
      variationId: data.variation_id,
      name: data.name,
      price: data.additional_price
    };
  } catch (error) {
    console.error('Unexpected error in createVariationOption:', error);
    throw error;
  }
}

export async function updateVariationOption(id: number, optionUpdate: Partial<VariationOption>): Promise<VariationOption | undefined> {
  try {
    console.log('Updating variation option with id:', id, 'Data:', optionUpdate);
    
    const updateData: any = { ...optionUpdate };
    
    // Mapeie campos específicos de camelCase para snake_case
    if (optionUpdate.variationId !== undefined) {
      updateData.variation_id = optionUpdate.variationId;
      delete updateData.variationId;
    }
    
    if (optionUpdate.price !== undefined) {
      updateData.additional_price = optionUpdate.price;
      delete updateData.price;
    }
    
    const { data, error } = await supabaseAdmin
      .from('variation_options')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();
      
    if (error) {
      console.error('Error updating variation option:', error);
      return undefined;
    }
    
    console.log('Variation option updated successfully:', data);
    
    return {
      id: data.id,
      variationId: data.variation_id,
      name: data.name,
      price: data.additional_price
    };
  } catch (error) {
    console.error('Unexpected error in updateVariationOption:', error);
    return undefined;
  }
}

export async function deleteVariationOption(id: number): Promise<boolean> {
  try {
    const { error } = await supabaseAdmin
      .from('variation_options')
      .delete()
      .eq('id', id);
      
    if (error) {
      console.error('Error deleting variation option:', error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Unexpected error in deleteVariationOption:', error);
    return false;
  }
}