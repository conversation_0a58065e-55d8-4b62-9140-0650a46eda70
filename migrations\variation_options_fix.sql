-- Script para corrigir inconsistências no esquema das variações de produtos

-- 1. Verificar a estrutura da tabela
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'variation_options';

-- 2. Criar uma função SQL que será chamada quando estiver salvando uma opção de variação
CREATE OR REPLACE FUNCTION process_variation_option()
RETURNS TRIGGER AS $$
BEGIN
  -- Para debugging
  RAISE NOTICE 'Processando variação: %', NEW;
  
  -- Se o campo price estiver presente (vindo do cliente), copie para additional_price
  IF NEW.price IS NOT NULL THEN
    NEW.additional_price := NEW.price;
    NEW.price := NULL;  -- Limpar o campo price que não existe realmente na tabela
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 3. Criar trigger que é executado antes de INSERT ou UPDATE
DROP TRIGGER IF EXISTS variation_option_trigger ON variation_options;
CREATE TRIGGER variation_option_trigger
  BEFORE INSERT OR UPDATE ON variation_options
  FOR EACH ROW
  EXECUTE FUNCTION process_variation_option();

-- 4. Comentário com instruções
/*
Este script resolve o problema de inconsistência entre o código e o banco de dados.
No código TypeScript, estamos usando 'price', mas no banco de dados o campo é 'additional_price'.

O trigger criado garantirá que, se o código enviar um campo 'price', ele será automaticamente
movido para 'additional_price' antes de ser salvo no banco de dados.
*/