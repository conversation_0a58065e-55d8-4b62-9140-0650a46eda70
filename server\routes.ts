import type { Express, Request, Response, NextFunction } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { db, pool, supabase } from "./db";
// Substituindo o Replit Auth com nosso próprio middleware para Firebase
// import { setupAuth, isAuthenticated } from "./replitAuth";
import multer from "multer";
import { v4 as uuidv4 } from "uuid";
import path from "path";
import { mkdir, writeFile } from "fs/promises";
import { uploadToBucket, deleteFromBucket } from "./storage-config";
import { createVariationOption, updateVariationOption } from "./variationOptionsFix";
import storageRouter from "./storage-api";
import { updateRevisionDiscount } from "./routes/discount";
import { recalculateAndUpdateRevision } from "./utils/revision-calculations";
import { recalculateRevisionValues, fixAllRevisionCalculations } from "./utils/fix-revision-calculations";
import { fixPercentageDiscountsEndpoint, fixAllRevisionsEndpoint } from "./routes/fix-discounts";
import {
  Customer,
  insertUserSchema,
  insertStoreSchema,
  insertProductSchema,
  insertCategorySchema,
  insertCustomerSchema,
  insertOrderSchema,
  insertOrderItemSchema,
  insertStoreVisitSchema,
  insertProductVariationSchema,
  insertVariationOptionSchema,
  insertOrderRevisionSchema,
  insertOrderRevisionItemSchema,
  insertCouponSchema,
  User,
  type InsertOrderRevision,
  type InsertOrderRevisionItem,
  type Coupon,
  type InsertCoupon
} from "@shared/schema";
import { z } from "zod";
import type { DashboardData } from "@shared/dashboard-types";

// Configuração do Multer para salvar uploads na memória
const multerStorage = multer.memoryStorage();
const upload = multer({
  storage: multerStorage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB max file size
  },
  fileFilter: (_req, file, cb) => {
    // Permitir apenas imagens
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Apenas imagens são permitidas.') as any);
    }
  }
});

// Função para fazer upload de arquivo para o sistema de arquivos local
// Em produção, devemos fazer upload para um serviço de armazenamento como AWS S3 ou similar

// ✅ FUNÇÃO UNIFICADA PARA CÁLCULO CORRETO DE REVISÕES
export async function updateRevisionTotalsUnified(revisionId: number, items: any[], revision: any): Promise<boolean> {
  try {
    console.log('🔧 MÉTODO UNIFICADO - Recalculando totais da revisão:', revisionId);

    // 1. Recalcular subtotal baseado nos itens atuais do banco
    let calculatedSubtotal = 0;

    // Buscar itens atualizados diretamente do banco para garantir valores corretos
    const { data: currentItems, error: itemsError } = await supabase
      .from('order_revision_items')
      .select('*')
      .eq('revision_id', revisionId);

    if (itemsError) {
      console.error('❌ Erro ao buscar itens da revisão:', itemsError);
      return false;
    }

    console.log('📦 Itens encontrados no banco:', currentItems?.length || 0);

    // Calcular subtotal correto item por item
    for (const item of currentItems || []) {
      const quantity = item.quantity || 1;
      const unitPrice = item.unit_price || 0;
      let variationsTotal = 0;

      // Calcular preço das variações
      if (item.selected_variations && Array.isArray(item.selected_variations)) {
        variationsTotal = item.selected_variations.reduce((sum: number, variation: any) => {
          const variationPrice = parseFloat(variation.price) || 0;
          const variationQuantity = parseInt(variation.quantity) || 1;

          // Para variações personalizadas (tipo "outros"), não multiplicar pela quantidade do produto
          if (variation.isCustom || variation.is_custom || variation.variationName?.toLowerCase() === 'outros') {
            return sum + (variationPrice * variationQuantity);
          } else {
            // Para variações normais, multiplicar pela quantidade do produto
            return sum + (variationPrice * variationQuantity * quantity);
          }
        }, 0);
      }

      const itemTotal = (unitPrice * quantity) + variationsTotal;
      calculatedSubtotal += itemTotal;

      console.log(`📊 Item ${item.id} (${item.product_name}):`, {
        quantity,
        unitPrice,
        variationsTotal,
        itemTotal,
        runningSubtotal: calculatedSubtotal
      });

      // Atualizar o subtotal do item se estiver incorreto
      const currentItemSubtotal = item.subtotal || 0;
      if (Math.abs(currentItemSubtotal - itemTotal) > 0.01) {
        console.log(`🔄 Corrigindo subtotal do item ${item.id}: ${currentItemSubtotal} → ${itemTotal}`);
        await supabase
          .from('order_revision_items')
          .update({ subtotal: itemTotal })
          .eq('id', item.id);
      }
    }

    console.log('📊 Subtotal calculado a partir dos itens do banco:', calculatedSubtotal);

    // 2. Recalcular desconto se for percentual
    const deliveryFee = revision.deliveryFee || revision.delivery_fee || 0;
    let discount = revision.discount || 0;
    const discountType = revision.discountType || revision.discount_type || 'fixed';
    const originalPercentage = revision.originalPercentage || revision.original_percentage || 0;

    if (discountType === 'percentage' && originalPercentage > 0) {
      discount = (originalPercentage / 100) * calculatedSubtotal;
      discount = parseFloat(discount.toFixed(2));
      console.log(`💰 Desconto recalculado: ${originalPercentage}% de ${calculatedSubtotal} = ${discount}`);
    }

    // 3. Calcular total final
    const total = Math.max(0, calculatedSubtotal - discount + deliveryFee);
    console.log(`🧮 Total final: ${calculatedSubtotal} - ${discount} + ${deliveryFee} = ${total}`);

    // 4. Verificar se os valores atuais são diferentes dos calculados
    const currentSubtotal = revision.subtotal || 0;
    const currentDiscount = revision.discount || 0;
    const currentTotal = revision.total || 0;

    const subtotalDiff = Math.abs(currentSubtotal - calculatedSubtotal);
    const discountDiff = Math.abs(currentDiscount - discount);
    const totalDiff = Math.abs(currentTotal - total);

    console.log('📊 Comparação de valores:', {
      atual: { subtotal: currentSubtotal, discount: currentDiscount, total: currentTotal },
      calculado: { subtotal: calculatedSubtotal, discount, total },
      diferencas: { subtotal: subtotalDiff, discount: discountDiff, total: totalDiff }
    });

    // 5. Sempre atualizar se houver diferenças significativas (> 0.01)
    if (subtotalDiff > 0.01 || discountDiff > 0.01 || totalDiff > 0.01) {
      // Preparar dados para atualização
      const updateData: any = {
        subtotal: calculatedSubtotal,
        discount: discount,
        total: total
      };

      // Adicionar campos específicos para desconto percentual
      if (discountType === 'percentage' && originalPercentage > 0) {
        updateData.discount_type = 'percentage';
        updateData.original_percentage = originalPercentage;
      }

      console.log('💾 Atualizando banco com dados corrigidos:', updateData);

      // Atualizar no banco usando Supabase
      const { data: result, error } = await supabase
        .from('order_revisions')
        .update(updateData)
        .eq('id', revisionId)
        .select('*')
        .single();

      if (error) {
        console.error('❌ Erro ao atualizar revisão no Supabase:', error);

        // Tentar método alternativo usando pool direto
        try {
          console.log('🔄 Tentando atualização direta via pool...');
          const query = `
            UPDATE order_revisions
            SET subtotal = $1, discount = $2, total = $3, discount_type = $4, original_percentage = $5
            WHERE id = $6
            RETURNING *
          `;
          const params = [
            calculatedSubtotal,
            discount,
            total,
            discountType,
            originalPercentage > 0 ? originalPercentage : null,
            revisionId
          ];

          const directResult = await pool.query(query, params);

          if (directResult.rows.length > 0) {
            console.log('✅ Atualização direta bem-sucedida:', directResult.rows[0]);
            return true;
          } else {
            console.error('❌ Nenhuma linha afetada na atualização direta');
            return false;
          }
        } catch (directError) {
          console.error('❌ Erro na atualização direta:', directError);
          return false;
        }
      } else {
        console.log('✅ Revisão atualizada com sucesso via Supabase:', result);

        // Verificar se os valores foram realmente gravados
        const { data: verification, error: verifyError } = await supabase
          .from('order_revisions')
          .select('subtotal, discount, total, discount_type, original_percentage')
          .eq('id', revisionId)
          .single();

        if (!verifyError && verification) {
          console.log('🔍 Verificação pós-atualização:', verification);

          const verifySubtotalDiff = Math.abs(verification.subtotal - calculatedSubtotal);
          const verifyDiscountDiff = Math.abs(verification.discount - discount);
          const verifyTotalDiff = Math.abs(verification.total - total);

          if (verifySubtotalDiff > 0.01 || verifyDiscountDiff > 0.01 || verifyTotalDiff > 0.01) {
            console.error('❌ ERRO: Valores não foram persistidos corretamente!', {
              esperado: { subtotal: calculatedSubtotal, discount, total },
              gravado: { subtotal: verification.subtotal, discount: verification.discount, total: verification.total },
              diferencas: { subtotal: verifySubtotalDiff, discount: verifyDiscountDiff, total: verifyTotalDiff }
            });
            return false;
          } else {
            console.log('✅ Valores confirmados no banco de dados');
            return true;
          }
        } else {
          console.warn('⚠️ Não foi possível verificar se os valores foram gravados corretamente');
          return true; // Assumir sucesso se não conseguiu verificar
        }
      }
    } else {
      console.log('✅ Valores já estão corretos, nenhuma atualização necessária');
      return true;
    }

  } catch (error) {
    console.error('❌ Erro no método unificado de cálculo:', error);
    return false;
  }
}

async function uploadFile(file: Express.Multer.File, storeId: number, fileType: string = 'logo'): Promise<string> {
  try {
    console.log('Iniciando upload de arquivo');
    const fileExt = file.originalname.split('.').pop()?.toLowerCase() || 'jpg'; // Pegar a extensão do arquivo
    const fileName = `${storeId}_${uuidv4()}.${fileExt}`;

    // Determinar pasta correta baseado no tipo de arquivo e uso
    // Usando estrutura organizada para o Supabase Storage
    let destinationFolder = 'logos';
    if (file.originalname.includes('product') || file.fieldname.includes('product')) {
      destinationFolder = 'products';
    } else if (file.originalname.includes('category') || file.fieldname.includes('category')) {
      destinationFolder = 'categories';
    } else if (fileType === 'headerImage') {
      destinationFolder = 'logos'; // Headers também vão para a pasta logos
    }

    const destinationPath = `${destinationFolder}/${fileName}`;

    console.log(`Fazendo upload do arquivo: ${destinationPath}, Tamanho: ${file.size} bytes, Tipo: ${file.mimetype}`);

    // Fazer upload para o Supabase Storage, Firebase Storage ou armazenamento local conforme configurado
    const publicUrl = await uploadToBucket(file.buffer, file.mimetype, destinationPath);

    console.log('Upload concluído com sucesso. URL pública:', publicUrl);
    return publicUrl;
  } catch (error) {
    console.error('Erro no uploadFile:', error);
    throw error;
  }
}

import { isAuthenticated, syncUserData, getCurrentUser } from "./firebaseAuth";
import { registerSubscriptionRoutes } from "./routes/subscriptions";
import { checkProductLimit, requirePdfGeneration, requireCoupons } from "./subscription-middleware";
import { globalAdminRouter } from "./routes/global-admin";

export async function registerRoutes(app: Express): Promise<Server> {
  // Auth middleware - agora usamos o isAuthenticated do Firebase Auth
  const requireAuth = isAuthenticated;

  // Firebase Auth routes
  app.post("/api/auth/sync-user", syncUserData);
  app.get("/api/auth/user", requireAuth, getCurrentUser);

  // Rotas de armazenamento de objetos/imagens
  app.use('/api/storage', storageRouter);

  // Store routes
  app.post("/api/stores", requireAuth, async (req: any, res) => {
    try {
      const firebaseUid = req.user.uid;
      console.log('Attempting to create store for Firebase UID:', firebaseUid);

      // Primeiro precisamos obter o ID numérico do usuário pelo Firebase UID
      const user = await storage.getUserByFirebaseUid(firebaseUid);
      if (!user) {
        console.log('User not found for Firebase UID:', firebaseUid);
        return res.status(404).json({ message: "User not found" });
      }

      const userId = user.id;
      console.log(`User found with ID: ${userId} for Firebase UID: ${firebaseUid}`);

      // Check if user already has a store
      const existingStore = await storage.getStoreByUserId(userId);
      if (existingStore) {
        console.log(`User with ID ${userId} already has a store with ID ${existingStore.id}`);
        return res.status(400).json({ message: "User already has a store" });
      }

      // Validate store data
      const storeData = {
        ...req.body,
        userId: userId,
        colors: req.body.colors || {
          primary: "#FF5722",
          secondary: "#2196F3",
          accent: "#FFEB3B"
        },
        paymentMethods: req.body.paymentMethods || {
          cash: true,
          creditCard: true,
          debitCard: true,
          pix: true,
          bankTransfer: false,
          customMethods: []
        }
      };

      // Check if slug is already taken
      const existingSlugStore = await storage.getStoreBySlug(storeData.slug);
      if (existingSlugStore) {
        return res.status(400).json({ message: "Store URL slug already in use" });
      }

      // Create store
      const store = await storage.createStore(storeData);

      return res.status(201).json(store);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid data", errors: error.errors });
      }
      return res.status(500).json({ message: "Failed to create store" });
    }
  });

  app.get("/api/stores/me", requireAuth, async (req: any, res) => {
    try {
      const firebaseUid = req.user.uid;
      console.log('Looking for store with Firebase UID:', firebaseUid);

      const store = await storage.getStoreByFirebaseUid(firebaseUid);

      if (!store) {
        // If no store found, return a 404
        return res.status(404).json({ message: "Store not found" });
      }

      // Transform snake_case to camelCase for consistency
      const transformedStore = { ...store };

      // Check if payment_methods exists (snake_case from Supabase)
      if (store.payment_methods) {
        // Convert to camelCase
        transformedStore.paymentMethods = {
          ...store.payment_methods,
          // Ensure customMethods exists
          customMethods: store.payment_methods.customMethods || []
        };
        // Remove the snake_case version
        delete transformedStore.payment_methods;
      } else if (store.paymentMethods) {
        // If already in camelCase, ensure customMethods exists
        if (!store.paymentMethods.customMethods) {
          transformedStore.paymentMethods.customMethods = [];
        }
      } else {
        // If neither exists, initialize with empty object
        transformedStore.paymentMethods = {
          cash: true,
          creditCard: false,
          debitCard: false,
          pix: false,
          bankTransfer: false,
          customMethods: []
        };
      }

      console.log('Sending transformed store data:', transformedStore);

      return res.status(200).json(transformedStore);
    } catch (error) {
      console.error('Error getting store:', error);
      return res.status(500).json({ message: "Failed to get store information" });
    }
  });

  app.put("/api/stores/me", requireAuth, async (req: any, res) => {
    try {
      const firebaseUid = req.user.uid;
      console.log('Attempting to update store for Firebase UID:', firebaseUid);

      // Obter a loja atual do usuário
      let store = await storage.getStoreByFirebaseUid(firebaseUid);

      // Validar os dados de atualização
      const updateData = { ...req.body };
      delete updateData.userId; // Evitar mudanças de propriedade

      console.log('Update data (full):', JSON.stringify(updateData, null, 2));
      console.log('Address fields in update data:', {
        addressStreet: updateData.addressStreet,
        addressNumber: updateData.addressNumber,
        addressComplement: updateData.addressComplement,
        addressNeighborhood: updateData.addressNeighborhood,
        addressCity: updateData.addressCity,
        addressState: updateData.addressState,
        contactEmail: updateData.contactEmail
      });

      // Se a loja não existir, precisamos criá-la primeiro
      if (!store) {
        console.log('Store not found, creating a new store for Firebase UID:', firebaseUid);

        try {
          // Obter o usuário pelo Firebase UID para pegar o ID numérico
          const user = await storage.getUserByFirebaseUid(firebaseUid);
          if (!user) {
            return res.status(404).json({ message: "User not found" });
          }

          const userId = user.id;
          console.log(`User found with ID: ${userId} for Firebase UID: ${firebaseUid}`);

          // Criar uma nova loja para o usuário
          store = await storage.createStore({
            name: updateData.name || "Minha Loja",
            slug: updateData.slug || `loja-${userId}`,
            description: updateData.description || "Minha loja no Doce Menu",
            userId: userId,
            logo: updateData.logo || null,
            colors: updateData.colors || {
              primary: "#FF5722",
              secondary: "#2196F3",
              accent: "#FFEB3B"
            },
            paymentMethods: updateData.paymentMethods || {
              cash: true,
              creditCard: true,
              debitCard: true,
              pix: true,
              bankTransfer: false
            }
          });

          console.log('Store created successfully with ID:', store.id);
          return res.status(201).json(store);
        } catch (createError) {
          console.error('Error creating store:', createError);
          return res.status(500).json({ message: "Failed to create store" });
        }
      }

      console.log('Updating existing store with ID:', store.id);

      // If slug is changing, check if it's already taken
      if (updateData.slug && updateData.slug !== store.slug) {
        const existingSlugStore = await storage.getStoreBySlug(updateData.slug);
        if (existingSlugStore && existingSlugStore.id !== store.id) {
          return res.status(400).json({ message: "Store URL slug already in use" });
        }
      }

      // Verificar e converter propriedades snake_case para camelCase
      if (store.payment_methods && updateData.paymentMethods) {
        // Preservar os métodos de pagamento personalizados existentes se não forem fornecidos
        if (!updateData.paymentMethods.customMethods && store.payment_methods.customMethods) {
          updateData.paymentMethods.customMethods = store.payment_methods.customMethods;
        } else {
          // Garantir que customMethods existe
          updateData.paymentMethods.customMethods = updateData.paymentMethods.customMethods || [];
        }
      } else if (updateData.paymentMethods) {
        // Garantir que customMethods existe
        updateData.paymentMethods.customMethods = updateData.paymentMethods.customMethods || [];
      }

      console.log('Sending update data:', updateData);

      // Atualizar a loja existente
      const updatedStore = await storage.updateStore(store.id, updateData);

      if (!updatedStore) {
        console.error('Failed to update store, unknown error');
        return res.status(500).json({ message: "Failed to update store" });
      }

      console.log('Store updated successfully:', updatedStore.id);
      return res.status(200).json(updatedStore);
    } catch (error) {
      console.error('Error in PUT /api/stores/me:', error);
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid data", errors: error.errors });
      }
      return res.status(500).json({ message: "Failed to update store" });
    }
  });

  // Rota para upload da logo
  app.post("/api/stores/me/logo", requireAuth, upload.single('logo'), async (req: any, res) => {
    try {
      // Usar req.user.uid do Firebase
      const firebaseUid = req.user.uid;
      console.log('Attempting to upload logo for Firebase UID:', firebaseUid);

      // Find the store for this user
      const store = await storage.getStoreByFirebaseUid(firebaseUid);

      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      // Verificar se foi feito upload de um arquivo
      if (!req.file) {
        return res.status(400).json({ message: "No file uploaded" });
      }

      // Fazer upload do arquivo para armazenamento local
      const logoUrl = await uploadFile(req.file, store.id, 'logo');

      // Atualizar a store com a URL da logo
      const updatedStore = await storage.updateStore(store.id, {
        logo: logoUrl
      });

      if (!updatedStore) {
        return res.status(500).json({ message: "Failed to update store with logo URL" });
      }

      console.log('Store logo updated successfully:', store.id);
      return res.status(200).json({
        message: "Logo uploaded successfully",
        logo: logoUrl,
        store: updatedStore
      });
    } catch (error) {
      console.error('Error uploading logo:', error);
      return res.status(500).json({
        message: "Failed to upload logo",
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // Rota para upload de imagem de cabeçalho da loja
  app.post("/api/stores/me/header-image", requireAuth, upload.single('headerImage'), async (req: any, res) => {
    try {
      // Usar req.user.uid do Firebase
      const firebaseUid = req.user.uid;
      console.log('Attempting to upload header image for Firebase UID:', firebaseUid);

      // Find the store for this user
      const store = await storage.getStoreByFirebaseUid(firebaseUid);

      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      // Verificar se foi feito upload de um arquivo
      if (!req.file) {
        return res.status(400).json({ message: "No file uploaded" });
      }

      // Fazer upload do arquivo para armazenamento local
      const headerImageUrl = await uploadFile(req.file, store.id, 'headerImage');

      // Atualizar a store com a URL da imagem de cabeçalho
      const updatedStore = await storage.updateStore(store.id, {
        headerImage: headerImageUrl
      });

      if (!updatedStore) {
        return res.status(500).json({ message: "Failed to update store with header image URL" });
      }

      console.log('Store header image updated successfully:', store.id);
      return res.status(200).json({
        message: "Header image uploaded successfully",
        headerImage: headerImageUrl,
        store: updatedStore
      });
    } catch (error) {
      console.error('Error uploading header image:', error);
      return res.status(500).json({
        message: "Failed to upload header image",
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // Rota para upload de imagem de produto
  app.post("/api/uploads/product-image/:storeId", requireAuth, upload.single('file'), async (req: any, res) => {
    try {
      const firebaseUid = req.user.uid;
      const storeId = parseInt(req.params.storeId);

      if (isNaN(storeId)) {
        return res.status(400).json({ message: "Invalid store ID" });
      }

      // Verificar se a loja pertence ao usuário
      const user = await storage.getUserByFirebaseUid(firebaseUid);
      if (!user) {
        console.log('User not found for Firebase UID:', firebaseUid);
        return res.status(404).json({ message: "User not found" });
      }

      const store = await storage.getStore(storeId);
      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      // Verificar se a loja pertence ao usuário pelo userId numérico
      if (store.userId !== user.id) {
        return res.status(403).json({ message: "Unauthorized" });
      }

      // Verificar se foi feito upload de um arquivo
      if (!req.file) {
        return res.status(400).json({ message: "No file uploaded" });
      }

      // Fazer upload do arquivo para armazenamento local
      const imageUrl = await uploadFile(req.file, store.id);

      console.log('Product image uploaded successfully for store:', store.id);
      return res.status(200).json({
        message: "Image uploaded successfully",
        url: imageUrl
      });
    } catch (error) {
      console.error('Error uploading product image:', error);
      return res.status(500).json({
        message: "Failed to upload product image",
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // Cache para limitar o registro de visitas por IP
  const visitCache = new Map<string, number>();
  const VISIT_CACHE_TTL = 30 * 60 * 1000; // 30 minutos em milissegundos

  // GET /api/public/stores/:slug/coupons/validate - Validar cupom
  app.get("/api/public/stores/:slug/coupons/validate", async (req, res) => {
    try {
      const { slug } = req.params;
      const { code, subtotal } = req.query;

      console.log(`Validando cupom: código=${code}, subtotal=${subtotal}, slug=${slug}`);

      if (!code) {
        console.log('Erro: Código do cupom não informado');
        return res.status(400).json({ message: "Código do cupom não informado" });
      }

      // Obter a loja pelo slug
      console.log(`Buscando loja pelo slug: ${slug}`);
      const store = await storage.getStoreBySlug(slug);
      if (!store) {
        console.log(`Erro: Loja não encontrada com slug ${slug}`);
        return res.status(404).json({ message: "Loja não encontrada" });
      }

      console.log(`Loja encontrada: ID=${store.id}, Nome=${store.name}`);

      // Buscar o cupom pelo código
      console.log(`Buscando cupom com código "${code}" para loja ID=${store.id}`);
      const coupon = await storage.getCouponByCode(store.id, code as string);

      // Verificar se o cupom existe
      if (!coupon) {
        console.log(`Erro: Cupom não encontrado com código "${code}" para loja ID=${store.id}`);
        return res.status(404).json({ message: "Cupom não encontrado" });
      }

      console.log(`Cupom encontrado:`, coupon);

      // Verificar se o cupom está ativo
      if (!coupon.ativo) {
        console.log(`Erro: Cupom não está ativo`);
        return res.status(400).json({ message: "Este cupom não está mais ativo" });
      }

      // Verificar se o cupom não expirou
      const now = new Date();
      const expirationDate = new Date(coupon.dataValidade);

      console.log(`Verificando validade: Data atual=${now}, Data de expiração=${expirationDate}`);

      if (expirationDate < now) {
        console.log(`Erro: Cupom expirado`);
        return res.status(400).json({ message: "Este cupom expirou" });
      }

      // Verificar valor mínimo de compra
      const subtotalValue = parseFloat(subtotal as string);
      console.log(`Verificando valor mínimo: Subtotal=${subtotalValue}, Mínimo requerido=${coupon.minimoCompra || 'não definido'}`);

      if (coupon.minimoCompra && subtotalValue < coupon.minimoCompra) {
        console.log(`Erro: Valor mínimo de compra não atingido`);
        return res.status(400).json({
          message: `Este cupom requer um valor mínimo de compra de ${coupon.minimoCompra}`,
          minimoPurchaseRequired: coupon.minimoCompra
        });
      }

      // Retornar o cupom validado
      console.log(`Cupom validado com sucesso, retornando dados`);
      return res.status(200).json(coupon);
    } catch (error) {
      console.error("Erro ao validar cupom:", error);
      return res.status(500).json({ message: "Erro ao validar cupom" });
    }
  });

  app.get("/api/public/stores/:slug", async (req, res) => {
    try {
      const { slug } = req.params;
      const visitorIp = req.ip || 'unknown';
      const cacheKey = `${slug}:${visitorIp}`;
      const now = Date.now();

      // Verificar se já registramos uma visita recente deste IP para esta loja
      const lastVisit = visitCache.get(cacheKey);
      const shouldRecordVisit = !lastVisit || (now - lastVisit > VISIT_CACHE_TTL);

      console.log(`Fetching store by slug: ${slug} (${shouldRecordVisit ? 'will record visit' : 'visit already recorded'})`);

      const store = await storage.getStoreBySlug(slug);

      if (!store) {
        console.log('Store not found with slug:', slug);
        return res.status(404).json({ message: "Store not found" });
      }

      // Registrar visita apenas se necessário
      if (shouldRecordVisit) {
        try {
          // Record store visit - usando try/catch para evitar que falhas nesta operação
          // interrompam o fluxo principal de retornar os dados da loja
          await storage.recordStoreVisit({
            storeId: store.id,
            visitorIp
          });

          // Atualizar o cache de visitas
          visitCache.set(cacheKey, now);

          console.log('Successfully recorded store visit for store ID:', store.id);
        } catch (visitError) {
          // Log o erro mas continue com a resposta da API
          console.error('Failed to record store visit, but continuing with store data response:', visitError);
        }
      }

      // Verificar e garantir que os objetos aninhados existam
      // Se qualquer um desses campos estiver faltando no banco de dados, vamos obtê-los novamente
      // de maneira explícita
      if (!store.paymentMethods) {
        console.log('WARNING: paymentMethods está faltando, consultando diretamente no banco');
        // Tentar obter os dados completos da loja
        const dbConnection = pool;
        const storeWithPaymentMethods = await dbConnection.query(
          'SELECT payment_methods FROM stores WHERE id = $1',
          [store.id]
        );

        if (storeWithPaymentMethods.rows[0]?.payment_methods) {
          store.paymentMethods = storeWithPaymentMethods.rows[0].payment_methods;
          console.log('paymentMethods recuperado do banco:', store.paymentMethods);
        } else {
          console.log('Não foi possível recuperar paymentMethods do banco');
        }
      }

      if (!store.deliverySettings) {
        console.log('WARNING: deliverySettings está faltando, consultando diretamente no banco');
        // Tentar obter os dados completos da loja
        const dbConnection = pool;
        const storeWithDeliverySettings = await dbConnection.query(
          'SELECT delivery_settings FROM stores WHERE id = $1',
          [store.id]
        );

        if (storeWithDeliverySettings.rows[0]?.delivery_settings) {
          store.deliverySettings = storeWithDeliverySettings.rows[0].delivery_settings;
          console.log('deliverySettings recuperado do banco:', store.deliverySettings);
        } else {
          console.log('Não foi possível recuperar deliverySettings do banco');
        }
      }

      // Retornar os dados necessários para exibição pública, incluindo TODOS os campos necessários
      // IMPORTANTE: Incluir paymentMethods e deliverySettings para o carrinho e checkout

      // Garantir que paymentMethods e deliverySettings sempre estejam presentes
      const defaultPaymentMethods = {
        cash: true,
        creditCard: false,
        debitCard: false,
        pix: false,
        bankTransfer: false,
        customMethods: []
      };

      const defaultDeliverySettings = {
        allowDelivery: false,
        allowPickup: true,
        pickupDays: [],
        pickupTimeSlots: [],
        deliveryDays: [],
        deliveryTimeSlots: [],
        deliveryFee: 0,
        minAdvanceDays: 0,
        customMessage: "",
        unavailablePeriods: []
      };

      const publicStoreData = {
        id: store.id,
        name: store.name,
        description: store.description,
        logo: store.logo,
        headerImage: store.headerImage,
        colors: store.colors,
        layout: store.layout,
        slug: store.slug,
        instagram: store.instagram,
        whatsapp: store.whatsapp,
        countryCode: store.countryCode,

        // Campos de endereço da loja
        addressStreet: store.addressStreet,
        addressNumber: store.addressNumber,
        addressComplement: store.addressComplement,
        addressNeighborhood: store.addressNeighborhood,
        addressCity: store.addressCity,
        addressState: store.addressState,
        contactEmail: store.contactEmail,

        // Usar os dados do banco com fallbacks para garantir que os campos sempre existam
        paymentMethods: store.paymentMethods || defaultPaymentMethods,
        deliverySettings: store.deliverySettings || defaultDeliverySettings,
        currency: store.currency || "$"
      };

      // Log simplificado para debug
      console.log(`Returning store data for ${slug} (ID: ${publicStoreData.id})`);
      return res.status(200).json(publicStoreData);
    } catch (error) {
      console.error('Error in /api/public/stores/:slug endpoint:', error);
      return res.status(500).json({ message: "Failed to get store information" });
    }
  });

  // Product routes

  // Product image upload route (usando Replit Storage Bucket)
  app.post("/api/products/upload-image", requireAuth, upload.single('image'), async (req: any, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ error: 'No file uploaded' });
      }

      const firebaseUid = req.user.uid;
      console.log('Uploading product image for Firebase UID:', firebaseUid);

      const store = await storage.getStoreByFirebaseUid(firebaseUid);

      if (!store) {
        console.log('Store not found for Firebase UID:', firebaseUid);
        return res.status(404).json({ message: "Store not found" });
      }

      const storeId = store.id;
      const file = req.file;

      try {
        // Preparar para upload usando estrutura organizada
        const fileExt = file.originalname.split('.').pop();
        const fileName = `products/${storeId}_${uuidv4()}.${fileExt}`;

        console.log(`Uploading product image to storage: ${fileName}, Size: ${file.size} bytes, Type: ${file.mimetype}`);

        // Upload para o Supabase Storage, Firebase Storage ou armazenamento local
        const publicUrl = await uploadToBucket(file.buffer, file.mimetype, fileName);

        console.log('Product image uploaded to storage, public URL:', publicUrl);

        res.json({ url: publicUrl });
      } catch (err) {
        console.error('Error uploading to storage:', err);
        return res.status(500).json({
          error: 'Failed to upload image to storage',
          details: err instanceof Error ? err.message : String(err)
        });
      }
    } catch (error) {
      console.error('Error in product image upload:', error);
      res.status(500).json({ error: error instanceof Error ? error.message : String(error) });
    }
  });

  // Rota temporária para executar a migração de campos de endereço da loja
  app.get("/api/admin/run-store-address-migration", requireAuth, async (req: any, res) => {
    try {
      const firebaseUid = req.user.uid;
      console.log('Attempting to run store address migration for Firebase UID:', firebaseUid);

      // Verificar se o usuário é administrador (simplificado para teste)
      const user = await storage.getUserByFirebaseUid(firebaseUid);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      // Executar a migração diretamente usando o cliente Supabase
      const migrationSQL = `
      DO $$
      BEGIN
          IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'stores') THEN
              -- Adicionar campos de endereço
              ALTER TABLE stores
              ADD COLUMN IF NOT EXISTS address_street VARCHAR(255),
              ADD COLUMN IF NOT EXISTS address_number VARCHAR(50),
              ADD COLUMN IF NOT EXISTS address_complement VARCHAR(255),
              ADD COLUMN IF NOT EXISTS address_neighborhood VARCHAR(255),
              ADD COLUMN IF NOT EXISTS address_city VARCHAR(255),
              ADD COLUMN IF NOT EXISTS address_state VARCHAR(100),
              ADD COLUMN IF NOT EXISTS contact_email VARCHAR(255);

              RAISE NOTICE 'Campos de endereço e email de contato adicionados à tabela stores';
          ELSE
              RAISE EXCEPTION 'A tabela stores não existe no banco de dados';
          END IF;
      END
      $$;
      `;

      // Executar o SQL usando o cliente Supabase
      const { error } = await supabase.rpc('pgaudit.exec_sql', { sql: migrationSQL });

      if (error) {
        console.error('Erro ao executar migração:', error);

        // Tentar uma abordagem alternativa se a primeira falhar
        console.log('Tentando abordagem alternativa para adicionar colunas...');

        // Executar comandos ALTER TABLE individuais
        const alterCommands = [
          "ALTER TABLE stores ADD COLUMN IF NOT EXISTS address_street VARCHAR(255)",
          "ALTER TABLE stores ADD COLUMN IF NOT EXISTS address_number VARCHAR(50)",
          "ALTER TABLE stores ADD COLUMN IF NOT EXISTS address_complement VARCHAR(255)",
          "ALTER TABLE stores ADD COLUMN IF NOT EXISTS address_neighborhood VARCHAR(255)",
          "ALTER TABLE stores ADD COLUMN IF NOT EXISTS address_city VARCHAR(255)",
          "ALTER TABLE stores ADD COLUMN IF NOT EXISTS address_state VARCHAR(100)",
          "ALTER TABLE stores ADD COLUMN IF NOT EXISTS contact_email VARCHAR(255)"
        ];

        let success = true;
        for (const cmd of alterCommands) {
          const { error: cmdError } = await supabase.rpc('pgaudit.exec_sql', { sql: cmd });
          if (cmdError) {
            console.error(`Erro ao executar comando: ${cmd}`, cmdError);
            success = false;
          }
        }

        if (!success) {
          return res.status(500).json({
            message: "Falha ao executar migração",
            error: "Erro ao adicionar colunas individualmente"
          });
        }
      }

      // Verificar se as colunas foram adicionadas
      const { data: columns, error: columnsError } = await supabase
        .rpc('pgaudit.exec_sql', {
          sql: `SELECT column_name, data_type
                FROM information_schema.columns
                WHERE table_name = 'stores'
                AND column_name IN ('address_street', 'address_number', 'address_complement',
                                   'address_neighborhood', 'address_city', 'address_state', 'contact_email')`
        });

      if (columnsError) {
        console.error('Erro ao verificar colunas:', columnsError);
        return res.status(500).json({
          message: "Falha ao verificar colunas adicionadas",
          error: columnsError
        });
      }

      return res.status(200).json({
        message: "Migração executada com sucesso",
        columns: columns
      });
    } catch (error) {
      console.error('Erro ao executar migração:', error);
      return res.status(500).json({
        message: "Falha ao executar migração",
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // Rota para verificar a estrutura da tabela stores
  app.get("/api/admin/check-store-table", requireAuth, async (req: any, res) => {
    try {
      const firebaseUid = req.user.uid;
      console.log('Checking store table structure for Firebase UID:', firebaseUid);

      // Verificar se o usuário existe
      const user = await storage.getUserByFirebaseUid(firebaseUid);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      // Consultar a estrutura da tabela stores
      const { data, error } = await supabase
        .from('stores')
        .select('*')
        .limit(1);

      if (error) {
        console.error('Erro ao consultar tabela stores:', error);
        return res.status(500).json({
          message: "Falha ao consultar tabela stores",
          error: error
        });
      }

      // Obter a lista de colunas da tabela stores
      const { data: columns, error: columnsError } = await supabase
        .rpc('pgaudit.exec_sql', {
          sql: `SELECT column_name, data_type
                FROM information_schema.columns
                WHERE table_name = 'stores'
                ORDER BY ordinal_position`
        });

      if (columnsError) {
        console.error('Erro ao consultar colunas da tabela stores:', columnsError);
        return res.status(500).json({
          message: "Falha ao consultar colunas da tabela stores",
          error: columnsError
        });
      }

      return res.status(200).json({
        message: "Estrutura da tabela stores consultada com sucesso",
        sample: data && data.length > 0 ? data[0] : null,
        columns: columns
      });
    } catch (error) {
      console.error('Erro ao consultar estrutura da tabela stores:', error);
      return res.status(500).json({
        message: "Falha ao consultar estrutura da tabela stores",
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // Rota para executar a migração usando SQL direto
  app.get("/api/admin/run-direct-migration", requireAuth, async (req: any, res) => {
    try {
      const firebaseUid = req.user.uid;
      console.log('Running direct migration for Firebase UID:', firebaseUid);

      // Verificar se o usuário existe
      const user = await storage.getUserByFirebaseUid(firebaseUid);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      // Usar o pool de conexão diretamente para executar SQL
      const dbConnection = pool;

      // Executar os comandos ALTER TABLE individualmente
      const alterCommands = [
        "ALTER TABLE stores ADD COLUMN IF NOT EXISTS address_street VARCHAR(255)",
        "ALTER TABLE stores ADD COLUMN IF NOT EXISTS address_number VARCHAR(50)",
        "ALTER TABLE stores ADD COLUMN IF NOT EXISTS address_complement VARCHAR(255)",
        "ALTER TABLE stores ADD COLUMN IF NOT EXISTS address_neighborhood VARCHAR(255)",
        "ALTER TABLE stores ADD COLUMN IF NOT EXISTS address_city VARCHAR(255)",
        "ALTER TABLE stores ADD COLUMN IF NOT EXISTS address_state VARCHAR(100)",
        "ALTER TABLE stores ADD COLUMN IF NOT EXISTS contact_email VARCHAR(255)"
      ];

      const results = [];
      for (const cmd of alterCommands) {
        try {
          console.log(`Executando comando: ${cmd}`);
          const result = await dbConnection.query(cmd);
          results.push({ command: cmd, success: true, result });
        } catch (cmdError) {
          console.error(`Erro ao executar comando: ${cmd}`, cmdError);
          results.push({ command: cmd, success: false, error: cmdError.message });
        }
      }

      // Verificar se as colunas foram adicionadas
      const columnsQuery = `
        SELECT column_name, data_type
        FROM information_schema.columns
        WHERE table_name = 'stores'
        AND column_name IN ('address_street', 'address_number', 'address_complement',
                           'address_neighborhood', 'address_city', 'address_state', 'contact_email')
      `;

      const columnsResult = await dbConnection.query(columnsQuery);

      return res.status(200).json({
        message: "Migração direta executada",
        results: results,
        columns: columnsResult.rows
      });
    } catch (error) {
      console.error('Erro ao executar migração direta:', error);
      return res.status(500).json({
        message: "Falha ao executar migração direta",
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // Rota para deletar imagem de produto
  app.delete("/api/products/delete-image/:filePath(*)", requireAuth, async (req: any, res) => {
    try {
      const firebaseUid = req.user.uid;
      console.log('Deleting product image for Firebase UID:', firebaseUid);

      const store = await storage.getStoreByFirebaseUid(firebaseUid);

      if (!store) {
        console.log('Store not found for Firebase UID:', firebaseUid);
        return res.status(404).json({ message: "Store not found" });
      }

      const { filePath } = req.params;
      console.log('Attempting to delete image:', filePath);

      // Verificar se o arquivo pertence à loja
      // Este é um ponto importante para segurança - precisamos garantir que o usuário só possa deletar imagens da sua própria loja
      const storePrefix = `${store.id}_`;
      const fileName = filePath.split('/').pop() || '';

      if (!fileName.startsWith(storePrefix)) {
        console.log('Attempted to delete file from another store. FileName:', fileName, 'StoreId:', store.id);
        return res.status(403).json({ error: 'Not authorized to delete this file' });
      }

      try {
        // Se for um caminho do Replit Storage Bucket
        if (filePath.startsWith('/storage/')) {
          console.log('Deleting file from Replit Storage bucket:', filePath);

          const deleted = await deleteFromBucket(filePath);

          if (deleted) {
            console.log('File deleted successfully from bucket:', filePath);
            return res.status(200).json({ success: true });
          } else {
            console.log('Failed to delete file from bucket');
            return res.status(404).json({ error: 'File not found in storage' });
          }
        } else {
          // Caso seja qualquer outro tipo de caminho não suportado
          console.log('Unsupported file path format:', filePath);
          return res.status(400).json({ error: 'Unsupported file path format' });
        }
      } catch (err) {
        console.error('Error deleting file:', err);
        return res.status(500).json({ error: 'Failed to delete file' });
      }
    } catch (error) {
      console.error('Error in product image deletion:', error);
      res.status(500).json({ error: error instanceof Error ? error.message : String(error) });
    }
  });

  app.post("/api/products", requireAuth, checkProductLimit, async (req: any, res) => {
    try {
      const firebaseUid = req.user.uid;
      console.log('Creating product for Firebase UID:', firebaseUid);

      const store = await storage.getStoreByFirebaseUid(firebaseUid);

      if (!store) {
        console.log('Store not found for Firebase UID:', firebaseUid);
        return res.status(404).json({ message: "Store not found" });
      }

      console.log('Store found with ID:', store.id);

      // Prepare product data with default values
      // Garantir que os campos númericos são tratados corretamente
      const productData = {
        ...req.body,
        storeId: store.id,
        // Garantir que price é um número
        price: typeof req.body.price === 'string' ? Number(req.body.price) : req.body.price,
        // Garantir que inStock existe
        inStock: req.body.inStock !== undefined ? req.body.inStock : true
      };

      console.log('Product data before validation:', productData);

      // Validate product data
      const validatedData = insertProductSchema.parse(productData);

      console.log('Validated product data:', validatedData);

      // Create product
      const product = await storage.createProduct(validatedData);

      console.log('Product created successfully with ID:', product.id);

      // Verifica se o produto tem variações para serem salvas
      const variations = req.body.variations || [];

      if (variations.length > 0 && product.hasVariations) {
        console.log(`Processing ${variations.length} variations for product ${product.id}`);

        // Criar cada variação com suas opções
        for (const variation of variations) {
          try {
            // Criar a variação
            const newVariation = await storage.createProductVariation({
              productId: product.id,
              name: variation.name,
              description: variation.description || null,
              required: variation.required || false,
              multipleChoice: variation.multipleChoice || false,
              maxSelections: variation.maxSelections || 1,
              minSelections: variation.minSelections || 0
            });

            console.log(`Created variation ${newVariation.id} (${variation.name}) for product ${product.id}`);

            // Criar as opções para esta variação
            if (variation.options && Array.isArray(variation.options)) {
              for (const option of variation.options) {
                const newOption = await storage.createVariationOption({
                  variationId: newVariation.id,
                  name: option.name,
                  price: option.price || 0
                });

                console.log(`Created option ${newOption.id} (${option.name}) for variation ${newVariation.id}`);
              }
            }
          } catch (error) {
            console.error(`Error creating variation "${variation.name}" for product ${product.id}:`, error);
            // Continuar com a próxima variação mesmo se esta falhar
          }
        }
      }

      return res.status(201).json(product);
    } catch (error) {
      console.error('Error creating product:', error);
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid data", errors: error.errors });
      }
      return res.status(500).json({
        message: "Failed to create product",
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  app.get("/api/products", requireAuth, async (req: any, res) => {
    try {
      const firebaseUid = req.user.uid;
      console.log('Looking for products with Firebase UID:', firebaseUid);

      const store = await storage.getStoreByFirebaseUid(firebaseUid);

      if (!store) {
        console.log('Store not found for Firebase UID:', firebaseUid);
        return res.status(404).json({ message: "Store not found" });
      }

      console.log('Getting products for store:', store.id);
      const products = await storage.getProductsByStoreId(store.id);

      // Se não houver produtos, retorna uma lista vazia
      if (!products || products.length === 0) {
        console.log('No products found for store:', store.id);
        return res.status(200).json([]);
      }

      console.log(`Found ${products.length} products for store ${store.id}`);
      return res.status(200).json(products);
    } catch (error) {
      console.error('Error getting products:', error);
      return res.status(500).json({ message: "Failed to get products" });
    }
  });

  // Rota para obter um produto específico por ID
  app.get("/api/products/:id", requireAuth, async (req: any, res) => {
    try {
      const firebaseUid = req.user.uid;
      console.log('Fetching product by ID:', req.params.id, 'for Firebase UID:', firebaseUid);

      const store = await storage.getStoreByFirebaseUid(firebaseUid);

      if (!store) {
        console.log('Store not found for Firebase UID:', firebaseUid);
        return res.status(404).json({ message: "Store not found" });
      }

      const productId = parseInt(req.params.id);
      const product = await storage.getProduct(productId);

      if (!product) {
        console.log('Product not found with ID:', productId);
        return res.status(404).json({ message: "Product not found" });
      }

      // Verificar se o produto pertence à loja do usuário
      if (product.storeId !== store.id) {
        console.log('Product does not belong to user store. Product storeId:', product.storeId, 'User store ID:', store.id);
        return res.status(403).json({ message: "Not authorized to access this product" });
      }

      console.log('Product found and returning:', product);
      return res.status(200).json(product);
    } catch (error) {
      console.error('Error fetching product by ID:', error);
      return res.status(500).json({ message: "Failed to get product details" });
    }
  });

  app.get("/api/public/stores/:slug/products", async (req, res) => {
    try {
      const { slug } = req.params;
      const store = await storage.getStoreBySlug(slug);

      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      // Buscar produtos da loja
      const products = await storage.getProductsByStoreId(store.id);

      console.log(`Found ${products.length} products for public store ${store.slug}`);

      // Filtrar apenas os produtos visíveis para o endpoint público
      const visibleProducts = products.filter(product => product.visible !== false);

      console.log(`Filtered ${visibleProducts.length} visible products out of ${products.length} total`);

      // Logando apenas o primeiro produto para debug se houver algum
      if (visibleProducts.length > 0) {
        console.log('Sample product data structure:',
          JSON.stringify({
            id: visibleProducts[0].id,
            name: visibleProducts[0].name,
            hasVariations: visibleProducts[0].hasVariations,
            visible: visibleProducts[0].visible,
            variationsCount: visibleProducts[0].hasVariations && visibleProducts[0].variations ?
              (Array.isArray(visibleProducts[0].variations) ? visibleProducts[0].variations.length : 'not an array') :
              'no variations'
          })
        );
      }

      return res.status(200).json(visibleProducts);
    } catch (error) {
      console.error('Error fetching public products:', error);
      return res.status(500).json({ message: "Failed to get products for this store" });
    }
  });

  // Endpoint público para obter detalhes de um produto específico por ID
  app.get("/api/public/products/:id", async (req, res) => {
    try {
      const productId = parseInt(req.params.id);

      if (isNaN(productId)) {
        return res.status(400).json({ message: "Invalid product ID" });
      }

      console.log('Fetching product by ID for public access:', productId);

      const product = await storage.getProduct(productId);

      if (!product) {
        console.log('Product not found with ID:', productId);
        return res.status(404).json({ message: "Product not found" });
      }

      // Verificar se o produto está visível para acesso público
      if (product.visible === false) {
        console.log('Product is not visible, denying access. Product ID:', productId);
        return res.status(404).json({ message: "Product not found" });
      }

      // Se o produto existir e estiver visível, garantimos que as variações JSON sejam formatadas corretamente
      if (product.hasVariations && product.variations && Array.isArray(product.variations)) {
        // As variações já estão no campo JSON do produto, não precisamos buscar no banco
        console.log(`Produto ${product.id} já possui ${product.variations.length} variações no campo JSONB`);
      } else {
        // Se produto.variations não for um array, inicializamos como array vazio
        product.variations = [];
      }

      console.log('Returning product details for ID:', productId);
      return res.status(200).json(product);
    } catch (error) {
      console.error('Error fetching public product details:', error);
      return res.status(500).json({ message: "Failed to get product details" });
    }
  });

  // Implementando a rota PATCH para suportar o método usado pelo cliente
  app.patch("/api/products/:id", requireAuth, async (req: any, res) => {
    console.log('Received PATCH request for product. Payload:', JSON.stringify(req.body));

    // Importar a função de atualização de produto com preservação de variações
    const { updateProductWithVariations } = await import('./productPatch');

    // Usar a implementação modificada que preserva as variações no campo JSONB
    return updateProductWithVariations(req, res);
  });

  // Mantenha a rota PUT também para compatibilidade
  app.put("/api/products/:id", requireAuth, async (req: any, res) => {
    // Importar a função de atualização de produto com preservação de variações
    const { updateProductWithVariations } = await import('./productPatch');

    // Usar a implementação modificada que preserva as variações no campo JSONB
    return updateProductWithVariations(req, res);
  });

  app.delete("/api/products/:id", requireAuth, async (req: any, res) => {
    try {
      const firebaseUid = req.user.uid;
      const store = await storage.getStoreByFirebaseUid(firebaseUid);

      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      const productId = parseInt(req.params.id);
      const product = await storage.getProduct(productId);

      if (!product) {
        return res.status(404).json({ message: "Product not found" });
      }

      // Verify ownership
      if (product.storeId !== store.id) {
        return res.status(403).json({ message: "Not authorized to delete this product" });
      }

      // Delete product
      await storage.deleteProduct(productId);

      return res.status(200).json({ message: "Product deleted successfully" });
    } catch (error) {
      return res.status(500).json({ message: "Failed to delete product" });
    }
  });

  // Category routes
  app.post("/api/categories", requireAuth, async (req: any, res) => {
    try {
      const firebaseUid = req.user.uid;
      const store = await storage.getStoreByFirebaseUid(firebaseUid);

      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      // Validate category data
      const categoryData = insertCategorySchema.parse({
        ...req.body,
        storeId: store.id
      });

      // Create category
      const category = await storage.createCategory(categoryData);

      return res.status(201).json(category);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid data", errors: error.errors });
      }
      return res.status(500).json({ message: "Failed to create category" });
    }
  });

  app.get("/api/categories", requireAuth, async (req: any, res) => {
    try {
      const firebaseUid = req.user.uid;
      console.log('Getting categories for Firebase UID:', firebaseUid);

      const store = await storage.getStoreByFirebaseUid(firebaseUid);

      if (!store) {
        console.log('Store not found for Firebase UID:', firebaseUid);
        return res.status(404).json({ message: "Store not found" });
      }

      console.log('Getting categories for store ID:', store.id);

      try {
        const categories = await storage.getCategoriesByStoreId(store.id);

        console.log(`Found ${categories.length} categories for store ID ${store.id}`);

        // Ordene as categorias pelo campo displayOrder
        categories.sort((a, b) => a.displayOrder - b.displayOrder);

        return res.status(200).json(categories);
      } catch (err) {
        console.error('Error getting categories:', err);
        return res.status(500).json({
          message: "Failed to get categories",
          error: err instanceof Error ? err.message : String(err)
        });
      }
    } catch (error) {
      console.error('Error in categories endpoint:', error);
      return res.status(500).json({
        message: "Failed to get categories",
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // Rota para atualizar a ordem das categorias
  app.post("/api/categories/reorder", requireAuth, async (req: any, res) => {
    try {
      const firebaseUid = req.user.uid;
      const store = await storage.getStoreByFirebaseUid(firebaseUid);

      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      const { categories } = req.body;

      if (!Array.isArray(categories)) {
        return res.status(400).json({ message: "Invalid data format. Expected an array of categories." });
      }

      // Verificar a propriedade das categorias
      const storeCategories = await storage.getCategoriesByStoreId(store.id);
      const storeCategoryIds = storeCategories.map(cat => cat.id);

      // Verificar se todas as categorias pertencem à loja
      const allCategoriesBelongToStore = categories.every(cat =>
        storeCategoryIds.includes(Number(cat.id))
      );

      if (!allCategoriesBelongToStore) {
        return res.status(403).json({
          message: "Not authorized to update some of these categories"
        });
      }

      // Atualizar a ordem de cada categoria
      const updatePromises = categories.map((cat, index) =>
        storage.updateCategory(Number(cat.id), { displayOrder: index })
      );

      await Promise.all(updatePromises);

      // Obter as categorias atualizadas
      const updatedCategories = await storage.getCategoriesByStoreId(store.id);
      updatedCategories.sort((a, b) => a.displayOrder - b.displayOrder);

      return res.status(200).json(updatedCategories);
    } catch (error) {
      console.error('Erro ao reordenar categorias:', error);
      return res.status(500).json({
        message: "Failed to reorder categories",
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  app.get("/api/categories/:id", requireAuth, async (req: any, res) => {
    try {
      const firebaseUid = req.user.uid;
      const store = await storage.getStoreByFirebaseUid(firebaseUid);

      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      const categoryId = parseInt(req.params.id);
      if (isNaN(categoryId)) {
        return res.status(400).json({ message: "Invalid category ID" });
      }

      const category = await storage.getCategory(categoryId);
      if (!category) {
        return res.status(404).json({ message: "Category not found" });
      }

      // Verify that the category belongs to the store
      if (category.storeId !== store.id) {
        return res.status(403).json({ message: "Not authorized to access this category" });
      }

      return res.status(200).json(category);
    } catch (error) {
      return res.status(500).json({ message: "Failed to get category" });
    }
  });

  app.patch("/api/categories/:id", requireAuth, async (req: any, res) => {
    try {
      const firebaseUid = req.user.uid;
      const store = await storage.getStoreByFirebaseUid(firebaseUid);

      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      const categoryId = parseInt(req.params.id);
      if (isNaN(categoryId)) {
        return res.status(400).json({ message: "Invalid category ID" });
      }

      const category = await storage.getCategory(categoryId);
      if (!category) {
        return res.status(404).json({ message: "Category not found" });
      }

      // Verify that the category belongs to the store
      if (category.storeId !== store.id) {
        return res.status(403).json({ message: "Not authorized to update this category" });
      }

      // Extract only the fields we want to update
      const { name, description, logo, visible } = req.body;
      const updateData = {
        name: name || category.name,
        description: description !== undefined ? description : category.description,
        logo: logo !== undefined ? logo : category.logo,
        visible: visible !== undefined ? visible : category.visible
      };

      const updatedCategory = await storage.updateCategory(categoryId, updateData);
      return res.status(200).json(updatedCategory);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid data", errors: error.errors });
      }
      return res.status(500).json({ message: "Failed to update category" });
    }
  });

  app.delete("/api/categories/:id", requireAuth, async (req: any, res) => {
    try {
      const firebaseUid = req.user.uid;
      const store = await storage.getStoreByFirebaseUid(firebaseUid);

      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      const categoryId = parseInt(req.params.id);
      if (isNaN(categoryId)) {
        return res.status(400).json({ message: "Invalid category ID" });
      }

      const category = await storage.getCategory(categoryId);
      if (!category) {
        return res.status(404).json({ message: "Category not found" });
      }

      // Verify that the category belongs to the store
      if (category.storeId !== store.id) {
        return res.status(403).json({ message: "Not authorized to delete this category" });
      }

      // Check if there are products associated with this category
      const productsInCategory = await storage.getProductsByCategoryId(categoryId);
      if (productsInCategory.length > 0) {
        // Update products to remove category reference
        for (const product of productsInCategory) {
          await storage.updateProduct(product.id, { categoryId: null });
        }
      }

      // Delete the category
      const success = await storage.deleteCategory(categoryId);
      if (!success) {
        return res.status(500).json({ message: "Failed to delete category" });
      }

      return res.status(200).json({ message: "Category deleted successfully" });
    } catch (error) {
      return res.status(500).json({ message: "Failed to delete category" });
    }
  });

  // Category logo upload route
  app.post("/api/categories/upload-logo", requireAuth, upload.single('image'), async (req: any, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ error: 'No file uploaded' });
      }

      const firebaseUid = req.user.uid;
      console.log('Uploading category logo for Firebase UID:', firebaseUid);

      const store = await storage.getStoreByFirebaseUid(firebaseUid);

      if (!store) {
        console.log('Store not found for Firebase UID:', firebaseUid);
        return res.status(404).json({ message: "Store not found" });
      }

      const storeId = store.id;
      const file = req.file;

      try {
        // Preparar para upload usando estrutura organizada
        const fileExt = file.originalname.split('.').pop();
        const fileName = `categories/${storeId}_${uuidv4()}.${fileExt}`;

        console.log(`Uploading category logo to storage: ${fileName}, Size: ${file.size} bytes, Type: ${file.mimetype}`);

        // Upload para o Supabase Storage, Firebase Storage ou armazenamento local
        const publicUrl = await uploadToBucket(file.buffer, file.mimetype, fileName);

        console.log('Category logo uploaded to storage, public URL:', publicUrl);

        res.json({ url: publicUrl });
      } catch (err) {
        console.error('Error uploading to storage:', err);
        res.status(500).json({
          error: 'Failed to upload logo to storage',
          details: err instanceof Error ? err.message : String(err)
        });
      }
    } catch (error) {
      console.error('Error in category logo upload:', error);
      res.status(500).json({ error: error instanceof Error ? error.message : String(error) });
    }
  });

  app.get("/api/public/stores/:slug/categories", async (req, res) => {
    try {
      const { slug } = req.params;
      const store = await storage.getStoreBySlug(slug);

      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      const categories = await storage.getCategoriesByStoreId(store.id);

      // Filtrar apenas categorias visíveis para o endpoint público
      const visibleCategories = categories.filter(category => category.visible !== false);

      console.log(`Filtered ${visibleCategories.length} visible categories out of ${categories.length} total`);

      // Ordenar categorias pelo campo displayOrder para a visualização pública também
      visibleCategories.sort((a, b) => a.displayOrder - b.displayOrder);

      return res.status(200).json(visibleCategories);
    } catch (error) {
      console.error('Error fetching public categories:', error);
      return res.status(500).json({ message: "Failed to get categories" });
    }
  });

  // Order routes
  // Get single order by ID for public store
  app.get("/api/public/stores/:slug/orders/:id", async (req, res) => {
    try {
      const { slug, id } = req.params;
      const store = await storage.getStoreBySlug(slug);

      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      const orderId = parseInt(id);
      const order = await storage.getOrder(orderId);

      if (!order) {
        return res.status(404).json({ message: "Order not found" });
      }

      // Verify order belongs to this store
      if (order.storeId !== store.id) {
        return res.status(403).json({ message: "Not authorized to view this order" });
      }

      // Get order items
      const orderItems = await storage.getOrderItemsByOrderId(order.id);

      // Get customer info
      const customer = await storage.getCustomer(order.customerId);

      return res.status(200).json({
        ...order,
        discount: order.discount || 0,
        couponId: order.couponId || null,
        couponCode: order.couponCode || null,
        couponType: order.couponType || null,
        items: orderItems,
        customer: customer ? {
          id: customer.id,
          name: customer.name,
          email: customer.email,
          phone: customer.phone
        } : null
      });
    } catch (error) {
      console.error("Error getting order:", error);
      return res.status(500).json({ message: "Failed to get order details" });
    }
  });

  // Update order status
  app.patch("/api/public/stores/:slug/orders/:id/status", async (req, res) => {
    try {
      const { slug, id } = req.params;
      const { status } = req.body;

      if (!status) {
        return res.status(400).json({ message: "Status is required" });
      }

      const store = await storage.getStoreBySlug(slug);

      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      const orderId = parseInt(id);
      const order = await storage.getOrder(orderId);

      if (!order) {
        return res.status(404).json({ message: "Order not found" });
      }

      // Verify order belongs to this store
      if (order.storeId !== store.id) {
        return res.status(403).json({ message: "Not authorized to update this order" });
      }

      // Update order status
      const updatedOrder = await storage.updateOrderStatus(orderId, status);

      if (!updatedOrder) {
        return res.status(500).json({ message: "Failed to update order status" });
      }

      return res.status(200).json({
        id: updatedOrder.id,
        status: updatedOrder.status,
        updatedAt: updatedOrder.updatedAt
      });
    } catch (error) {
      console.error("Error updating order status:", error);
      return res.status(500).json({ message: "Failed to update order status" });
    }
  });

  // Create order from admin panel
  app.post("/api/admin/orders", requireAuth, async (req: any, res) => {
    try {
      const firebaseUid = req.user.uid;
      const store = await storage.getStoreByFirebaseUid(firebaseUid);

      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      // Extract and validate order data
      let { customer, items, notes, paymentMethod, paymentMethodName, receivingMethod, receivingDate, receivingTime, deliveryAddress, subtotal, deliveryFee, total } = req.body;

      if (!customer || !items || !items.length || !paymentMethod || !receivingMethod) {
        return res.status(400).json({ message: "Missing required order data" });
      }

      // Use existing customer or create a new one
      let customerToUse;

      // Check if customer already exists by ID
      if (customer.id) {
        customerToUse = await storage.getCustomer(customer.id);

        // Verify the customer belongs to this store
        if (!customerToUse || customerToUse.storeId !== store.id) {
          return res.status(404).json({ message: "Customer not found" });
        }
      } else {
        // Create new customer
        const customerData = {
          name: customer.name,
          email: customer.email || null,
          phone: customer.phone || null,
          storeId: store.id
        };

        customerToUse = await storage.createCustomer(customerData);
      }

      // Create order
      const orderData = {
        storeId: store.id,
        customerId: customerToUse.id,
        status: "pending",
        total,
        paymentMethod,
        paymentMethodName, // Adicionar o nome do método de pagamento
        notes,
        receivingMethod,
        receivingDate: new Date(receivingDate),
        receivingTime,
        deliveryAddress,
        subtotal: subtotal || total,
        deliveryFee: deliveryFee || 0
      };

      console.log('Creating order from admin panel:', orderData);

      // Validate and create order
      const validatedOrderData = insertOrderSchema.parse(orderData);
      const order = await storage.createOrder(validatedOrderData);

      // Create order items
      for (const item of items) {
        // Verificar se é um produto personalizado (ID negativo)
        const isCustomProduct = item.productId < 0;

        if (isCustomProduct) {
          console.log(`Processando produto personalizado com ID: ${item.productId}`);

          // Para produtos personalizados, não precisamos verificar no banco de dados
          // pois eles existem apenas no contexto do pedido

          // Validate selected variations
          let selectedOptionsJson = [];

          if (item.selectedVariations && Array.isArray(item.selectedVariations) && item.selectedVariations.length > 0) {
            // Convert selected variations to database format
            selectedOptionsJson = item.selectedVariations.map(variation => ({
              variationId: variation.variationId,
              variationName: variation.variationName,
              optionId: variation.optionId,
              optionName: variation.optionName,
              price: variation.price || 0,
              quantity: variation.quantity || 1,
              isCustom: variation.isCustom || false
            }));
          }

          // Para produtos personalizados, usamos um ID temporário negativo no banco
          // mas armazenamos todos os dados do produto no item do pedido
          const orderItemData = {
            orderId: order.id,
            productId: -1, // ID fixo para produtos personalizados
            quantity: item.quantity,
            price: item.price,
            selectedOptions: selectedOptionsJson,
            observation: item.observation || item.name, // Armazenar o nome do produto personalizado na observação
            productName: item.name, // Armazenar o nome do produto personalizado
            productDescription: item.observation // Armazenar a descrição do produto personalizado
          };

          console.log('Criando item de pedido para produto personalizado:', orderItemData);

          // Validar e criar o item do pedido
          const validatedOrderItemData = insertOrderItemSchema.parse(orderItemData);
          await storage.createOrderItem(validatedOrderItemData);
        } else {
          // Para produtos normais, verificar se existem no banco de dados
          const productFromDb = await storage.getProduct(item.productId);

          if (!productFromDb) {
            return res.status(404).json({ message: `Product with ID ${item.productId} not found` });
          }

          if (productFromDb.storeId !== store.id) {
            return res.status(400).json({ message: `Product with ID ${item.productId} does not belong to this store` });
          }

          // Validate selected variations
          let selectedOptionsJson = [];

          if (item.selectedVariations && Array.isArray(item.selectedVariations) && item.selectedVariations.length > 0) {
            // Convert selected variations to database format
            selectedOptionsJson = item.selectedVariations.map(variation => ({
              variationId: variation.variationId,
              variationName: variation.variationName,
              optionId: variation.optionId,
              optionName: variation.optionName,
              price: variation.price || 0
            }));
          }

          const orderItemData = insertOrderItemSchema.parse({
            orderId: order.id,
            productId: item.productId,
            quantity: item.quantity,
            price: item.price, // Use the price from the request
            selectedOptions: selectedOptionsJson,
            observation: item.observation
          });

          await storage.createOrderItem(orderItemData);
        }
      }

      return res.status(201).json({
        orderId: order.id,
        status: order.status,
        total: order.total
      });
    } catch (error) {
      console.error('Error creating order from admin panel:', error);
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid data", errors: error.errors });
      }
      return res.status(500).json({ message: "Failed to create order" });
    }
  });

  // Create new order from public store
  app.post("/api/public/stores/:slug/orders", async (req, res) => {
    try {
      const { slug } = req.params;
      const store = await storage.getStoreBySlug(slug);

      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      // Extract and validate customer data
      let { customer, items, notes, paymentMethod, coupon } = req.body;

      if (!customer || !items || !items.length || !paymentMethod) {
        return res.status(400).json({ message: "Missing required order data" });
      }

      // Normalizar os dados do cliente antes de processá-los
      if (customer) {
        console.log('Dados do cliente recebidos do frontend:', JSON.stringify(customer));

        // Normalizar email e telefone
        if (customer.email) {
          customer.email = customer.email.toLowerCase().trim();
        }

        if (customer.phone || customer.whatsapp) {
          // Alguns clientes podem enviar como 'phone', outros como 'whatsapp'
          const phoneValue = customer.phone || customer.whatsapp;
          customer.phone = phoneValue.replace(/[\s\-\(\)\+]/g, '').trim();
          // Garantir que temos apenas um campo
          delete customer.whatsapp;
        }

        console.log('Dados do cliente normalizados:', JSON.stringify(customer));
      }

      // Check if the customer already exists by email first, then by phone
      console.log('Verificando se o cliente já existe no sistema...');
      console.log('Dados do cliente recebidos:', JSON.stringify(customer));
      let existingCustomer: Customer | undefined = undefined;

      // Verificar primeiro pelo email (se estiver presente)
      if (customer.email) {
        console.log('Buscando cliente pelo email:', customer.email);
        existingCustomer = await storage.getCustomerByEmail(store.id, customer.email);
        if (existingCustomer) {
          console.log('Cliente encontrado pelo email:', JSON.stringify(existingCustomer));
        } else {
          console.log('Nenhum cliente encontrado pelo email:', customer.email);
        }
      } else {
        console.log('Email não fornecido, pulando busca por email');
      }

      // Se não encontrou pelo email, verificar pelo telefone (se estiver presente)
      if (!existingCustomer && customer.phone) {
        console.log('Buscando cliente pelo telefone:', customer.phone);
        existingCustomer = await storage.getCustomerByPhone(store.id, customer.phone);
        if (existingCustomer) {
          console.log('Cliente encontrado pelo telefone:', JSON.stringify(existingCustomer));
        } else {
          console.log('Nenhum cliente encontrado pelo telefone:', customer.phone);
        }
      } else if (!customer.phone) {
        console.log('Telefone não fornecido, pulando busca por telefone');
      } else {
        console.log('Cliente já encontrado pelo email, pulando busca por telefone');
      }

      let customerToUse: Customer;

      if (existingCustomer) {
        // Se encontrou o cliente, usar o existente
        console.log('Usando cliente existente para o pedido. ID:', existingCustomer.id);
        customerToUse = existingCustomer;

        // Atualizar os dados do cliente se necessário (como nome, email ou telefone)
        const updateFields: Partial<Customer> = {};
        let needsUpdate = false;

        if (customer.name && customer.name !== existingCustomer.name) {
          updateFields.name = customer.name;
          console.log(`Atualizando nome: "${existingCustomer.name}" -> "${customer.name}"`);
          needsUpdate = true;
        }

        if (customer.email && customer.email !== existingCustomer.email) {
          updateFields.email = customer.email;
          console.log(`Atualizando email: "${existingCustomer.email}" -> "${customer.email}"`);
          needsUpdate = true;
        }

        if (customer.phone && customer.phone !== existingCustomer.phone) {
          updateFields.phone = customer.phone;
          console.log(`Atualizando telefone: "${existingCustomer.phone}" -> "${customer.phone}"`);
          needsUpdate = true;
        }

        if (customer.countryCode && customer.countryCode !== existingCustomer.countryCode) {
          updateFields.countryCode = customer.countryCode;
          console.log(`Atualizando código do país: "${existingCustomer.countryCode}" -> "${customer.countryCode}"`);
          needsUpdate = true;
        }

        if (needsUpdate) {
          console.log('Atualizando dados do cliente existente:', JSON.stringify(updateFields));
          const updatedCustomer = await storage.updateCustomer(existingCustomer.id, updateFields);
          if (updatedCustomer) {
            customerToUse = updatedCustomer;
            console.log('Cliente atualizado com sucesso:', JSON.stringify(customerToUse));
          } else {
            console.log('Falha ao atualizar cliente, usando dados originais');
          }
        } else {
          console.log('Nenhuma atualização necessária para o cliente existente');
        }
      } else {
        // Se não encontrou, criar um novo cliente
        console.log('Cliente não encontrado. Criando novo cliente...');
        try {
          const customerData = insertCustomerSchema.parse({
            ...customer,
            storeId: store.id
          });

          console.log('Dados validados para criação do cliente:', JSON.stringify(customerData));
          customerToUse = await storage.createCustomer(customerData);
          console.log('Novo cliente criado com ID:', customerToUse.id);
        } catch (error) {
          console.error('Erro ao validar ou criar cliente:', error);
          throw error;
        }
      }

      // Calculate total
      let total = 0;
      for (const item of items) {
        const product = await storage.getProduct(item.productId);
        if (!product) {
          return res.status(404).json({ message: `Product with ID ${item.productId} not found` });
        }

        // Verify product belongs to this store
        if (product.storeId !== store.id) {
          return res.status(400).json({ message: `Product with ID ${item.productId} does not belong to this store` });
        }

        total += product.price * item.quantity;
      }

      // Extrair dados de entrega/retirada
      const { receivingMethod, receivingDate, receivingTime, deliveryAddress, subtotal, deliveryFee = 0, discount = 0 } = req.body;

      // Validar dados obrigatórios
      if (!receivingMethod) {
        return res.status(400).json({ message: "Missing receivingMethod" });
      }

      if (!receivingDate) {
        return res.status(400).json({ message: "Missing receivingDate" });
      }

      // Verificar e validar o cupom, se fornecido
      let couponDiscount = 0;
      let appliedCouponId = null;

      if (coupon && coupon.id) {
        console.log('Cupom fornecido:', coupon);

        // Buscar o cupom no banco de dados para validar
        const couponFromDb = await storage.getCoupon(coupon.id);

        if (!couponFromDb) {
          return res.status(400).json({ message: "Cupom inválido ou não encontrado" });
        }

        // Verificar se o cupom pertence a esta loja
        if (couponFromDb.storeId !== store.id) {
          return res.status(400).json({ message: "Cupom não pertence a esta loja" });
        }

        // Verificar se o cupom está ativo
        if (!couponFromDb.ativo) {
          return res.status(400).json({ message: "Este cupom não está mais ativo" });
        }

        // Verificar se o cupom não expirou
        const now = new Date();
        const expirationDate = new Date(couponFromDb.dataValidade);

        if (expirationDate < now) {
          return res.status(400).json({ message: "Este cupom expirou" });
        }

        // Verificar valor mínimo de compra
        const orderSubtotal = subtotal || total;
        if (couponFromDb.minimoCompra && orderSubtotal < couponFromDb.minimoCompra) {
          return res.status(400).json({
            message: `Este cupom requer um valor mínimo de compra de ${couponFromDb.minimoCompra}`
          });
        }

        // Calcular o desconto com base no tipo de cupom
        if (couponFromDb.tipo === 'valor_fixo') {
          // Para cupons de valor fixo, o desconto é o valor do cupom
          // Mas não pode ser maior que o subtotal
          couponDiscount = Math.min(couponFromDb.valor, orderSubtotal);
        } else {
          // Para cupons percentuais, o desconto é o percentual do subtotal
          couponDiscount = (couponFromDb.valor / 100) * orderSubtotal;
        }

        // Arredondar o desconto para 2 casas decimais com precisão matemática
        couponDiscount = parseFloat(couponDiscount.toFixed(2));

        // Armazenar o ID do cupom para registrar o uso
        appliedCouponId = couponFromDb.id;

        console.log('Desconto calculado:', couponDiscount);
      }

      // Calcular o total final considerando o desconto do cupom
      const finalTotal = Math.max(0, (subtotal || total) - couponDiscount + (deliveryFee || 0));

      // Create order
      const orderData = {
        storeId: store.id,
        customerId: customerToUse.id,
        status: "pending",
        total: finalTotal,
        paymentMethod,
        notes,
        receivingMethod,
        receivingDate: new Date(receivingDate),
        receivingTime,
        deliveryAddress,
        subtotal: subtotal || total,
        deliveryFee: deliveryFee || 0,
        discount: couponDiscount,
        couponId: appliedCouponId || null,
        couponCode: appliedCouponId ? coupon.code : null,
        couponType: appliedCouponId ? coupon.tipo : null
      };

      console.log('Preparando para criar pedido:', orderData);

      // Validar e criar pedido
      const validatedOrderData = insertOrderSchema.parse(orderData);
      const order = await storage.createOrder(validatedOrderData);

      // Registrar o uso do cupom, se aplicado
      if (appliedCouponId) {
        try {
          await storage.createCouponUsage({
            couponId: appliedCouponId,
            customerId: customerToUse.id,
            orderId: order.id
          });
          console.log('Uso do cupom registrado com sucesso');
        } catch (couponError) {
          console.error('Erro ao registrar uso do cupom:', couponError);
          // Não interromper o fluxo se houver erro ao registrar o uso do cupom
        }
      }

      // Create order items
      for (const item of items) {
        // Obter o preço base do produto do banco de dados (para segurança)
        const productFromDb = await storage.getProduct(item.productId);
        if (!productFromDb) {
          return res.status(404).json({ message: `Product with ID ${item.productId} not found` });
        }

        // Validar as variações enviadas
        let selectedOptionsJson = [];

        if (item.selectedVariations && Array.isArray(item.selectedVariations) && item.selectedVariations.length > 0) {
          // Converter as variações selecionadas para o formato do banco de dados
          selectedOptionsJson = item.selectedVariations.map(variation => ({
            variationId: variation.variationId,
            variationName: variation.variationName,
            optionId: variation.optionId,
            optionName: variation.optionName,
            price: variation.price || 0
          }));
        }

        const orderItemData = insertOrderItemSchema.parse({
          orderId: order.id,
          productId: item.productId,
          quantity: item.quantity,
          price: productFromDb.price, // Preço base do produto
          selectedOptions: selectedOptionsJson,
          observation: item.observation
        });

        await storage.createOrderItem(orderItemData);
      }

      return res.status(201).json({
        orderId: order.id,
        status: order.status,
        total: order.total
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid data", errors: error.errors });
      }
      return res.status(500).json({ message: "Failed to create order" });
    }
  });

  // Helper function to get or create a session ID
  const getOrCreateSessionId = (req: Request): string => {
    if (!req.session.cartId) {
      req.session.cartId = uuidv4();
    }
    return req.session.cartId;
  };

  // Cart endpoints
  app.get("/api/public/stores/:slug/cart", async (req, res) => {
    try {
      const { slug } = req.params;
      const store = await storage.getStoreBySlug(slug);

      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      const sessionId = getOrCreateSessionId(req);
      const cartItems = await storage.getCartItemsBySessionId(store.id, sessionId);

      return res.status(200).json(cartItems);
    } catch (error) {
      console.error("Get cart error:", error);
      return res.status(500).json({ message: "Failed to retrieve cart items" });
    }
  });

  app.post("/api/public/stores/:slug/cart", async (req, res) => {
    try {
      const { slug } = req.params;
      const store = await storage.getStoreBySlug(slug);

      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      const { productId, quantity, price, selectedOptions, observation } = req.body;

      // Validate product exists
      const product = await storage.getProduct(productId);
      if (!product) {
        return res.status(404).json({ message: "Product not found" });
      }

      const sessionId = getOrCreateSessionId(req);

      // Create cart item
      const cartItem = await storage.createCartItem({
        storeId: store.id,
        sessionId,
        productId,
        quantity: quantity || 1,
        price,
        selectedOptions: selectedOptions || [],
        observation
      });

      return res.status(201).json(cartItem);
    } catch (error) {
      console.error("Add to cart error:", error);
      return res.status(500).json({ message: "Failed to add item to cart" });
    }
  });

  app.put("/api/public/stores/:slug/cart/:id", async (req, res) => {
    try {
      const { slug, id } = req.params;
      const store = await storage.getStoreBySlug(slug);

      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      const cartItemId = parseInt(id);
      const { quantity, observation } = req.body;

      const updatedCartItem = await storage.updateCartItem(cartItemId, {
        quantity,
        observation,
        updatedAt: new Date()
      });

      if (!updatedCartItem) {
        return res.status(404).json({ message: "Cart item not found" });
      }

      return res.status(200).json(updatedCartItem);
    } catch (error) {
      console.error("Update cart item error:", error);
      return res.status(500).json({ message: "Failed to update cart item" });
    }
  });

  app.delete("/api/public/stores/:slug/cart/:id", async (req, res) => {
    try {
      const { slug, id } = req.params;
      const store = await storage.getStoreBySlug(slug);

      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      const cartItemId = parseInt(id);
      const deleted = await storage.deleteCartItem(cartItemId);

      if (!deleted) {
        return res.status(404).json({ message: "Cart item not found" });
      }

      return res.status(200).json({ message: "Cart item removed successfully" });
    } catch (error) {
      console.error("Delete cart item error:", error);
      return res.status(500).json({ message: "Failed to remove cart item" });
    }
  });

  app.delete("/api/public/stores/:slug/cart", async (req, res) => {
    try {
      const { slug } = req.params;
      const store = await storage.getStoreBySlug(slug);

      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      const sessionId = getOrCreateSessionId(req);
      await storage.clearCartItems(store.id, sessionId);

      return res.status(200).json({ message: "Cart cleared successfully" });
    } catch (error) {
      console.error("Clear cart error:", error);
      return res.status(500).json({ message: "Failed to clear cart" });
    }
  });

  app.get("/api/orders", requireAuth, async (req: any, res) => {
    try {
      const firebaseUid = req.user.uid;
      const store = await storage.getStoreByFirebaseUid(firebaseUid);

      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      const orders = await storage.getOrdersByStoreId(store.id);

      // Get customer info and revision data for each order
      const ordersWithRevisionData = await Promise.all(orders.map(async (order) => {
        const customer = await storage.getCustomer(order.customerId);

        // Check for current revision
        const revisions = await storage.getOrderRevisionsByOrderId(order.id);
        const currentRevision = revisions.find(rev => rev.isCurrent);

        // Use revision data if available, otherwise use original order data
        const displayData = currentRevision ? {
          ...order,
          // ✅ KEEP ORIGINAL ORDER STATUS - never override with revision status
          status: order.status,
          // Override with revision data for other fields
          paymentStatus: currentRevision.paymentStatus || order.paymentStatus,
          receivingMethod: currentRevision.receivingMethod,
          receivingDate: currentRevision.receivingDate,
          receivingTime: currentRevision.receivingTime,
          deliveryAddress: currentRevision.deliveryAddress,
          paymentMethod: currentRevision.paymentMethod,
          subtotal: currentRevision.subtotal,
          deliveryFee: currentRevision.deliveryFee,
          discount: currentRevision.discount || 0,
          total: currentRevision.total,
          notes: currentRevision.notes,
          // Keep track that this has revision data
          hasCurrentRevision: true,
          currentRevisionId: currentRevision.id,
          revisionNumber: currentRevision.revisionNumber
        } : {
          ...order,
          discount: order.discount || 0,
          hasCurrentRevision: false
        };

        return {
          ...displayData,
          couponId: order.couponId || null,
          couponCode: order.couponCode || null,
          couponType: order.couponType || null,
          customer: customer ? {
            id: customer.id,
            name: customer.name,
            email: customer.email,
            phone: customer.phone
          } : null
        };
      }));

      return res.status(200).json(ordersWithRevisionData);
    } catch (error) {
      console.error('Error fetching orders with revision data:', error);
      return res.status(500).json({ message: "Failed to get orders" });
    }
  });

  // Get single order with details (admin)
  app.get("/api/orders/:id", requireAuth, async (req: any, res) => {
    try {
      console.log('Fetching order details for order ID:', req.params.id);
      const firebaseUid = req.user.uid;
      console.log('Firebase UID:', firebaseUid);

      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      console.log('Store retrieved:', store ? `ID: ${store.id}, Name: ${store.name}` : 'No store found');

      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      const orderId = parseInt(req.params.id);
      console.log('Order ID (parsed):', orderId);

      const order = await storage.getOrder(orderId);
      console.log('Order retrieved:', order ? `ID: ${order.id}, StoreID: ${order.storeId}` : 'Order not found');

      if (!order) {
        return res.status(404).json({ message: "Order not found" });
      }

      // Verify ownership
      console.log('Order store ID:', order.storeId, 'Current store ID:', store.id);
      if (order.storeId !== store.id) {
        console.log('Order store ID does not match current store ID - access denied');
        return res.status(403).json({ message: "Not authorized to view this order" });
      }

      // Get customer
      console.log('Fetching customer details for customer ID:', order.customerId);
      const customer = await storage.getCustomer(order.customerId);
      console.log('Customer retrieved:', customer ? `ID: ${customer.id}, Name: ${customer.name}` : 'No customer found');

      // Get order items
      console.log('Fetching order items for order ID:', order.id);
      const orderItems = await storage.getOrderItemsByOrderId(order.id);
      console.log(`Found ${orderItems.length} order items`);

      // Get product details for each item
      console.log('Fetching product details for each order item');
      const itemsWithProducts = await Promise.all(orderItems.map(async (item) => {
        // Verificar se é um produto customizado (ID negativo ou flag isCustomProduct)
        const isCustomProduct = item.productId < 0 || item.isCustomProduct;

        if (isCustomProduct) {
          console.log(`Item ${item.id} é um produto customizado. Usando dados do próprio item.`);
          // Para produtos customizados, usamos os dados do próprio item
          return {
            ...item,
            product: {
              id: item.productId,
              name: item.productName || 'Produto Personalizado',
              price: item.price,
              images: []
            }
          };
        } else {
          // Para produtos regulares, buscamos no banco de dados
          const product = await storage.getProduct(item.productId);

          if (!product) {
            console.log(`Produto regular com ID ${item.productId} não encontrado. Usando dados do item.`);
            // Se o produto não for encontrado, usamos os dados do item
            return {
              ...item,
              product: {
                id: item.productId,
                name: item.productName || 'Produto Indisponível',
                price: item.price,
                images: []
              }
            };
          }

          return {
            ...item,
            product: {
              id: product.id,
              name: product.name,
              price: product.price,
              images: product.images
            }
          };
        }
      }));

      // Adicionar informações do cupom explicitamente para garantir que sejam incluídas na resposta
      const response = {
        ...order,
        discount: order.discount || 0,
        couponId: order.couponId || null,
        couponCode: order.couponCode || null,
        couponType: order.couponType || null,
        customer: customer ? {
          id: customer.id,
          name: customer.name,
          email: customer.email,
          phone: customer.phone
        } : null,
        items: itemsWithProducts
      };

      console.log('Sending order details response:', response);
      return res.status(200).json(response);
    } catch (error) {
      console.error('Error fetching order details:', error);
      return res.status(500).json({ message: "Failed to get order details" });
    }
  });

  app.patch("/api/orders/:id/status", requireAuth, async (req: any, res) => {
    try {
      console.log('Updating order status for order ID:', req.params.id);
      const firebaseUid = req.user.uid;
      console.log('Firebase UID:', firebaseUid);

      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      console.log('Store retrieved:', store ? `ID: ${store.id}, Name: ${store.name}` : 'No store found');

      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      const orderId = parseInt(req.params.id);
      console.log('Order ID (parsed):', orderId);

      const order = await storage.getOrder(orderId);
      console.log('Order retrieved:', order ? `ID: ${order.id}, StoreID: ${order.storeId}` : 'Order not found');

      if (!order) {
        return res.status(404).json({ message: "Order not found" });
      }

      // Verify ownership
      console.log('Order store ID:', order.storeId, 'Current store ID:', store.id);
      if (order.storeId !== store.id) {
        console.log('Order store ID does not match current store ID - access denied');
        return res.status(403).json({ message: "Not authorized to update this order" });
      }

      const { status } = req.body;
      console.log('New status from request body:', status);
      if (!status) {
        console.log('Status is missing in request body');
        return res.status(400).json({ message: "Status is required" });
      }

      // Valid statuses
      const validStatuses = ["pending", "confirmed", "delivered", "cancelled"];
      console.log('Checking if status is valid:', validStatuses.includes(status));
      if (!validStatuses.includes(status)) {
        return res.status(400).json({ message: "Invalid status" });
      }

      // Update order status
      console.log(`Updating order ${orderId} status to ${status}`);
      const updatedOrder = await storage.updateOrderStatus(orderId, status);
      console.log('Order status updated, result:', updatedOrder);

      return res.status(200).json(updatedOrder);
    } catch (error) {
      console.error('Error updating order status:', error);
      return res.status(500).json({ message: "Failed to update order status" });
    }
  });

  // Create order revision
  app.post("/api/orders/:id/revisions", requireAuth, async (req: any, res) => {
    try {
      console.log('Creating revision for order ID:', req.params.id);
      const firebaseUid = req.user.uid;
      console.log('Firebase UID:', firebaseUid);

      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      const orderId = parseInt(req.params.id);
      const order = await storage.getOrder(orderId);
      if (!order) {
        return res.status(404).json({ message: "Order not found" });
      }

      // Verify ownership
      if (order.storeId !== store.id) {
        return res.status(403).json({ message: "Not authorized to create revision for this order" });
      }

      // Get order items
      const orderItems = await storage.getOrderItemsByOrderId(orderId);
      if (!orderItems || orderItems.length === 0) {
        return res.status(400).json({ message: "Cannot create revision for order with no items" });
      }

      // Get the next revision number
      const latestRevisionNumber = await storage.getLatestRevisionNumber(orderId);
      const newRevisionNumber = latestRevisionNumber + 1;

      // Create the revision
      const revisionData: InsertOrderRevision = {
        orderId,
        customerId: order.customerId, // Incluindo o ID do cliente do pedido original
        revisionNumber: newRevisionNumber,
        status: order.status,
        receivingMethod: order.receivingMethod,
        receivingDate: order.receivingDate,
        receivingTime: order.receivingTime,
        deliveryAddress: order.deliveryAddress,
        paymentMethod: order.paymentMethod,
        subtotal: order.subtotal,
        deliveryFee: order.deliveryFee,
        discount: order.discount || 0,
        couponId: order.couponId || null,
        couponCode: order.couponCode || null,
        couponType: order.couponType || null,
        total: order.total,
        notes: order.notes,
        createdBy: store.userId, // Usando o ID do usuário da loja
        isCurrent: true // Definir automaticamente como a revisão atual
      };

      const revision = await storage.createOrderRevision(revisionData);

      // Create revision items with complete product data
      for (const item of orderItems) {
        console.log('Processando item para revisão:', {
          id: item.id,
          productId: item.productId,
          productName: item.productName,
          isCustomProduct: item.productId < 0 || item.isCustomProduct
        });

        // Verificar se é um produto customizado (ID negativo ou flag isCustomProduct)
        const isCustomProduct = item.productId < 0 || item.isCustomProduct;

        let revisionItem: InsertOrderRevisionItem;

        if (isCustomProduct) {
          console.log('Produto customizado detectado:', item.productName || 'Produto Personalizado');

          // Calcular subtotal incluindo variações para produtos personalizados
          let variationsTotal = 0;
          if (item.selectedOptions && Array.isArray(item.selectedOptions)) {
            variationsTotal = item.selectedOptions.reduce((sum: number, variation: any) => {
              const variationPrice = variation.price || 0;
              const variationQuantity = variation.quantity || 1;

              // Para variações personalizadas (tipo "outros"), não multiplicar pela quantidade do produto
              if (variation.isCustom || variation.variationName?.toLowerCase() === 'outros') {
                return sum + (variationPrice * variationQuantity);
              } else {
                // Para variações normais, multiplicar pela quantidade do produto
                return sum + (variationPrice * variationQuantity * item.quantity);
              }
            }, 0);
          }

          const subtotalWithVariations = (item.price * item.quantity) + variationsTotal;

          console.log('Cálculo do subtotal para produto personalizado:', {
            productName: item.productName,
            unitPrice: item.price,
            quantity: item.quantity,
            baseSubtotal: item.price * item.quantity,
            variationsTotal: variationsTotal,
            variationsTotalForQuantity: variationsTotal * item.quantity,
            finalSubtotal: subtotalWithVariations,
            selectedOptions: item.selectedOptions
          });

          // Para produtos customizados, usamos os dados do próprio item
          revisionItem = {
            revisionId: revision.id,
            productId: item.productId, // Mantemos o ID negativo para indicar produto customizado
            productName: item.productName || 'Produto Personalizado',
            productDescription: item.productDescription || item.observation || '',
            productImage: '', // Produtos customizados geralmente não têm imagem
            quantity: item.quantity,
            unitPrice: item.price,
            subtotal: subtotalWithVariations, // ✅ CORRIGIDO - inclui variações
            selectedVariations: item.selectedOptions || [],
            observation: item.observation
          };
        } else {
          // Para produtos regulares, buscamos no banco de dados
          const product = await storage.getProduct(item.productId);
          if (!product) {
            console.log(`Produto regular com ID ${item.productId} não encontrado, pulando`);
            continue;
          }

          // Extrair detalhes completos das variações selecionadas
          const selectedVariationsWithDetails = [];

          if (item.selectedOptions && Array.isArray(item.selectedOptions)) {
            // Obter todas as variações do produto
            const productVariations = await storage.getProductVariationsByProductId(item.productId);

            for (const selectedOption of item.selectedOptions) {
              // Verificar se é uma opção personalizada
              if (selectedOption.variationId === 'custom' || selectedOption.isCustom) {
                // Para opções personalizadas, manter os dados originais
                selectedVariationsWithDetails.push(selectedOption);
              } else {
                // Para opções regulares, buscar detalhes adicionais
                const variation = productVariations.find(v => v.id === Number(selectedOption.variationId));
                if (variation) {
                  // Obter as opções desta variação
                  const options = await storage.getVariationOptionsByVariationId(variation.id);
                  // Encontrar a opção selecionada
                  const option = options.find(o => o.id === Number(selectedOption.optionId));

                  if (option) {
                    // Adicionar todos os detalhes da opção e variação
                    selectedVariationsWithDetails.push({
                      ...selectedOption,
                      variationName: variation.name,
                      optionName: option.name,
                      price: option.price || 0
                    });
                  }
                }
              }
            }
          }

          // Calcular subtotal incluindo variações para produtos regulares
          let variationsTotal = 0;
          if (selectedVariationsWithDetails.length > 0) {
            variationsTotal = selectedVariationsWithDetails.reduce((sum: number, variation: any) => {
              return sum + (variation.price || 0);
            }, 0);
          }

          const subtotalWithVariations = (item.price * item.quantity) + (variationsTotal * item.quantity);

          console.log('Cálculo do subtotal para produto regular:', {
            productName: product.name,
            unitPrice: item.price,
            quantity: item.quantity,
            baseSubtotal: item.price * item.quantity,
            variationsTotal: variationsTotal,
            variationsTotalForQuantity: variationsTotal * item.quantity,
            finalSubtotal: subtotalWithVariations,
            selectedVariations: selectedVariationsWithDetails
          });

          revisionItem = {
            revisionId: revision.id,
            productId: item.productId,
            productName: product.name,
            productDescription: product.description || "",
            productImage: product.images && product.images.length > 0 ? product.images[0] : "",
            quantity: item.quantity,
            unitPrice: item.price,
            subtotal: subtotalWithVariations, // ✅ CORRIGIDO - inclui variações
            // Usar os detalhes completos das variações
            selectedVariations: selectedVariationsWithDetails.length > 0 ? selectedVariationsWithDetails : item.selectedOptions,
            observation: item.observation
          };
        }

        console.log('Criando item de revisão:', {
          revisionId: revisionItem.revisionId,
          productId: revisionItem.productId,
          productName: revisionItem.productName,
          quantity: revisionItem.quantity
        });

        await storage.createOrderRevisionItem(revisionItem);
      }

      // Return the created revision with its number
      return res.status(200).json({
        id: revision.id,
        revisionNumber: newRevisionNumber
      });
    } catch (error) {
      console.error('Error creating order revision:', error);
      return res.status(500).json({
        message: "Failed to create order revision",
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // Get all revisions for an order
  app.get("/api/orders/:id/revisions", requireAuth, async (req: any, res) => {
    try {
      console.log('Getting revisions for order ID:', req.params.id);
      const firebaseUid = req.user.uid;

      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      const orderId = parseInt(req.params.id);
      const order = await storage.getOrder(orderId);
      if (!order) {
        return res.status(404).json({ message: "Order not found" });
      }

      // Verify ownership
      if (order.storeId !== store.id) {
        return res.status(403).json({ message: "Not authorized to access revisions for this order" });
      }

      // Get all revisions for this order
      const revisions = await storage.getOrderRevisionsByOrderId(orderId);

      // Obter todos os clientes da loja para usar como um cache
      const customers = await storage.getCustomersByStoreId(store.id);
      const customersMap = new Map();
      customers.forEach(c => customersMap.set(c.id, c));

      // Get items for each revision and add customer information
      const revisionsWithItems = await Promise.all(revisions.map(async (revision) => {
        const items = await storage.getOrderRevisionItems(revision.id);

        // Procurar o cliente pelo ID ou usar o cliente do pedido
        let customer = null;
        if (revision.customerId && customersMap.has(revision.customerId)) {
          customer = customersMap.get(revision.customerId);
        } else if (order.customerId && customersMap.has(order.customerId)) {
          customer = customersMap.get(order.customerId);
        }

        return {
          ...revision,
          discount: revision.discount || 0,
          couponId: revision.couponId || null,
          couponCode: revision.couponCode || null,
          couponType: revision.couponType || null,
          items,
          customer
        };
      }));

      return res.status(200).json(revisionsWithItems);
    } catch (error) {
      console.error('Error getting order revisions:', error);
      return res.status(500).json({
        message: "Failed to get order revisions",
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // Get specific revision details
  app.get("/api/orders/revisions/:revisionId", requireAuth, async (req: any, res) => {
    try {
      const revisionId = parseInt(req.params.revisionId);
      console.log('Getting details for revision ID:', revisionId);

      const firebaseUid = req.user.uid;
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      // Get the revision
      const revision = await storage.getOrderRevision(revisionId);
      if (!revision) {
        return res.status(404).json({ message: "Revision not found" });
      }

      // Get the order to verify ownership
      const order = await storage.getOrder(revision.orderId);
      if (!order || order.storeId !== store.id) {
        return res.status(403).json({ message: "Not authorized to access this revision" });
      }

      // Get revision items
      const items = await storage.getOrderRevisionItems(revisionId);

      // ⚠️ CORREÇÃO AUTOMÁTICA REMOVIDA PARA EVITAR ALTERAÇÕES DURANTE CONSULTAS
      // A correção de subtotais deve ser feita apenas quando há mudanças nos dados
      // não durante consultas de dados para exibição ou validação de pagamentos

      // Obter dados do cliente associado à revisão (se houver)
      let customer = null;
      if (revision.customerId) {
        customer = await storage.getCustomer(revision.customerId);
      } else if (order.customerId) {
        // Se a revisão não tiver um cliente específico, usar o cliente do pedido
        customer = await storage.getCustomer(order.customerId);
      }

      return res.status(200).json({
        ...revision,
        discount: revision.discount || 0,
        couponId: revision.couponId || null,
        couponCode: revision.couponCode || null,
        couponType: revision.couponType || null,
        items,
        customer
      });
    } catch (error) {
      console.error('Error getting revision details:', error);
      return res.status(500).json({
        message: "Failed to get revision details",
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // Get specific revision item details
  app.get("/api/orders/revisions/:revisionId/items/:itemId", requireAuth, async (req: any, res) => {
    try {
      const revisionId = parseInt(req.params.revisionId);
      const itemId = parseInt(req.params.itemId);
      console.log('Getting details for revision item ID:', itemId, 'in revision ID:', revisionId);

      const firebaseUid = req.user.uid;
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      // Get the revision
      const revision = await storage.getOrderRevision(revisionId);
      if (!revision) {
        return res.status(404).json({ message: "Revision not found" });
      }

      // Get the order to verify ownership
      const order = await storage.getOrder(revision.orderId);
      if (!order || order.storeId !== store.id) {
        return res.status(403).json({ message: "Not authorized to access this revision" });
      }

      // Get all revision items
      const items = await storage.getOrderRevisionItems(revisionId);

      // Find the specific item
      const item = items.find(i => i.id === itemId);

      if (!item) {
        return res.status(404).json({ message: "Item not found in revision" });
      }

      // Buscar o produto relacionado para obter dados atualizados das variações
      const product = await storage.getProduct(item.productId);
      console.log('Found revision item:', JSON.stringify(item, null, 2));
      console.log('Related product:', product ? product.id : 'not found');
      console.log('Raw selectedVariations in item:', item.selectedVariations);
      console.log('Type of selectedVariations:', typeof item.selectedVariations);

      // Processar selectedVariations para garantir que é um array
      let selectedVariations = item.selectedVariations || [];

      // Se for uma string, tente converter para objeto
      if (typeof selectedVariations === 'string') {
        try {
          selectedVariations = JSON.parse(selectedVariations);
          console.log('Parsed selectedVariations from string:', selectedVariations);
        } catch (e) {
          console.error('Failed to parse selectedVariations from string:', e);
          selectedVariations = [];
        }
      }

      // Se ainda não for um array, inicialize como array vazio
      if (!Array.isArray(selectedVariations)) {
        console.log('selectedVariations is not an array, initializing as empty array');
        selectedVariations = [];
      }

      // Calcular o subtotal correto incluindo variações
      let calculatedSubtotal = (item.unitPrice || (item as any).unit_price || 0) * item.quantity;

      // Verificar se é um produto personalizado (ID negativo) e tem variações
      const isCustomProduct = item.productId < 0;
      if (isCustomProduct && selectedVariations && Array.isArray(selectedVariations) && selectedVariations.length > 0) {
        console.log('Recalculando subtotal para produto personalizado na normalização:', {
          productName: item.productName,
          unitPrice: item.unitPrice || (item as any).unit_price || 0,
          quantity: item.quantity,
          baseSubtotal: calculatedSubtotal,
          variations: selectedVariations
        });

        // Para produtos personalizados, calcular o total das variações
        const variationsTotal = selectedVariations.reduce((sum: number, variation: any) => {
          const variationPrice = parseFloat(variation.price) || 0;
          const variationQuantity = parseInt(variation.quantity) || 1;

          // Para variações personalizadas (tipo "outros"), não multiplicar pela quantidade do produto
          if (variation.isCustom || variation.is_custom || variation.variationName?.toLowerCase() === 'outros') {
            return sum + (variationPrice * variationQuantity);
          } else {
            // Para variações normais, multiplicar pela quantidade do produto
            return sum + (variationPrice * variationQuantity * item.quantity);
          }
        }, 0);

        calculatedSubtotal = ((item.unitPrice || (item as any).unit_price || 0) * item.quantity) + variationsTotal;

        console.log('Subtotal recalculado na normalização:', {
          baseTotal: (item.unitPrice || (item as any).unit_price || 0) * item.quantity,
          variationsTotal: variationsTotal,
          finalSubtotal: calculatedSubtotal,
          subtotalAnterior: item.subtotal
        });
      }

      // Normalizar os nomes dos campos para o formato esperado pelo frontend
      const normalizedItem = {
        id: item.id,
        revisionId: item.revisionId,
        productId: item.productId,
        productName: item.productName,
        productDescription: item.productDescription,
        productImage: item.productImage,
        quantity: item.quantity,
        unitPrice: item.unitPrice || (item as any).unit_price || 0,
        subtotal: calculatedSubtotal, // ✅ CORRIGIDO - usar o subtotal calculado com variações
        selectedVariations: selectedVariations,
        observation: item.observation || ''
      };

      // Normalizar os campos das variações selecionadas
      normalizedItem.selectedVariations = normalizedItem.selectedVariations.map((variation: any) => {
        // Log para depuração
        console.log('Normalizing variation:', variation);

        return {
          variationId: variation.variationId || variation.variation_id || variation.variationid || '',
          variationName: variation.variationName || variation.variation_name || variation.variationname || '',
          optionId: variation.optionId || variation.option_id || variation.optionid || '',
          optionName: variation.optionName || variation.option_name || variation.optionname || '',
          price: parseFloat(variation.price) || 0,
          quantity: parseInt(variation.quantity) || 1,
          isCustom: Boolean(variation.isCustom || variation.is_custom || variation.iscustom || false)
        };
      });

      console.log('Normalized item for frontend:', JSON.stringify(normalizedItem, null, 2));

      return res.status(200).json(normalizedItem);
    } catch (error) {
      console.error('Error getting revision item details:', error);
      return res.status(500).json({
        message: "Failed to get revision item details",
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // Update order revision
  app.patch("/api/orders/revisions/:revisionId", requireAuth, async (req: any, res) => {
    try {
      const revisionId = parseInt(req.params.revisionId);
      console.log('Updating revision ID:', revisionId, 'with data:', req.body);

      const firebaseUid = req.user.uid;
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      // Get the revision
      const revision = await storage.getOrderRevision(revisionId);
      if (!revision) {
        return res.status(404).json({ message: "Revision not found" });
      }

      // Get the order to verify ownership
      const order = await storage.getOrder(revision.orderId);
      if (!order || order.storeId !== store.id) {
        return res.status(403).json({ message: "Not authorized to modify this revision" });
      }

      // Update the revision with the new data
      const updatedRevision = await storage.updateOrderRevision(revisionId, req.body);

      return res.status(200).json(updatedRevision);
    } catch (error) {
      console.error('Error updating revision:', error);
      return res.status(500).json({
        message: "Failed to update revision",
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // Fix revision subtotals manually
  app.post("/api/orders/revisions/:revisionId/fix-subtotals", requireAuth, async (req: any, res) => {
    try {
      const revisionId = parseInt(req.params.revisionId);
      console.log('Fixing subtotals for revision ID:', revisionId);

      const firebaseUid = req.user.uid;
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      // Get the revision
      const revision = await storage.getOrderRevision(revisionId);
      if (!revision) {
        return res.status(404).json({ message: "Revision not found" });
      }

      // Get the order to verify ownership
      const order = await storage.getOrder(revision.orderId);
      if (!order || order.storeId !== store.id) {
        return res.status(403).json({ message: "Not authorized to modify this revision" });
      }

      // Get revision items
      const items = await storage.getOrderRevisionItems(revisionId);

      // Apply unified correction
      console.log('🔄 Aplicando correção manual de subtotais...');
      const correctionSuccess = await updateRevisionTotalsUnified(revisionId, items, revision);

      if (correctionSuccess) {
        // Get updated revision
        const updatedRevision = await storage.getOrderRevision(revisionId);
        return res.status(200).json({
          success: true,
          message: "Subtotals fixed successfully",
          revision: updatedRevision
        });
      } else {
        return res.status(500).json({
          success: false,
          message: "Failed to fix subtotals"
        });
      }
    } catch (error) {
      console.error('Error fixing revision subtotals:', error);
      return res.status(500).json({
        success: false,
        message: "Failed to fix revision subtotals",
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // Add new item to order revision
  app.post("/api/orders/revisions/:revisionId/items", requireAuth, async (req: any, res) => {
    try {
      const revisionId = parseInt(req.params.revisionId);
      console.log('Adding new item to revision ID:', revisionId);
      const { item } = req.body;

      console.log('Dados recebidos para adição de item:', JSON.stringify(item, null, 2));

      if (!item) {
        return res.status(400).json({ message: "Item data is required" });
      }

      const firebaseUid = req.user.uid;
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      // Get the revision
      const revision = await storage.getOrderRevision(revisionId);
      if (!revision) {
        return res.status(404).json({ message: "Revision not found" });
      }

      // Get the order to verify ownership
      const order = await storage.getOrder(revision.orderId);
      if (!order || order.storeId !== store.id) {
        return res.status(403).json({ message: "Not authorized to modify this revision" });
      }

      // Calcular o subtotal correto incluindo variações
      let calculatedSubtotal = item.unitPrice * item.quantity;

      // Verificar se é um produto personalizado (ID negativo)
      const isCustomProduct = item.productId < 0;

      if (isCustomProduct && item.selectedVariations && Array.isArray(item.selectedVariations)) {
        console.log('Calculando subtotal para produto personalizado com variações:', {
          productName: item.productName,
          unitPrice: item.unitPrice,
          quantity: item.quantity,
          baseSubtotal: calculatedSubtotal,
          variations: item.selectedVariations
        });

        // Para produtos personalizados, calcular o total das variações
        const variationsTotal = item.selectedVariations.reduce((sum, variation) => {
          const variationPrice = variation.price || 0;
          const variationQuantity = variation.quantity || 1;

          // Para variações personalizadas (tipo "outros"), não multiplicar pela quantidade do produto
          if (variation.isCustom || variation.variationName?.toLowerCase() === 'outros') {
            return sum + (variationPrice * variationQuantity);
          } else {
            // Para variações normais, multiplicar pela quantidade do produto
            return sum + (variationPrice * variationQuantity * item.quantity);
          }
        }, 0);

        calculatedSubtotal = (item.unitPrice * item.quantity) + variationsTotal;

        console.log('Subtotal calculado com variações:', {
          baseTotal: item.unitPrice * item.quantity,
          variationsTotal: variationsTotal,
          finalSubtotal: calculatedSubtotal
        });
      }

      // Prepare the item data for insertion
      const revisionItem: InsertOrderRevisionItem = {
        revisionId: revisionId,
        productId: item.productId,
        productName: item.productName,
        productDescription: item.productDescription || null,
        productImage: item.productImage || null,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        subtotal: calculatedSubtotal, // ✅ CORRIGIDO - usar o subtotal calculado com variações
        selectedVariations: item.selectedVariations || [],
        observation: item.observation || null
      };

      // Create the new item
      const newItem = await storage.createOrderRevisionItem(revisionItem);
      console.log('Novo item criado com sucesso:', newItem);

      // ✅ APLICAR MÉTODO UNIFICADO APÓS ADIÇÃO DE ITEM
      console.log('🔄 Aplicando método unificado após adição de item...');
      const items = await storage.getOrderRevisionItems(revisionId);
      const revisionAfterAdd = await storage.getOrderRevision(revisionId);
      const unifiedSuccess = await updateRevisionTotalsUnified(revisionId, items, revisionAfterAdd);

      if (!unifiedSuccess) {
        console.error('❌ Falha no método unificado após adição de item');
      } else {
        console.log('✅ Método unificado aplicado com sucesso após adição');
      }

      // Return the updated revision with all items
      const updatedRevision = await storage.getOrderRevision(revisionId);
      const updatedItems = await storage.getOrderRevisionItems(revisionId);

      return res.status(201).json({
        ...updatedRevision,
        items: updatedItems
      });
    } catch (error) {
      console.error('Error adding item to revision:', error);
      return res.status(500).json({
        message: "Failed to add item to revision",
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // Delete specific revision item
  app.delete("/api/orders/revisions/:revisionId/items/:itemId", requireAuth, async (req: any, res) => {
    try {
      const revisionId = parseInt(req.params.revisionId);
      const itemId = parseInt(req.params.itemId);
      console.log('Deleting item ID:', itemId, 'from revision ID:', revisionId);

      const firebaseUid = req.user.uid;
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      // Get the revision
      const revision = await storage.getOrderRevision(revisionId);
      if (!revision) {
        return res.status(404).json({ message: "Revision not found" });
      }

      // Get the order to verify ownership
      const order = await storage.getOrder(revision.orderId);
      if (!order || order.storeId !== store.id) {
        return res.status(403).json({ message: "Not authorized to modify this revision" });
      }

      // Delete the item
      const success = await storage.deleteOrderRevisionItem(itemId);
      if (!success) {
        return res.status(500).json({ message: "Failed to delete item" });
      }

      // ✅ APLICAR MÉTODO UNIFICADO APÓS REMOÇÃO DE ITEM
      console.log('🔄 Aplicando método unificado após remoção de item...');
      const items = await storage.getOrderRevisionItems(revisionId);
      const revisionAfterDelete = await storage.getOrderRevision(revisionId);
      const unifiedSuccess = await updateRevisionTotalsUnified(revisionId, items, revisionAfterDelete);

      if (!unifiedSuccess) {
        console.error('❌ Falha no método unificado após remoção de item');
      } else {
        console.log('✅ Método unificado aplicado com sucesso após remoção');
      }

      return res.status(200).json({
        message: "Item deleted successfully",
        itemId,
        revisionId,
        updatedRevision: {
          subtotal,
          total
        }
      });
    } catch (error) {
      console.error('Error deleting revision item:', error);
      return res.status(500).json({
        message: "Failed to delete revision item",
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // Update order revision items
  app.patch("/api/orders/revisions/:revisionId/items", requireAuth, async (req: any, res) => {
    try {
      const revisionId = parseInt(req.params.revisionId);
      console.log('Updating items for revision ID:', revisionId);
      const { items } = req.body;

      console.log('Dados recebidos para atualização:', JSON.stringify(items, null, 2));

      if (!items || !Array.isArray(items)) {
        return res.status(400).json({ message: "Items array is required" });
      }

      const firebaseUid = req.user.uid;
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      // Get the revision
      const revision = await storage.getOrderRevision(revisionId);
      if (!revision) {
        return res.status(404).json({ message: "Revision not found" });
      }

      // Get the order to verify ownership
      const order = await storage.getOrder(revision.orderId);
      if (!order || order.storeId !== store.id) {
        return res.status(403).json({ message: "Not authorized to modify this revision" });
      }

      // Get current items
      const currentItems = await storage.getOrderRevisionItems(revisionId);

      // Processar cada item da lista para atualização
      for (const itemData of items) {
        console.log('Processando item para atualização:', JSON.stringify(itemData, null, 2));

        if (!itemData.id) {
          console.log('Item sem ID, ignorando');
          continue;
        }

        // Encontrar o item original na lista de itens atuais
        const originalItem = currentItems.find((item: any) => item.id === itemData.id);
        if (!originalItem) {
          console.log(`Item com ID ${itemData.id} não encontrado na revisão, ignorando`);
          continue;
        }

        // Verificar se há opções personalizadas
        if (itemData.selectedVariations) {
          console.log('Variações selecionadas:', JSON.stringify(itemData.selectedVariations, null, 2));

          // Verificar e corrigir quantidades das variações
          if (Array.isArray(itemData.selectedVariations)) {
            itemData.selectedVariations = itemData.selectedVariations.map((variation: any) => {
              // Garantir que a quantidade seja pelo menos 1
              if (!variation.quantity || variation.quantity < 1) {
                console.warn(`Corrigindo quantidade da variação ${variation.variationName} - ${variation.optionName}: ${variation.quantity} -> 1`);
                return { ...variation, quantity: 1 };
              }
              return variation;
            });
          }

          // Verificar opções personalizadas
          const customOptions = itemData.selectedVariations.filter((v: any) => v.variationId === 'custom' || v.isCustom);
          if (customOptions.length > 0) {
            console.log('Opções personalizadas encontradas:', JSON.stringify(customOptions, null, 2));
          }
        }

        // Calcular o subtotal correto incluindo variações antes da atualização
        let calculatedSubtotal = itemData.unitPrice * itemData.quantity;

        // Verificar se é um produto personalizado (ID negativo)
        const isCustomProduct = itemData.productId < 0;

        if (isCustomProduct && itemData.selectedVariations && Array.isArray(itemData.selectedVariations)) {
          console.log('Recalculando subtotal para produto personalizado com variações:', {
            productName: itemData.productName,
            unitPrice: itemData.unitPrice,
            quantity: itemData.quantity,
            baseSubtotal: calculatedSubtotal,
            variations: itemData.selectedVariations
          });

          // Para produtos personalizados, calcular o total das variações
          const variationsTotal = itemData.selectedVariations.reduce((sum, variation) => {
            const variationPrice = variation.price || 0;
            const variationQuantity = variation.quantity || 1;

            // Para variações personalizadas (tipo "outros"), não multiplicar pela quantidade do produto
            if (variation.isCustom || variation.variationName?.toLowerCase() === 'outros') {
              return sum + (variationPrice * variationQuantity);
            } else {
              // Para variações normais, multiplicar pela quantidade do produto
              return sum + (variationPrice * variationQuantity * itemData.quantity);
            }
          }, 0);

          calculatedSubtotal = (itemData.unitPrice * itemData.quantity) + variationsTotal;

          console.log('Subtotal recalculado com variações:', {
            baseTotal: itemData.unitPrice * itemData.quantity,
            variationsTotal: variationsTotal,
            finalSubtotal: calculatedSubtotal
          });
        }

        // Atualizar o item no banco de dados
        try {
          // Preparar os dados para atualização (sem o ID) com o subtotal correto
          const { id, ...updateData } = itemData;
          updateData.subtotal = calculatedSubtotal; // ✅ CORRIGIDO - usar o subtotal calculado com variações

          console.log(`Atualizando item ${id} com:`, JSON.stringify(updateData, null, 2));

          // Atualizar no banco de dados
          try {
            console.log('Tentando atualizar item...');

            // Usar Supabase diretamente
            const { data: result, error } = await supabase
              .from('order_revision_items')
              .update({
                quantity: updateData.quantity,
                unit_price: updateData.unitPrice,
                subtotal: updateData.subtotal,
                selected_variations: updateData.selectedVariations,
                observation: updateData.observation
              })
              .eq('id', id)
              .select('*')
              .single();

            if (error) {
              console.error('Erro ao atualizar item usando Supabase:', error);

              // Tentar com SQL direto
              console.log('Tentando com SQL direto...');
              const queryParams = [
                updateData.quantity,
                updateData.unitPrice,
                updateData.subtotal,
                JSON.stringify(updateData.selectedVariations),
                updateData.observation,
                id
              ];

              const query = `
                UPDATE order_revision_items
                SET
                  quantity = $1,
                  unit_price = $2,
                  subtotal = $3,
                  selected_variations = $4,
                  observation = $5
                WHERE id = $6
                RETURNING *
              `;

              const result = await pool.query(query, queryParams);
              console.log('Item atualizado com sucesso usando SQL direto:', result.rows[0]);
            } else {
              console.log('Item atualizado com sucesso usando Supabase:', result);
            }
          } catch (updateError) {
            console.error('Erro ao atualizar item:', updateError);
            throw updateError;
          }
        } catch (updateError) {
          console.error(`Erro ao atualizar item ${itemData.id}:`, updateError);
          throw updateError; // Propagar o erro para ser capturado pelo catch externo
        }
      }

      // Buscar todos os itens atualizados no banco de dados para garantir cálculos precisos
      const updatedItemsFromDB = await storage.getOrderRevisionItems(revisionId);
      console.log('Itens atualizados obtidos do banco:', updatedItemsFromDB.length);

      // ✅ MÉTODO UNIFICADO PARA CÁLCULO E SALVAMENTO DA REVISÃO
      await updateRevisionTotalsUnified(revisionId, updatedItemsFromDB, revision);

      // ✅ APLICAR MÉTODO UNIFICADO PARA GARANTIR GRAVAÇÃO CORRETA
      console.log('🔄 Aplicando método unificado após atualização dos itens...');
      const revisionUpdated = await storage.getOrderRevision(revisionId);
      const unifiedSuccess = await updateRevisionTotalsUnified(revisionId, updatedItemsFromDB, revisionUpdated);

      if (!unifiedSuccess) {
        console.error('❌ Falha no método unificado de atualização');
      } else {
        console.log('✅ Método unificado aplicado com sucesso');
      }

      // Fetch updated items and revision after unified calculation
      const finalUpdatedItems = await storage.getOrderRevisionItems(revisionId);
      const finalUpdatedRevision = await storage.getOrderRevision(revisionId);

      return res.status(200).json({
        ...finalUpdatedRevision,
        items: finalUpdatedItems,
        recalculationPerformed: unifiedSuccess
      });
    } catch (error) {
      console.error('Error updating revision items:', error);
      return res.status(500).json({
        message: "Failed to update revision items",
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // Update customer for a revision
  app.patch("/api/orders/revisions/:revisionId/customer", requireAuth, async (req: any, res) => {
    try {
      const revisionId = parseInt(req.params.revisionId);
      console.log('Updating customer for revision ID:', revisionId);
      console.log('Request body:', req.body);

      const { customerId } = req.body;

      if (customerId === undefined) {
        return res.status(400).json({ message: "Customer ID is required" });
      }

      const firebaseUid = req.user.uid;
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      // Get the revision
      const revision = await storage.getOrderRevision(revisionId);
      if (!revision) {
        return res.status(404).json({ message: "Revision not found" });
      }

      // Get the order to verify ownership
      const order = await storage.getOrder(revision.orderId);
      if (!order || order.storeId !== store.id) {
        return res.status(403).json({ message: "Not authorized to modify this revision" });
      }

      // Get the customer to verify it exists and belongs to this store
      const customer = await storage.getCustomer(customerId);
      if (!customer || customer.storeId !== store.id) {
        return res.status(404).json({ message: "Customer not found" });
      }

      // Update the order with the new customer ID
      try {
        console.log('Tentando atualizar cliente da revisão usando Supabase...');
        console.log('Dados para atualização:', { customerId });

        // Atualizar a revisão com o novo ID de cliente
        const { data: result, error } = await supabase
          .from('order_revisions')
          .update({
            customer_id: customerId
          })
          .eq('id', revisionId)
          .select('*')
          .single();

        if (error) {
          console.error('Erro ao atualizar cliente da revisão usando Supabase:', error);
          // Fallback para o método de armazenamento padrão
          await storage.updateOrderRevision(revisionId, {
            customerId
          });
        } else {
          console.log('Cliente da revisão atualizado com sucesso usando Supabase:', result);
        }
      } catch (supabaseError) {
        console.error('Erro ao usar Supabase para atualizar cliente da revisão, usando método padrão:', supabaseError);
        // Fallback para o método de armazenamento padrão
        await storage.updateOrderRevision(revisionId, {
          customerId
        });
      }

      // Fetch updated revision
      const updatedRevision = await storage.getOrderRevision(revisionId);

      // Get customer details to include in response
      const updatedCustomer = await storage.getCustomer(customerId);

      return res.status(200).json({
        ...updatedRevision,
        customer: updatedCustomer
      });
    } catch (error) {
      console.error('Error updating customer for revision:', error);
      return res.status(500).json({
        message: "Failed to update customer",
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // Update delivery fee for a revision
  app.patch("/api/orders/revisions/:revisionId/delivery-fee", requireAuth, async (req: any, res) => {
    try {
      const revisionId = parseInt(req.params.revisionId);
      if (isNaN(revisionId)) {
        return res.status(400).json({ message: "ID de revisão inválido" });
      }

      // Extrair a taxa de entrega do corpo da requisição
      const { deliveryFee } = req.body;

      if (deliveryFee === undefined) {
        return res.status(400).json({ message: "Taxa de entrega é obrigatória" });
      }

      console.log('Atualizando taxa de entrega para revisão ID:', revisionId);
      console.log('Dados da requisição:', req.body);
      console.log('Taxa de entrega extraída:', deliveryFee, 'Tipo:', typeof deliveryFee);

      // Converter para número
      const numericDeliveryFee = typeof deliveryFee === 'string'
        ? parseFloat(deliveryFee.replace(',', '.'))
        : parseFloat(String(deliveryFee));

      if (isNaN(numericDeliveryFee)) {
        return res.status(400).json({ message: "Taxa de entrega inválida" });
      }

      console.log('Taxa de entrega convertida para número:', numericDeliveryFee);

      // Obter a loja atual para verificar autorização
      const firebaseUid = req.user.uid;
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(404).json({ message: "Loja não encontrada" });
      }

      // Obter os detalhes da revisão atual
      const revision = await storage.getOrderRevision(revisionId);
      if (!revision) {
        return res.status(404).json({ message: "Revisão não encontrada" });
      }

      // Verificar se a revisão pertence a um pedido desta loja
      const order = await storage.getOrder(revision.orderId);
      if (!order || order.storeId !== store.id) {
        return res.status(403).json({ message: "Não autorizado a modificar esta revisão" });
      }

      // Recalcular o subtotal com base nos itens
      const revisionItems = await storage.getOrderRevisionItems(revisionId);

      // Recalcular o subtotal com base nos itens atuais
      let calculatedSubtotal = 0;
      if (revisionItems && revisionItems.length > 0) {
        calculatedSubtotal = revisionItems.reduce((sum, item) => sum + (item.subtotal || 0), 0);
      } else {
        calculatedSubtotal = revision.subtotal || 0;
      }

      console.log('Subtotal recalculado com base nos itens:', calculatedSubtotal);

      // Verificar se o desconto é do tipo percentual e recalcular se necessário
      let discount = revision.discount || 0;
      const discountType = revision.discountType || 'fixed';
      const originalPercentage = revision.originalPercentage || 0;

      // Recalcular o desconto se for do tipo percentual
      if (discountType === 'percentage' && originalPercentage > 0) {
        console.log('Recalculando desconto percentual após atualização da taxa de entrega...');
        console.log('Dados para recálculo:', {
          subtotal: calculatedSubtotal,
          originalPercentage,
          discountType,
          descontoAnterior: discount
        });

        // Calcular o novo valor do desconto mantendo a mesma porcentagem
        discount = (originalPercentage / 100) * calculatedSubtotal;
        discount = parseFloat(discount.toFixed(2));

        console.log(`Desconto recalculado: ${originalPercentage}% de ${calculatedSubtotal} = ${discount}`);
      }

      // Garantir que o desconto não seja maior que o subtotal
      discount = Math.min(discount, calculatedSubtotal);

      // Calcular o novo total
      const numericTotal = Math.max(0, calculatedSubtotal - discount + numericDeliveryFee);

      console.log('Novo total calculado:', numericTotal);
      console.log('(Subtotal:', calculatedSubtotal, '- Desconto:', discount, '+ Taxa de entrega:', numericDeliveryFee, ')');

      // Preparar os dados para atualização
      const updateData: any = {
        deliveryFee: numericDeliveryFee,
        total: numericTotal
      };

      // Se for desconto percentual, atualizar o valor do desconto e manter o percentual original
      if (discountType === 'percentage' && originalPercentage > 0) {
        updateData.discount = discount;
        updateData.originalPercentage = originalPercentage;
      }

      // Atualizar a revisão
      const updatedRevision = await storage.updateOrderRevision(revisionId, updateData);

      if (!updatedRevision) {
        return res.status(500).json({ message: "Falha ao atualizar revisão" });
      }

      // ✅ APLICAR MÉTODO UNIFICADO APÓS ATUALIZAÇÃO DA TAXA DE ENTREGA
      console.log('🔄 Aplicando método unificado após atualização da taxa de entrega...');
      const items = await storage.getOrderRevisionItems(revisionId);
      const revisionAfterFeeUpdate = await storage.getOrderRevision(revisionId);
      const unifiedSuccess = await updateRevisionTotalsUnified(revisionId, items, revisionAfterFeeUpdate);

      if (!unifiedSuccess) {
        console.error('❌ Falha no método unificado após atualização da taxa de entrega');
      } else {
        console.log('✅ Método unificado aplicado com sucesso após atualização da taxa');
      }

      // Buscar revisão final atualizada
      const finalRevision = await storage.getOrderRevision(revisionId);

      console.log('Revisão atualizada com sucesso');
      return res.status(200).json({
        ...finalRevision,
        recalculationPerformed: unifiedSuccess
      });
    } catch (error) {
      console.error('Erro ao processar requisição:', error);
      return res.status(500).json({
        message: "Falha ao atualizar taxa de entrega",
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // Set a revision as the current active one for an order
  app.patch("/api/orders/revisions/:revisionId/set-current", requireAuth, async (req: any, res) => {
    try {
      const revisionId = parseInt(req.params.revisionId);
      console.log('Setting revision ID as current:', revisionId);

      const firebaseUid = req.user.uid;
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      // Get the revision
      const revision = await storage.getOrderRevision(revisionId);
      if (!revision) {
        return res.status(404).json({ message: "Revision not found" });
      }

      // Get the order to verify ownership
      const order = await storage.getOrder(revision.orderId);
      if (!order || order.storeId !== store.id) {
        return res.status(403).json({ message: "Not authorized to modify this revision" });
      }

      // Set this revision as current
      await storage.setCurrentRevision(revisionId, revision.orderId);

      return res.status(200).json({
        message: "Revision set as current successfully",
        revisionId
      });
    } catch (error) {
      console.error('Error setting current revision:', error);
      return res.status(500).json({
        message: "Failed to set current revision",
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // Set original order as current (unmark all revisions)
  app.patch("/api/orders/:orderId/set-original-as-current", requireAuth, async (req: any, res) => {
    try {
      const orderId = parseInt(req.params.orderId);
      console.log('Setting original order as current for order ID:', orderId);

      const firebaseUid = req.user.uid;
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      // Get the order to verify ownership
      const order = await storage.getOrder(orderId);
      if (!order || order.storeId !== store.id) {
        return res.status(403).json({ message: "Not authorized to modify this order" });
      }

      // Unmark all revisions for this order by setting isCurrent to false
      await storage.clearCurrentRevisions(orderId);

      return res.status(200).json({
        message: "Original order set as current",
        orderId
      });
    } catch (error) {
      console.error('Error setting original order as current:', error);
      return res.status(500).json({
        message: "Failed to set original order as current",
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // Update discount for a revision
  app.patch("/api/orders/revisions/:revisionId/discount", requireAuth, updateRevisionDiscount);

  // Endpoint temporário para testar recálculo isolado
  app.post("/api/debug/test-recalculation/:revisionId", requireAuth, async (req: Request, res: Response) => {
    try {
      const revisionId = parseInt(req.params.revisionId);

      console.log('=== TESTE DE RECÁLCULO ISOLADO ===');
      const result = await recalculateAndUpdateRevision(revisionId);

      return res.json({
        revisionId,
        recalculationSuccess: result,
        message: result ? 'Recálculo executado com sucesso' : 'Falha no recálculo'
      });
    } catch (error) {
      console.error('Erro no teste de recálculo:', error);
      return res.status(500).json({ error: error instanceof Error ? error.message : String(error) });
    }
  });

  // Endpoint para forçar correção de subtotal de uma revisão específica
  app.post("/api/debug/fix-subtotal/:revisionId", requireAuth, async (req: Request, res: Response) => {
    try {
      const revisionId = parseInt(req.params.revisionId);

      console.log('=== CORREÇÃO FORÇADA DE SUBTOTAL ===');

      // Buscar itens da revisão
      const items = await storage.getOrderRevisionItems(revisionId);
      if (!items || items.length === 0) {
        return res.status(404).json({ message: "Itens da revisão não encontrados" });
      }

      // Calcular subtotal correto (soma dos subtotais armazenados)
      const correctSubtotal = items.reduce((sum, item) => sum + (item.subtotal || 0), 0);

      console.log(`📊 Subtotal correto calculado: ${correctSubtotal}`);

      // Buscar revisão atual
      const revision = await storage.getOrderRevision(revisionId);
      if (!revision) {
        return res.status(404).json({ message: "Revisão não encontrada" });
      }

      // Recalcular desconto e total com o subtotal correto
      let correctDiscount = revision.discount || 0;
      if (revision.discountType === 'percentage' && revision.originalPercentage) {
        correctDiscount = (correctSubtotal * revision.originalPercentage) / 100;
      }

      const correctTotal = correctSubtotal - correctDiscount + (revision.deliveryFee || 0);

      // Atualizar no banco
      const updateData = {
        subtotal: correctSubtotal,
        discount: correctDiscount,
        total: correctTotal
      };

      console.log(`💾 Forçando atualização:`, updateData);

      const updatedRevision = await storage.updateOrderRevision(revisionId, updateData);

      if (updatedRevision) {
        // Verificar se foi salvo corretamente
        const verificationRevision = await storage.getOrderRevision(revisionId);

        return res.json({
          revisionId,
          success: true,
          message: 'Subtotal corrigido com sucesso',
          valoresAnteriores: {
            subtotal: revision.subtotal,
            discount: revision.discount,
            total: revision.total
          },
          valoresCorrigidos: updateData,
          valoresVerificados: {
            subtotal: verificationRevision?.subtotal,
            discount: verificationRevision?.discount,
            total: verificationRevision?.total
          }
        });
      } else {
        return res.status(500).json({ message: "Falha ao atualizar revisão" });
      }
    } catch (error) {
      console.error('Erro na correção forçada:', error);
      return res.status(500).json({ error: error instanceof Error ? error.message : String(error) });
    }
  });

  // Endpoint temporário para debug de valores de revisão
  app.get("/api/debug/revision/:revisionId", requireAuth, async (req: Request, res: Response) => {
    try {
      const revisionId = parseInt(req.params.revisionId);

      // Buscar dados da revisão
      const revision = await storage.getOrderRevision(revisionId);
      if (!revision) {
        return res.status(404).json({ message: "Revisão não encontrada" });
      }

      // Buscar itens da revisão
      const items = await storage.getOrderRevisionItems(revisionId);

      // Analisar cada item detalhadamente
      console.log('=== ANÁLISE DETALHADA DOS ITENS ===');
      items.forEach((item: any, index: number) => {
        const calculatedSubtotal = (item.unitPrice || 0) * (item.quantity || 0);
        const storedSubtotal = item.subtotal || 0;
        const difference = storedSubtotal - calculatedSubtotal;

        console.log(`Item ${index + 1} (ID: ${item.id}):`);
        console.log(`  Nome: ${item.productName}`);
        console.log(`  Quantidade: ${item.quantity}`);
        console.log(`  Preço unitário: ${item.unitPrice}`);
        console.log(`  Subtotal calculado: ${calculatedSubtotal}`);
        console.log(`  Subtotal armazenado: ${storedSubtotal}`);
        console.log(`  Diferença (variações): ${difference}`);
        console.log(`  Variações: ${JSON.stringify(item.selectedVariations)}`);
        console.log('  ---');
      });

      // Calcular subtotal baseado nos itens - DUAS FORMAS
      const calculatedSubtotalFromUnitPrice = items.reduce((sum: number, item: any) => {
        const itemSubtotal = (item.unitPrice || 0) * (item.quantity || 0);
        return sum + itemSubtotal;
      }, 0);

      const calculatedSubtotalFromStoredSubtotal = items.reduce((sum: number, item: any) => {
        return sum + (item.subtotal || 0);
      }, 0);

      const totalVariationsValue = calculatedSubtotalFromStoredSubtotal - calculatedSubtotalFromUnitPrice;

      console.log('=== RESUMO DOS CÁLCULOS ===');
      console.log('Subtotal sem variações:', calculatedSubtotalFromUnitPrice);
      console.log('Subtotal com variações:', calculatedSubtotalFromStoredSubtotal);
      console.log('Valor total das variações:', totalVariationsValue);
      console.log('Percentual das variações:', calculatedSubtotalFromUnitPrice > 0 ? ((totalVariationsValue / calculatedSubtotalFromUnitPrice) * 100).toFixed(2) + '%' : '0%');

      // Calcular desconto
      let calculatedDiscount = 0;
      if (revision.discountType === 'percentage' && revision.originalPercentage) {
        calculatedDiscount = (calculatedSubtotal * revision.originalPercentage) / 100;
      } else {
        calculatedDiscount = revision.discount || 0;
      }

      // Usar o subtotal dos itens armazenados (que pode incluir variações)
      const correctSubtotal = calculatedSubtotalFromStoredSubtotal;

      // Recalcular desconto com o subtotal correto
      if (revision.discountType === 'percentage' && revision.originalPercentage) {
        calculatedDiscount = (correctSubtotal * revision.originalPercentage) / 100;
      }

      // Calcular total correto
      const calculatedTotal = correctSubtotal - calculatedDiscount + (revision.deliveryFee || 0);

      return res.json({
        revisionId,
        valoresNoBanco: {
          subtotal: revision.subtotal,
          discount: revision.discount,
          deliveryFee: revision.deliveryFee,
          total: revision.total,
          discountType: revision.discountType,
          originalPercentage: revision.originalPercentage
        },
        valoresCalculados: {
          subtotalFromUnitPrice: calculatedSubtotalFromUnitPrice,
          subtotalFromStoredSubtotal: calculatedSubtotalFromStoredSubtotal,
          subtotalCorreto: correctSubtotal,
          discount: calculatedDiscount,
          deliveryFee: revision.deliveryFee || 0,
          total: calculatedTotal
        },
        diferencas: {
          subtotal: Math.abs((revision.subtotal || 0) - correctSubtotal),
          discount: Math.abs((revision.discount || 0) - calculatedDiscount),
          total: Math.abs((revision.total || 0) - calculatedTotal)
        },
        itens: items.map(item => ({
          id: item.id,
          name: item.productName,
          price: item.price,
          quantity: item.quantity,
          subtotalItem: item.subtotal,
          subtotalCalculado: (item.price || 0) * (item.quantity || 0)
        }))
      });
    } catch (error) {
      console.error('Erro no debug de revisão:', error);
      return res.status(500).json({ error: error instanceof Error ? error.message : String(error) });
    }
  });

  // Rota administrativa para corrigir cálculos de todas as revisões
  app.post("/api/admin/fix-revision-calculations", requireAuth, async (req: any, res) => {
    try {
      // Verificar se o usuário tem acesso de administrador
      const firebaseUid = req.user.uid;
      const user = await storage.getUserByFirebaseUid(firebaseUid);

      if (!user) {
        return res.status(403).json({ message: "Acesso negado" });
      }

      console.log(`🔧 Iniciando correção de cálculos para todas as revisões. Solicitado por: ${user.id} (${user.username || 'Desconhecido'})`);

      // Buscar todas as revisões
      const { data: revisions, error } = await supabase
        .from('order_revisions')
        .select('id')
        .order('id');

      if (error) {
        console.error('Erro ao buscar revisões:', error);
        return res.status(500).json({ message: "Erro ao buscar revisões", error: error.message });
      }

      if (!revisions || revisions.length === 0) {
        return res.status(200).json({
          message: "Nenhuma revisão encontrada para corrigir",
          correctedCount: 0,
          requestedBy: user.id,
          timestamp: new Date().toISOString()
        });
      }

      console.log(`📊 Encontradas ${revisions.length} revisões para verificar`);

      let correctedCount = 0;
      let errorCount = 0;

      // Processar cada revisão usando a nova função centralizada
      for (const revision of revisions) {
        try {
          const success = await recalculateAndUpdateRevision(revision.id);
          if (success) {
            correctedCount++;
          } else {
            errorCount++;
          }
        } catch (error) {
          console.error(`Erro ao processar revisão ${revision.id}:`, error);
          errorCount++;
        }
      }

      console.log(`✅ Processamento concluído: ${correctedCount} revisões corrigidas, ${errorCount} erros`);

      return res.status(200).json({
        message: "Correção de cálculos concluída",
        totalRevisions: revisions.length,
        correctedCount,
        errorCount,
        requestedBy: user.id,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Erro ao corrigir cálculos de revisões:', error);
      return res.status(500).json({
        message: "Falha ao corrigir cálculos de revisões",
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // Remove coupon from revision
  app.delete("/api/orders/revisions/:revisionId/coupon", requireAuth, async (req: any, res) => {
    try {
      const revisionId = parseInt(req.params.revisionId);
      console.log('Removing coupon from revision ID:', revisionId);

      const firebaseUid = req.user.uid;
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      // Get the revision
      const revision = await storage.getOrderRevision(revisionId);
      if (!revision) {
        return res.status(404).json({ message: "Revision not found" });
      }

      // Get the order to verify ownership
      const order = await storage.getOrder(revision.orderId);
      if (!order || order.storeId !== store.id) {
        return res.status(403).json({ message: "Not authorized to modify this revision" });
      }

      // Verificar se há um cupom aplicado
      if (!revision.couponId && !revision.couponCode) {
        return res.status(400).json({ message: "No coupon applied to this revision" });
      }

      // Recalcular o total sem o desconto
      const subtotal = revision.subtotal || 0;
      const deliveryFee = revision.deliveryFee || 0;
      const total = subtotal + deliveryFee;

      // Atualizar a revisão removendo o cupom e o desconto
      try {
        const { data: updatedRevision, error } = await supabase
          .from('order_revisions')
          .update({
            coupon_id: null,
            coupon_code: null,
            coupon_type: null,
            discount: 0,
            total: total
          })
          .eq('id', revisionId)
          .select('*')
          .single();

        if (error) {
          console.error('Erro ao remover cupom da revisão usando Supabase:', error);
          throw error;
        }

        console.log('Cupom removido com sucesso da revisão:', updatedRevision);
        return res.status(200).json(updatedRevision);
      } catch (error) {
        console.error('Erro ao remover cupom da revisão:', error);
        return res.status(500).json({
          message: "Failed to remove coupon from revision",
          error: error instanceof Error ? error.message : String(error)
        });
      }
    } catch (error) {
      console.error('Error removing coupon from revision:', error);
      return res.status(500).json({
        message: "Failed to remove coupon from revision",
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // Delete order revision
  app.delete("/api/orders/revisions/:revisionId", requireAuth, async (req: any, res) => {
    try {
      const revisionId = parseInt(req.params.revisionId);
      console.log('Deleting revision ID:', revisionId);

      const firebaseUid = req.user.uid;
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      // Get the revision
      const revision = await storage.getOrderRevision(revisionId);
      if (!revision) {
        return res.status(404).json({ message: "Revision not found" });
      }

      // Get the order to verify ownership
      const order = await storage.getOrder(revision.orderId);
      if (!order || order.storeId !== store.id) {
        return res.status(403).json({ message: "Not authorized to delete this revision" });
      }

      // Verificar se é a revisão atual
      if (revision.isCurrent) {
        // Se for a atual, vamos configurar o pedido original como atual
        await storage.clearCurrentRevisions(revision.orderId);

        // Não é necessário definir outra revisão como atual,
        // pois o pedido original será mostrado por padrão quando
        // não houver revisões marcadas como atuais
        console.log("Removendo a revisão atual, o pedido original será exibido");
      }

      // Delete the revision
      const success = await storage.deleteOrderRevision(revisionId);

      if (!success) {
        return res.status(500).json({ message: "Failed to delete revision" });
      }

      return res.status(200).json({
        message: "Revision deleted successfully",
        revisionId,
        orderId: revision.orderId
      });
    } catch (error) {
      console.error('Error deleting revision:', error);
      return res.status(500).json({
        message: "Failed to delete revision",
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // Customer routes
  // Create a new customer
  app.post("/api/customers", requireAuth, async (req: any, res) => {
    try {
      const firebaseUid = req.user.uid;
      console.log('Creating customer for Firebase UID:', firebaseUid);

      const store = await storage.getStoreByFirebaseUid(firebaseUid);

      if (!store) {
        console.error('Store not found for Firebase UID:', firebaseUid);
        return res.status(404).json({ message: "Store not found" });
      }

      console.log('Store found:', store.id);

      // Extract customer data from request body
      const { name, email, countryCode, phone } = req.body;

      // Validate required fields
      if (!name) {
        return res.status(400).json({ message: "Name is required" });
      }

      // Create customer data object
      const customerData = {
        name,
        email: email || null,
        countryCode: countryCode || "+55",
        phone: phone || null,
        storeId: store.id
      };

      console.log('Creating customer with data:', customerData);

      // Create the customer
      const customer = await storage.createCustomer(customerData);

      if (!customer) {
        console.error('Failed to create customer, null response from storage');
        return res.status(500).json({ message: "Failed to create customer" });
      }

      console.log('Customer created successfully:', customer);
      return res.status(201).json(customer);
    } catch (error) {
      console.error('Error creating customer:', error);
      return res.status(500).json({ message: "Failed to create customer", error: String(error) });
    }
  });

  app.get("/api/customers", requireAuth, async (req: any, res) => {
    try {
      const firebaseUid = req.user.uid;
      const store = await storage.getStoreByFirebaseUid(firebaseUid);

      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      // Check if there's a search query
      const searchQuery = req.query.search as string;

      if (searchQuery && searchQuery.trim()) {
        console.log('Searching customers with query:', searchQuery);

        // Get all customers and filter them by name or email
        const allCustomers = await storage.getCustomersByStoreId(store.id);
        const filteredCustomers = allCustomers.filter(customer => {
          const nameMatch = customer.name && customer.name.toLowerCase().includes(searchQuery.toLowerCase());
          const emailMatch = customer.email && customer.email.toLowerCase().includes(searchQuery.toLowerCase());
          const phoneMatch = customer.phone && customer.phone.includes(searchQuery);

          return nameMatch || emailMatch || phoneMatch;
        });

        console.log(`Found ${filteredCustomers.length} customers matching "${searchQuery}"`);
        return res.status(200).json(filteredCustomers);
      } else {
        // Return all customers if no search query
        const customers = await storage.getCustomersByStoreId(store.id);
        return res.status(200).json(customers);
      }
    } catch (error) {
      console.error('Error getting customers:', error);
      return res.status(500).json({ message: "Failed to get customers" });
    }
  });

  // Get customer by ID
  app.get("/api/customers/:id", requireAuth, async (req: any, res) => {
    try {
      const firebaseUid = req.user.uid;
      const customerId = parseInt(req.params.id);

      if (isNaN(customerId)) {
        return res.status(400).json({ message: "Invalid customer ID" });
      }

      console.log('Fetching customer with ID:', customerId);

      const store = await storage.getStoreByFirebaseUid(firebaseUid);

      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      const customer = await storage.getCustomer(customerId);

      if (!customer) {
        return res.status(404).json({ message: "Customer not found" });
      }

      // Verify the customer belongs to this store
      if (customer.storeId !== store.id) {
        return res.status(403).json({ message: "Not authorized to access this customer" });
      }

      return res.status(200).json(customer);
    } catch (error) {
      console.error('Error fetching customer by ID:', error);
      return res.status(500).json({ message: "Failed to get customer details" });
    }
  });

  // Update customer details
  app.patch("/api/customers/:id", requireAuth, async (req: any, res) => {
    try {
      const firebaseUid = req.user.uid;
      const customerId = parseInt(req.params.id);

      if (isNaN(customerId)) {
        return res.status(400).json({ message: "Invalid customer ID" });
      }

      console.log('Updating customer with ID:', customerId);
      console.log('Request body:', req.body);

      const store = await storage.getStoreByFirebaseUid(firebaseUid);

      if (!store) {
        console.error('Store not found for Firebase UID:', firebaseUid);
        return res.status(404).json({ message: "Store not found" });
      }

      console.log('Store found:', store.id);

      // Verify the customer belongs to this store
      const customer = await storage.getCustomer(customerId);

      if (!customer) {
        console.error('Customer not found with ID:', customerId);
        return res.status(404).json({ message: "Customer not found" });
      }

      if (customer.storeId !== store.id) {
        console.error('Customer belongs to store', customer.storeId, 'not to', store.id);
        return res.status(403).json({ message: "Customer does not belong to this store" });
      }

      console.log('Customer found:', customer);

      // Only allow updating name, email, countryCode and phone fields
      const { name, email, countryCode, phone } = req.body;
      const customerUpdate: Partial<typeof customer> = {};

      if (name !== undefined) customerUpdate.name = name;
      if (email !== undefined) customerUpdate.email = email;
      if (countryCode !== undefined) customerUpdate.countryCode = countryCode;
      if (phone !== undefined) customerUpdate.phone = phone;

      console.log('Customer update data:', customerUpdate);

      const updatedCustomer = await storage.updateCustomer(customerId, customerUpdate);

      if (!updatedCustomer) {
        console.error('Failed to update customer, null response from storage');
        return res.status(500).json({ message: "Failed to update customer" });
      }

      console.log('Customer updated successfully:', updatedCustomer);
      return res.status(200).json(updatedCustomer);
    } catch (error) {
      console.error('Error updating customer:', error);
      return res.status(500).json({ message: "Failed to update customer", error: String(error) });
    }
  });

  // Get orders by customer ID
  app.get("/api/customers/:id/orders", requireAuth, async (req: any, res) => {
    try {
      const firebaseUid = req.user.uid;
      const customerId = parseInt(req.params.id);

      if (isNaN(customerId)) {
        return res.status(400).json({ message: "Invalid customer ID" });
      }

      console.log('Fetching orders for customer ID:', customerId);

      const store = await storage.getStoreByFirebaseUid(firebaseUid);

      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      // Verify the customer belongs to this store
      const customer = await storage.getCustomer(customerId);

      if (!customer || customer.storeId !== store.id) {
        return res.status(404).json({ message: "Customer not found" });
      }

      // Get orders for this customer
      const orders = await storage.getOrdersByCustomerId(customerId);

      // Add customer info to each order
      const ordersWithDetails = await Promise.all(orders.map(async (order) => {
        const items = await storage.getOrderItemsByOrderId(order.id);

        // For each item, get the product details
        const itemsWithProducts = await Promise.all(items.map(async (item) => {
          const product = await storage.getProduct(item.productId);
          return {
            ...item,
            product
          };
        }));

        return {
          ...order,
          items: itemsWithProducts
        };
      }));

      return res.status(200).json(ordersWithDetails);
    } catch (error) {
      console.error('Error getting customer orders:', error);
      return res.status(500).json({ message: "Failed to get customer orders" });
    }
  });

  // Analytics routes
  app.get("/api/analytics/dashboard", requireAuth, async (req: any, res) => {
    try {
      const firebaseUid = req.user.uid;
      console.log('Getting analytics dashboard for Firebase UID:', firebaseUid);

      const store = await storage.getStoreByFirebaseUid(firebaseUid);

      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      // Get monthly visit count
      const visitCount = await storage.getMonthlyVisitCount(store.id);

      // Get monthly order count
      const orderCount = await storage.getMonthlyOrderCount(store.id);

      // Calculate monthly revenue
      const orders = await storage.getOrdersByStoreId(store.id);
      const now = new Date();
      const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

      const monthlyOrders = orders.filter(order => new Date(order.createdAt) >= firstDayOfMonth);
      const revenue = monthlyOrders.reduce((sum, order) => sum + order.total, 0);

      return res.status(200).json({
        visitCount,
        orderCount,
        revenue
      });
    } catch (error) {
      console.error('Error getting analytics dashboard:', error);
      return res.status(500).json({ message: "Failed to get analytics data" });
    }
  });

  // ===== Rotas para Variações de Produto =====

  // Obter todas as variações de um produto
  app.get("/api/products/:productId/variations", requireAuth, async (req: any, res) => {
    try {
      const firebaseUid = req.user.uid;
      const productId = Number(req.params.productId);

      // Verificar acesso ao produto
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(403).json({ error: "Loja não encontrada" });
      }

      const product = await storage.getProduct(productId);
      if (!product || product.storeId !== store.id) {
        return res.status(403).json({ error: "Acesso negado a este produto" });
      }

      // Usar apenas as variações do campo JSONB do produto
      if (product.hasVariations && product.variations && Array.isArray(product.variations) && product.variations.length > 0) {
        console.log("Usando variações do campo JSONB do produto:", product.variations.length);

        // Mapeia as variações do campo JSONB para o formato esperado
        const formattedVariations = product.variations.map((variation: any) => {
          return {
            id: variation.id || String(Math.random()), // ID aleatório para facilitar o mapeamento no frontend
            name: variation.name,
            description: variation.description || "",
            required: variation.required || false,
            multipleChoice: variation.multipleChoice || false,
            maxSelections: variation.maxSelections || 1,
            minSelections: variation.minSelections || 0,
            options: Array.isArray(variation.options) ? variation.options.map((option: any) => ({
              id: option.id || String(Math.random()), // Gera ID temporário se não existir
              name: option.name,
              precoAdicional: option.price || option.precoAdicional || 0
            })) : []
          };
        });

        return res.json(formattedVariations);
      }
      // Se não encontrar variações, retorna array vazio
      else {
        console.log("Nenhuma variação encontrada para o produto:", productId);
        return res.json([]);
      }
    } catch (error) {
      console.error("Erro ao obter variações de produto:", error);
      return res.status(500).json({ error: "Erro interno do servidor" });
    }
  });

  // Criar uma nova variação para um produto
  app.post("/api/products/:productId/variations", requireAuth, async (req: any, res) => {
    try {
      const firebaseUid = req.user.uid;

      const productId = Number(req.params.productId);
      const { name, description, required, maxSelections, minSelections } = req.body;

      // Verificar acesso ao produto
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(403).json({ error: "Loja não encontrada" });
      }

      const product = await storage.getProduct(productId);
      if (!product || product.storeId !== store.id) {
        return res.status(403).json({ error: "Acesso negado a este produto" });
      }

      // Criar a variação
      const variation = await storage.createProductVariation({
        productId,
        name,
        description,
        required: required || false,
        maxSelections: maxSelections || 1,
        minSelections: minSelections || 0
      });

      // Atualizar o produto para indicar que tem variações
      if (!product.hasVariations) {
        await storage.updateProduct(productId, { hasVariations: true });
      }

      return res.status(201).json(variation);
    } catch (error) {
      console.error("Erro ao criar variação de produto:", error);
      return res.status(500).json({ error: "Erro interno do servidor" });
    }
  });

  // Atualizar uma variação existente
  app.put("/api/variations/:variationId", requireAuth, async (req: any, res) => {
    try {
      const firebaseUid = req.user.uid;

      const variationId = Number(req.params.variationId);
      const { name, description, required, maxSelections, minSelections, productId } = req.body;

      // Verificar acesso ao produto
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(403).json({ error: "Loja não encontrada" });
      }

      const product = await storage.getProduct(Number(productId));
      if (!product || product.storeId !== store.id) {
        return res.status(403).json({ error: "Acesso negado a este produto" });
      }

      // Obter a variação para verificar o produto
      const variations = await storage.getProductVariationsByProductId(product.id);
      const variation = variations.find(v => v.id === variationId);

      if (!variation) {
        return res.status(404).json({ error: "Variação não encontrada" });
      }

      // Atualizar a variação
      const updatedVariation = await storage.updateProductVariation(variationId, {
        name,
        description,
        required,
        maxSelections,
        minSelections
      });

      if (!updatedVariation) {
        return res.status(404).json({ error: "Erro ao atualizar variação" });
      }

      return res.json(updatedVariation);
    } catch (error) {
      console.error("Erro ao atualizar variação de produto:", error);
      return res.status(500).json({ error: "Erro interno do servidor" });
    }
  });

  // Excluir uma variação
  app.delete("/api/variations/:variationId", requireAuth, async (req: any, res) => {
    try {
      const firebaseUid = req.user.uid;

      const variationId = Number(req.params.variationId);
      const productId = Number(req.body.productId);

      // Verificar acesso ao produto
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(403).json({ error: "Loja não encontrada" });
      }

      const product = await storage.getProduct(productId);
      if (!product || product.storeId !== store.id) {
        return res.status(403).json({ error: "Acesso negado a este produto" });
      }

      // Obter a variação para verificar o produto
      const variations = await storage.getProductVariationsByProductId(product.id);
      const variation = variations.find(v => v.id === variationId);

      if (!variation) {
        return res.status(404).json({ error: "Variação não encontrada" });
      }

      // Excluir a variação (as opções serão excluídas automaticamente devido à restrição ON DELETE CASCADE)
      const deleted = await storage.deleteProductVariation(variationId);

      if (!deleted) {
        return res.status(500).json({ error: "Erro ao excluir variação" });
      }

      // Verificar se o produto ainda tem outras variações
      const remainingVariations = await storage.getProductVariationsByProductId(product.id);
      if (remainingVariations.length === 0) {
        // Se não houver mais variações, atualizar o produto
        await storage.updateProduct(product.id, { hasVariations: false });
      }

      return res.json({ success: true });
    } catch (error) {
      console.error("Erro ao excluir variação de produto:", error);
      return res.status(500).json({ error: "Erro interno do servidor" });
    }
  });

  // ===== Rotas para Opções de Variação =====

  // Obter todas as opções de uma variação
  app.get("/api/variations/:variationId/options", requireAuth, async (req: any, res) => {
    try {
      const firebaseUid = req.user.uid;

      const variationId = Number(req.params.variationId);
      const productId = Number(req.query.productId);

      // Verificar acesso ao produto
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(403).json({ error: "Loja não encontrada" });
      }

      const product = await storage.getProduct(productId);
      if (!product || product.storeId !== store.id) {
        return res.status(403).json({ error: "Acesso negado a este produto" });
      }

      // Obter a variação para verificar o produto
      const variations = await storage.getProductVariationsByProductId(product.id);
      const variation = variations.find(v => v.id === variationId);

      if (!variation) {
        return res.status(404).json({ error: "Variação não encontrada" });
      }

      const options = await storage.getVariationOptionsByVariationId(variationId);
      return res.json(options);
    } catch (error) {
      console.error("Erro ao obter opções de variação:", error);
      return res.status(500).json({ error: "Erro interno do servidor" });
    }
  });

  // Criar uma nova opção para uma variação
  app.post("/api/variations/:variationId/options", requireAuth, async (req, res) => {
    try {
      const firebaseUid = req.user.uid;

      const variationId = Number(req.params.variationId);
      const { name, price, productId } = req.body;

      // Verificar acesso ao produto
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(403).json({ error: "Loja não encontrada" });
      }

      const product = await storage.getProduct(Number(productId));
      if (!product || product.storeId !== store.id) {
        return res.status(403).json({ error: "Acesso negado a este produto" });
      }

      // Obter a variação para verificar o produto
      const variations = await storage.getProductVariationsByProductId(product.id);
      const variation = variations.find(v => v.id === variationId);

      if (!variation) {
        return res.status(404).json({ error: "Variação não encontrada" });
      }

      // Criar a opção
      const option = await storage.createVariationOption({
        variationId,
        name,
        price: price || 0
      });

      return res.status(201).json(option);
    } catch (error) {
      console.error("Erro ao criar opção de variação:", error);
      return res.status(500).json({ error: "Erro interno do servidor" });
    }
  });

  // Atualizar uma opção existente
  app.put("/api/options/:optionId", requireAuth, async (req, res) => {
    try {
      const firebaseUid = req.user.uid;

      const optionId = Number(req.params.optionId);
      const { name, price, variationId, productId } = req.body;

      // Verificar acesso ao produto
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(403).json({ error: "Loja não encontrada" });
      }

      const product = await storage.getProduct(Number(productId));
      if (!product || product.storeId !== store.id) {
        return res.status(403).json({ error: "Acesso negado a este produto" });
      }

      // Obter a variação para verificar o produto
      const variations = await storage.getProductVariationsByProductId(product.id);
      const variation = variations.find(v => v.id === Number(variationId));

      if (!variation) {
        return res.status(404).json({ error: "Variação não encontrada" });
      }

      // Atualizar a opção
      const updatedOption = await storage.updateVariationOption(optionId, {
        name,
        price
      });

      if (!updatedOption) {
        return res.status(404).json({ error: "Erro ao atualizar opção" });
      }

      return res.json(updatedOption);
    } catch (error) {
      console.error("Erro ao atualizar opção de variação:", error);
      return res.status(500).json({ error: "Erro interno do servidor" });
    }
  });

  // Excluir uma opção
  app.delete("/api/options/:optionId", requireAuth, async (req, res) => {
    try {
      const firebaseUid = req.user.uid;

      const optionId = Number(req.params.optionId);
      const variationId = Number(req.body.variationId);
      const productId = Number(req.body.productId);

      // Verificar acesso ao produto
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(403).json({ error: "Loja não encontrada" });
      }

      const product = await storage.getProduct(productId);
      if (!product || product.storeId !== store.id) {
        return res.status(403).json({ error: "Acesso negado a este produto" });
      }

      // Obter a variação para verificar o produto
      const variations = await storage.getProductVariationsByProductId(product.id);
      const variation = variations.find(v => v.id === variationId);

      if (!variation) {
        return res.status(404).json({ error: "Variação não encontrada" });
      }

      // Excluir a opção
      const deleted = await storage.deleteVariationOption(optionId);

      if (!deleted) {
        return res.status(500).json({ error: "Erro ao excluir opção" });
      }

      return res.json({ success: true });
    } catch (error) {
      console.error("Erro ao excluir opção de variação:", error);
      return res.status(500).json({ error: "Erro interno do servidor" });
    }
  });

  // Endpoints de Cupons

  // POST /admin/cupons - Cadastrar novo cupom
  app.post("/admin/cupons", requireAuth, requireCoupons, async (req: any, res) => {
    try {
      const firebaseUid = req.user.uid;
      console.log('Dados recebidos do frontend:', req.body);

      // Verificar se o usuário tem uma loja
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(404).json({ message: "Loja não encontrada" });
      }

      // Mapear os campos do frontend para o formato esperado pelo banco de dados
      const couponData = {
        storeId: store.id,
        code: req.body.code,
        tipo: req.body.tipo, // Será mapeado para 'type' no schema
        valor: req.body.valor, // Será mapeado para 'value' no schema
        minimoCompra: req.body.minimoCompra, // Será mapeado para 'min_purchase' no schema
        dataValidade: req.body.dataValidade, // Será mapeado para 'expiration_date' no schema
        usoUnico: req.body.usoUnico, // Será mapeado para 'single_use' no schema
        ativo: req.body.ativo // Será mapeado para 'active' no schema
      };

      console.log('Dados mapeados para o banco:', couponData);

      // Verificar se já existe um cupom com o mesmo código para esta loja
      const existingCoupon = await storage.getCouponByCode(store.id, couponData.code);
      if (existingCoupon) {
        return res.status(400).json({ message: "Já existe um cupom com este código" });
      }

      // Criar o cupom
      const coupon = await storage.createCoupon(couponData);
      console.log('Cupom criado com sucesso:', coupon);

      return res.status(201).json(coupon);
    } catch (error) {
      console.error("Erro ao criar cupom:", error);
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Dados inválidos", errors: error.errors });
      }
      return res.status(500).json({ message: "Erro ao criar cupom" });
    }
  });

  // PUT /admin/cupons/:id - Editar cupom existente
  app.put("/admin/cupons/:id", requireAuth, async (req: any, res) => {
    try {
      const firebaseUid = req.user.uid;
      const couponId = parseInt(req.params.id);
      console.log('Dados recebidos para atualização do cupom:', req.body);

      if (isNaN(couponId)) {
        return res.status(400).json({ message: "ID de cupom inválido" });
      }

      // Verificar se o usuário tem uma loja
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(404).json({ message: "Loja não encontrada" });
      }

      // Verificar se o cupom existe e pertence à loja do usuário
      const coupon = await storage.getCoupon(couponId);
      if (!coupon) {
        return res.status(404).json({ message: "Cupom não encontrado" });
      }

      if (coupon.storeId !== store.id) {
        return res.status(403).json({ message: "Acesso negado a este cupom" });
      }

      // Mapear os campos do frontend para o formato esperado pelo banco de dados
      const updateData = {
        code: req.body.code,
        tipo: req.body.tipo, // Será mapeado para 'type' no schema
        valor: req.body.valor, // Será mapeado para 'value' no schema
        minimoCompra: req.body.minimoCompra, // Será mapeado para 'min_purchase' no schema
        dataValidade: req.body.dataValidade, // Será mapeado para 'expiration_date' no schema
        usoUnico: req.body.usoUnico, // Será mapeado para 'single_use' no schema
        ativo: req.body.ativo // Será mapeado para 'active' no schema
      };

      // Remover campos undefined
      Object.keys(updateData).forEach(key =>
        updateData[key] === undefined && delete updateData[key]
      );

      console.log('Dados mapeados para atualização:', updateData);

      // Se o código estiver sendo alterado, verificar se já existe outro cupom com este código
      if (updateData.code && updateData.code !== coupon.code) {
        const existingCoupon = await storage.getCouponByCode(store.id, updateData.code);
        if (existingCoupon && existingCoupon.id !== couponId) {
          return res.status(400).json({ message: "Já existe um cupom com este código" });
        }
      }

      // Atualizar o cupom
      const updatedCoupon = await storage.updateCoupon(couponId, updateData);
      console.log('Cupom atualizado com sucesso:', updatedCoupon);

      return res.status(200).json(updatedCoupon);
    } catch (error) {
      console.error("Erro ao atualizar cupom:", error);
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Dados inválidos", errors: error.errors });
      }
      return res.status(500).json({ message: "Erro ao atualizar cupom" });
    }
  });

  // GET /admin/cupons - Listar cupons
  app.get("/admin/cupons", requireAuth, async (req: any, res) => {
    try {
      const firebaseUid = req.user.uid;

      // Verificar se o usuário tem uma loja
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(404).json({ message: "Loja não encontrada" });
      }

      // Obter todos os cupons da loja
      const coupons = await storage.getCouponsByStoreId(store.id);

      return res.status(200).json(coupons);
    } catch (error) {
      console.error("Erro ao listar cupons:", error);
      return res.status(500).json({ message: "Erro ao listar cupons" });
    }
  });

  // GET /admin/cupons/:id - Obter cupom específico por ID
  app.get("/admin/cupons/:id", requireAuth, async (req: any, res) => {
    try {
      const firebaseUid = req.user.uid;
      const couponId = parseInt(req.params.id);

      if (isNaN(couponId)) {
        return res.status(400).json({ message: "ID de cupom inválido" });
      }

      console.log('Buscando cupom com ID:', couponId, 'para Firebase UID:', firebaseUid);

      // Verificar se o usuário tem uma loja
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(404).json({ message: "Loja não encontrada" });
      }

      // Buscar o cupom
      const coupon = await storage.getCoupon(couponId);
      if (!coupon) {
        console.log('Cupom não encontrado com ID:', couponId);
        return res.status(404).json({ message: "Cupom não encontrado" });
      }

      // Verificar se o cupom pertence à loja do usuário
      if (coupon.storeId !== store.id) {
        console.log('Cupom não pertence à loja do usuário:', coupon.storeId, 'vs', store.id);
        return res.status(403).json({ message: "Acesso negado a este cupom" });
      }

      console.log('Cupom encontrado:', coupon);
      return res.status(200).json(coupon);
    } catch (error) {
      console.error("Erro ao buscar cupom:", error);
      return res.status(500).json({ message: "Erro ao buscar cupom" });
    }
  });

  // PATCH /admin/cupons/:id/status - Ativar/desativar cupom
  app.patch("/admin/cupons/:id/status", requireAuth, async (req: any, res) => {
    try {
      const firebaseUid = req.user.uid;
      const couponId = parseInt(req.params.id);

      if (isNaN(couponId)) {
        return res.status(400).json({ message: "ID de cupom inválido" });
      }

      // Verificar se o body contém o campo 'active'
      const { active } = req.body;
      if (active === undefined) {
        return res.status(400).json({ message: "Campo 'active' é obrigatório" });
      }

      // Verificar se o usuário tem uma loja
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(404).json({ message: "Loja não encontrada" });
      }

      // Verificar se o cupom existe e pertence à loja do usuário
      const coupon = await storage.getCoupon(couponId);
      if (!coupon) {
        return res.status(404).json({ message: "Cupom não encontrado" });
      }

      if (coupon.storeId !== store.id) {
        return res.status(403).json({ message: "Acesso negado a este cupom" });
      }

      // Atualizar o status do cupom
      const updatedCoupon = await storage.updateCouponStatus(couponId, active);

      return res.status(200).json(updatedCoupon);
    } catch (error) {
      console.error("Erro ao atualizar status do cupom:", error);
      return res.status(500).json({ message: "Erro ao atualizar status do cupom" });
    }
  });

  // Rotas administrativas para correção de descontos
  app.post("/api/admin/fix-percentage-discounts", requireAuth, fixPercentageDiscountsEndpoint);
  app.post("/api/admin/fix-all-revisions", requireAuth, fixAllRevisionsEndpoint);

  // Order Payment routes - Rotas para gerenciar recebimentos

  // Criar um novo recebimento para um pedido
  app.post("/api/orders/:orderId/payments", requireAuth, async (req: any, res) => {
    try {
      console.log('=== CRIANDO RECEBIMENTO ===');
      console.log('Firebase UID:', req.user.uid);
      console.log('Order ID:', req.params.orderId);
      console.log('Request body:', req.body);

      const firebaseUid = req.user.uid;
      const orderId = parseInt(req.params.orderId);
      const { valor, data, metodo, observacao } = req.body;

      if (isNaN(orderId)) {
        console.log('Erro: ID do pedido inválido');
        return res.status(400).json({ message: "ID do pedido inválido" });
      }

      if (!valor || !data || !metodo) {
        console.log('Erro: Dados obrigatórios faltando', { valor, data, metodo });
        return res.status(400).json({ message: "Dados obrigatórios: valor, data, metodo" });
      }

      // Verificar se o usuário tem acesso ao pedido
      console.log('Buscando loja para Firebase UID:', firebaseUid);
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        console.log('Erro: Loja não encontrada');
        return res.status(404).json({ message: "Loja não encontrada" });
      }
      console.log('Loja encontrada:', store.id);

      console.log('Buscando pedido:', orderId);
      const order = await storage.getOrder(orderId);
      if (!order || order.storeId !== store.id) {
        console.log('Erro: Pedido não encontrado ou não pertence à loja', { order: !!order, storeId: order?.storeId, expectedStoreId: store.id });
        return res.status(404).json({ message: "Pedido não encontrado" });
      }
      console.log('Pedido encontrado:', order.id);

      // Buscar o usuário para obter o ID
      console.log('Buscando usuário para Firebase UID:', firebaseUid);
      const user = await storage.getUserByFirebaseUid(firebaseUid);
      if (!user) {
        console.log('Erro: Usuário não encontrado');
        return res.status(404).json({ message: "Usuário não encontrado" });
      }
      console.log('Usuário encontrado:', user.id);

      // Validar se o valor não excede o valor pendente
      const existingPayments = await storage.getOrderPaymentsByOrderId(orderId);
      const totalReceived = existingPayments.reduce((sum, payment) => sum + payment.valor, 0);

      // Determinar o total a ser usado (revisão ativa ou pedido original)
      const { data: activeRevisionData } = await supabase
        .from('order_revisions')
        .select('total')
        .eq('order_id', orderId)
        .eq('is_current', true)
        .single();

      const orderTotal = activeRevisionData?.total || order.total;
      const pendingAmount = Math.max(0, orderTotal - totalReceived);
      const tolerance = 0.01; // Tolerância de 1 centavo

      console.log('Validação de valor:', {
        orderTotal,
        totalReceived,
        pendingAmount,
        newPaymentValue: parseFloat(valor),
        tolerance,
        exceedsLimit: parseFloat(valor) > (pendingAmount + tolerance)
      });

      if (parseFloat(valor) > (pendingAmount + tolerance)) {
        return res.status(400).json({
          message: "Valor do recebimento excede o valor pendente do pedido",
          details: {
            orderTotal,
            totalReceived,
            pendingAmount,
            requestedValue: parseFloat(valor),
            excess: parseFloat(valor) - pendingAmount
          }
        });
      }

      // Criar o recebimento
      const paymentData = {
        orderId: orderId,
        valor: parseFloat(valor),
        data: new Date(data),
        metodo: metodo,
        observacao: observacao || null,
        createdBy: user.id
      };
      console.log('Dados do pagamento a serem criados:', paymentData);

      const payment = await storage.createOrderPayment(paymentData);
      console.log('Recebimento criado com sucesso:', payment);

      // Recalcular automaticamente o status de pagamento após criar recebimento
      await storage.atualizarStatusPagamentoAutomatico(orderId);
      console.log('Status de pagamento recalculado automaticamente após criação do recebimento');

      return res.status(201).json({
        message: "Recebimento criado com sucesso",
        payment: payment
      });
    } catch (error) {
      console.error('Erro ao criar recebimento:', error);
      return res.status(500).json({ message: "Erro interno do servidor" });
    }
  });

  // Listar recebimentos de um pedido
  app.get("/api/orders/:orderId/payments", requireAuth, async (req: any, res) => {
    try {
      const firebaseUid = req.user.uid;
      const orderId = parseInt(req.params.orderId);

      if (isNaN(orderId)) {
        return res.status(400).json({ message: "ID do pedido inválido" });
      }

      // Verificar se o usuário tem acesso ao pedido
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(404).json({ message: "Loja não encontrada" });
      }

      const order = await storage.getOrder(orderId);
      if (!order || order.storeId !== store.id) {
        return res.status(404).json({ message: "Pedido não encontrado" });
      }

      // Buscar recebimentos
      const payments = await storage.getOrderPaymentsByOrderId(orderId);

      return res.status(200).json(payments);
    } catch (error) {
      console.error('Erro ao buscar recebimentos:', error);
      return res.status(500).json({ message: "Erro interno do servidor" });
    }
  });

  // Atualizar um recebimento existente
  app.patch("/api/orders/:orderId/payments/:paymentId", requireAuth, async (req: any, res) => {
    try {
      console.log('=== ATUALIZANDO RECEBIMENTO ===');
      console.log('Firebase UID:', req.user.uid);
      console.log('Order ID:', req.params.orderId);
      console.log('Payment ID:', req.params.paymentId);
      console.log('Request body:', req.body);

      const firebaseUid = req.user.uid;
      const orderId = parseInt(req.params.orderId);
      const paymentId = parseInt(req.params.paymentId);
      const { valor, data, metodo, observacao } = req.body;

      if (isNaN(orderId) || isNaN(paymentId)) {
        console.log('Erro: ID do pedido ou recebimento inválido');
        return res.status(400).json({ message: "ID do pedido ou recebimento inválido" });
      }

      // Verificar se o usuário tem acesso ao pedido
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        console.log('Erro: Loja não encontrada para o Firebase UID:', firebaseUid);
        return res.status(404).json({ message: "Loja não encontrada" });
      }

      const order = await storage.getOrder(orderId);
      if (!order || order.storeId !== store.id) {
        console.log('Erro: Pedido não encontrado ou não pertence à loja');
        return res.status(404).json({ message: "Pedido não encontrado" });
      }

      // Verificar se o recebimento existe e pertence ao pedido
      const existingPayment = await storage.getOrderPayment(paymentId);
      if (!existingPayment || existingPayment.orderId !== orderId) {
        console.log('Erro: Recebimento não encontrado ou não pertence ao pedido');
        return res.status(404).json({ message: "Recebimento não encontrado" });
      }

      // Validar dados de entrada
      if (!valor || !data || !metodo) {
        console.log('Erro: Dados obrigatórios não fornecidos');
        return res.status(400).json({ message: "Valor, data e método são obrigatórios" });
      }

      // Buscar todos os recebimentos do pedido (exceto o que está sendo atualizado)
      const allPayments = await storage.getOrderPayments(orderId);
      const otherPayments = allPayments.filter(p => p.id !== paymentId);
      const totalOtherPayments = otherPayments.reduce((sum, p) => sum + p.valor, 0);

      // Buscar revisão ativa se existir
      const { data: activeRevisionData } = await supabase
        .from('order_revisions')
        .select('total')
        .eq('order_id', orderId)
        .eq('is_current', true)
        .single();

      const orderTotal = activeRevisionData?.total || order.total;
      const pendingAmount = Math.max(0, orderTotal - totalOtherPayments);
      const tolerance = 0.01; // Tolerância de 1 centavo

      console.log('Validação de valor para atualização:', {
        orderTotal,
        totalOtherPayments,
        pendingAmount,
        newPaymentValue: parseFloat(valor),
        tolerance,
        exceedsLimit: parseFloat(valor) > (pendingAmount + tolerance)
      });

      if (parseFloat(valor) > (pendingAmount + tolerance)) {
        return res.status(400).json({
          message: "Valor do recebimento excede o valor pendente do pedido",
          details: {
            orderTotal,
            totalOtherPayments,
            pendingAmount,
            requestedValue: parseFloat(valor),
            excess: parseFloat(valor) - pendingAmount
          }
        });
      }

      // Atualizar o recebimento
      const updatedPaymentData = {
        valor: parseFloat(valor),
        data: new Date(data),
        metodo: metodo,
        observacao: observacao || null
      };
      console.log('Dados do pagamento a serem atualizados:', updatedPaymentData);

      const success = await storage.updateOrderPayment(paymentId, updatedPaymentData);
      if (!success) {
        return res.status(500).json({ message: "Erro ao atualizar recebimento" });
      }

      console.log('Recebimento atualizado com sucesso:', paymentId);

      // Recalcular automaticamente o status de pagamento após atualização
      await storage.atualizarStatusPagamentoAutomatico(orderId);
      console.log('Status de pagamento recalculado automaticamente após atualização do recebimento');

      return res.status(200).json({
        message: "Recebimento atualizado com sucesso"
      });
    } catch (error) {
      console.error('Erro ao atualizar recebimento:', error);
      return res.status(500).json({ message: "Erro interno do servidor" });
    }
  });

  // Excluir um recebimento
  app.delete("/api/orders/:orderId/payments/:paymentId", requireAuth, async (req: any, res) => {
    try {
      console.log('=== EXCLUINDO RECEBIMENTO ===');
      console.log('Firebase UID:', req.user.uid);
      console.log('Order ID:', req.params.orderId);
      console.log('Payment ID:', req.params.paymentId);

      const firebaseUid = req.user.uid;
      const orderId = parseInt(req.params.orderId);
      const paymentId = parseInt(req.params.paymentId);

      if (isNaN(orderId) || isNaN(paymentId)) {
        console.log('Erro: ID do pedido ou recebimento inválido');
        return res.status(400).json({ message: "ID do pedido ou recebimento inválido" });
      }

      // Verificar se o usuário tem acesso ao pedido
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(404).json({ message: "Loja não encontrada" });
      }

      const order = await storage.getOrder(orderId);
      if (!order || order.storeId !== store.id) {
        return res.status(404).json({ message: "Pedido não encontrado" });
      }

      // Verificar se o recebimento existe e pertence ao pedido
      const payments = await storage.getOrderPaymentsByOrderId(orderId);
      const payment = payments.find(p => p.id === paymentId);
      if (!payment) {
        return res.status(404).json({ message: "Recebimento não encontrado" });
      }

      // Excluir o recebimento
      const success = await storage.deleteOrderPayment(paymentId);
      if (!success) {
        return res.status(500).json({ message: "Erro ao excluir recebimento" });
      }

      // Recalcular automaticamente o status de pagamento após exclusão
      await storage.atualizarStatusPagamentoAutomatico(orderId);

      console.log('Recebimento excluído com sucesso:', paymentId);

      return res.status(200).json({
        message: "Recebimento excluído com sucesso"
      });
    } catch (error) {
      console.error('Erro ao excluir recebimento:', error);
      return res.status(500).json({ message: "Erro interno do servidor" });
    }
  });

  // Atualizar status de pagamento manualmente (força recálculo)
  app.post("/api/orders/:orderId/payments/recalculate", requireAuth, async (req: any, res) => {
    try {
      console.log('=== RECÁLCULO MANUAL DE STATUS DE PAGAMENTO ===');
      console.log('Firebase UID:', req.user.uid);
      console.log('Order ID:', req.params.orderId);

      const firebaseUid = req.user.uid;
      const orderId = parseInt(req.params.orderId);

      if (isNaN(orderId)) {
        console.log('Erro: ID do pedido inválido');
        return res.status(400).json({ message: "ID do pedido inválido" });
      }

      // Verificar se o usuário tem acesso ao pedido
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        console.log('Erro: Loja não encontrada para o Firebase UID:', firebaseUid);
        return res.status(404).json({ message: "Loja não encontrada" });
      }

      const order = await storage.getOrder(orderId);
      if (!order || order.storeId !== store.id) {
        console.log('Erro: Pedido não encontrado ou não pertence à loja');
        return res.status(404).json({ message: "Pedido não encontrado" });
      }

      console.log('Status atual do pedido:', order.paymentStatus);

      // Buscar revisão ativa se existir (usando a mesma lógica da função atualizarStatusPagamentoAutomatico)
      const { data: activeRevisionData, error: revisionError } = await supabase
        .from('order_revisions')
        .select('id, total, payment_status')
        .eq('order_id', orderId)
        .eq('is_current', true)
        .single();

      if (activeRevisionData) {
        console.log('Revisão ativa encontrada:', {
          id: activeRevisionData.id,
          total: activeRevisionData.total,
          paymentStatus: activeRevisionData.payment_status
        });
      } else {
        console.log('Nenhuma revisão ativa encontrada para o pedido');
      }

      // Forçar recálculo do status de pagamento
      console.log('Iniciando recálculo automático do status...');
      await storage.atualizarStatusPagamentoAutomatico(orderId);

      // Buscar o pedido atualizado
      const updatedOrder = await storage.getOrder(orderId);
      console.log('Status após recálculo:', updatedOrder?.paymentStatus);

      // Buscar revisão ativa atualizada se existir
      const { data: updatedActiveRevisionData } = await supabase
        .from('order_revisions')
        .select('id, total, payment_status')
        .eq('order_id', orderId)
        .eq('is_current', true)
        .single();

      if (updatedActiveRevisionData) {
        console.log('Status da revisão ativa após recálculo:', updatedActiveRevisionData.payment_status);
      }

      return res.status(200).json({
        message: "Status de pagamento recalculado com sucesso",
        paymentStatus: updatedOrder?.paymentStatus,
        revisionPaymentStatus: updatedActiveRevisionData?.payment_status
      });
    } catch (error) {
      console.error('Erro ao recalcular status de pagamento:', error);
      return res.status(500).json({ message: "Erro interno do servidor" });
    }
  });

  // Funções auxiliares para o dashboard
  async function getSummaryData(storeId: number) {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    // Buscar dados usando Supabase
    const { data: ordersData } = await supabase
      .from('orders')
      .select(`
        id, status, total, payment_status, receiving_date, receiving_time,
        order_revisions!inner(total, payment_status, is_current)
      `)
      .eq('store_id', storeId);

    const { data: monthlyRevenue } = await supabase
      .from('orders')
      .select('total, order_revisions(total, payment_status, is_current)')
      .eq('store_id', storeId)
      .gte('created_at', startOfMonth.toISOString())
      .or('payment_status.eq.recebido,order_revisions.payment_status.eq.recebido');

    // Contar pedidos pendentes
    const pendingStatuses = ['pending', 'preparing', 'ready'];
    const pendingOrders = ordersData?.filter(order =>
      pendingStatuses.includes(order.status)
    ).length || 0;

    // Contar pedidos confirmados
    const confirmedOrders = ordersData?.filter(order =>
      order.status === 'confirmed'
    ).length || 0;

    // ATUALIZADO: Calcular receita mensal baseada apenas em pedidos confirmados e entregues
    const currentDate = new Date();
    const firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);

    const { data: monthlyConfirmedOrders } = await supabase
      .from('orders')
      .select(`
        total,
        order_revisions(total, is_current)
      `)
      .eq('store_id', storeId)
      .in('status', ['confirmed', 'delivered'])
      .gte('created_at', firstDayOfMonth.toISOString());

    let monthlyRevenueTotal = 0;
    monthlyConfirmedOrders?.forEach(order => {
      const activeRevision = order.order_revisions?.find((r: any) => r.is_current);
      const total = activeRevision?.total || order.total;
      monthlyRevenueTotal += total;
    });

    // Buscar próxima entrega (baseado em pedidos confirmados, considerando revisões)
    const { data: allConfirmedOrders, error: confirmedOrdersError } = await supabase
      .from('orders')
      .select(`
        id, receiving_date, receiving_time,
        customers(name),
        order_revisions(receiving_date, receiving_time, is_current)
      `)
      .eq('store_id', storeId)
      .eq('status', 'confirmed');

    console.log('Next delivery - Confirmed orders found:', allConfirmedOrders?.length || 0);
    if (confirmedOrdersError) {
      console.error('Error fetching confirmed orders:', confirmedOrdersError);
    }

    let nextDelivery = null;
    if (allConfirmedOrders && allConfirmedOrders.length > 0) {
      // Use local timezone for today's date comparison
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const todayString = today.toISOString().split('T')[0];

      console.log('Next delivery - Today date for comparison:', todayString);

      // Processar todos os pedidos para encontrar a próxima entrega
      const upcomingDeliveries = allConfirmedOrders
        .map(order => {
          const activeRevision = order.order_revisions?.find((r: any) => r.is_current);
          const deliveryDate = activeRevision?.receiving_date || order.receiving_date;
          const deliveryTime = activeRevision?.receiving_time || order.receiving_time;

          // Ensure we have a valid delivery date
          if (!deliveryDate) {
            console.warn(`Order ${order.id} has no delivery date`);
            return null;
          }

          // Parse the delivery date properly
          let parsedDeliveryDate;
          try {
            parsedDeliveryDate = new Date(deliveryDate);
            if (isNaN(parsedDeliveryDate.getTime())) {
              console.warn(`Order ${order.id} has invalid delivery date:`, deliveryDate);
              return null;
            }
          } catch (error) {
            console.warn(`Order ${order.id} delivery date parsing error:`, error);
            return null;
          }

          const dateForComparison = parsedDeliveryDate.toISOString().split('T')[0];

          console.log(`Order ${order.id}: delivery date ${dateForComparison}, time ${deliveryTime}, customer: ${order.customers?.name}`);

          return {
            order,
            deliveryDate,
            deliveryTime,
            dateForComparison,
            parsedDeliveryDate
          };
        })
        .filter(item => {
          if (!item) return false;
          // Include today and future dates
          const isUpcoming = item.dateForComparison >= todayString;
          console.log(`Order ${item.order.id}: ${item.dateForComparison} >= ${todayString} = ${isUpcoming}`);
          return isUpcoming;
        })
        .sort((a, b) => {
          // Primeiro ordenar por data
          const dateCompare = a.dateForComparison.localeCompare(b.dateForComparison);
          if (dateCompare !== 0) return dateCompare;
          // Depois por horário (tratar horários vazios)
          const timeA = a.deliveryTime || '00:00';
          const timeB = b.deliveryTime || '00:00';
          return timeA.localeCompare(timeB);
        });

      console.log('Next delivery - Upcoming deliveries found:', upcomingDeliveries.length);

      if (upcomingDeliveries.length > 0) {
        const nextDeliveryItem = upcomingDeliveries[0];

        try {
          const formattedDate = nextDeliveryItem.parsedDeliveryDate.toLocaleDateString('pt-BR');
          const formattedTime = nextDeliveryItem.deliveryTime || 'Horário não definido';

          nextDelivery = {
            id: nextDeliveryItem.order.id,
            customerName: nextDeliveryItem.order.customers?.name || 'Cliente',
            deliveryTime: `${formattedDate} às ${formattedTime}`
          };

          console.log('Next delivery found:', nextDelivery);
        } catch (error) {
          console.error('Error formatting next delivery:', error);
        }
      } else {
        console.log('No upcoming deliveries found');
      }
    }

    return {
      pendingOrders,
      confirmedOrders,
      monthlyRevenue: monthlyRevenueTotal,
      nextDelivery
    };
  }

  async function getPendingOrders(storeId: number) {
    const { data: ordersData } = await supabase
      .from('orders')
      .select(`
        id, status, total, receiving_date, receiving_time,
        customers(name, whatsapp),
        order_revisions(total, receiving_date, receiving_time, is_current),
        order_items(product_name, quantity)
      `)
      .eq('store_id', storeId)
      .in('status', ['pending', 'preparing', 'ready'])
      .order('receiving_date', { ascending: true });

    return ordersData?.map(order => {
      const activeRevision = order.order_revisions?.find((r: any) => r.is_current);
      const deliveryDate = activeRevision?.receiving_date || order.receiving_date;
      const deliveryTime = activeRevision?.receiving_time || order.receiving_time;
      const total = activeRevision?.total || order.total;

      // Concatenar nomes dos produtos
      const products = order.order_items?.map((item: any) => item.product_name).join(', ') || '';

      const isLate = deliveryDate < new Date().toISOString().split('T')[0];

      return {
        id: order.id,
        customerName: order.customers?.name || 'Cliente',
        customerPhone: order.customers?.whatsapp || '',
        products,
        total,
        status: order.status,
        deliveryTime: `${deliveryDate} ${deliveryTime}`,
        isLate
      };
    }) || [];
  }

  async function getSiteActivity(storeId: number) {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const today = new Date().toISOString().split('T')[0];

    // Visitas mensais
    const { data: monthlyVisitsData } = await supabase
      .from('store_visits')
      .select('id')
      .eq('store_id', storeId)
      .gte('created_at', startOfMonth.toISOString());

    // Visitas hoje
    const { data: todayVisitsData } = await supabase
      .from('store_visits')
      .select('id')
      .eq('store_id', storeId)
      .gte('created_at', `${today}T00:00:00.000Z`);

    // Pedidos para calcular conversão
    const { data: monthlyOrdersData } = await supabase
      .from('orders')
      .select('id')
      .eq('store_id', storeId)
      .gte('created_at', startOfMonth.toISOString());

    // Clientes recorrentes
    const { data: customerOrdersData } = await supabase
      .from('orders')
      .select('customer_id')
      .eq('store_id', storeId);

    const customerOrderCounts = customerOrdersData?.reduce((acc: any, order) => {
      acc[order.customer_id] = (acc[order.customer_id] || 0) + 1;
      return acc;
    }, {}) || {};

    const returningCustomers = Object.values(customerOrderCounts).filter((count: any) => count > 1).length;

    const monthlyVisits = monthlyVisitsData?.length || 0;
    const todayVisits = todayVisitsData?.length || 0;
    const monthlyOrders = monthlyOrdersData?.length || 0;
    const conversionRate = monthlyVisits > 0 ? (monthlyOrders / monthlyVisits) * 100 : 0;

    return {
      monthlyVisits,
      todayVisits,
      conversionRate: Math.round(conversionRate * 100) / 100,
      returningCustomers
    };
  }

  async function getTopProducts(storeId: number) {
    console.log('Top products calculation for store:', storeId);

    // Primeiro, buscar pedidos confirmados e entregues
    const { data: ordersData } = await supabase
      .from('orders')
      .select('id, status, created_at')
      .eq('store_id', storeId)
      .in('status', ['confirmed', 'delivered']);

    console.log('Orders found (confirmed/delivered):', ordersData?.length || 0);

    if (!ordersData || ordersData.length === 0) {
      return [];
    }

    const orderIds = ordersData.map(order => order.id);
    const productStats: Record<string, { totalSold: number; revenue: number }> = {};

    // Para cada pedido, verificar se há revisão atual
    for (const order of ordersData) {
      // Buscar revisão atual do pedido
      const { data: currentRevision } = await supabase
        .from('order_revisions')
        .select('id')
        .eq('order_id', order.id)
        .eq('is_current', true)
        .single();

      let itemsToProcess = [];

      if (currentRevision) {
        // Usar itens da revisão atual
        const { data: revisionItems } = await supabase
          .from('order_revision_items')
          .select('product_name, quantity, subtotal')
          .eq('revision_id', currentRevision.id);

        itemsToProcess = revisionItems || [];
        console.log(`Order ${order.id}: Using revision items (${itemsToProcess.length} items)`);
      } else {
        // Usar itens originais do pedido
        const { data: orderItems } = await supabase
          .from('order_items')
          .select('product_name, quantity, subtotal')
          .eq('order_id', order.id);

        itemsToProcess = orderItems || [];
        console.log(`Order ${order.id}: Using original items (${itemsToProcess.length} items)`);
      }

      // Processar itens
      itemsToProcess.forEach((item: any) => {
        const productName = item.product_name;
        if (!productName) return;

        if (!productStats[productName]) {
          productStats[productName] = { totalSold: 0, revenue: 0 };
        }

        productStats[productName].totalSold += item.quantity || 0;
        productStats[productName].revenue += item.subtotal || 0;
      });
    }

    console.log('Product stats calculated:', Object.keys(productStats).length, 'unique products');
    console.log('Sample product stats:', Object.entries(productStats).slice(0, 3));

    // Retornar top 10 produtos ordenados por quantidade vendida
    return Object.entries(productStats)
      .map(([name, stats]) => ({
        name,
        totalSold: stats.totalSold,
        revenue: stats.revenue
      }))
      .sort((a, b) => b.totalSold - a.totalSold)
      .slice(0, 10);
  }

  async function getRecentCustomers(storeId: number) {
    const { data: recentCustomersData } = await supabase
      .from('customers')
      .select(`
        id, name, whatsapp,
        orders(created_at)
      `)
      .eq('store_id', storeId)
      .order('created_at', { ascending: false })
      .limit(5);

    return recentCustomersData?.map(customer => ({
      id: customer.id,
      name: customer.name,
      phone: customer.whatsapp || '',
      lastOrderDate: customer.orders?.[0]?.created_at?.split('T')[0] || '',
      totalOrders: customer.orders?.length || 0
    })) || [];
  }



  async function getFinancialMetrics(storeId: number) {
    console.log('[DEBUG] getFinancialMetrics - Starting for store:', storeId);

    const now = new Date();
    const currentMonth = now.toISOString().slice(0, 7); // YYYY-MM
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1).toISOString().slice(0, 7);
    const last6Months = new Date(now.getFullYear(), now.getMonth() - 5, 1).toISOString().slice(0, 7);

    console.log('[DEBUG] getFinancialMetrics - Date range:', { currentMonth, lastMonth, last6Months });

    // Buscar dados dos últimos 6 meses para análise de tendências
    // ATUALIZADO: Filtrar apenas pedidos confirmados e entregues
    // CORRIGIDO: Removida referência a products que não existe na FK
    const { data: fetchedOrdersData, error: ordersError } = await supabase
      .from('orders')
      .select(`
        id, created_at, total, payment_status, status,
        order_revisions(total, payment_status, is_current),
        order_items(product_id, quantity, price, product_name)
      `)
      .eq('store_id', storeId)
      .in('status', ['confirmed', 'delivered'])
      .gte('created_at', `${last6Months}-01`);

    if (ordersError) {
      console.error('[DEBUG] getFinancialMetrics - Error fetching orders:', ordersError);
    }

    console.log('[DEBUG] getFinancialMetrics - Found orders:', fetchedOrdersData?.length || 0);

    // Processar dados mensais
    const monthlyData = {};
    const productRevenue = {};
    let totalRevenue = 0;
    let totalOrders = 0;
    let avgOrderValue = 0;

    fetchedOrdersData?.forEach(order => {
      const orderMonth = order.created_at.slice(0, 7);
      const activeRevision = order.order_revisions?.find((r: any) => r.is_current);
      const total = activeRevision?.total || order.total;

      // ATUALIZADO: Contar apenas pedidos confirmados e entregues (já filtrados na query)
      if (!monthlyData[orderMonth]) {
        monthlyData[orderMonth] = { revenue: 0, orders: 0 };
      }
      monthlyData[orderMonth].revenue += total;
      monthlyData[orderMonth].orders += 1;
      totalRevenue += total;
      totalOrders += 1;

      // Análise por produto
      // CORRIGIDO: Usar product_name em vez de products.name
      order.order_items?.forEach((item: any) => {
        const productName = item.product_name || 'Produto sem nome';
        if (!productRevenue[productName]) {
          productRevenue[productName] = { revenue: 0, quantity: 0 };
        }
        productRevenue[productName].revenue += item.price * item.quantity;
        productRevenue[productName].quantity += item.quantity;
      });
    });

    avgOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

    // Calcular crescimento mensal
    const currentMonthRevenue = monthlyData[currentMonth]?.revenue || 0;
    const lastMonthRevenue = monthlyData[lastMonth]?.revenue || 0;
    const monthlyGrowth = lastMonthRevenue > 0
      ? ((currentMonthRevenue - lastMonthRevenue) / lastMonthRevenue) * 100
      : 0;

    // Gerar dados para gráfico de receita mensal
    const revenueChart = [];
    for (let i = 5; i >= 0; i--) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const monthKey = date.toISOString().slice(0, 7);
      const monthName = date.toLocaleDateString('pt-BR', { month: 'short' });

      revenueChart.push({
        month: monthName,
        revenue: monthlyData[monthKey]?.revenue || 0,
        orders: monthlyData[monthKey]?.orders || 0
      });
    }

    // Top produtos mais lucrativos
    const topProductsByRevenue = Object.entries(productRevenue)
      .map(([name, data]: [string, any]) => ({
        name,
        revenue: data.revenue,
        quantity: data.quantity,
        avgPrice: data.quantity > 0 ? data.revenue / data.quantity : 0
      }))
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5);

    console.log('[DEBUG] getFinancialMetrics - topProductsByRevenue:', topProductsByRevenue);

    return {
      summary: {
        totalRevenue,
        currentMonthRevenue,
        monthlyGrowth,
        avgOrderValue,
        totalOrders
      },
      revenueChart,
      topProductsByRevenue,
      monthlyComparison: {
        current: currentMonthRevenue,
        previous: lastMonthRevenue,
        growth: monthlyGrowth
      }
    };
  }

  // Nova função para dados financeiros com filtro de período
  async function getFinancialMetricsWithPeriod(storeId: number, startDate: string, endDate: string) {
    console.log(`[DEBUG] Getting financial metrics for store ${storeId} from ${startDate} to ${endDate}`);

    // Primeiro, vamos verificar TODOS os pedidos da loja para debug
    const { data: allOrdersDebug } = await supabase
      .from('orders')
      .select('id, status, total, created_at')
      .eq('store_id', storeId);

    console.log(`[DEBUG] Total orders in store: ${allOrdersDebug?.length || 0}`);
    if (allOrdersDebug && allOrdersDebug.length > 0) {
      const statusCounts = allOrdersDebug.reduce((acc, order) => {
        acc[order.status] = (acc[order.status] || 0) + 1;
        return acc;
      }, {});
      console.log(`[DEBUG] Orders by status:`, statusCounts);

      const confirmedDelivered = allOrdersDebug.filter(o => ['confirmed', 'delivered'].includes(o.status));
      console.log(`[DEBUG] Confirmed/Delivered orders: ${confirmedDelivered.length}`);

      if (confirmedDelivered.length > 0) {
        const totalRevenue = confirmedDelivered.reduce((sum, order) => sum + (order.total || 0), 0);
        console.log(`[DEBUG] Total revenue from confirmed/delivered: R$ ${totalRevenue}`);
      }
    }

    // Buscar dados do período especificado
    // FILTRAR APENAS pedidos confirmados e entregues
    console.log(`[DEBUG] Executing Supabase query with:`, {
      storeId,
      statuses: ['confirmed', 'delivered'],
      startDate,
      endDate
    });

    // Primeiro, vamos testar uma query mais simples para verificar se há dados
    const { data: simpleOrdersTest } = await supabase
      .from('orders')
      .select('id, status, total, created_at')
      .eq('store_id', storeId)
      .in('status', ['confirmed', 'delivered']);

    console.log(`[DEBUG] Simple query test - found ${simpleOrdersTest?.length || 0} confirmed/delivered orders (any date)`);

    // Agora a query completa com filtro de período
    // CORRIGIDO: Removida referência a products que não existe na FK
    const { data: ordersData, error: ordersError } = await supabase
      .from('orders')
      .select(`
        id, created_at, total, payment_status, status,
        order_revisions(total, payment_status, is_current),
        order_items(product_id, quantity, price, product_name)
      `)
      .eq('store_id', storeId)
      .in('status', ['confirmed', 'delivered'])
      .gte('created_at', startDate)
      .lte('created_at', endDate);

    if (ordersError) {
      console.error(`[DEBUG] Error fetching orders:`, ordersError);
      console.error(`[DEBUG] Query details:`, {
        table: 'orders',
        filters: {
          store_id: storeId,
          status_in: ['confirmed', 'delivered'],
          created_at_gte: startDate,
          created_at_lte: endDate
        }
      });
    }

    console.log(`[DEBUG] Found ${ordersData?.length || 0} confirmed/delivered orders in period`);

    // Se não encontrou pedidos no período, usar todos os pedidos confirmed/delivered
    let dataToProcess = ordersData;
    if (!dataToProcess || dataToProcess.length === 0) {
      console.log(`[DEBUG] No orders found in period, using ALL confirmed/delivered orders...`);

      // CORRIGIDO: Removida referência a products que não existe na FK
      const { data: allOrdersData, error: allOrdersError } = await supabase
        .from('orders')
        .select(`
          id, created_at, total, payment_status, status,
          order_revisions(total, payment_status, is_current),
          order_items(product_id, quantity, price, product_name)
        `)
        .eq('store_id', storeId)
        .in('status', ['confirmed', 'delivered']);

      if (!allOrdersError && allOrdersData && allOrdersData.length > 0) {
        console.log(`[DEBUG] Found ${allOrdersData.length} confirmed/delivered orders (all time)`);
        dataToProcess = allOrdersData;

        // Atualizar as datas para refletir o período real dos dados
        const dates = allOrdersData.map(o => o.created_at).sort();
        startDate = dates[0];
        endDate = dates[dates.length - 1];

        console.log(`[DEBUG] Período ajustado para todos os pedidos: ${startDate} até ${endDate}`);
      }
    }

    if (dataToProcess && dataToProcess.length > 0) {
      console.log(`[DEBUG] Sample orders from query:`, dataToProcess.slice(0, 3).map(o => ({
        id: o.id,
        status: o.status,
        total: o.total,
        created_at: o.created_at,
        store_id: storeId
      })));
    }

    if (dataToProcess && dataToProcess.length > 0) {
      console.log(`[DEBUG] Sample order data:`, dataToProcess[0]);
      const totalFromPeriod = dataToProcess.reduce((sum, order) => {
        const activeRevision = order.order_revisions?.find((r: any) => r.is_current);
        const total = activeRevision?.total || order.total;
        return sum + (total || 0);
      }, 0);
      console.log(`[DEBUG] Total revenue from period: R$ ${totalFromPeriod}`);
    }

    // Processar dados
    const dailyData = {};
    const productRevenue = {};
    let totalRevenue = 0;
    let totalOrders = 0;

    dataToProcess?.forEach((order, index) => {
      console.log(`[DEBUG] Processing order ${index + 1}/${ordersData.length}:`, {
        id: order.id,
        status: order.status,
        created_at: order.created_at,
        total: order.total,
        revisions_count: order.order_revisions?.length || 0
      });

      const orderDate = order.created_at.split('T')[0]; // YYYY-MM-DD
      const activeRevision = order.order_revisions?.find((r: any) => r.is_current);
      const total = activeRevision?.total || order.total;

      console.log(`[DEBUG] Order ${order.id} - Values:`, {
        order_total: order.total,
        active_revision_total: activeRevision?.total,
        final_total: total,
        has_active_revision: !!activeRevision
      });

      // Verificar se o total é válido
      if (total === null || total === undefined || isNaN(total)) {
        console.warn(`[DEBUG] Order ${order.id} has invalid total:`, total);
        return; // Pular este pedido
      }

      // Contar apenas pedidos confirmados e entregues
      if (!dailyData[orderDate]) {
        dailyData[orderDate] = { revenue: 0, orders: 0 };
      }
      dailyData[orderDate].revenue += total;
      dailyData[orderDate].orders += 1;
      totalRevenue += total;
      totalOrders += 1;

      console.log(`[DEBUG] After processing order ${order.id}:`, {
        totalRevenue,
        totalOrders,
        dailyRevenue: dailyData[orderDate].revenue
      });

      // Análise por produto
      // CORRIGIDO: Usar product_name em vez de products.name
      order.order_items?.forEach((item: any) => {
        const productName = item.product_name || 'Produto sem nome';
        if (!productRevenue[productName]) {
          productRevenue[productName] = { revenue: 0, quantity: 0 };
        }
        productRevenue[productName].revenue += item.price * item.quantity;
        productRevenue[productName].quantity += item.quantity;
      });
    });

    const avgOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

    // Gerar dados de sparkline (últimos 30 pontos ou menos)
    const sparklineData = {
      totalRevenue: [],
      monthlyRevenue: [],
      avgOrderValue: [],
      totalOrders: []
    };

    // Ordenar datas e pegar os dados diários
    const sortedDates = Object.keys(dailyData).sort();
    const maxPoints = Math.min(sortedDates.length, 30);
    const startIndex = Math.max(0, sortedDates.length - maxPoints);

    for (let i = startIndex; i < sortedDates.length; i++) {
      const date = sortedDates[i];
      const dayData = dailyData[date];

      sparklineData.totalRevenue.push({
        value: dayData.revenue,
        date: date
      });

      sparklineData.monthlyRevenue.push({
        value: dayData.revenue,
        date: date
      });

      sparklineData.avgOrderValue.push({
        value: dayData.orders > 0 ? dayData.revenue / dayData.orders : 0,
        date: date
      });

      sparklineData.totalOrders.push({
        value: dayData.orders,
        date: date
      });
    }

    // Top produtos mais lucrativos
    const topProductsByRevenue = Object.entries(productRevenue)
      .map(([name, data]: [string, any]) => ({
        name,
        revenue: data.revenue,
        quantity: data.quantity,
        avgPrice: data.quantity > 0 ? data.revenue / data.quantity : 0
      }))
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5);

    console.log('[DEBUG] topProductsByRevenue final result:', topProductsByRevenue);

    // Calcular crescimento (comparar com período anterior)
    const periodDays = Math.ceil((new Date(endDate).getTime() - new Date(startDate).getTime()) / (24 * 60 * 60 * 1000));
    const previousStartDate = new Date(new Date(startDate).getTime() - periodDays * 24 * 60 * 60 * 1000).toISOString();
    const previousEndDate = startDate;

    const { data: previousOrdersData } = await supabase
      .from('orders')
      .select('total, order_revisions(total, is_current)')
      .eq('store_id', storeId)
      .in('status', ['confirmed', 'delivered'])
      .gte('created_at', previousStartDate)
      .lt('created_at', previousEndDate);

    let previousRevenue = 0;
    previousOrdersData?.forEach(order => {
      const activeRevision = order.order_revisions?.find((r: any) => r.is_current);
      const total = activeRevision?.total || order.total;
      previousRevenue += total;
    });

    const growth = previousRevenue > 0 ? ((totalRevenue - previousRevenue) / previousRevenue) * 100 : 0;

    // Gerar dados para gráfico de receita (agrupado por semana ou mês dependendo do período)
    const revenueChart = [];
    if (periodDays <= 30) {
      // Para períodos curtos, agrupar por semana
      const weeklyData = {};
      Object.entries(dailyData).forEach(([date, data]: [string, any]) => {
        const weekStart = getWeekStart(new Date(date));
        const weekKey = weekStart.toISOString().split('T')[0];
        if (!weeklyData[weekKey]) {
          weeklyData[weekKey] = { revenue: 0, orders: 0 };
        }
        weeklyData[weekKey].revenue += data.revenue;
        weeklyData[weekKey].orders += data.orders;
      });

      Object.entries(weeklyData)
        .sort(([a], [b]) => a.localeCompare(b))
        .forEach(([weekStart, data]: [string, any]) => {
          revenueChart.push({
            month: new Date(weekStart).toLocaleDateString('pt-BR', { day: '2-digit', month: 'short' }),
            revenue: data.revenue,
            orders: data.orders
          });
        });
    } else {
      // Para períodos longos, agrupar por mês
      const monthlyData = {};
      Object.entries(dailyData).forEach(([date, data]: [string, any]) => {
        const monthKey = date.slice(0, 7); // YYYY-MM
        if (!monthlyData[monthKey]) {
          monthlyData[monthKey] = { revenue: 0, orders: 0 };
        }
        monthlyData[monthKey].revenue += data.revenue;
        monthlyData[monthKey].orders += data.orders;
      });

      Object.entries(monthlyData)
        .sort(([a], [b]) => a.localeCompare(b))
        .forEach(([month, data]: [string, any]) => {
          revenueChart.push({
            month: new Date(month + '-01').toLocaleDateString('pt-BR', { month: 'short' }),
            revenue: data.revenue,
            orders: data.orders
          });
        });
    }

    const result = {
      summary: {
        totalRevenue,
        currentMonthRevenue: totalRevenue, // Para o período selecionado
        monthlyGrowth: growth,
        avgOrderValue,
        totalOrders
      },
      revenueChart,
      topProductsByRevenue,
      monthlyComparison: {
        current: totalRevenue,
        previous: previousRevenue,
        growth
      },
      sparklineData
    };

    console.log(`[DEBUG] Final result summary:`, {
      totalRevenue,
      totalOrders,
      avgOrderValue,
      growth,
      sparklineDataPoints: sparklineData.totalRevenue.length,
      revenueChartPoints: revenueChart.length
    });

    return result;
  }

  // Função auxiliar para obter o início da semana
  function getWeekStart(date: Date): Date {
    const d = new Date(date);
    const day = d.getDay();
    const diff = d.getDate() - day;
    return new Date(d.setDate(diff));
  }

  // Dashboard administrativo - endpoint único
  app.get("/api/dashboard", requireAuth, async (req: Request, res: Response) => {
    try {
      const userObj = req.user as any;
      console.log('Dashboard - Objeto do usuário:', userObj);

      if (!userObj || !userObj.uid) {
        return res.status(401).json({ message: "Usuário não autenticado" });
      }

      // Buscar a loja diretamente pelo Firebase UID
      console.log('Buscando loja com Firebase UID:', userObj.uid);
      const store = await storage.getStoreByFirebaseUid(userObj.uid);
      console.log('Loja encontrada:', store);

      // Se não encontrar a loja pelo UID, usar a loja ID 1 (dados existentes)
      let storeToUse = store;
      if (!storeToUse) {
        console.log('Loja não encontrada pelo UID, usando loja ID 1...');
        storeToUse = await storage.getStore(1);
        console.log('Usando loja padrão:', storeToUse);
      }

      if (!storeToUse) {
        return res.status(404).json({ message: "Nenhuma loja encontrada" });
      }

      const storeId = storeToUse.id;

      // Buscar todos os dados em paralelo
      console.log('Dashboard - Starting data fetch for store:', storeId);
      const [
        summaryData,
        pendingOrders,
        siteActivity,
        topProducts,
        recentCustomers,
        financialMetrics
      ] = await Promise.all([
        getSummaryData(storeId),
        getPendingOrders(storeId),
        getSiteActivity(storeId),
        getTopProducts(storeId),
        getRecentCustomers(storeId),
        getFinancialMetrics(storeId)
      ]);

      console.log('Dashboard - Summary data:', {
        pendingOrders: summaryData.pendingOrders,
        confirmedOrders: summaryData.confirmedOrders,
        monthlyRevenue: summaryData.monthlyRevenue,
        nextDelivery: summaryData.nextDelivery
      });

      res.json({
        summary: summaryData,
        pendingOrders,
        siteActivity,
        topProducts,
        recentCustomers,
        financialMetrics
      });
    } catch (error) {
      console.error('Erro ao carregar dashboard:', error);
      res.status(500).json({ error: 'Erro ao carregar dashboard' });
    }
  });

  // Novo endpoint para dados financeiros com filtro de período
  app.get("/api/dashboard/financial", requireAuth, async (req: Request, res: Response) => {
    try {
      const userObj = req.user as any;
      console.log('[DEBUG] Financial Dashboard - Request received');
      console.log('[DEBUG] Query params:', req.query);

      if (!userObj || !userObj.uid) {
        console.log('[DEBUG] User not authenticated');
        return res.status(401).json({ message: "Usuário não autenticado" });
      }

      // Buscar a loja
      const store = await storage.getStoreByFirebaseUid(userObj.uid);
      let storeToUse = store;
      if (!storeToUse) {
        console.log('[DEBUG] Store not found by UID, using default store 1');
        storeToUse = await storage.getStore(1);
      }

      if (!storeToUse) {
        console.log('[DEBUG] No store found');
        return res.status(404).json({ message: "Nenhuma loja encontrada" });
      }

      const storeId = storeToUse.id;
      console.log('[DEBUG] Using store ID:', storeId);
      console.log('[DEBUG] Store details:', {
        id: storeToUse.id,
        name: storeToUse.name,
        slug: storeToUse.slug
      });

      // Verificar se existem pedidos para esta loja (qualquer status)
      const { data: allStoreOrders } = await supabase
        .from('orders')
        .select('id, status, total, created_at')
        .eq('store_id', storeId)
        .limit(10);

      console.log('[DEBUG] Sample orders for this store:', {
        total_found: allStoreOrders?.length || 0,
        sample_orders: allStoreOrders?.map(o => ({
          id: o.id,
          status: o.status,
          total: o.total,
          created_at: o.created_at
        })) || []
      });

      // Obter parâmetros de período da query string
      const { period, startDate, endDate } = req.query;
      console.log('[DEBUG] Period params:', { period, startDate, endDate });

      let calculatedStartDate: string;
      let calculatedEndDate: string;

      if (startDate && endDate) {
        // Usar datas específicas fornecidas
        calculatedStartDate = startDate as string;
        calculatedEndDate = endDate as string;
      } else if (period) {
        // Calcular datas baseadas no período
        const now = new Date();
        calculatedEndDate = now.toISOString();

        switch (period) {
          case "last7Days":
            calculatedStartDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString();
            break;
          case "thisWeek":
            const startOfWeek = new Date(now);
            startOfWeek.setDate(now.getDate() - now.getDay());
            startOfWeek.setHours(0, 0, 0, 0);
            calculatedStartDate = startOfWeek.toISOString();
            break;
          case "last30Days":
            calculatedStartDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString();
            break;
          case "thisMonth":
            // CORREÇÃO: Se há pedidos com datas futuras, ajustar para incluí-los
            // Primeiro, verificar se há pedidos na loja
            const { data: sampleOrders } = await supabase
              .from('orders')
              .select('created_at')
              .eq('store_id', storeId)
              .in('status', ['confirmed', 'delivered'])
              .order('created_at', { ascending: false })
              .limit(1);

            if (sampleOrders && sampleOrders.length > 0) {
              const latestOrderDate = new Date(sampleOrders[0].created_at);
              const latestOrderYear = latestOrderDate.getFullYear();
              const latestOrderMonth = latestOrderDate.getMonth();

              // Usar o mês do pedido mais recente em vez do mês atual
              const startOfMonth = new Date(latestOrderYear, latestOrderMonth, 1);
              startOfMonth.setHours(0, 0, 0, 0);
              calculatedStartDate = startOfMonth.toISOString();

              console.log(`[DEBUG] Ajustado thisMonth para o mês dos pedidos: ${latestOrderYear}-${latestOrderMonth + 1}`);
            } else {
              // Fallback para o mês atual se não há pedidos
              const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
              startOfMonth.setHours(0, 0, 0, 0);
              calculatedStartDate = startOfMonth.toISOString();
            }
            break;
          case "last90Days":
            calculatedStartDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000).toISOString();
            break;
          case "thisQuarter":
            const quarter = Math.floor(now.getMonth() / 3);
            calculatedStartDate = new Date(now.getFullYear(), quarter * 3, 1).toISOString();
            break;
          case "last365Days":
            calculatedStartDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000).toISOString();
            break;
          case "thisYear":
            calculatedStartDate = new Date(now.getFullYear(), 0, 1).toISOString();
            break;
          default:
            // Default para este mês
            calculatedStartDate = new Date(now.getFullYear(), now.getMonth(), 1).toISOString();
        }
      } else {
        // Default para este mês
        const now = new Date();
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        startOfMonth.setHours(0, 0, 0, 0);
        calculatedStartDate = startOfMonth.toISOString();
        calculatedEndDate = now.toISOString();
      }

      console.log('[DEBUG] Financial Dashboard - Calculated dates:', {
        calculatedStartDate,
        calculatedEndDate,
        period: period || 'default'
      });

      // Verificar se as datas estão em formato válido
      const startDateObj = new Date(calculatedStartDate);
      const endDateObj = new Date(calculatedEndDate);

      console.log('[DEBUG] Date validation:', {
        startDate_valid: !isNaN(startDateObj.getTime()),
        endDate_valid: !isNaN(endDateObj.getTime()),
        startDate_formatted: startDateObj.toISOString(),
        endDate_formatted: endDateObj.toISOString(),
        period_days: Math.ceil((endDateObj.getTime() - startDateObj.getTime()) / (24 * 60 * 60 * 1000))
      });

      console.log(`Financial Dashboard - Period: ${period}, Start: ${calculatedStartDate}, End: ${calculatedEndDate}`);

      // Buscar dados financeiros com filtro de período
      console.log('[DEBUG] About to call getFinancialMetricsWithPeriod with:', {
        storeId,
        startDate: calculatedStartDate,
        endDate: calculatedEndDate
      });

      const financialMetrics = await getFinancialMetricsWithPeriod(storeId, calculatedStartDate, calculatedEndDate);

      console.log('[DEBUG] Financial metrics result:', {
        topProductsByRevenue: financialMetrics.topProductsByRevenue
      });

      res.json(financialMetrics);
    } catch (error) {
      console.error('Erro ao carregar dados financeiros:', error);
      res.status(500).json({ error: 'Erro ao carregar dados financeiros' });
    }
  });

  // Endpoint de debug para verificar pedidos na base de dados
  app.get("/api/debug/orders", requireAuth, async (req: Request, res: Response) => {
    try {
      const userObj = req.user as any;

      if (!userObj || !userObj.uid) {
        return res.status(401).json({ message: "Usuário não autenticado" });
      }

      // Buscar a loja
      const store = await storage.getStoreByFirebaseUid(userObj.uid);
      let storeToUse = store;
      if (!storeToUse) {
        storeToUse = await storage.getStore(1);
      }

      if (!storeToUse) {
        return res.status(404).json({ message: "Nenhuma loja encontrada" });
      }

      const storeId = storeToUse.id;

      // Buscar todos os pedidos da loja
      const { data: allOrders } = await supabase
        .from('orders')
        .select('id, status, total, created_at, payment_status')
        .eq('store_id', storeId)
        .order('created_at', { ascending: false })
        .limit(20);

      // Contar por status
      const statusCounts = allOrders?.reduce((acc, order) => {
        acc[order.status] = (acc[order.status] || 0) + 1;
        return acc;
      }, {}) || {};

      // Calcular receita por status
      const revenueByStatus = allOrders?.reduce((acc, order) => {
        if (!acc[order.status]) {
          acc[order.status] = 0;
        }
        acc[order.status] += order.total || 0;
        return acc;
      }, {}) || {};

      res.json({
        storeId,
        totalOrders: allOrders?.length || 0,
        statusCounts,
        revenueByStatus,
        sampleOrders: allOrders?.slice(0, 5) || []
      });
    } catch (error) {
      console.error('Erro ao buscar dados de debug:', error);
      res.status(500).json({ error: 'Erro ao buscar dados de debug' });
    }
  });

  // Endpoint para criar dados de teste (apenas para desenvolvimento)
  app.post("/api/debug/create-test-orders", requireAuth, async (req: Request, res: Response) => {
    try {
      const userObj = req.user as any;

      if (!userObj || !userObj.uid) {
        return res.status(401).json({ message: "Usuário não autenticado" });
      }

      // Buscar a loja
      const store = await storage.getStoreByFirebaseUid(userObj.uid);
      let storeToUse = store;
      if (!storeToUse) {
        storeToUse = await storage.getStore(1);
      }

      if (!storeToUse) {
        return res.status(404).json({ message: "Nenhuma loja encontrada" });
      }

      const storeId = storeToUse.id;

      // Verificar se já existem pedidos confirmados/entregues
      const { data: existingOrders } = await supabase
        .from('orders')
        .select('id')
        .eq('store_id', storeId)
        .in('status', ['confirmed', 'delivered']);

      if (existingOrders && existingOrders.length > 0) {
        return res.json({
          message: "Já existem pedidos confirmados/entregues na base",
          count: existingOrders.length
        });
      }

      // Buscar um cliente existente ou criar um de teste
      const { data: customers } = await supabase
        .from('customers')
        .select('id')
        .eq('store_id', storeId)
        .limit(1);

      let customerId = customers?.[0]?.id;

      if (!customerId) {
        // Criar cliente de teste
        const { data: newCustomer } = await supabase
          .from('customers')
          .insert({
            store_id: storeId,
            name: 'Cliente Teste',
            whatsapp: '11999999999',
            email: '<EMAIL>'
          })
          .select('id')
          .single();

        customerId = newCustomer?.id;
      }

      if (!customerId) {
        return res.status(500).json({ error: 'Não foi possível criar/encontrar cliente' });
      }

      // Criar pedidos de teste
      const testOrders = [];
      const now = new Date();

      for (let i = 0; i < 5; i++) {
        const orderDate = new Date(now.getTime() - (i * 24 * 60 * 60 * 1000)); // Últimos 5 dias
        const total = Math.floor(Math.random() * 200) + 50; // Entre R$ 50 e R$ 250

        const { data: newOrder } = await supabase
          .from('orders')
          .insert({
            store_id: storeId,
            customer_id: customerId,
            status: i % 2 === 0 ? 'confirmed' : 'delivered',
            total: total,
            subtotal: total,
            payment_method: 'pix',
            payment_status: 'pago',
            receiving_method: 'delivery',
            receiving_date: orderDate.toISOString().split('T')[0],
            receiving_time: '18:00',
            created_at: orderDate.toISOString(),
            updated_at: orderDate.toISOString()
          })
          .select('id, status, total')
          .single();

        if (newOrder) {
          testOrders.push(newOrder);
        }
      }

      res.json({
        message: "Pedidos de teste criados com sucesso",
        orders: testOrders,
        customerId
      });

    } catch (error) {
      console.error('Erro ao criar pedidos de teste:', error);
      res.status(500).json({ error: 'Erro ao criar pedidos de teste' });
    }
  });

  // Endpoint de teste para forçar uso de todos os pedidos
  app.get("/api/debug/financial-all", requireAuth, async (req: Request, res: Response) => {
    try {
      const userObj = req.user as any;

      if (!userObj || !userObj.uid) {
        return res.status(401).json({ message: "Usuário não autenticado" });
      }

      // Buscar a loja
      const store = await storage.getStoreByFirebaseUid(userObj.uid);
      let storeToUse = store;
      if (!storeToUse) {
        storeToUse = await storage.getStore(1);
      }

      if (!storeToUse) {
        return res.status(404).json({ message: "Nenhuma loja encontrada" });
      }

      const storeId = storeToUse.id;
      console.log('[DEBUG] Financial ALL - Using store ID:', storeId);

      // Buscar TODOS os pedidos confirmed/delivered sem filtro de data
      // CORRIGIDO: Removida referência a products que não existe na FK
      const { data: allOrders, error: ordersError } = await supabase
        .from('orders')
        .select(`
          id, created_at, total, payment_status, status,
          order_revisions(total, payment_status, is_current),
          order_items(product_id, quantity, price, product_name)
        `)
        .eq('store_id', storeId)
        .in('status', ['confirmed', 'delivered']);

      if (ordersError) {
        console.error('[DEBUG] Error fetching all orders:', ordersError);
        return res.status(500).json({ error: 'Erro ao buscar pedidos' });
      }

      console.log(`[DEBUG] Found ${allOrders?.length || 0} total confirmed/delivered orders`);

      if (!allOrders || allOrders.length === 0) {
        return res.json({
          summary: { totalRevenue: 0, totalOrders: 0, avgOrderValue: 0 },
          message: "Nenhum pedido confirmed/delivered encontrado"
        });
      }

      // Processar todos os pedidos
      let totalRevenue = 0;
      let totalOrders = 0;

      allOrders.forEach((order, index) => {
        console.log(`[DEBUG] Processing order ${index + 1}/${allOrders.length}:`, {
          id: order.id,
          status: order.status,
          total: order.total,
          created_at: order.created_at
        });

        const activeRevision = order.order_revisions?.find((r: any) => r.is_current);
        const total = activeRevision?.total || order.total;

        console.log(`[DEBUG] Order ${order.id} values:`, {
          order_total: order.total,
          revision_total: activeRevision?.total,
          final_total: total
        });

        if (total && !isNaN(total)) {
          totalRevenue += total;
          totalOrders += 1;
          console.log(`[DEBUG] Running totals: revenue=${totalRevenue}, orders=${totalOrders}`);
        } else {
          console.warn(`[DEBUG] Skipping order ${order.id} - invalid total:`, total);
        }
      });

      const avgOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

      const result = {
        summary: {
          totalRevenue,
          totalOrders,
          avgOrderValue,
          currentMonthRevenue: totalRevenue,
          monthlyGrowth: 0
        },
        ordersProcessed: allOrders.length,
        dateRange: {
          start: allOrders[0]?.created_at,
          end: allOrders[allOrders.length - 1]?.created_at
        }
      };

      console.log('[DEBUG] Final result:', result);
      res.json(result);

    } catch (error) {
      console.error('Erro ao processar dados financeiros (all):', error);
      res.status(500).json({ error: 'Erro ao processar dados financeiros' });
    }
  });

  // Registrar rotas de assinatura
  registerSubscriptionRoutes(app);

  // Registrar rotas de administração global
  app.use("/api/admin/global", globalAdminRouter);

  const httpServer = createServer(app);
  return httpServer;
}