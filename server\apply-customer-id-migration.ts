import dotenv from 'dotenv';
dotenv.config();

import { drizzle } from 'drizzle-orm/postgres-js';
import { migrate } from 'drizzle-orm/postgres-js/migrator';
import postgres from 'postgres';
import { sql } from 'drizzle-orm';
import * as schema from '../shared/schema';

/**
 * Script para adicionar a coluna customer_id à tabela order_revisions
 * Este script usa o Drizzle ORM que já está configurado no projeto
 */
async function main() {
  try {
    console.log('Iniciando processo de migração para adicionar customer_id na tabela order_revisions...');
    
    // Configuração da conexão
    const connectionString = process.env.DATABASE_URL;
    if (!connectionString) {
      throw new Error('DATABASE_URL não encontrada nas variáveis de ambiente');
    }
    
    console.log('Conectando ao banco de dados...');
    const queryClient = postgres(connectionString, { ssl: 'require' });
    const db = drizzle(queryClient, { schema });
    
    console.log('Verificando se a coluna já existe...');
    // Fazemos primeiro um query para verificar se a coluna customer_id já existe
    const checkColumn = await db.execute(sql`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'order_revisions' AND column_name = 'customer_id'
    `);
    
    if (checkColumn.length === 0) {
      console.log('Coluna customer_id não encontrada. Adicionando coluna...');
      
      // Adicionar a coluna customer_id
      await db.execute(sql`
        ALTER TABLE order_revisions 
        ADD COLUMN customer_id INTEGER REFERENCES customers(id)
      `);
      
      console.log('Coluna customer_id adicionada com sucesso!');
      
      // Preencher com os IDs de cliente dos pedidos originais
      console.log('Populando a nova coluna com os dados dos pedidos originais...');
      await db.execute(sql`
        UPDATE order_revisions r
        SET customer_id = o.customer_id
        FROM orders o
        WHERE r.order_id = o.id AND r.customer_id IS NULL
      `);
      
      console.log('Dados de cliente copiados com sucesso dos pedidos originais!');
    } else {
      console.log('A coluna customer_id já existe na tabela order_revisions.');
    }
    
    console.log('Migração concluída com sucesso!');
    process.exit(0);
  } catch (error) {
    console.error('Erro durante o processo de migração:', error);
    process.exit(1);
  }
}

main();