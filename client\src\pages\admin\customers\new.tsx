import { useEffect } from 'react';
import { useLocation } from 'wouter';
import { useMutation, useQueryClient } from "@tanstack/react-query";
import AdminLayout from '@/components/admin/AdminLayout';
import { useTranslation } from '@/hooks/useTranslation';
import { useStore } from '@/context/StoreContext';
import { apiRequest } from "@/lib/queryClient";
import { countryCodes } from "@/lib/countryCodes";
import { useToast } from "@/hooks/use-toast";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ArrowLeft, Check } from "lucide-react";

// Schema de validação para criação de cliente
const customerFormSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  email: z.string().email({ message: "Please enter a valid email address" }).optional().or(z.literal("")),
  countryCode: z.string().default("+55"),
  phone: z.string().optional().or(z.literal(""))
});

type CustomerFormValues = z.infer<typeof customerFormSchema>;

export default function NewCustomerPage() {
  const { t } = useTranslation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [, navigate] = useLocation();
  const { store, isLoading: isStoreLoading } = useStore();

  // Form for creating customer
  const form = useForm<CustomerFormValues>({
    resolver: zodResolver(customerFormSchema),
    defaultValues: {
      name: "",
      email: "",
      countryCode: "+55", // Padrão para Brasil
      phone: ""
    }
  });

  // Redirect to settings if no store exists
  useEffect(() => {
    if (!isStoreLoading && !store) {
      navigate('/admin/settings');
    }
  }, [store, isStoreLoading, navigate]);

  // Mutation to create customer
  const createCustomerMutation = useMutation({
    mutationFn: async (values: CustomerFormValues) => {
      console.log('Enviando dados para criar cliente:', values);
      const response = await apiRequest(
        'POST',
        `/api/customers`,
        values
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Erro ao criar cliente');
      }

      return response.json();
    },
    onSuccess: (data) => {
      console.log('Resposta da API ao criar cliente:', data);

      // Verificar se o cliente foi criado ou se um existente foi retornado
      const message = data.id ?
        (t('customers.createSuccess') || 'Cliente criado com sucesso') :
        (t('customers.customerExists') || 'Cliente já existe');

      toast({
        title: message,
        description: data.id ?
          (t('customers.createSuccessMessage') || 'O cliente foi criado com sucesso') :
          (t('customers.customerExistsMessage') || 'Um cliente com este email ou telefone já existe')
      });

      // Invalidar a consulta para atualizar a lista de clientes
      queryClient.invalidateQueries({ queryKey: ['/api/customers'] });

      // Sempre redirecionar para a lista de clientes, independentemente do resultado
      navigate('/admin/customers');
    },
    onError: (error) => {
      console.error('Erro ao criar cliente:', error);
      toast({
        title: t('customers.createError') || 'Erro ao criar cliente',
        description: t('customers.createErrorMessage') || 'Ocorreu um erro ao criar o cliente. Tente novamente.',
        variant: "destructive"
      });
    }
  });

  // Handle form submission
  const onSubmit = (values: CustomerFormValues) => {
    createCustomerMutation.mutate(values);
  };

  return (
    <AdminLayout title={t('customers.newCustomer') || 'Novo Cliente'}>
      <div className="mb-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => navigate('/admin/customers')}
          className="flex items-center"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          {t('common.back') || 'Voltar'}
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t('customers.newCustomer') || 'Novo Cliente'}</CardTitle>
          <CardDescription>{t('customers.newCustomerDescription') || 'Preencha os dados para criar um novo cliente'}</CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('customers.name') || 'Nome'}</FormLabel>
                    <FormControl>
                      <Input placeholder={t('customers.namePlaceholder') || 'Nome do cliente'} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('customers.email') || 'Email'}</FormLabel>
                    <FormControl>
                      <Input placeholder={t('customers.emailPlaceholder') || 'Email do cliente'} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="countryCode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('customers.countryCode') || 'Código do País'}</FormLabel>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={t('customers.selectCountryCode') || 'Selecione o código do país'} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {countryCodes.map((country) => (
                            <SelectItem key={country.id} value={country.code}>
                              <div className="flex items-center">
                                <span className="mr-2">{country.flag}</span>
                                <span>{country.name}</span>
                                <span className="ml-1 text-muted-foreground">{country.code}</span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel>{t('customers.phone') || 'Telefone'}</FormLabel>
                      <FormControl>
                        <Input placeholder={t('customers.phonePlaceholder') || 'Telefone do cliente'} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="flex justify-end space-x-2 pt-4">
                <Button
                  variant="outline"
                  type="button"
                  onClick={() => navigate('/admin/customers')}
                >
                  {t('common.cancel') || 'Cancelar'}
                </Button>
                <Button
                  type="submit"
                  disabled={createCustomerMutation.isPending}
                >
                  {createCustomerMutation.isPending ? (
                    <span className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      {t('common.saving') || 'Salvando...'}
                    </span>
                  ) : (
                    <span className="flex items-center">
                      <Check className="mr-2 h-4 w-4" />
                      {t('common.save') || 'Salvar'}
                    </span>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </AdminLayout>
  );
}
