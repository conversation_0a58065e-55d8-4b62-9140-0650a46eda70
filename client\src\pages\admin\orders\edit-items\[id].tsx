import { useState, useEffect } from 'react';
import { useLocation, useParams } from 'wouter';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import {
  ChevronRight,
  ArrowLeft,
  Save,
  Trash2,
  Plus,
  PencilIcon
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Table,
  TableBody,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { useToast } from '@/hooks/use-toast';
import { formatCurrency } from '@/lib/utils';
import { useTranslation } from '@/hooks/useTranslation';

interface OrderRevisionItem {
  id: number;
  productId?: number;
  quantity: number;
  unitPrice: number;
  productName: string;
  productImage?: string;
  selectedVariations: any[];
  observation: string | null;
}

export default function EditOrderItemsPage() {
  const { t } = useTranslation();
  const { toast } = useToast();
  const [, setLocation] = useLocation();
  const params = useParams();
  const queryClient = useQueryClient();
  const revisionId = params.id;
  const orderId = localStorage.getItem('currentOrderId');

  const [items, setItems] = useState<OrderRevisionItem[]>([]);
  const [selectedItem, setSelectedItem] = useState<OrderRevisionItem | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  // Fetch revision details
  const { data: revision, isLoading } = useQuery({
    queryKey: ['/api/orders/revisions', revisionId],
    enabled: !!revisionId
  });

  // Update revision items mutation
  const updateItemsMutation = useMutation({
    mutationFn: (data: { items: OrderRevisionItem[] }) => {
      return apiRequest(`/api/orders/revisions/${revisionId}/update-items`, 'PATCH', data);
    },
    onSuccess: () => {
      toast({
        title: t('orders.itemsUpdated') || 'Itens atualizados',
        description: t('orders.itemsUpdatedDesc') || 'Os itens do pedido foram atualizados com sucesso',
      });

      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['/api/orders/revisions', revisionId] });

      if (orderId) {
        // Navigate back to order details
        setLocation(`/admin/orders/${orderId}`);
      } else {
        setLocation('/admin/orders');
      }
    },
    onError: (error) => {
      toast({
        title: t('common.error') || 'Erro',
        description: String(error),
        variant: 'destructive',
      });
    }
  });

  // Delete item mutation
  const deleteItemMutation = useMutation({
    mutationFn: (itemId: number) => {
      return apiRequest(`/api/orders/revisions/${revisionId}/items/${itemId}`, 'DELETE');
    },
    onSuccess: () => {
      toast({
        title: t('orders.itemDeleted') || 'Item excluído',
        description: t('orders.itemDeletedDesc') || 'O item foi excluído com sucesso',
      });

      // Remove item from local state
      if (selectedItem) {
        setItems(prev => prev.filter(item => item.id !== selectedItem.id));
        setSelectedItem(null);
      }

      setShowDeleteDialog(false);
    },
    onError: (error) => {
      toast({
        title: t('common.error') || 'Erro',
        description: String(error),
        variant: 'destructive',
      });
    }
  });

  useEffect(() => {
    if (revision && revision.items) {
      setItems(revision.items);
    }
  }, [revision]);

  const handleGoBack = () => {
    if (orderId) {
      setLocation(`/admin/orders/${orderId}`);
    } else {
      setLocation('/admin/orders');
    }
  };

  const handleSaveItems = () => {
    updateItemsMutation.mutate({ items });
  };

  const handleEditItem = (item: OrderRevisionItem) => {
    // Save item ID in localStorage for the product details page
    localStorage.setItem('currentItemId', String(item.id));
    localStorage.setItem('currentRevisionId', String(revisionId));
    localStorage.setItem('currentOrderId', String(orderId));

    // Verificar se é um produto customizado (ID negativo ou com variações customizadas)
    const isCustomProduct =
      (item.productId && item.productId < 0) ||
      (item.selectedVariations && item.selectedVariations.some(v => v.isCustom || v.variationId === 'custom'));

    if (isCustomProduct) {
      // Marcar como produto customizado
      localStorage.setItem('isCustomProduct', 'true');

      // Ir para a página de produto customizado
      setLocation('/admin/orders/custom-product');
    } else {
      // Ir para a página de detalhes do produto
      setLocation(`/admin/orders/product-details/${item.id}`);
    }
  };

  const handleDeleteItem = (item: OrderRevisionItem) => {
    setSelectedItem(item);
    setShowDeleteDialog(true);
  };

  const confirmDeleteItem = () => {
    if (selectedItem) {
      deleteItemMutation.mutate(selectedItem.id);
    }
  };

  const calculateTotal = () => {
    return items.reduce((sum, item) => {
      return sum + (item.unitPrice * item.quantity);
    }, 0);
  };

  if (isLoading) {
    return (
      <div className="p-4 flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
          <p>{t('common.loading') || 'Carregando...'}</p>
        </div>
      </div>
    );
  }

  if (!revision) {
    return (
      <div className="p-4">
        <div className="text-center py-8">
          <h2 className="text-xl font-semibold mb-2">{t('orders.revisionNotFound') || 'Revisão não encontrada'}</h2>
          <Button onClick={handleGoBack}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('common.back') || 'Voltar'}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container max-w-4xl pb-20 px-4 sm:px-6">
      <div className="sticky top-0 pt-4 pb-3 bg-background z-10">
        <div className="flex items-center justify-between">
          <Button variant="ghost" size="sm" onClick={handleGoBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t('common.back') || 'Voltar'}
          </Button>

          <Button
            onClick={handleSaveItems}
            disabled={updateItemsMutation.isPending}
            className="ml-auto"
          >
            <Save className="h-4 w-4 mr-2" />
            {t('common.save') || 'Salvar'}
          </Button>
        </div>

        <h1 className="text-2xl font-bold mt-2">{t('orders.editItems') || 'Editar Itens'}</h1>
        <p className="text-muted-foreground">
          {t('orders.editItemsDesc') || 'Edite os itens da revisão do pedido'}
        </p>
      </div>

      <div className="mt-6 space-y-6">
        <div className="grid gap-4">
          {items.length > 0 ? (
            <>
              {/* Mobile Layout */}
              <div className="md:hidden space-y-4">
                {items.map((item) => (
                  <Card
                    key={item.id}
                    className="border overflow-hidden cursor-pointer hover:border-primary/50 transition-colors"
                    onClick={() => handleEditItem(item)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start gap-3">
                        {item.productImage && (
                          <div className="w-20 h-20 overflow-hidden rounded-md flex-shrink-0">
                            <img
                              src={item.productImage}
                              alt={item.productName}
                              className="w-full h-full object-cover"
                            />
                          </div>
                        )}

                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between">
                            <h3 className="font-medium flex items-center">
                              {item.productName}
                              <PencilIcon className="h-3.5 w-3.5 ml-1 text-muted-foreground" />
                            </h3>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-7 w-7 -mt-1 -mr-1"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteItem(item);
                              }}
                            >
                              <Trash2 className="h-4 w-4 text-destructive" />
                            </Button>
                          </div>

                          <div className="flex justify-between items-center text-sm mt-2">
                            <span className="text-muted-foreground">{t('products.quantity') || "Qtd"}:</span>
                            <span className="font-medium">{item.quantity}</span>
                          </div>

                          <div className="flex justify-between items-center text-sm mt-1">
                            <span className="text-muted-foreground">{t('products.unitPrice') || "Preço Unit."}:</span>
                            <span className="font-medium">{formatCurrency(item.unitPrice)}</span>
                          </div>

                          <div className="flex justify-between items-center text-sm font-medium mt-1">
                            <span className="text-muted-foreground">{t('products.total') || "Total"}:</span>
                            <span className="text-primary">{formatCurrency(item.unitPrice * item.quantity)}</span>
                          </div>
                        </div>
                      </div>

                      {/* Variations */}
                      {item.selectedVariations && item.selectedVariations.length > 0 && (
                        <div className="mt-3 pt-3 border-t">
                          <div className="text-xs text-muted-foreground mb-1">
                            {t('orders.selectedOptions') || 'Opções selecionadas'}:
                          </div>
                          <div className="space-y-1">
                            {item.selectedVariations.map((option, index) => (
                              <div key={index} className="flex items-center justify-between text-xs">
                                <div>
                                  <span className="font-medium">{option.variationName}:</span>
                                  <span className="ml-1">{option.optionName}</span>
                                  {option.quantity > 1 && (
                                    <span className="ml-1">x{option.quantity}</span>
                                  )}
                                </div>
                                {option.price > 0 && (
                                  <span className="text-primary">
                                    (+{formatCurrency(option.price * (option.quantity || 1))})
                                  </span>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Observation */}
                      {item.observation && (
                        <div className="mt-3 pt-3 border-t">
                          <div className="text-xs text-muted-foreground mb-1">
                            {t('orders.observation') || 'Observação'}:
                          </div>
                          <div className="text-xs bg-muted/30 p-2 rounded italic">
                            {item.observation}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>

              {/* Desktop Layout */}
              <div className="hidden md:block">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>{t('products.product') || 'Produto'}</TableHead>
                      <TableHead className="text-right">{t('products.unitPrice') || 'Preço Unit.'}</TableHead>
                      <TableHead className="text-center">{t('products.quantity') || 'Qtd'}</TableHead>
                      <TableHead className="text-right">{t('products.total') || 'Total'}</TableHead>
                      <TableHead className="w-[70px]"></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {items.map((item) => (
                      <TableRow
                        key={item.id}
                        className="cursor-pointer hover:bg-muted/40"
                        onClick={() => handleEditItem(item)}
                      >
                        <TableCell>
                          <div className="flex items-center gap-3">
                            {item.productImage && (
                              <div className="w-12 h-12 overflow-hidden rounded">
                                <img
                                  src={item.productImage}
                                  alt={item.productName}
                                  className="w-full h-full object-cover"
                                />
                              </div>
                            )}
                            <div>
                              <div className="font-medium flex items-center">
                                {item.productName}
                                <PencilIcon className="h-3.5 w-3.5 ml-1 text-muted-foreground" />
                              </div>

                              {/* Variations */}
                              {item.selectedVariations && item.selectedVariations.length > 0 && (
                                <div className="text-xs text-muted-foreground mt-1 space-y-0.5">
                                  <span className="font-medium">{t('orders.selectedOptions') || 'Opções selecionadas'}:</span>
                                  <ul className="ml-2">
                                    {item.selectedVariations.map((option, index) => (
                                      <li key={index} className="flex items-center">
                                        <span className="font-medium">{option.variationName}:</span>
                                        <span className="ml-1">{option.optionName}</span>
                                        {option.quantity > 1 && (
                                          <span className="ml-1">x{option.quantity}</span>
                                        )}
                                        {option.price > 0 && (
                                          <span className="ml-1 text-primary">
                                            (+{formatCurrency(option.price * (option.quantity || 1))})
                                          </span>
                                        )}
                                      </li>
                                    ))}
                                  </ul>
                                </div>
                              )}

                              {/* Observation */}
                              {item.observation && (
                                <div className="text-xs text-muted-foreground mt-1">
                                  <span className="font-medium">{t('orders.observation')}:</span> {item.observation}
                                </div>
                              )}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          {formatCurrency(item.unitPrice)}
                        </TableCell>
                        <TableCell className="text-center">
                          {item.quantity}
                        </TableCell>
                        <TableCell className="text-right font-medium">
                          {formatCurrency(item.unitPrice * item.quantity)}
                        </TableCell>
                        <TableCell>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteItem(item);
                            }}
                          >
                            <Trash2 className="h-4 w-4 text-destructive" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                  <TableFooter>
                    <TableRow>
                      <TableCell colSpan={3} className="text-right font-medium">
                        {t('orders.subtotal') || "Subtotal"}:
                      </TableCell>
                      <TableCell className="text-right">
                        {formatCurrency(calculateTotal())}
                      </TableCell>
                      <TableCell></TableCell>
                    </TableRow>
                  </TableFooter>
                </Table>
              </div>
            </>
          ) : (
            <div className="text-center py-10 border rounded-lg">
              <p className="text-muted-foreground">
                {t('orders.noItems') || 'Nenhum item encontrado'}
              </p>
            </div>
          )}
        </div>

        <div className="sticky bottom-0 pt-4 pb-6 bg-background border-t mt-6">
          <div className="flex justify-between items-center">
            <Button variant="outline" onClick={handleGoBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              {t('common.cancel') || 'Cancelar'}
            </Button>

            <Button
              onClick={handleSaveItems}
              disabled={updateItemsMutation.isPending}
            >
              <Save className="h-4 w-4 mr-2" />
              {t('common.save') || 'Salvar'}
            </Button>
          </div>
        </div>
      </div>

      {/* Delete confirmation dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('orders.confirmDelete') || 'Confirmar exclusão'}</DialogTitle>
          </DialogHeader>
          <p>
            {t('orders.confirmDeleteDesc') || 'Tem certeza que deseja excluir este item?'}
            {selectedItem && (
              <span className="font-medium"> {selectedItem.productName}</span>
            )}
          </p>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
            >
              {t('common.cancel') || 'Cancelar'}
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDeleteItem}
              disabled={deleteItemMutation.isPending}
            >
              {t('common.delete') || 'Excluir'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}