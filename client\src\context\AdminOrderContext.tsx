import { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';

interface SelectedVariation {
  variationId: string;
  variationName: string;
  optionId: string;
  optionName: string;
  price: number;
  quantity?: number;
  isCustom?: boolean;
}

interface OrderItem {
  id: number;
  productId: number;
  name: string;
  price: number;
  image?: string;
  quantity: number;
  selectedVariations?: SelectedVariation[];
  observation?: string;
}

interface Customer {
  id: number;
  name: string;
  email?: string;
  phone?: string;
}

interface AdminOrderContextType {
  items: OrderItem[];
  customer: Customer | null;
  receivingMethod: string;
  receivingDate: Date | null;
  receivingTime: string;
  paymentMethod: string;
  deliveryAddress: any;
  notes: string;
  deliveryFee: number;

  // Métodos para gerenciar itens
  addItem: (item: Omit<OrderItem, 'id'>) => void;
  removeItem: (itemId: number) => void;
  updateQuantity: (itemId: number, quantity: number) => void;
  clearItems: () => void;

  // Métodos para gerenciar cliente
  setCustomer: (customer: Customer | null) => void;

  // Métodos para gerenciar informações do pedido
  setReceivingMethod: (method: string) => void;
  setReceivingDate: (date: Date | null) => void;
  setReceivingTime: (time: string) => void;
  setPaymentMethod: (method: string) => void;
  setDeliveryAddress: (address: any) => void;
  setNotes: (notes: string) => void;
  setDeliveryFee: (fee: number) => void;

  // Cálculos
  subtotal: number;
  total: number;

  // Limpar tudo
  resetOrder: () => void;
}

const AdminOrderContext = createContext<AdminOrderContextType | undefined>(undefined);

export function AdminOrderProvider({ children }: { children: ReactNode }) {
  const [items, setItems] = useState<OrderItem[]>([]);
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [receivingMethod, setReceivingMethod] = useState<string>('delivery');
  const [receivingDate, setReceivingDate] = useState<Date | null>(new Date());
  const [receivingTime, setReceivingTime] = useState<string>('');
  const [paymentMethod, setPaymentMethod] = useState<string>('');
  const [deliveryAddress, setDeliveryAddress] = useState<any>(null);
  const [notes, setNotes] = useState<string>('');
  const [deliveryFee, setDeliveryFee] = useState<number>(0);

  const { toast } = useToast();

  // Adicionar item ao pedido
  const addItem = (item: Omit<OrderItem, 'id'>) => {
    setItems(prev => {
      // Garantir que os preços das variações sejam números
      let processedItem = { ...item };

      // Converter o preço base para número
      processedItem.price = parseFloat(processedItem.price as any) || 0;

      // Converter os preços das variações para números e garantir que as quantidades sejam válidas
      if (processedItem.selectedVariations && processedItem.selectedVariations.length > 0) {
        processedItem.selectedVariations = processedItem.selectedVariations.map(variation => {
          // Garantir que a quantidade seja pelo menos 1
          const quantity = variation.quantity && variation.quantity > 0 ? variation.quantity : 1;

          return {
            ...variation,
            price: parseFloat(variation.price as any) || 0,
            quantity: quantity
          };
        });

        console.log('Variações processadas:', processedItem.selectedVariations);
      }

      // Verificar se é um produto personalizado (ID negativo)
      const isCustomProduct = typeof processedItem.productId === 'number' && processedItem.productId < 0;

      console.log('Adicionando item ao pedido:', {
        nome: processedItem.name,
        productId: processedItem.productId,
        isCustomProduct,
        precoBase: processedItem.price,
        quantidade: processedItem.quantity,
        variacoes: processedItem.selectedVariations
      });

      const newItem = {
        ...processedItem,
        id: Date.now(), // Usar timestamp como ID único
      };

      toast({
        title: 'Item adicionado',
        description: `${item.name} foi adicionado ao pedido`,
        duration: 2000,
      });

      return [...prev, newItem];
    });
  };

  // Remover item do pedido
  const removeItem = (itemId: number) => {
    setItems(prev => {
      const itemToRemove = prev.find(item => item.id === itemId);
      if (itemToRemove) {
        toast({
          title: 'Item removido',
          description: `${itemToRemove.name} foi removido do pedido`,
          duration: 2000,
        });
      }

      return prev.filter(item => item.id !== itemId);
    });
  };

  // Atualizar quantidade do item
  const updateQuantity = (itemId: number, quantity: number) => {
    if (quantity < 1) {
      removeItem(itemId);
      return;
    }

    setItems(prev => {
      return prev.map(item => {
        if (item.id === itemId) {
          return { ...item, quantity };
        }
        return item;
      });
    });
  };

  // Limpar itens
  const clearItems = () => {
    setItems([]);
  };

  // Resetar pedido
  const resetOrder = () => {
    setItems([]);
    setCustomer(null);
    setReceivingMethod('delivery');
    setReceivingDate(new Date());
    setReceivingTime('');
    setPaymentMethod('');
    setDeliveryAddress(null);
    setNotes('');
    setDeliveryFee(0);
  };

  // Calcular subtotal (preço base + variações)
  const subtotal = items.reduce((total, item) => {
    // Preço base × quantidade
    let basePrice = item.price * item.quantity;

    // Adicionar preço das variações
    let variationsTotal = 0;
    if (item.selectedVariations && item.selectedVariations.length > 0) {
      // Calcular o total das variações considerando a quantidade de cada variação
      variationsTotal = item.selectedVariations.reduce((varTotal, variation) => {
        // Obter a quantidade da variação (padrão: 1 se não estiver definida)
        const variationQuantity = variation.quantity || 1;

        // Calcular o preço total desta variação: preço unitário × quantidade da variação × quantidade do item
        const variationPrice = (parseFloat(variation.price) || 0) * variationQuantity;

        console.log(`Variação ${variation.variationName} - ${variation.optionName}:`, {
          precoUnitario: parseFloat(variation.price) || 0,
          quantidade: variationQuantity,
          precoTotal: variationPrice
        });

        return varTotal + variationPrice;
      }, 0) * item.quantity;
    }

    const itemSubtotal = basePrice + variationsTotal;

    console.log('Cálculo do subtotal:', {
      nome: item.name,
      precoBase: item.price,
      quantidade: item.quantity,
      precoBaseTotal: basePrice,
      totalVariacoes: variationsTotal,
      subtotalItem: itemSubtotal,
      subtotalAcumulado: total + itemSubtotal
    });

    return total + itemSubtotal;
  }, 0);

  // Calcular total (subtotal + taxa de entrega)
  const total = subtotal + deliveryFee;

  return (
    <AdminOrderContext.Provider
      value={{
        items,
        customer,
        receivingMethod,
        receivingDate,
        receivingTime,
        paymentMethod,
        deliveryAddress,
        notes,
        deliveryFee,

        addItem,
        removeItem,
        updateQuantity,
        clearItems,

        setCustomer,
        setReceivingMethod,
        setReceivingDate,
        setReceivingTime,
        setPaymentMethod,
        setDeliveryAddress,
        setNotes,
        setDeliveryFee,

        subtotal,
        total,

        resetOrder
      }}
    >
      {children}
    </AdminOrderContext.Provider>
  );
}

export function useAdminOrder() {
  const context = useContext(AdminOrderContext);
  if (context === undefined) {
    throw new Error('useAdminOrder must be used within an AdminOrderProvider');
  }
  return context;
}
