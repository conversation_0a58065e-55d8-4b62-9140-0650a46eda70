import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Receipt, Calendar, CreditCard, MessageSquare, Trash2 } from 'lucide-react';
import { formatCurrency, formatDate } from '@/lib/utils';
import { useTranslation } from '@/hooks/useTranslation';
import { type OrderPayment } from '@/hooks/useOrderPayments';
import DeletePaymentDialog from './DeletePaymentDialog';

interface PaymentsListProps {
  payments: OrderPayment[];
  currency?: string;
  onDeletePayment?: (paymentId: number) => void;
  isDeleting?: boolean;
}

export default function PaymentsList({ payments, currency = 'R$', onDeletePayment, isDeleting = false }: PaymentsListProps) {
  const { t } = useTranslation();
  const [selectedPayment, setSelectedPayment] = useState<OrderPayment | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const handleDeleteClick = (payment: OrderPayment) => {
    setSelectedPayment(payment);
    setIsDeleteDialogOpen(true);
  };

  const handleConfirmDelete = () => {
    if (selectedPayment && onDeletePayment) {
      onDeletePayment(selectedPayment.id);
      setIsDeleteDialogOpen(false);
      setSelectedPayment(null);
    }
  };

  const getMethodColor = (method: string) => {
    switch (method) {
      case 'Pix':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'Dinheiro':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Cartão':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Transferência':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'Outro':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getMethodIcon = (method: string) => {
    switch (method) {
      case 'Pix':
        return '💳';
      case 'Dinheiro':
        return '💵';
      case 'Cartão':
        return '💳';
      case 'Transferência':
        return '🏦';
      case 'Outro':
        return '📄';
      default:
        return '💰';
    }
  };

  if (payments.length === 0) {
    return (
      <Card className="overflow-hidden shadow-sm">
        <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100/50 px-6 py-4 border-b">
          <CardTitle className="text-lg flex items-center">
            <Receipt className="h-5 w-5 mr-2 text-gray-600" />
            {t('payments.paymentHistory') || 'Histórico de Recebimentos'}
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="text-center py-8 text-muted-foreground">
            <Receipt className="h-12 w-12 mx-auto mb-3 text-gray-300" />
            <p className="text-lg font-medium mb-1">
              {t('payments.noPayments') || 'Nenhum recebimento registrado'}
            </p>
            <p className="text-sm">
              {t('payments.noPaymentsDescription') || 'Clique em "Registrar Pagamento" para adicionar o primeiro recebimento'}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="overflow-hidden shadow-sm">
      <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100/50 px-6 py-4 border-b">
        <CardTitle className="text-lg flex items-center justify-between">
          <div className="flex items-center">
            <Receipt className="h-5 w-5 mr-2 text-gray-600" />
            {t('payments.paymentHistory') || 'Histórico de Recebimentos'}
          </div>
          <Badge variant="secondary" className="text-xs">
            {t('payments.totalPayments', { count: payments.length }) || `${payments.length} recebimento(s)`}
          </Badge>
        </CardTitle>
      </CardHeader>

      <CardContent className="p-0">
        {/* Mobile Layout */}
        <div className="md:hidden">
          {payments.map((payment) => (
            <div key={payment.id} className="border-b last:border-b-0 p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-2">
                  <span className="text-lg">{getMethodIcon(payment.metodo)}</span>
                  <Badge
                    variant="outline"
                    className={`text-xs ${getMethodColor(payment.metodo)}`}
                  >
                    {payment.metodo}
                  </Badge>
                </div>
                <div className="text-right">
                  <div className="font-bold text-lg text-green-600">
                    {payment.valor < 0 ? '-' : '+'}{formatCurrency(Math.abs(payment.valor), currency)}
                  </div>
                  {payment.valor < 0 && (
                    <div className="text-xs text-red-600 font-medium">
                      {t('payments.refund') || 'Estorno'}
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Calendar className="h-4 w-4" />
                  <span>{formatDate(payment.data)}</span>
                </div>

                {payment.observacao && (
                  <div className="flex items-start gap-2 text-muted-foreground">
                    <MessageSquare className="h-4 w-4 mt-0.5 flex-shrink-0" />
                    <span className="text-xs bg-muted/50 p-2 rounded italic">
                      {payment.observacao}
                    </span>
                  </div>
                )}

                {onDeletePayment && (
                  <div className="flex justify-end mt-3">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      onClick={() => handleDeleteClick(payment)}
                      disabled={isDeleting}
                    >
                      <Trash2 className="h-4 w-4 mr-1" />
                      {t('common.delete') || 'Excluir'}
                    </Button>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Desktop Layout */}
        <div className="hidden md:block">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[120px]">
                  {t('payments.date') || 'Data'}
                </TableHead>
                <TableHead className="w-[120px]">
                  {t('payments.method') || 'Método'}
                </TableHead>
                <TableHead className="text-right w-[120px]">
                  {t('payments.amount') || 'Valor'}
                </TableHead>
                <TableHead>
                  {t('payments.observation') || 'Observação'}
                </TableHead>
                {onDeletePayment && (
                  <TableHead className="w-[80px]">
                    {t('common.actions') || 'Ações'}
                  </TableHead>
                )}
              </TableRow>
            </TableHeader>
            <TableBody>
              {payments.map((payment) => (
                <TableRow key={payment.id}>
                  <TableCell className="font-medium">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      {formatDate(payment.data)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant="outline"
                      className={`text-xs ${getMethodColor(payment.metodo)}`}
                    >
                      <span className="mr-1">{getMethodIcon(payment.metodo)}</span>
                      {payment.metodo}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className={`font-bold ${payment.valor < 0 ? 'text-red-600' : 'text-green-600'}`}>
                      {payment.valor < 0 ? '-' : '+'}{formatCurrency(Math.abs(payment.valor), currency)}
                    </div>
                    {payment.valor < 0 && (
                      <div className="text-xs text-red-600 font-medium">
                        {t('payments.refund') || 'Estorno'}
                      </div>
                    )}
                  </TableCell>
                  <TableCell>
                    {payment.observacao ? (
                      <div className="flex items-start gap-2">
                        <MessageSquare className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                        <span className="text-sm text-muted-foreground italic">
                          {payment.observacao}
                        </span>
                      </div>
                    ) : (
                      <span className="text-muted-foreground text-sm">—</span>
                    )}
                  </TableCell>
                  {onDeletePayment && (
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        onClick={() => handleDeleteClick(payment)}
                        disabled={isDeleting}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  )}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>

      {/* Modal de confirmação de exclusão */}
      <DeletePaymentDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        payment={selectedPayment}
        onConfirm={handleConfirmDelete}
        isDeleting={isDeleting}
        currency={currency}
      />
    </Card>
  );
}
