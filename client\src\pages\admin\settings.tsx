import AdminLayout from '@/components/admin/AdminLayout';
import StoreSettings from '@/components/admin/StoreSettings';
import { useTranslation } from '@/hooks/useTranslation';

export default function SettingsPage() {
  const { t } = useTranslation();

  return (
    <AdminLayout 
      title={t('settings.title')} 
      description={t('settings.subtitle')}
    >
      <StoreSettings />
    </AdminLayout>
  );
}
