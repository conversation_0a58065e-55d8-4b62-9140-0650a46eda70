import { Request, Response } from 'express';
import { storage } from './storage';
import { db } from './db';
import { products, Product } from '@shared/schema';
import { eq } from 'drizzle-orm';

/**
 * Função para atualizar um produto mantendo as variações diretamente no campo JSONB
 * Substitui a lógica das rotas PATCH e PUT para produtos no arquivo routes.ts
 */
export async function updateProductWithVariations(req: Request, res: Response) {
  try {
    const firebaseUid = (req as any).user?.uid;
    console.log('PATCH - Updating product for Firebase UID:', firebaseUid);
    console.log('Product ID:', req.params.id);
    console.log('Update data:', req.body);

    // Obter a loja do usuário
    const store = await storage.getStoreByFirebaseUid(firebaseUid);

    if (!store) {
      console.log('Store not found for Firebase UID:', firebaseUid);
      return res.status(404).json({ message: "Store not found" });
    }

    console.log('Store found with ID:', store.id);
    const productId = parseInt(req.params.id);

    // Buscar o produto para verificar a propriedade
    const product = await storage.getProduct(productId);

    if (!product) {
      console.log('Product not found with ID:', productId);
      return res.status(404).json({ message: "Product not found" });
    }

    console.log('Product found:', product);
    console.log('Verifying ownership - Product storeId:', product.storeId, 'User store ID:', store.id);

    // Verificar a propriedade do produto
    if (product.storeId !== store.id) {
      console.log('Ownership verification failed');
      return res.status(403).json({ message: "Not authorized to modify this product" });
    }

    console.log('Ownership verified successfully');

    // Preparar dados de atualização (sem permitir alteração de storeId)
    const updateData = { ...req.body };
    delete updateData.storeId;

    // Garantir que o campo images seja um array válido
    if (!updateData.images || !Array.isArray(updateData.images)) {
      console.log('Images field is missing or not an array, using original images from product');
      updateData.images = product.images || [];
    }

    console.log('Prepared update data:', updateData);
    console.log('Images to be saved:', JSON.stringify(updateData.images));
    
    // * IMPORTANTE: Não removemos as variações do objeto para preservar o campo JSONB
    if (updateData.variations) {
      console.log('Preserving variations in JSONB field:', updateData.variations.length);
    } else {
      console.log('No variations provided, keeping original ones');
      updateData.variations = product.variations;
    }

    // Atualizar o produto usando a função updateProduct melhorada
    const updatedProduct = await storage.updateProduct(productId, updateData);

    if (!updatedProduct) {
      console.log('Failed to update product');
      return res.status(500).json({ message: "Failed to update product" });
    }

    return res.status(200).json(updatedProduct);
  } catch (error) {
    console.error('Error updating product:', error);
    return res.status(500).json({
      message: "Failed to update product",
      error: error instanceof Error ? error.message : String(error)
    });
  }
}