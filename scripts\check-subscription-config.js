#!/usr/bin/env node

/**
 * Script simples para verificar configuração do sistema de assinaturas
 */

import dotenv from 'dotenv';
dotenv.config();

// Cores para output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(color, message) {
  console.log(`${color}${message}${colors.reset}`);
}

function checkEnvironmentVariables() {
  log(colors.blue, '\n🔍 Verificando variáveis de ambiente...');

  const requiredVars = [
    'SUPABASE_URL',
    'SUPABASE_SERVICE_ROLE_KEY',
    'STRIPE_SECRET_KEY',
    'STRIPE_WEBHOOK_SECRET',
    'STRIPE_PREMIUM_MONTHLY_PRICE_ID',
    'STRIPE_PREMIUM_YEARLY_PRICE_ID'
  ];

  const optionalVars = [
    'STRIPE_PUBLISHABLE_KEY',
    'STRIPE_SUCCESS_URL',
    'STRIPE_CANCEL_URL'
  ];

  let allRequired = true;
  let optionalCount = 0;

  // Verificar variáveis obrigatórias
  log(colors.bold, '\n📋 Variáveis Obrigatórias:');
  requiredVars.forEach(varName => {
    if (process.env[varName]) {
      log(colors.green, `✅ ${varName}`);
    } else {
      log(colors.red, `❌ ${varName} - FALTANDO`);
      allRequired = false;
    }
  });

  // Verificar variáveis opcionais
  log(colors.bold, '\n📋 Variáveis Opcionais:');
  optionalVars.forEach(varName => {
    if (process.env[varName]) {
      log(colors.green, `✅ ${varName}`);
      optionalCount++;
    } else {
      log(colors.yellow, `⚠️ ${varName} - não configurada`);
    }
  });

  return { allRequired, optionalCount };
}

function checkStripeKeys() {
  log(colors.blue, '\n🔍 Verificando formato das chaves Stripe...');

  const stripeSecret = process.env.STRIPE_SECRET_KEY;
  const stripePublishable = process.env.STRIPE_PUBLISHABLE_KEY;
  const monthlyPriceId = process.env.STRIPE_PREMIUM_MONTHLY_PRICE_ID;
  const yearlyPriceId = process.env.STRIPE_PREMIUM_YEARLY_PRICE_ID;

  let validKeys = 0;

  if (stripeSecret) {
    if (stripeSecret.startsWith('sk_test_') || stripeSecret.startsWith('sk_live_')) {
      log(colors.green, `✅ STRIPE_SECRET_KEY formato válido (${stripeSecret.startsWith('sk_test_') ? 'TEST' : 'LIVE'})`);
      validKeys++;
    } else {
      log(colors.red, '❌ STRIPE_SECRET_KEY formato inválido');
    }
  }

  if (stripePublishable) {
    if (stripePublishable.startsWith('pk_test_') || stripePublishable.startsWith('pk_live_')) {
      log(colors.green, `✅ STRIPE_PUBLISHABLE_KEY formato válido (${stripePublishable.startsWith('pk_test_') ? 'TEST' : 'LIVE'})`);
      validKeys++;
    } else {
      log(colors.red, '❌ STRIPE_PUBLISHABLE_KEY formato inválido');
    }
  }

  if (monthlyPriceId) {
    if (monthlyPriceId.startsWith('price_')) {
      log(colors.green, '✅ STRIPE_PREMIUM_MONTHLY_PRICE_ID formato válido');
      validKeys++;
    } else {
      log(colors.red, '❌ STRIPE_PREMIUM_MONTHLY_PRICE_ID formato inválido');
    }
  }

  if (yearlyPriceId) {
    if (yearlyPriceId.startsWith('price_')) {
      log(colors.green, '✅ STRIPE_PREMIUM_YEARLY_PRICE_ID formato válido');
      validKeys++;
    } else {
      log(colors.red, '❌ STRIPE_PREMIUM_YEARLY_PRICE_ID formato inválido');
    }
  }

  return validKeys;
}

function checkSupabaseConfig() {
  log(colors.blue, '\n🔍 Verificando configuração do Supabase...');

  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  let validConfig = 0;

  if (supabaseUrl) {
    if (supabaseUrl.includes('.supabase.co')) {
      log(colors.green, '✅ SUPABASE_URL formato válido');
      validConfig++;
    } else {
      log(colors.yellow, '⚠️ SUPABASE_URL formato não reconhecido');
    }
  }

  if (supabaseKey) {
    if (supabaseKey.length > 100) {
      log(colors.green, '✅ SUPABASE_SERVICE_ROLE_KEY parece válida');
      validConfig++;
    } else {
      log(colors.red, '❌ SUPABASE_SERVICE_ROLE_KEY muito curta');
    }
  }

  return validConfig;
}

function showPlanConfiguration() {
  log(colors.blue, '\n🔍 Configuração dos Planos...');

  const planConfigs = {
    free: {
      name: 'Plano Gratuito',
      price: 'R$ 0,00',
      products: '10 produtos',
      orders: '5 pedidos/mês',
      features: ['Loja básica', 'Suporte email']
    },
    premiumMonthly: {
      name: 'Premium Mensal',
      price: 'R$ 29,90/mês',
      trial: '7 dias grátis',
      products: '50 produtos',
      orders: 'Ilimitados',
      features: ['PDF', 'Analytics', 'WhatsApp', 'Cupons']
    },
    premiumYearly: {
      name: 'Premium Anual',
      price: 'R$ 299,00/ano (R$ 24,92/mês)',
      trial: 'Sem trial',
      savings: 'Economia: R$ 59,80/ano (16.6%)',
      products: '50 produtos',
      orders: 'Ilimitados',
      features: ['PDF', 'Analytics', 'WhatsApp', 'Cupons']
    }
  };

  Object.entries(planConfigs).forEach(([key, config]) => {
    log(colors.bold, `\n📦 ${config.name}:`);
    log(colors.green, `   💰 ${config.price}`);
    if (config.trial) log(colors.blue, `   🎁 ${config.trial}`);
    if (config.savings) log(colors.green, `   💚 ${config.savings}`);
    log(colors.blue, `   📦 ${config.products}`);
    log(colors.blue, `   📋 ${config.orders}`);
    log(colors.blue, `   ✨ ${config.features.join(', ')}`);
  });
}

function showNextSteps(envCheck, stripeCheck, supabaseCheck) {
  log(colors.bold, '\n📋 PRÓXIMOS PASSOS:');

  if (!envCheck.allRequired) {
    log(colors.red, '\n❌ 1. Configure as variáveis de ambiente obrigatórias no arquivo .env');
    log(colors.blue, '   Copie o .env.example e preencha com suas credenciais');
  } else {
    log(colors.green, '\n✅ 1. Variáveis de ambiente configuradas');
  }

  if (stripeCheck < 2) {
    log(colors.red, '\n❌ 2. Configure o Stripe:');
    log(colors.blue, '   - Crie conta no Stripe Dashboard');
    log(colors.blue, '   - Configure produtos Premium (mensal e anual)');
    log(colors.blue, '   - Configure webhooks');
    log(colors.blue, '   - Consulte: docs/stripe-setup-guide.md');
  } else {
    log(colors.green, '\n✅ 2. Stripe configurado');
  }

  if (supabaseCheck < 2) {
    log(colors.red, '\n❌ 3. Configure o Supabase:');
    log(colors.blue, '   - Execute: npm run setup:subscriptions');
  } else {
    log(colors.green, '\n✅ 3. Supabase configurado');
  }

  if (envCheck.allRequired && stripeCheck >= 2 && supabaseCheck >= 2) {
    log(colors.green, '\n🎉 4. Sistema pronto! Execute: npm run dev');
  } else {
    log(colors.yellow, '\n⏳ 4. Complete os passos acima antes de executar o sistema');
  }
}

function main() {
  log(colors.bold, '🧪 VERIFICAÇÃO DO SISTEMA DE ASSINATURAS - DOCE MENU\n');

  const envCheck = checkEnvironmentVariables();
  const stripeCheck = checkStripeKeys();
  const supabaseCheck = checkSupabaseConfig();
  
  showPlanConfiguration();
  showNextSteps(envCheck, stripeCheck, supabaseCheck);

  // Resumo final
  const totalChecks = 3;
  const passedChecks = (envCheck.allRequired ? 1 : 0) + (stripeCheck >= 2 ? 1 : 0) + (supabaseCheck >= 2 ? 1 : 0);

  log(colors.bold, `\n📊 RESUMO: ${passedChecks}/${totalChecks} verificações passaram`);

  if (passedChecks === totalChecks) {
    log(colors.green, '🎉 Sistema de assinaturas está pronto para uso!');
  } else {
    log(colors.yellow, '⚠️ Complete a configuração antes de usar o sistema');
  }

  log(colors.blue, '\n📖 Documentação completa: README-SUBSCRIPTIONS.md');
}

main();
