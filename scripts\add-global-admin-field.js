import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Carregar variáveis de ambiente
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Erro: VITE_SUPABASE_URL ou VITE_SUPABASE_SERVICE_KEY não definidos.');
  console.error('Por favor, defina essas variáveis no arquivo .env');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function addGlobalAdminField() {
  try {
    console.log('🔧 Adicionando campo is_global_admin na tabela users...');

    // Verificar se a coluna já existe
    const { data: columns, error: columnsError } = await supabase
      .from('information_schema.columns')
      .select('column_name')
      .eq('table_name', 'users')
      .eq('column_name', 'is_global_admin');

    if (columnsError) {
      console.error('❌ Erro ao verificar colunas:', columnsError);
      return;
    }

    if (columns && columns.length > 0) {
      console.log('✅ Campo is_global_admin já existe na tabela users');
      return;
    }

    // Adicionar a coluna usando SQL direto
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        ALTER TABLE users 
        ADD COLUMN IF NOT EXISTS is_global_admin BOOLEAN DEFAULT FALSE NOT NULL;
      `
    });

    if (error) {
      // Se a função exec_sql não existir, tentar uma abordagem alternativa
      console.log('⚠️ Função exec_sql não disponível. Tentando abordagem alternativa...');
      
      // Tentar inserir um registro de teste para forçar a criação da coluna
      const { error: insertError } = await supabase
        .from('users')
        .upsert({
          id: 999999, // ID temporário que não deve existir
          email: '<EMAIL>',
          is_global_admin: false
        }, { onConflict: 'id' });

      if (insertError && !insertError.message.includes('duplicate key')) {
        console.error('❌ Erro ao adicionar campo:', insertError);
        return;
      }

      // Remover o registro temporário se foi criado
      await supabase
        .from('users')
        .delete()
        .eq('id', 999999);
    }

    console.log('✅ Campo is_global_admin adicionado com sucesso!');
    
    // Verificar se existe algum usuário para promover a admin global
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, email, is_global_admin')
      .limit(5);

    if (usersError) {
      console.error('❌ Erro ao buscar usuários:', usersError);
      return;
    }

    if (users && users.length > 0) {
      console.log('\n📋 Usuários encontrados:');
      users.forEach((user, index) => {
        console.log(`${index + 1}. ${user.email} (ID: ${user.id}) - Admin Global: ${user.is_global_admin || false}`);
      });

      console.log('\n💡 Para promover um usuário a super-administrador global, execute:');
      console.log('node scripts/promote-global-admin.js <user_id>');
    }

  } catch (error) {
    console.error('❌ Erro inesperado:', error);
  }
}

// Executar se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  addGlobalAdminField();
}

export { addGlobalAdminField };
