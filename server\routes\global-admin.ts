import { Router } from 'express';
import { storage } from '../storage';
import { requireGlobalAdmin } from '../middleware/global-admin';
import { isAuthenticated } from '../firebaseAuth';
import { subscriptionService } from '../subscription-service';

const router = Router();

// Aplicar middlewares de autenticação em todas as rotas
router.use(isAuthenticated);
router.use(requireGlobalAdmin);

/**
 * GET /api/admin/global/analytics
 * Retorna métricas globais da plataforma
 */
router.get('/analytics', async (req, res) => {
  try {
    console.log('Buscando analytics globais...');

    // Buscar todas as lojas
    const stores = await storage.getAllStores();
    
    // Buscar todas as assinaturas
    const subscriptions = await storage.getAllSubscriptions();
    
    // Buscar pedidos dos últimos 30 dias
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const recentOrders = await storage.getOrdersByDateRange(thirtyDaysAgo, new Date());
    
    // Calcular métricas
    const totalStores = stores.length;
    const activeStores = stores.filter(store => store.isActive !== false).length;
    const premiumStores = subscriptions.filter(sub => sub.planType === 'premium' && sub.status === 'active').length;
    
    const totalRevenue = recentOrders.reduce((sum, order) => {
      const orderTotal = order.activeRevision?.total || order.total || 0;
      return sum + orderTotal;
    }, 0);
    
    const totalOrders = recentOrders.length;
    const avgOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;
    
    // Top 10 lojas por número de pedidos
    const storeOrderCounts = recentOrders.reduce((acc, order) => {
      acc[order.storeId] = (acc[order.storeId] || 0) + 1;
      return acc;
    }, {} as Record<number, number>);
    
    const topStoresByOrders = Object.entries(storeOrderCounts)
      .map(([storeId, orderCount]) => {
        const store = stores.find(s => s.id === parseInt(storeId));
        return {
          storeId: parseInt(storeId),
          storeName: store?.name || 'Loja não encontrada',
          storeSlug: store?.slug || '',
          orderCount,
          isActive: store?.isActive !== false
        };
      })
      .sort((a, b) => b.orderCount - a.orderCount)
      .slice(0, 10);

    // Top 10 lojas por receita
    const storeRevenues = recentOrders.reduce((acc, order) => {
      const orderTotal = order.activeRevision?.total || order.total || 0;
      acc[order.storeId] = (acc[order.storeId] || 0) + orderTotal;
      return acc;
    }, {} as Record<number, number>);
    
    const topStoresByRevenue = Object.entries(storeRevenues)
      .map(([storeId, revenue]) => {
        const store = stores.find(s => s.id === parseInt(storeId));
        return {
          storeId: parseInt(storeId),
          storeName: store?.name || 'Loja não encontrada',
          storeSlug: store?.slug || '',
          revenue,
          isActive: store?.isActive !== false
        };
      })
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 10);

    // Crescimento mensal (comparar com mês anterior)
    const sixtyDaysAgo = new Date();
    sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60);
    
    const previousMonthOrders = await storage.getOrdersByDateRange(sixtyDaysAgo, thirtyDaysAgo);
    const previousMonthRevenue = previousMonthOrders.reduce((sum, order) => {
      const orderTotal = order.activeRevision?.total || order.total || 0;
      return sum + orderTotal;
    }, 0);
    
    const revenueGrowth = previousMonthRevenue > 0 
      ? ((totalRevenue - previousMonthRevenue) / previousMonthRevenue) * 100 
      : 0;
    
    const orderGrowth = previousMonthOrders.length > 0 
      ? ((totalOrders - previousMonthOrders.length) / previousMonthOrders.length) * 100 
      : 0;

    const analytics = {
      summary: {
        totalStores,
        activeStores,
        premiumStores,
        totalRevenue,
        totalOrders,
        avgOrderValue,
        revenueGrowth,
        orderGrowth
      },
      topStoresByOrders,
      topStoresByRevenue,
      subscriptionDistribution: {
        free: subscriptions.filter(sub => sub.planType === 'free').length,
        premium: premiumStores,
        canceled: subscriptions.filter(sub => sub.status === 'canceled').length,
        pastDue: subscriptions.filter(sub => sub.status === 'past_due').length
      }
    };

    res.json(analytics);
  } catch (error) {
    console.error('Erro ao buscar analytics globais:', error);
    res.status(500).json({ 
      message: 'Erro ao buscar analytics globais',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

/**
 * GET /api/admin/global/stores
 * Lista todas as lojas com filtros e paginação
 */
router.get('/stores', async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      status, 
      planType, 
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    console.log('Buscando lojas globais com filtros:', { page, limit, status, planType, search, sortBy, sortOrder });

    // Buscar todas as lojas
    const allStores = await storage.getAllStores();
    
    // Buscar assinaturas para cada loja
    const storesWithSubscriptions = await Promise.all(
      allStores.map(async (store) => {
        const subscription = await storage.getActiveSubscription(store.id);
        const user = await storage.getUserById(store.userId);
        
        return {
          ...store,
          subscription,
          user: user ? {
            id: user.id,
            email: user.email,
            fullName: user.fullName,
            createdAt: user.createdAt
          } : null,
          isActive: store.isActive !== false // Default para true se não definido
        };
      })
    );

    // Aplicar filtros
    let filteredStores = storesWithSubscriptions;

    if (status) {
      const isActive = status === 'active';
      filteredStores = filteredStores.filter(store => store.isActive === isActive);
    }

    if (planType) {
      filteredStores = filteredStores.filter(store => 
        store.subscription?.planType === planType
      );
    }

    if (search) {
      const searchLower = (search as string).toLowerCase();
      filteredStores = filteredStores.filter(store =>
        store.name.toLowerCase().includes(searchLower) ||
        store.slug.toLowerCase().includes(searchLower) ||
        store.user?.email?.toLowerCase().includes(searchLower)
      );
    }

    // Aplicar ordenação
    filteredStores.sort((a, b) => {
      let aValue, bValue;
      
      switch (sortBy) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'createdAt':
          aValue = new Date(a.createdAt).getTime();
          bValue = new Date(b.createdAt).getTime();
          break;
        case 'lastActivity':
          // Por enquanto usar createdAt, depois implementar lastActivity real
          aValue = new Date(a.updatedAt || a.createdAt).getTime();
          bValue = new Date(b.updatedAt || b.createdAt).getTime();
          break;
        default:
          aValue = a.id;
          bValue = b.id;
      }

      if (sortOrder === 'desc') {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      } else {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      }
    });

    // Aplicar paginação
    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = startIndex + limitNum;

    const paginatedStores = filteredStores.slice(startIndex, endIndex);
    const totalStores = filteredStores.length;
    const totalPages = Math.ceil(totalStores / limitNum);

    res.json({
      stores: paginatedStores,
      pagination: {
        currentPage: pageNum,
        totalPages,
        totalStores,
        hasNextPage: pageNum < totalPages,
        hasPrevPage: pageNum > 1
      }
    });
  } catch (error) {
    console.error('Erro ao buscar lojas globais:', error);
    res.status(500).json({ 
      message: 'Erro ao buscar lojas globais',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

/**
 * GET /api/admin/global/stores/:id
 * Retorna detalhes específicos de uma loja
 */
router.get('/stores/:id', async (req, res) => {
  try {
    const storeId = parseInt(req.params.id);

    if (isNaN(storeId)) {
      return res.status(400).json({ message: 'ID da loja inválido' });
    }

    console.log('Buscando detalhes da loja:', storeId);

    const store = await storage.getStoreById(storeId);

    if (!store) {
      return res.status(404).json({ message: 'Loja não encontrada' });
    }

    // Buscar dados adicionais
    const [subscriptions, user, products, orders, customers] = await Promise.all([
      storage.getSubscriptionsByStoreId(storeId),
      storage.getUserById(store.userId),
      storage.getProductsByStoreId(storeId),
      storage.getOrdersByStoreId(storeId),
      storage.getCustomersByStoreId(storeId)
    ]);

    // Pegar a assinatura mais recente (independente do status)
    const subscription = subscriptions.length > 0 ? subscriptions[0] : null;

    // Calcular métricas dos últimos 30 dias
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentOrders = orders.filter(order =>
      new Date(order.createdAt) >= thirtyDaysAgo
    );

    const totalRevenue = recentOrders.reduce((sum, order) => {
      const orderTotal = order.activeRevision?.total || order.total || 0;
      return sum + orderTotal;
    }, 0);

    const storeDetails = {
      ...store,
      isActive: store.isActive !== false,
      subscription,
      user: user ? {
        id: user.id,
        email: user.email,
        fullName: user.fullName,
        createdAt: user.createdAt
      } : null,
      metrics: {
        totalProducts: products.length,
        totalOrders: orders.length,
        totalCustomers: customers.length,
        recentOrders: recentOrders.length,
        recentRevenue: totalRevenue,
        avgOrderValue: recentOrders.length > 0 ? totalRevenue / recentOrders.length : 0
      }
    };

    res.json(storeDetails);
  } catch (error) {
    console.error('Erro ao buscar detalhes da loja:', error);
    res.status(500).json({
      message: 'Erro ao buscar detalhes da loja',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

/**
 * PATCH /api/admin/global/stores/:id/toggle-status
 * Ativa ou desativa uma loja
 */
router.patch('/stores/:id/toggle-status', async (req, res) => {
  try {
    const storeId = parseInt(req.params.id);
    const { isActive } = req.body;

    if (isNaN(storeId)) {
      return res.status(400).json({ message: 'ID da loja inválido' });
    }

    if (typeof isActive !== 'boolean') {
      return res.status(400).json({ message: 'Status deve ser um valor booleano' });
    }

    console.log('Alterando status da loja:', storeId, 'para:', isActive);

    const store = await storage.getStoreById(storeId);

    if (!store) {
      return res.status(404).json({ message: 'Loja não encontrada' });
    }

    // Atualizar status da loja
    const updatedStore = await storage.updateStore(storeId, { isActive });

    res.json({
      message: `Loja ${isActive ? 'ativada' : 'desativada'} com sucesso`,
      store: updatedStore
    });
  } catch (error) {
    console.error('Erro ao alterar status da loja:', error);
    res.status(500).json({
      message: 'Erro ao alterar status da loja',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

/**
 * GET /api/admin/global/subscriptions
 * Retorna todas as assinaturas da plataforma com filtros
 */
router.get('/subscriptions', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      planType,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    console.log('Buscando assinaturas globais com filtros:', { page, limit, status, planType, search, sortBy, sortOrder });

    // Buscar todas as assinaturas
    const allSubscriptions = await storage.getAllSubscriptions();

    // Buscar dados das lojas e usuários para cada assinatura
    const subscriptionsWithDetails = await Promise.all(
      allSubscriptions.map(async (subscription) => {
        const store = await storage.getStoreById(subscription.storeId);
        const user = store ? await storage.getUserById(store.userId) : null;

        return {
          ...subscription,
          store: store ? {
            id: store.id,
            name: store.name,
            slug: store.slug,
            isActive: store.isActive !== false
          } : null,
          user: user ? {
            id: user.id,
            email: user.email,
            fullName: user.fullName,
            createdAt: user.createdAt
          } : null
        };
      })
    );

    // Aplicar filtros
    let filteredSubscriptions = subscriptionsWithDetails;

    if (status) {
      filteredSubscriptions = filteredSubscriptions.filter(sub => sub.status === status);
    }

    if (planType) {
      filteredSubscriptions = filteredSubscriptions.filter(sub => sub.planType === planType);
    }

    if (search) {
      const searchLower = (search as string).toLowerCase();
      filteredSubscriptions = filteredSubscriptions.filter(sub =>
        sub.store?.name.toLowerCase().includes(searchLower) ||
        sub.store?.slug.toLowerCase().includes(searchLower) ||
        sub.user?.email?.toLowerCase().includes(searchLower) ||
        sub.stripeCustomerId?.toLowerCase().includes(searchLower)
      );
    }

    // Aplicar ordenação
    filteredSubscriptions.sort((a, b) => {
      let aValue, bValue;

      switch (sortBy) {
        case 'storeName':
          aValue = a.store?.name || '';
          bValue = b.store?.name || '';
          break;
        case 'userEmail':
          aValue = a.user?.email || '';
          bValue = b.user?.email || '';
          break;
        case 'planType':
          aValue = a.planType;
          bValue = b.planType;
          break;
        case 'status':
          aValue = a.status;
          bValue = b.status;
          break;
        case 'createdAt':
        default:
          aValue = new Date(a.createdAt).getTime();
          bValue = new Date(b.createdAt).getTime();
          break;
      }

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortOrder === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
      }

      return sortOrder === 'asc' ? (aValue as number) - (bValue as number) : (bValue as number) - (aValue as number);
    });

    // Aplicar paginação
    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = startIndex + limitNum;

    const paginatedSubscriptions = filteredSubscriptions.slice(startIndex, endIndex);
    const totalSubscriptions = filteredSubscriptions.length;
    const totalPages = Math.ceil(totalSubscriptions / limitNum);

    res.json({
      subscriptions: paginatedSubscriptions,
      pagination: {
        currentPage: pageNum,
        totalPages,
        totalSubscriptions,
        hasNextPage: pageNum < totalPages,
        hasPrevPage: pageNum > 1
      }
    });
  } catch (error) {
    console.error('Erro ao buscar assinaturas globais:', error);
    res.status(500).json({
      message: 'Erro ao buscar assinaturas globais',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

/**
 * GET /api/admin/global/subscriptions/:id
 * Retorna detalhes específicos de uma assinatura
 */
router.get('/subscriptions/:id', async (req, res) => {
  try {
    const subscriptionId = parseInt(req.params.id);

    if (isNaN(subscriptionId)) {
      return res.status(400).json({ message: 'ID da assinatura inválido' });
    }

    console.log('Buscando detalhes da assinatura:', subscriptionId);

    const subscription = await storage.getSubscription(subscriptionId);

    if (!subscription) {
      return res.status(404).json({ message: 'Assinatura não encontrada' });
    }

    // Buscar dados adicionais
    const store = await storage.getStoreById(subscription.storeId);
    const user = store ? await storage.getUserById(store.userId) : null;

    // Buscar dados do Stripe se disponível
    let stripeData = null;
    if (subscription.stripeSubscriptionId) {
      try {
        stripeData = await subscriptionService.getStripeSubscriptionDetails(subscription.stripeSubscriptionId);
      } catch (error) {
        console.warn('Erro ao buscar dados do Stripe:', error);
      }
    }

    const subscriptionDetails = {
      ...subscription,
      store: store ? {
        id: store.id,
        name: store.name,
        slug: store.slug,
        isActive: store.isActive !== false,
        createdAt: store.createdAt
      } : null,
      user: user ? {
        id: user.id,
        email: user.email,
        fullName: user.fullName,
        createdAt: user.createdAt
      } : null,
      stripeData
    };

    res.json(subscriptionDetails);
  } catch (error) {
    console.error('Erro ao buscar detalhes da assinatura:', error);
    res.status(500).json({
      message: 'Erro ao buscar detalhes da assinatura',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

// Função auxiliar para calcular datas de assinatura
function calculateSubscriptionDates(planType: 'free' | 'premium') {
  const now = new Date();
  const currentPeriodStart = new Date(now);

  let currentPeriodEnd: Date | null = null;

  if (planType === 'premium') {
    // Premium: 30 dias de validade
    currentPeriodEnd = new Date(now);
    currentPeriodEnd.setDate(currentPeriodEnd.getDate() + 30);
  }
  // Free: sem data de expiração (null)

  return {
    currentPeriodStart,
    currentPeriodEnd
  };
}

/**
 * PATCH /api/admin/global/subscriptions/:id/update-plan
 * Atualiza o plano de uma assinatura
 */
router.patch('/subscriptions/:id/update-plan', async (req, res) => {
  try {
    const subscriptionId = parseInt(req.params.id);
    const { planType } = req.body;

    if (isNaN(subscriptionId)) {
      return res.status(400).json({ message: 'ID da assinatura inválido' });
    }

    if (!['free', 'premium'].includes(planType)) {
      return res.status(400).json({ message: 'Tipo de plano inválido' });
    }

    console.log('Atualizando plano da assinatura:', subscriptionId, 'para:', planType);

    const subscription = await storage.getSubscription(subscriptionId);

    if (!subscription) {
      return res.status(404).json({ message: 'Assinatura não encontrada' });
    }

    // Calcular novas datas baseadas no plano
    const { currentPeriodStart, currentPeriodEnd } = calculateSubscriptionDates(planType);

    console.log('Datas calculadas:', {
      planType,
      currentPeriodStart,
      currentPeriodEnd
    });

    // Atualizar assinatura com novo plano e datas
    const updatedSubscription = await storage.updateSubscription(subscriptionId, {
      planType,
      currentPeriodStart,
      currentPeriodEnd,
      updatedAt: new Date()
    });

    console.log('Assinatura atualizada:', updatedSubscription);

    if (!updatedSubscription) {
      return res.status(500).json({ message: 'Erro ao atualizar assinatura' });
    }

    res.json({
      message: 'Plano da assinatura atualizado com sucesso',
      subscription: updatedSubscription
    });
  } catch (error) {
    console.error('Erro ao atualizar plano da assinatura:', error);
    res.status(500).json({
      message: 'Erro ao atualizar plano da assinatura',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

/**
 * PATCH /api/admin/global/subscriptions/:id/update-status
 * Atualiza o status de uma assinatura
 */
router.patch('/subscriptions/:id/update-status', async (req, res) => {
  try {
    const subscriptionId = parseInt(req.params.id);
    const { status } = req.body;

    if (isNaN(subscriptionId)) {
      return res.status(400).json({ message: 'ID da assinatura inválido' });
    }

    if (!['active', 'past_due', 'canceled', 'unpaid', 'incomplete'].includes(status)) {
      return res.status(400).json({ message: 'Status inválido' });
    }

    console.log('Atualizando status da assinatura:', subscriptionId, 'para:', status);

    const subscription = await storage.getSubscription(subscriptionId);

    if (!subscription) {
      return res.status(404).json({ message: 'Assinatura não encontrada' });
    }

    // Atualizar assinatura
    const updatedSubscription = await storage.updateSubscription(subscriptionId, {
      status,
      updatedAt: new Date()
    });

    if (!updatedSubscription) {
      return res.status(500).json({ message: 'Erro ao atualizar assinatura' });
    }

    res.json({
      message: 'Status da assinatura atualizado com sucesso',
      subscription: updatedSubscription
    });
  } catch (error) {
    console.error('Erro ao atualizar status da assinatura:', error);
    res.status(500).json({
      message: 'Erro ao atualizar status da assinatura',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

/**
 * PATCH /api/admin/global/users/:id/toggle-admin
 * Promove ou remove permissões de super-administrador
 */
router.patch('/users/:id/toggle-admin', async (req, res) => {
  try {
    const userId = parseInt(req.params.id);
    const { isGlobalAdmin } = req.body;

    if (isNaN(userId)) {
      return res.status(400).json({ message: 'ID do usuário inválido' });
    }

    if (typeof isGlobalAdmin !== 'boolean') {
      return res.status(400).json({ message: 'Status de admin deve ser um valor booleano' });
    }

    console.log('Alterando permissões de admin global do usuário:', userId, 'para:', isGlobalAdmin);

    const user = await storage.getUserById(userId);

    if (!user) {
      return res.status(404).json({ message: 'Usuário não encontrado' });
    }

    // Não permitir que o usuário remova suas próprias permissões
    if (req.user && req.user.id === userId && !isGlobalAdmin) {
      return res.status(400).json({
        message: 'Você não pode remover suas próprias permissões de super-administrador'
      });
    }

    // Atualizar permissões do usuário
    const updatedUser = await storage.updateUser(userId, { isGlobalAdmin });

    res.json({
      message: `Usuário ${isGlobalAdmin ? 'promovido a' : 'removido de'} super-administrador com sucesso`,
      user: {
        id: updatedUser.id,
        email: updatedUser.email,
        fullName: updatedUser.fullName,
        isGlobalAdmin: updatedUser.isGlobalAdmin
      }
    });
  } catch (error) {
    console.error('Erro ao alterar permissões de admin:', error);
    res.status(500).json({
      message: 'Erro ao alterar permissões de admin',
      error: error instanceof Error ? error.message : 'Erro desconhecido'
    });
  }
});

export { router as globalAdminRouter };
