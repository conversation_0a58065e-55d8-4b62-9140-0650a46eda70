// Interface de dados para o dashboard administrativo
export interface DashboardData {
  // Resumo rápido
  summary: {
    pendingOrders: number;
    confirmedOrders: number;
    monthlyRevenue: number;
    nextDelivery: {
      id: number;
      customerName: string;
      deliveryTime: string;
    } | null;
  };

  // Lista de pedidos pendentes
  pendingOrders: {
    id: number;
    customerName: string;
    customerPhone: string;
    products: string; // Lista concatenada de produtos
    total: number;
    status: string;
    deliveryTime: string;
    isLate: boolean;
  }[];

  // Atividade do site
  siteActivity: {
    monthlyVisits: number;
    todayVisits: number;
    conversionRate: number;
    returningCustomers: number;
  };

  // Top produtos
  topProducts: {
    name: string;
    totalSold: number;
    revenue: number;
  }[];

  // Clientes recentes
  recentCustomers: {
    id: number;
    name: string;
    phone: string;
    lastOrderDate: string;
    totalOrders: number;
  }[];
}

export interface EffectiveOrder {
  id: number;
  status: string;
  created_at: string;
  customer_id: number;
  customer_name: string;
  customer_phone: string;
  total: number;
  subtotal: number;
  payment_status: string;
  receiving_date: string;
  receiving_time: string;
  receiving_method: string;
  has_revision: boolean;
  is_late: boolean;
  store_id: number;
}