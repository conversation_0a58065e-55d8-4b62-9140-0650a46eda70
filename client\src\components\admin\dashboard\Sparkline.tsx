import { LineChart, Line, ResponsiveContainer } from 'recharts';
import { cn } from "@/lib/utils";

interface SparklineProps {
  data: Array<{ value: number; date?: string }>;
  color?: string;
  trend?: 'up' | 'down' | 'neutral';
  className?: string;
  height?: number;
}

export function Sparkline({ 
  data, 
  color = "#4361EE", 
  trend = 'neutral',
  className,
  height = 40 
}: SparklineProps) {
  if (!data || data.length === 0) {
    return (
      <div 
        className={cn("flex items-center justify-center bg-gray-50 rounded", className)}
        style={{ height }}
      >
        <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
      </div>
    );
  }

  // Determine color based on trend
  const lineColor = trend === 'up' ? '#10B981' : trend === 'down' ? '#EF4444' : color;
  
  return (
    <div className={cn("relative", className)} style={{ height }}>
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={data}>
          <Line
            type="monotone"
            dataKey="value"
            stroke={lineColor}
            strokeWidth={2}
            dot={false}
            activeDot={false}
            animationDuration={800}
            animationEasing="ease-in-out"
          />
        </LineChart>
      </ResponsiveContainer>
      
      {/* Gradient overlay for iOS-like effect */}
      <div 
        className="absolute inset-0 pointer-events-none rounded"
        style={{
          background: `linear-gradient(135deg, ${lineColor}08 0%, transparent 50%, ${lineColor}05 100%)`
        }}
      />
    </div>
  );
}
