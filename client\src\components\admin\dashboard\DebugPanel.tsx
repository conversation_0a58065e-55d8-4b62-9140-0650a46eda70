import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useFinancialData } from "@/hooks/useFinancialData";
import { PeriodValue } from "./PeriodFilter";
import { useAuth } from "@/context/FirebaseAuthContext";

interface DebugPanelProps {
  selectedPeriod: PeriodValue;
  financialData: any;
  isLoading: boolean;
  error: any;
}

export function DebugPanel({ selectedPeriod, financialData, isLoading, error }: DebugPanelProps) {
  const [debugOrders, setDebugOrders] = useState<any>(null);
  const [isLoadingDebug, setIsLoadingDebug] = useState(false);
  const { user, isAuthenticated } = useAuth();
  const [authInfo, setAuthInfo] = useState<any>(null);

  // Verificar informações de autenticação
  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Verificar se temos o Firebase inicializado e o usuário está logado
        const firebaseModule = await import('@/lib/firebase');
        const { auth } = firebaseModule;
        const currentUser = auth.currentUser;

        setAuthInfo({
          isAuthenticated,
          user: user ? {
            uid: user.uid,
            email: user.email,
            displayName: user.displayName
          } : null,
          currentUser: currentUser ? {
            uid: currentUser.uid,
            email: currentUser.email,
            displayName: currentUser.displayName
          } : null
        });
      } catch (error) {
        console.error('Error checking auth:', error);
        setAuthInfo({ error: error.message });
      }
    };

    checkAuth();
  }, [user, isAuthenticated]);

  const fetchDebugOrders = async () => {
    setIsLoadingDebug(true);
    try {
      const response = await fetch('/api/debug/orders');
      if (response.ok) {
        const data = await response.json();
        setDebugOrders(data);
      } else {
        console.error('Error fetching debug orders:', response.statusText);
      }
    } catch (error) {
      console.error('Error fetching debug orders:', error);
    } finally {
      setIsLoadingDebug(false);
    }
  };

  const createTestOrders = async () => {
    setIsLoadingDebug(true);
    try {
      const response = await fetch('/api/debug/create-test-orders', {
        method: 'POST'
      });
      if (response.ok) {
        const data = await response.json();
        console.log('Test orders created:', data);
        // Refresh debug orders
        await fetchDebugOrders();
      } else {
        console.error('Error creating test orders:', response.statusText);
      }
    } catch (error) {
      console.error('Error creating test orders:', error);
    } finally {
      setIsLoadingDebug(false);
    }
  };

  const testAuthenticatedRequest = async () => {
    setIsLoadingDebug(true);
    try {
      console.log('[DEBUG] Testing authenticated request...');

      // Verificar estado do Firebase antes da requisição
      const firebaseModule = await import('@/lib/firebase');
      const { auth } = firebaseModule;
      const currentUser = auth.currentUser;

      console.log('[DEBUG] Firebase state before request:', {
        currentUser: currentUser ? {
          uid: currentUser.uid,
          email: currentUser.email,
          displayName: currentUser.displayName
        } : null,
        isSignedIn: !!currentUser
      });

      if (!currentUser) {
        throw new Error('No Firebase user found - user may not be authenticated');
      }

      // Testar usando apiRequest
      const { apiRequest } = await import('@/lib/queryClient');
      console.log('[DEBUG] Calling apiRequest...');

      const response = await apiRequest('GET', '/api/debug/orders');
      console.log('[DEBUG] Authenticated request successful:', response);

      // Se response é um objeto Response, extrair JSON
      let data;
      if (response instanceof Response) {
        data = await response.json();
      } else {
        data = response;
      }

      setDebugOrders(data);
    } catch (error) {
      console.error('[DEBUG] Authenticated request failed:', error);
    } finally {
      setIsLoadingDebug(false);
    }
  };

  return (
    <Card className="p-4 bg-yellow-50 border-yellow-200">
      <h3 className="text-lg font-semibold text-yellow-800 mb-4">🔍 Debug Panel - Cards Financeiros</h3>

      <div className="space-y-4">
        {/* Informações de Autenticação */}
        <div>
          <h4 className="font-medium text-yellow-700">Status de Autenticação:</h4>
          <div className="text-sm bg-white p-2 rounded border">
            {authInfo ? (
              <>
                <p><strong>Autenticado:</strong> {authInfo.isAuthenticated ? 'Sim' : 'Não'}</p>
                <p><strong>User Context:</strong> {authInfo.user ? `${authInfo.user.uid} (${authInfo.user.email})` : 'Nenhum'}</p>
                <p><strong>Firebase Current User:</strong> {authInfo.currentUser ? `${authInfo.currentUser.uid} (${authInfo.currentUser.email})` : 'Nenhum'}</p>
                {authInfo.error && <p className="text-red-600"><strong>Erro:</strong> {authInfo.error}</p>}
              </>
            ) : (
              <p>Verificando autenticação...</p>
            )}
          </div>
        </div>

        {/* Status do Hook */}
        <div>
          <h4 className="font-medium text-yellow-700">Status do Hook useFinancialData:</h4>
          <div className="text-sm space-y-1">
            <p><strong>Período:</strong> {selectedPeriod}</p>
            <p><strong>URL da Requisição:</strong> /api/dashboard/financial?period={selectedPeriod}</p>
            <p><strong>Loading:</strong> {isLoading ? 'Sim' : 'Não'}</p>
            <p><strong>Error:</strong> {error ? error.message : 'Nenhum'}</p>
            <p><strong>Dados recebidos:</strong> {financialData ? 'Sim' : 'Não'}</p>
            {error && (
              <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded">
                <p className="text-red-700 text-xs"><strong>Erro completo:</strong></p>
                <pre className="text-red-600 text-xs whitespace-pre-wrap">{JSON.stringify(error, null, 2)}</pre>
              </div>
            )}
          </div>
        </div>

        {/* Dados Financeiros */}
        {financialData && (
          <div>
            <h4 className="font-medium text-yellow-700">Dados Financeiros Recebidos:</h4>
            <div className="text-sm bg-white p-2 rounded border">
              <p><strong>Receita Total:</strong> R$ {financialData.summary?.totalRevenue || 0}</p>
              <p><strong>Receita do Mês:</strong> R$ {financialData.summary?.currentMonthRevenue || 0}</p>
              <p><strong>Total de Pedidos:</strong> {financialData.summary?.totalOrders || 0}</p>
              <p><strong>Ticket Médio:</strong> R$ {financialData.summary?.avgOrderValue || 0}</p>
              <p><strong>Sparkline Points:</strong> {financialData.sparklineData?.totalRevenue?.length || 0}</p>
            </div>
          </div>
        )}

        {/* Botões de Debug */}
        <div className="flex flex-wrap gap-2">
          <Button
            onClick={fetchDebugOrders}
            disabled={isLoadingDebug}
            variant="outline"
            size="sm"
          >
            {isLoadingDebug ? 'Carregando...' : 'Verificar Pedidos (fetch)'}
          </Button>

          <Button
            onClick={testAuthenticatedRequest}
            disabled={isLoadingDebug}
            variant="outline"
            size="sm"
          >
            {isLoadingDebug ? 'Testando...' : 'Testar com apiRequest'}
          </Button>

          <Button
            onClick={async () => {
              console.log('[DEBUG] Testing manual URL with UID...');
              setIsLoadingDebug(true);
              try {
                const firebaseModule = await import('@/lib/firebase');
                const { auth } = firebaseModule;

                // Aguardar o estado de autenticação se necessário
                let currentUser = auth.currentUser;
                if (!currentUser) {
                  console.log('[DEBUG] No current user, waiting for auth state...');
                  await new Promise((resolve) => {
                    const unsubscribe = auth.onAuthStateChanged((user) => {
                      if (user) {
                        currentUser = user;
                        unsubscribe();
                        resolve(user);
                      }
                    });
                    // Timeout após 3 segundos
                    setTimeout(() => {
                      unsubscribe();
                      resolve(null);
                    }, 3000);
                  });
                }

                if (currentUser) {
                  const url = `/api/debug/orders?uid=${currentUser.uid}`;
                  console.log('[DEBUG] Manual URL:', url);
                  const response = await fetch(url, { credentials: 'include' });
                  console.log('[DEBUG] Manual response:', response.status, response.statusText);
                  if (response.ok) {
                    const data = await response.json();
                    console.log('[DEBUG] Manual data:', data);
                    setDebugOrders(data);
                  } else {
                    const errorText = await response.text();
                    console.error('[DEBUG] Manual response error:', errorText);
                  }
                } else {
                  console.error('[DEBUG] No current user for manual test after waiting');
                }
              } catch (error) {
                console.error('[DEBUG] Manual test failed:', error);
              } finally {
                setIsLoadingDebug(false);
              }
            }}
            disabled={isLoadingDebug}
            variant="outline"
            size="sm"
          >
            {isLoadingDebug ? 'Testando...' : 'Teste Manual com UID'}
          </Button>

          <Button
            onClick={async () => {
              console.log('[DEBUG] Testing financial-all endpoint...');
              setIsLoadingDebug(true);
              try {
                const { apiRequest } = await import('@/lib/queryClient');
                const response = await apiRequest('GET', '/api/debug/financial-all');

                let data;
                if (response instanceof Response) {
                  data = await response.json();
                } else {
                  data = response;
                }

                console.log('[DEBUG] Financial-all result:', data);
                alert(`Resultado: Receita Total: R$ ${data.summary?.totalRevenue || 0}, Pedidos: ${data.summary?.totalOrders || 0}`);
              } catch (error) {
                console.error('[DEBUG] Financial-all test failed:', error);
                alert(`Erro: ${error.message}`);
              } finally {
                setIsLoadingDebug(false);
              }
            }}
            disabled={isLoadingDebug}
            variant="outline"
            size="sm"
          >
            {isLoadingDebug ? 'Testando...' : 'Testar Todos os Pedidos'}
          </Button>

          <Button
            onClick={createTestOrders}
            disabled={isLoadingDebug}
            variant="outline"
            size="sm"
          >
            {isLoadingDebug ? 'Criando...' : 'Criar Pedidos de Teste'}
          </Button>
        </div>

        {/* Dados de Debug dos Pedidos */}
        {debugOrders && (
          <div>
            <h4 className="font-medium text-yellow-700">Pedidos na Base de Dados:</h4>
            <div className="text-sm bg-white p-2 rounded border">
              <p><strong>Store ID:</strong> {debugOrders.storeId}</p>
              <p><strong>Total de Pedidos:</strong> {debugOrders.totalOrders}</p>

              <div className="mt-2">
                <p><strong>Pedidos por Status:</strong></p>
                <ul className="ml-4">
                  {Object.entries(debugOrders.statusCounts || {}).map(([status, count]) => (
                    <li key={status}>
                      {status}: {count as number} pedidos
                    </li>
                  ))}
                </ul>
              </div>

              <div className="mt-2">
                <p><strong>Receita por Status:</strong></p>
                <ul className="ml-4">
                  {Object.entries(debugOrders.revenueByStatus || {}).map(([status, revenue]) => (
                    <li key={status}>
                      {status}: R$ {(revenue as number).toFixed(2)}
                    </li>
                  ))}
                </ul>
              </div>

              {debugOrders.sampleOrders && debugOrders.sampleOrders.length > 0 && (
                <div className="mt-2">
                  <p><strong>Pedidos de Exemplo:</strong></p>
                  <div className="max-h-32 overflow-y-auto">
                    {debugOrders.sampleOrders.map((order: any) => (
                      <div key={order.id} className="text-xs border-b py-1">
                        ID: {order.id} | Status: {order.status} | Total: R$ {order.total} | Data: {order.created_at?.split('T')[0]}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Instruções */}
        <div className="text-xs text-yellow-600">
          <p><strong>Instruções:</strong></p>
          <ol className="list-decimal ml-4 space-y-1">
            <li>Clique em "Verificar Pedidos na Base" para ver se existem pedidos</li>
            <li>Se não houver pedidos confirmados/entregues, clique em "Criar Pedidos de Teste"</li>
            <li>Verifique o console do navegador (F12) para logs detalhados</li>
            <li>Se os dados aparecerem aqui mas não nos cards, há problema na UI</li>
          </ol>
        </div>
      </div>
    </Card>
  );
}
