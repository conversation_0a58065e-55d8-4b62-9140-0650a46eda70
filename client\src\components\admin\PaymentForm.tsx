import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CreditCard, Calendar, DollarSign, MessageSquare } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';
import { useTranslation } from '@/hooks/useTranslation';
import { type CreatePaymentData } from '@/hooks/useOrderPayments';

// Schema será criado dinamicamente para incluir validação de valor máximo
const createPaymentSchema = (maxAmount: number) => z.object({
  valor: z.number()
    .min(0.01, 'Valor deve ser maior que zero')
    .max(maxAmount + 0.01, `Valor não pode ser maior que ${(maxAmount + 0.01).toFixed(2).replace('.', ',')}`), // Tolerância de 1 centavo
  data: z.string().min(1, 'Data é obrigatória'),
  metodo: z.enum(['Pix', 'Dinheiro', 'Cartão', 'Transferência', 'Outro'], {
    required_error: 'Método de pagamento é obrigatório',
  }),
  observacao: z.string().optional(),
});

type PaymentFormData = {
  valor: number;
  data: string;
  metodo: 'Pix' | 'Dinheiro' | 'Cartão' | 'Transferência' | 'Outro';
  observacao?: string;
};

interface PaymentFormProps {
  onSubmit: (data: CreatePaymentData) => void;
  onCancel: () => void;
  isSubmitting?: boolean;
  orderTotal: number;
  totalReceived: number;
  currency?: string;
}

export default function PaymentForm({
  onSubmit,
  onCancel,
  isSubmitting = false,
  orderTotal,
  totalReceived,
  currency = 'R$'
}: PaymentFormProps) {
  const { t } = useTranslation();
  const [valorText, setValorText] = useState('');

  const pendingAmount = Math.max(0, orderTotal - totalReceived);

  const paymentSchema = createPaymentSchema(pendingAmount);

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors }
  } = useForm<PaymentFormData>({
    resolver: zodResolver(paymentSchema),
    defaultValues: {
      data: new Date().toISOString().split('T')[0], // Data atual
      metodo: 'Pix',
    }
  });

  const watchedMethod = watch('metodo');
  const watchedValor = watch('valor');

  const handleValorChange = (value: string) => {
    // Remove caracteres não numéricos exceto vírgula e ponto
    const cleanValue = value.replace(/[^\d.,]/g, '');

    // Substitui vírgula por ponto para conversão
    const numericValue = parseFloat(cleanValue.replace(',', '.'));

    if (!isNaN(numericValue)) {
      setValue('valor', numericValue);
    }

    setValorText(cleanValue);
  };

  const handleQuickAmount = (amount: number) => {
    setValue('valor', amount);
    setValorText(amount.toFixed(2).replace('.', ','));
  };

  const onFormSubmit = (data: PaymentFormData) => {
    onSubmit({
      valor: data.valor,
      data: data.data,
      metodo: data.metodo,
      observacao: data.observacao || undefined,
    });
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2">
          <CreditCard className="h-5 w-5 text-green-600" />
          {t('payments.registerPayment') || 'Registrar Pagamento'}
        </CardTitle>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-4">
          {/* Resumo do Pedido */}
          <div className="bg-muted/30 p-3 rounded-lg text-sm">
            <div className="flex justify-between mb-1">
              <span>{t('payments.orderTotal') || 'Total do Pedido'}:</span>
              <span className="font-medium">{formatCurrency(orderTotal, currency)}</span>
            </div>
            <div className="flex justify-between mb-1">
              <span>{t('payments.totalReceived') || 'Total Recebido'}:</span>
              <span className="font-medium text-green-600">{formatCurrency(totalReceived, currency)}</span>
            </div>
            <div className="flex justify-between border-t pt-1">
              <span className="font-medium">{t('payments.pendingAmount') || 'Valor Pendente'}:</span>
              <span className="font-bold text-yellow-600">{formatCurrency(pendingAmount, currency)}</span>
            </div>
          </div>

          {/* Valor */}
          <div className="space-y-2">
            <Label htmlFor="valor" className="flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              {t('payments.amount') || 'Valor Recebido'}
            </Label>
            <div className="relative">
              <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                {currency}
              </span>
              <Input
                id="valor"
                type="text"
                value={valorText}
                onChange={(e) => handleValorChange(e.target.value)}
                placeholder="0,00"
                className="pl-12"
              />
            </div>
            {errors.valor && (
              <p className="text-sm text-red-600">{errors.valor.message}</p>
            )}

            {/* Aviso quando valor excede o pendente */}
            {watchedValor && watchedValor > pendingAmount && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 text-sm">
                <div className="flex items-start gap-2">
                  <div className="text-yellow-600 mt-0.5">⚠️</div>
                  <div>
                    <p className="font-medium text-yellow-800">
                      {t('payments.valueExceedsPending') || 'Valor excede o pendente'}
                    </p>
                    <p className="text-yellow-700 mt-1">
                      {t('payments.valueExceedsPendingMessage') || 'O valor informado é maior que o valor pendente do pedido. Isso resultará em pagamento em excesso.'}
                    </p>
                    <p className="text-yellow-700 mt-1">
                      <strong>{t('payments.excess') || 'Excesso'}:</strong> {formatCurrency(watchedValor - pendingAmount, currency)}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Botões de valor rápido */}
            {pendingAmount > 0 && (
              <div className="flex gap-2 flex-wrap">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => handleQuickAmount(pendingAmount)}
                  className="text-xs"
                >
                  {t('payments.fullAmount') || 'Valor Total'} ({formatCurrency(pendingAmount, currency)})
                </Button>
                {pendingAmount > 20 && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => handleQuickAmount(pendingAmount / 2)}
                    className="text-xs"
                  >
                    {t('payments.halfAmount') || 'Metade'} ({formatCurrency(pendingAmount / 2, currency)})
                  </Button>
                )}
              </div>
            )}
          </div>

          {/* Data */}
          <div className="space-y-2">
            <Label htmlFor="data" className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              {t('payments.date') || 'Data do Recebimento'}
            </Label>
            <Input
              id="data"
              type="date"
              {...register('data')}
            />
            {errors.data && (
              <p className="text-sm text-red-600">{errors.data.message}</p>
            )}
          </div>

          {/* Método */}
          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <CreditCard className="h-4 w-4" />
              {t('payments.method') || 'Método de Pagamento'}
            </Label>
            <Select
              value={watchedMethod}
              onValueChange={(value) => setValue('metodo', value as any)}
            >
              <SelectTrigger>
                <SelectValue placeholder={t('payments.selectMethod') || 'Selecione o método'} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Pix">💳 Pix</SelectItem>
                <SelectItem value="Dinheiro">💵 Dinheiro</SelectItem>
                <SelectItem value="Cartão">💳 Cartão</SelectItem>
                <SelectItem value="Transferência">🏦 Transferência</SelectItem>
                <SelectItem value="Outro">📄 Outro</SelectItem>
              </SelectContent>
            </Select>
            {errors.metodo && (
              <p className="text-sm text-red-600">{errors.metodo.message}</p>
            )}
          </div>

          {/* Observação */}
          <div className="space-y-2">
            <Label htmlFor="observacao" className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              {t('payments.observation') || 'Observação'} ({t('common.optional') || 'opcional'})
            </Label>
            <Textarea
              id="observacao"
              {...register('observacao')}
              placeholder={t('payments.observationPlaceholder') || 'Informações adicionais sobre o recebimento...'}
              rows={3}
            />
          </div>

          {/* Botões */}
          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              className="flex-1"
              disabled={isSubmitting}
            >
              {t('common.cancel') || 'Cancelar'}
            </Button>
            <Button
              type="submit"
              className="flex-1 bg-green-600 hover:bg-green-700"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <div className="flex items-center gap-2">
                  <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  {t('common.saving') || 'Salvando...'}
                </div>
              ) : (
                t('payments.registerPayment') || 'Registrar Pagamento'
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
