import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

interface MetricCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  color?: 'blue' | 'green' | 'orange' | 'red';
  subtitle?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  onClick?: () => void;
  clickable?: boolean;
}

const colorClasses = {
  blue: 'bg-blue-100 text-blue-600',
  green: 'bg-green-100 text-green-600',
  orange: 'bg-orange-100 text-orange-600',
  red: 'bg-red-100 text-red-600'
};

const iosColorClasses = {
  blue: 'bg-blue-500/10 text-blue-600 border-blue-200/30',
  green: 'bg-green-500/10 text-green-600 border-green-200/30',
  orange: 'bg-orange-500/10 text-orange-600 border-orange-200/30',
  red: 'bg-red-500/10 text-red-600 border-red-200/30'
};

export function MetricCard({
  title,
  value,
  icon,
  color = 'blue',
  subtitle,
  action,
  onClick,
  clickable = false
}: MetricCardProps) {
  const cardClasses = `p-5 bg-white/90 backdrop-blur-sm border-0 shadow-lg rounded-2xl transition-all duration-300 ${
    clickable
      ? 'hover:shadow-xl hover:scale-[1.02] cursor-pointer active:scale-[0.98] active:shadow-md'
      : 'hover:shadow-xl'
  }`;

  const CardContent = () => (
    <div className="flex items-start justify-between">
      <div className="flex items-center space-x-3">
        <div className={`p-2.5 rounded-xl border ${iosColorClasses[color]}`}>
          <div className="w-5 h-5">{icon}</div>
        </div>
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          {subtitle && <p className="text-xs text-gray-500 mt-1">{subtitle}</p>}
        </div>
      </div>
      {action && !clickable && (
        <Button
          variant="ghost"
          size="sm"
          onClick={action.onClick}
          className="rounded-xl hover:bg-gray-100/80"
        >
          {action.label}
        </Button>
      )}
    </div>
  );

  if (clickable && onClick) {
    return (
      <Card className={cardClasses} onClick={onClick}>
        <CardContent />
      </Card>
    );
  }

  return (
    <Card className={cardClasses}>
      <CardContent />
    </Card>
  );
}