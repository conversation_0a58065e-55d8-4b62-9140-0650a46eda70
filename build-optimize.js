/**
 * This script optimizes the build output for Firebase hosting
 */

const fs = require('fs');
const path = require('path');

// Define paths
const distPath = path.resolve(__dirname, 'dist');
const publicPath = path.resolve(distPath, 'public');

// Ensure dist/public directory exists
if (!fs.existsSync(publicPath)) {
  console.log('Creating dist/public directory...');
  fs.mkdirSync(publicPath, { recursive: true });
}

// Create a minimal web.config file for Firebase hosting
const firebaseConfig = {
  "hosting": {
    "public": "dist/public",
    "ignore": [
      "firebase.json",
      "**/.*",
      "**/node_modules/**"
    ],
    "rewrites": [
      {
        "source": "**",
        "destination": "/index.html"
      }
    ],
    "headers": [
      {
        "source": "**/*.@(js|css)",
        "headers": [
          {
            "key": "Cache-Control",
            "value": "max-age=31536000"
          }
        ]
      },
      {
        "source": "**/*.@(jpg|jpeg|gif|png|svg|webp)",
        "headers": [
          {
            "key": "Cache-Control",
            "value": "max-age=31536000"
          }
        ]
      }
    ]
  }
};

// Write firebase.json with the optimized config
fs.writeFileSync(
  path.resolve(__dirname, 'firebase.json'), 
  JSON.stringify(firebaseConfig, null, 2)
);

console.log('✅ Optimized firebase.json created');

// Copy .firebaserc to ensure it's available
const firebaseRcContent = {
  "projects": {
    "default": "docemenu"
  }
};

fs.writeFileSync(
  path.resolve(__dirname, '.firebaserc'), 
  JSON.stringify(firebaseRcContent, null, 2)
);

console.log('✅ Optimized .firebaserc created');

console.log('✅ Build optimization completed successfully!');