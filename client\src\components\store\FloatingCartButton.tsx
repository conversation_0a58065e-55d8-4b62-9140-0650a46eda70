import React from 'react';
import { ShoppingCart } from 'lucide-react';
import { useLocation, useParams } from 'wouter';
import { useCart } from '@/context/CartContext';
import { useTranslation } from '@/hooks/useTranslation';
import { formatStoreCurrency } from '@/lib/utils';
import { useStore } from '@/context/StoreContext';

// A prop onClick não é mais necessária pois o botão agora navega diretamente para a página do carrinho
interface FloatingCartButtonProps {
  onClick?: () => void; // Opcional para compatibilidade com código existente
}

const FloatingCartButton: React.FC<FloatingCartButtonProps> = ({ onClick }) => {
  const { t } = useTranslation();
  const { totalItems, subtotal } = useCart();
  const { store } = useStore();
  const [, navigate] = useLocation();
  const { slug } = useParams<{ slug: string }>();
  
  // Navegar para a página do carrinho
  const navigateToCart = () => {
    navigate(`/${slug}/cart`);
  };
  
  // Não mostrar o botão se não houver itens no carrinho
  if (totalItems === 0) return null;
  
  return (
    <button
      onClick={navigateToCart}
      className="fixed bottom-6 left-1/2 transform -translate-x-1/2 bg-primary text-white 
                 flex items-center justify-center py-3 px-5 rounded-full shadow-lg 
                 transition-all duration-300 ease-in-out z-50 hover:brightness-110 hover:text-white 
                 focus:outline-none focus:ring-2 focus:ring-primary/50 min-w-[260px] whitespace-nowrap"
      style={{
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
        boxShadow: '0 4px 10px rgba(0, 0, 0, 0.1)',
        transform: 'translate(-50%, 0) scale(1)',
        transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out'
      }}
      onMouseOver={(e) => {
        e.currentTarget.style.transform = 'translate(-50%, -3px) scale(1.02)';
        e.currentTarget.style.boxShadow = '0 6px 14px rgba(0, 0, 0, 0.15)';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.transform = 'translate(-50%, 0) scale(1)';
        e.currentTarget.style.boxShadow = '0 4px 10px rgba(0, 0, 0, 0.1)';
      }}
    >
      <div className="flex items-center justify-between w-full">
        <div className="flex items-center">
          <div className="bg-white bg-opacity-20 rounded-full h-7 w-7 flex items-center justify-center mr-3">
            <ShoppingCart className="h-4 w-4" />
          </div>
          <div className="font-medium text-sm whitespace-nowrap">
            {t('storefront.viewCart')} ({totalItems})
          </div>
        </div>
        <div className="font-semibold text-sm ml-3 whitespace-nowrap">
          {formatStoreCurrency(subtotal, store?.currency)}
        </div>
      </div>
    </button>
  );
};

export default FloatingCartButton;