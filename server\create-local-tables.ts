import { db, sql } from './db';
import fs from 'fs';
import path from 'path';

async function executeSql(query: string): Promise<void> {
  try {
    await db.execute(sql.raw(query));
    console.log('SQL executado com sucesso.');
  } catch (error) {
    console.error('Erro ao executar SQL:', error);
    throw error;
  }
}

async function createTables(): Promise<void> {
  console.log('Iniciando criação das tabelas no banco de dados local...');
  
  try {
    // Ler o arquivo SQL
    const sqlFilePath = path.join(process.cwd(), 'recreate_tables.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');

    // Drop todas as tabelas existentes primeiro
    console.log('Removendo tabelas existentes...');
    await executeSql(`
      DROP TABLE IF EXISTS cart_items CASCADE;
      DROP TABLE IF EXISTS store_visits CASCADE;
      DROP TABLE IF EXISTS order_items CASCADE;
      DROP TABLE IF EXISTS orders CASCADE;
      DROP TABLE IF EXISTS customers CASCADE;
      DROP TABLE IF EXISTS variation_options CASCADE;
      DROP TABLE IF EXISTS product_variations CASCADE;
      DROP TABLE IF EXISTS products CASCADE;
      DROP TABLE IF EXISTS categories CASCADE;
      DROP TABLE IF EXISTS stores CASCADE;
      DROP TABLE IF EXISTS users CASCADE;
    `);

    // Criar todas as tabelas novamente
    console.log('Criando novas tabelas...');
    await executeSql(sqlContent);

    console.log('Tabelas criadas com sucesso!');
  } catch (error) {
    console.error('Falha ao criar tabelas:', error);
    process.exit(1);
  }
}

// Executar o script
createTables().then(() => {
  console.log('Script finalizado.');
  process.exit(0);
}).catch(err => {
  console.error('Erro na execução do script:', err);
  process.exit(1);
});
