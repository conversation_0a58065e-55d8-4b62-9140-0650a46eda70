#!/usr/bin/env node

/**
 * Script para configurar o sistema de assinaturas
 * Este script executa a migração do banco de dados e configura dados iniciais
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

dotenv.config();

// Para obter __dirname em ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuração do Supabase
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Erro: SUPABASE_URL e SUPABASE_SERVICE_ROLE_KEY são obrigatórios');
  console.error('Configure essas variáveis no arquivo .env');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function runMigration() {
  console.log('🚀 Iniciando configuração do sistema de assinaturas...\n');

  try {
    // Ler o arquivo de migração
    const migrationPath = path.join(__dirname, '..', 'migrations', 'add_subscriptions_table.sql');
    
    if (!fs.existsSync(migrationPath)) {
      console.error('❌ Arquivo de migração não encontrado:', migrationPath);
      process.exit(1);
    }

    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    console.log('📄 Executando migração do banco de dados...');
    
    // Executar a migração
    const { error } = await supabase.rpc('exec_sql', { sql: migrationSQL });
    
    if (error) {
      console.error('❌ Erro ao executar migração:', error);
      process.exit(1);
    }

    console.log('✅ Migração executada com sucesso!');

    // Verificar se a tabela foi criada
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .eq('table_name', 'subscriptions');

    if (tablesError) {
      console.error('❌ Erro ao verificar tabela:', tablesError);
      process.exit(1);
    }

    if (tables && tables.length > 0) {
      console.log('✅ Tabela "subscriptions" criada com sucesso!');
    } else {
      console.error('❌ Tabela "subscriptions" não foi encontrada após a migração');
      process.exit(1);
    }

    // Verificar se existem lojas sem assinatura e criar assinaturas gratuitas
    console.log('🔍 Verificando lojas sem assinatura...');
    
    const { data: storesWithoutSubscription, error: storesError } = await supabase
      .from('stores')
      .select('id, name')
      .not('id', 'in', 
        supabase
          .from('subscriptions')
          .select('store_id')
      );

    if (storesError) {
      console.error('❌ Erro ao buscar lojas:', storesError);
      process.exit(1);
    }

    if (storesWithoutSubscription && storesWithoutSubscription.length > 0) {
      console.log(`📝 Criando assinaturas gratuitas para ${storesWithoutSubscription.length} loja(s)...`);
      
      const subscriptionsToCreate = storesWithoutSubscription.map(store => ({
        store_id: store.id,
        plan_type: 'free',
        status: 'active',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }));

      const { error: insertError } = await supabase
        .from('subscriptions')
        .insert(subscriptionsToCreate);

      if (insertError) {
        console.error('❌ Erro ao criar assinaturas:', insertError);
        process.exit(1);
      }

      console.log('✅ Assinaturas gratuitas criadas com sucesso!');
    } else {
      console.log('ℹ️ Todas as lojas já possuem assinatura');
    }

    // Verificar configuração do Stripe
    console.log('\n🔧 Verificando configuração do Stripe...');
    
    const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
    const stripeWebhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
    const stripePremiumPriceId = process.env.STRIPE_PREMIUM_PRICE_ID;

    if (!stripeSecretKey) {
      console.log('⚠️ STRIPE_SECRET_KEY não configurada');
    } else {
      console.log('✅ STRIPE_SECRET_KEY configurada');
    }

    if (!stripeWebhookSecret) {
      console.log('⚠️ STRIPE_WEBHOOK_SECRET não configurada');
    } else {
      console.log('✅ STRIPE_WEBHOOK_SECRET configurada');
    }

    if (!stripePremiumPriceId) {
      console.log('⚠️ STRIPE_PREMIUM_PRICE_ID não configurada');
    } else {
      console.log('✅ STRIPE_PREMIUM_PRICE_ID configurada');
    }

    console.log('\n🎉 Configuração do sistema de assinaturas concluída!');
    console.log('\n📋 Próximos passos:');
    console.log('1. Configure as variáveis do Stripe no arquivo .env');
    console.log('2. Crie os produtos e preços no Stripe Dashboard');
    console.log('3. Configure os webhooks do Stripe');
    console.log('4. Teste o fluxo de upgrade para Premium');
    console.log('\n📖 Consulte docs/subscription-system.md para mais detalhes');

  } catch (error) {
    console.error('❌ Erro inesperado:', error);
    process.exit(1);
  }
}

// Função para verificar o status atual
async function checkStatus() {
  console.log('🔍 Verificando status do sistema de assinaturas...\n');

  try {
    // Verificar se a tabela existe
    const { data: tables } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .eq('table_name', 'subscriptions');

    if (!tables || tables.length === 0) {
      console.log('❌ Tabela "subscriptions" não encontrada');
      console.log('Execute: npm run setup:subscriptions');
      return;
    }

    console.log('✅ Tabela "subscriptions" encontrada');

    // Verificar assinaturas existentes
    const { data: subscriptions, error } = await supabase
      .from('subscriptions')
      .select('plan_type, status, count(*)')
      .group('plan_type, status');

    if (error) {
      console.error('❌ Erro ao buscar assinaturas:', error);
      return;
    }

    console.log('\n📊 Resumo das assinaturas:');
    if (subscriptions && subscriptions.length > 0) {
      subscriptions.forEach(sub => {
        console.log(`- ${sub.plan_type} (${sub.status}): ${sub.count} loja(s)`);
      });
    } else {
      console.log('- Nenhuma assinatura encontrada');
    }

    // Verificar configuração do Stripe
    const stripeConfigured = !!(
      process.env.STRIPE_SECRET_KEY &&
      process.env.STRIPE_WEBHOOK_SECRET &&
      process.env.STRIPE_PREMIUM_PRICE_ID
    );

    console.log('\n🔧 Configuração do Stripe:');
    console.log(stripeConfigured ? '✅ Configurado' : '⚠️ Incompleto');

  } catch (error) {
    console.error('❌ Erro ao verificar status:', error);
  }
}

// Verificar argumentos da linha de comando
const command = process.argv[2];

if (command === 'status') {
  checkStatus();
} else if (command === 'setup' || !command) {
  runMigration();
} else {
  console.log('Uso: node setup-subscriptions.js [setup|status]');
  console.log('  setup  - Executa a migração e configuração inicial (padrão)');
  console.log('  status - Verifica o status atual do sistema');
}
