import { pgTable, text, serial, integer, boolean, timestamp, jsonb, real, uuid, varchar, index } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// Session storage table for Replit Auth
export const sessions = pgTable(
  "sessions",
  {
    sid: varchar("sid").primaryKey(),
    sess: jsonb("sess").notNull(),
    expire: timestamp("expire").notNull(),
  },
  (table) => [index("IDX_session_expire").on(table.expire)],
);

// Users table - modified for Firebase Auth
export const users = pgTable("users", {
  id: integer("id").primaryKey().notNull(), // Mantendo coluna id como integer para compatibilidade
  username: varchar("username"),
  email: varchar("email").unique(),
  password: varchar("password"), // Campo opcional para compatibilidade com login por email/senha
  fullName: text("full_name"), // Campo para nome completo
  firstName: varchar("first_name"),
  lastName: varchar("last_name"),
  bio: text("bio"),
  profileImageUrl: varchar("profile_image_url"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),

  // Adicionando o Firebase UID como uma coluna separada
  firebaseUid: varchar("firebase_uid").unique(),

  // Campo para super-administradores da plataforma
  isGlobalAdmin: boolean("is_global_admin").default(false).notNull(),
});


// Stores table
export const stores = pgTable("stores", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id),
  name: text("name").notNull(),
  slug: text("slug").notNull().unique(),
  description: text("description"),
  logo: text("logo"),
  headerImage: text("header_image"), // Image to display in the store header
  countryCode: text("country_code").default("+55"), // Prefixo de país para WhatsApp (padrão Brasil +55)
  whatsapp: text("whatsapp"),
  instagram: text("instagram"),
  currency: text("currency").default("R$").notNull(),
  colors: jsonb("colors").notNull(),
  paymentMethods: jsonb("payment_methods").notNull(),
  deliverySettings: jsonb("delivery_settings").default(JSON.stringify({
    allowDelivery: false,
    allowPickup: false,
    pickupDays: [],      // Array de dias permitidos para retirada (0-6, sendo 0=domingo, 1=segunda, etc)
    pickupTimeSlots: [], // Array de horários disponíveis para retirada, cada item no formato HH:MM
    deliveryDays: [],    // Array de dias permitidos para delivery (0-6, sendo 0=domingo, 1=segunda, etc)
    deliveryTimeSlots: [], // Array de horários disponíveis para delivery, cada item no formato HH:MM
    deliveryFee: 0,      // Taxa de entrega fixa (opcional, pode ser zero)
    minAdvanceDays: 0,   // Prazo mínimo de antecedência para pedido (em dias)
    customMessage: "",   // Mensagem personalizada para o cliente
    unavailablePeriods: []  // Períodos de indisponibilidade (férias, feriados, etc)
  })).notNull(), // Configurações de entrega e retirada
  layout: integer("layout_type").default(1).notNull(), // 1: Categorias horizontais, 2: Bottom nav, 3: Seções com carrossel
  layoutSettings: jsonb("layout_settings").default(JSON.stringify({})).notNull(), // Configurações específicas do layout
  // Campos de endereço da loja
  addressStreet: text("address_street"),
  addressNumber: text("address_number"),
  addressComplement: text("address_complement"),
  addressNeighborhood: text("address_neighborhood"),
  addressCity: text("address_city"),
  addressState: text("address_state"),
  contactEmail: text("contact_email"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

// Product categories
export const categories = pgTable("categories", {
  id: serial("id").primaryKey(),
  storeId: integer("store_id").notNull().references(() => stores.id),
  name: text("name").notNull(),
  description: text("description"),
  logo: text("logo"),
  displayOrder: integer("display_order").default(0).notNull(), // Ordem de exibição na loja
  visible: boolean("visible").default(true).notNull(), // Visibilidade da categoria na loja
});

// Products table
export const products = pgTable("products", {
  id: serial("id").primaryKey(),
  storeId: integer("store_id").notNull().references(() => stores.id),
  categoryId: integer("category_id").references(() => categories.id),
  name: text("name").notNull(),
  description: text("description"),
  price: real("price").notNull(),
  images: jsonb("images").default(JSON.stringify([])).notNull(),
  inStock: boolean("in_stock").default(true).notNull(),
  hasVariations: boolean("has_variations").default(false).notNull(),
  variations: jsonb("variations").default(JSON.stringify([])).notNull(), // Array de grupos de variações com suas opções
  visible: boolean("visible").default(true).notNull(), // Visibilidade do produto na loja
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

// Product variations (like "Tamanho", "Recheio", etc.)
export const productVariations = pgTable("product_variations", {
  id: serial("id").primaryKey(),
  productId: integer("product_id").notNull().references(() => products.id),
  name: text("name").notNull(), // e.g. "Tamanho", "Recheio"
  description: text("description"),
  required: boolean("required").default(false).notNull(),
  multipleChoice: boolean("multiple_choice").default(false).notNull(), // Allows selecting multiple options
  maxSelections: integer("max_selections").notNull().default(1), // Agora é notNull para ser sempre salvo
  minSelections: integer("min_selections").notNull().default(0), // Agora é notNull para ser sempre salvo
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

// Product variation options (like "Pequeno", "Médio", "Grande" or "Chocolate", "Morango", etc.)
export const variationOptions = pgTable("variation_options", {
  id: serial("id").primaryKey(),
  variationId: integer("variation_id").notNull().references(() => productVariations.id),
  name: text("name").notNull(), // e.g. "Pequeno", "Chocolate"
  price: real("additional_price").default(0).notNull(), // Valor adicional que esta opção acrescenta ao preço do produto
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

// Customers table (for storing customer information from orders)
export const customers = pgTable("customers", {
  id: serial("id").primaryKey(),
  storeId: integer("store_id").notNull().references(() => stores.id),
  name: text("name").notNull(),
  email: text("email"),
  phone: text("whatsapp"),
  countryCode: text("country_code").default("+55"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

// Orders table
export const orders = pgTable("orders", {
  id: serial("id").primaryKey(),
  storeId: integer("store_id").notNull().references(() => stores.id),
  customerId: integer("customer_id").notNull().references(() => customers.id),
  status: text("status").notNull().default("pending"), // 'pending' | 'confirmed' | 'delivered' | 'cancelled'
  paymentStatus: text("payment_status").notNull().default("pendente"), // 'pendente' | 'parcialmente_recebido' | 'recebido' | 'em_disputa' | 'estornado' | 'cancelado'
  receivingMethod: text("receiving_method").notNull(),
  receivingDate: timestamp("receiving_date").notNull(),
  receivingTime: text("receiving_time"),
  deliveryAddress: jsonb("delivery_address"),
  paymentMethod: text("payment_method").notNull(),
  subtotal: real("subtotal").notNull(),
  deliveryFee: real("delivery_fee").default(0),
  discount: real("discount").default(0),
  discountType: varchar("discount_type", { length: 10 }).default("fixed"), // "fixed" ou "percentage"
  originalPercentage: real("original_percentage"), // Valor original do percentual quando discount_type é percentage
  couponId: integer("coupon_id").references(() => coupons.id),
  couponCode: varchar("coupon_code", { length: 50 }),
  couponType: varchar("coupon_type", { length: 10 }),
  total: real("total").notNull(),
  notes: text("notes"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Order items table
export const orderItems = pgTable("order_items", {
  id: serial("id").primaryKey(),
  orderId: integer("order_id").notNull().references(() => orders.id),
  productId: integer("product_id").notNull(), // Removida referência para permitir que produtos sejam excluídos
  quantity: integer("quantity").notNull(),
  price: real("price").notNull(),
  selectedOptions: jsonb("selected_options").default(JSON.stringify([])).notNull(), // Array de IDs de variationOptions com suas quantidades
  observation: text("observation"), // Customer notes/observations about this item
  productName: text("product_name"), // Nome do produto (para produtos personalizados)
  productDescription: text("product_description"), // Descrição do produto (para produtos personalizados)
  isCustomProduct: boolean("is_custom_product").default(false), // Indica se é um produto personalizado
});

// Store visits for analytics
export const storeVisits = pgTable("store_visits", {
  id: serial("id").primaryKey(),
  storeId: integer("store_id").notNull().references(() => stores.id),
  visitorIp: varchar("visitor_ip", { length: 50 }),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  sessionId: varchar("session_id", { length: 255 }),
  userId: integer("user_id").references(() => users.id),
  page: varchar("page", { length: 255 }),
  userAgent: text("user_agent"),
  referrer: text("referrer"),
});

// Order revisions table
export const orderRevisions = pgTable("order_revisions", {
  id: serial("id").primaryKey(),
  orderId: integer("order_id").notNull().references(() => orders.id),
  customerId: integer("customer_id").references(() => customers.id), // Campo para permitir trocar o cliente na revisão
  revisionNumber: integer("revision_number").notNull(),
  status: text("status").notNull().default("pending"), // 'pending' | 'confirmed' | 'delivered' | 'cancelled'
  paymentStatus: text("payment_status").notNull().default("pendente"), // 'pendente' | 'parcialmente_recebido' | 'recebido' | 'em_disputa' | 'estornado' | 'cancelado'
  receivingMethod: text("receiving_method").notNull(),
  receivingDate: timestamp("receiving_date").notNull(),
  receivingTime: text("receiving_time"),
  deliveryAddress: jsonb("delivery_address"),
  paymentMethod: text("payment_method").notNull(),
  subtotal: real("subtotal").notNull(),
  deliveryFee: real("delivery_fee").default(0),
  discount: real("discount").default(0),
  discountType: varchar("discount_type", { length: 10 }).default("fixed"), // "fixed" ou "percentage"
  originalPercentage: real("original_percentage"), // Valor original do percentual quando discount_type é percentage
  couponId: integer("coupon_id").references(() => coupons.id),
  couponCode: varchar("coupon_code", { length: 50 }),
  couponType: varchar("coupon_type", { length: 10 }),
  total: real("total").notNull(),
  notes: text("notes"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  createdBy: integer("created_by"),
  isCurrent: boolean("is_current").notNull().default(false),
});

// Order revision items table
export const orderRevisionItems = pgTable("order_revision_items", {
  id: serial("id").primaryKey(),
  revisionId: integer("revision_id").notNull().references(() => orderRevisions.id),
  productId: integer("product_id"), // Removida referência para permitir que produtos sejam excluídos
  productName: text("product_name").notNull(),
  productDescription: text("product_description"),
  productImage: text("product_image"),
  quantity: integer("quantity").notNull(),
  unitPrice: real("unit_price").notNull(),
  subtotal: real("subtotal").notNull(),
  selectedVariations: jsonb("selected_variations").default(JSON.stringify([])),
  observation: text("observation"),
});

// Cart items table for saving cart data
export const cartItems = pgTable("cart_items", {
  id: serial("id").primaryKey(),
  storeId: integer("store_id").notNull().references(() => stores.id),
  userId: varchar("user_id"), // Optional, for logged in users
  sessionId: varchar("session_id").notNull(), // Session identifier for guest users
  productId: integer("product_id").notNull(), // Removida referência para permitir que produtos sejam excluídos
  quantity: integer("quantity").notNull().default(1),
  price: real("price").notNull(),
  selectedOptions: jsonb("selected_options").default(JSON.stringify([])).notNull(),
  observation: text("observation"), // Customer notes/observations about this item
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Coupons table
export const coupons = pgTable("coupons", {
  id: serial("id").primaryKey(),
  storeId: integer("store_id").notNull().references(() => stores.id),
  code: varchar("code", { length: 50 }).notNull(),
  tipo: varchar("type", { length: 10 }).notNull(), // "valor_fixo" ou "percentual"
  valor: real("value").notNull(),
  minimoCompra: real("min_purchase"),
  dataValidade: timestamp("expiration_date").notNull(),
  usoUnico: boolean("single_use").default(false),
  ativo: boolean("active").default(true),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

// Coupon usage table
export const couponUsage = pgTable("coupon_usage", {
  id: serial("id").primaryKey(),
  couponId: integer("coupon_id").notNull().references(() => coupons.id),
  customerId: integer("customer_id").notNull().references(() => customers.id),
  orderId: integer("order_id").notNull().references(() => orders.id),
  usedAt: timestamp("used_at").defaultNow().notNull(),
});

// Order payments table - Registra os recebimentos de pagamento
export const orderPayments = pgTable("order_payments", {
  id: serial("id").primaryKey(),
  orderId: integer("order_id").notNull().references(() => orders.id),
  valor: real("value").notNull(), // Valor recebido
  data: timestamp("date").notNull(), // Data do recebimento
  metodo: text("method").notNull(), // 'Pix' | 'Dinheiro' | 'Cartão' | 'Transferência' | 'Outro'
  observacao: text("observation"), // Observação opcional sobre o recebimento
  createdAt: timestamp("created_at").defaultNow().notNull(),
  createdBy: integer("created_by"), // ID do usuário que registrou o recebimento
});

// Subscriptions table - Gerencia assinaturas dos planos
export const subscriptions = pgTable("subscriptions", {
  id: serial("id").primaryKey(),
  storeId: integer("store_id").notNull().references(() => stores.id),
  stripeCustomerId: varchar("stripe_customer_id", { length: 255 }),
  stripeSubscriptionId: varchar("stripe_subscription_id", { length: 255 }),
  planType: varchar("plan_type", { length: 50 }).notNull().default("free"), // 'free' | 'premium'
  status: varchar("status", { length: 50 }).notNull().default("active"), // 'active' | 'past_due' | 'canceled' | 'unpaid' | 'incomplete'
  currentPeriodStart: timestamp("current_period_start"),
  currentPeriodEnd: timestamp("current_period_end"),
  trialEnd: timestamp("trial_end"), // Data de fim do período de trial
  cancelAtPeriodEnd: boolean("cancel_at_period_end").default(false),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Insert schemas for validation
export const insertUserSchema = createInsertSchema(users).omit({ createdAt: true, updatedAt: true });
export const upsertUserSchema = createInsertSchema(users);
export const insertStoreSchema = createInsertSchema(stores).omit({ id: true, createdAt: true });
export const insertProductSchema = createInsertSchema(products).omit({ id: true, createdAt: true });
export const insertCategorySchema = createInsertSchema(categories).omit({ id: true });
export const insertCustomerSchema = createInsertSchema(customers).omit({ id: true, createdAt: true });
export const insertOrderSchema = createInsertSchema(orders).omit({ id: true, createdAt: true });
export const insertOrderItemSchema = createInsertSchema(orderItems).omit({ id: true });
export const insertStoreVisitSchema = createInsertSchema(storeVisits).omit({ id: true, createdAt: true });
export const insertProductVariationSchema = createInsertSchema(productVariations).omit({ id: true });
export const insertVariationOptionSchema = createInsertSchema(variationOptions).omit({ id: true });
export const insertCartItemSchema = createInsertSchema(cartItems).omit({ id: true, createdAt: true, updatedAt: true });
export const insertOrderRevisionSchema = createInsertSchema(orderRevisions).omit({ id: true, createdAt: true });
export const insertOrderRevisionItemSchema = createInsertSchema(orderRevisionItems).omit({ id: true });
export const insertCouponSchema = createInsertSchema(coupons).omit({ id: true, createdAt: true });
export const insertCouponUsageSchema = createInsertSchema(couponUsage).omit({ id: true, usedAt: true });
export const insertOrderPaymentSchema = createInsertSchema(orderPayments).omit({ id: true, createdAt: true });
export const insertSubscriptionSchema = createInsertSchema(subscriptions).omit({ id: true, createdAt: true, updatedAt: true });

// Types

// Tipo para representar as configurações de entrega e retirada
export type DeliverySettings = {
  allowDelivery: boolean;
  allowPickup: boolean;
  pickupDays: string[];      // Array de dias permitidos para retirada (0-6, sendo 0=domingo, 1=segunda, etc)
  pickupTimeSlots: string[]; // Array de horários disponíveis para retirada, cada item no formato HH:MM
  deliveryDays: string[];    // Array de dias permitidos para delivery (0-6, sendo 0=domingo, 1=segunda, etc)
  deliveryTimeSlots: string[]; // Array de horários disponíveis para delivery, cada item no formato HH:MM
  deliveryFee: number;      // Taxa de entrega fixa (opcional, pode ser zero)
  minAdvanceDays: number;   // Prazo mínimo de antecedência para pedido (em dias)
  customMessage: string;   // Mensagem personalizada para o cliente
  unavailablePeriods: Array<{
    startDate: string;
    endDate: string;
    reason?: string;
  }>;  // Períodos de indisponibilidade (férias, feriados, etc)
};

export type User = typeof users.$inferSelect;
export type InsertUser = z.infer<typeof insertUserSchema>;
export type UpsertUser = z.infer<typeof upsertUserSchema>;

export type Store = typeof stores.$inferSelect & {
  deliverySettings?: DeliverySettings;
  // Campos de endereço da loja
  addressStreet?: string;
  addressNumber?: string;
  addressComplement?: string;
  addressNeighborhood?: string;
  addressCity?: string;
  addressState?: string;
  contactEmail?: string;
};
export type InsertStore = z.infer<typeof insertStoreSchema>;

export type Category = typeof categories.$inferSelect;
export type InsertCategory = z.infer<typeof insertCategorySchema>;

export type Product = typeof products.$inferSelect;
export type InsertProduct = z.infer<typeof insertProductSchema>;

export type Customer = typeof customers.$inferSelect;
export type InsertCustomer = z.infer<typeof insertCustomerSchema>;

export type Order = typeof orders.$inferSelect & {
  recebimentos?: OrderPayment[]; // Relação com os recebimentos do pedido
};
export type InsertOrder = z.infer<typeof insertOrderSchema>;

export type OrderItem = typeof orderItems.$inferSelect & {
  productName?: string;
  productDescription?: string;
  isCustomProduct?: boolean;
};
export type InsertOrderItem = z.infer<typeof insertOrderItemSchema> & {
  productName?: string;
  productDescription?: string;
  isCustomProduct?: boolean;
};

export type StoreVisit = typeof storeVisits.$inferSelect;
export type InsertStoreVisit = z.infer<typeof insertStoreVisitSchema>;

export type ProductVariation = typeof productVariations.$inferSelect;
export type InsertProductVariation = z.infer<typeof insertProductVariationSchema>;

export type DatabaseVariationOption = typeof variationOptions.$inferSelect;
export type InsertVariationOption = z.infer<typeof insertVariationOptionSchema>;

export type CartItem = typeof cartItems.$inferSelect;
export type InsertCartItem = z.infer<typeof insertCartItemSchema>;

export type OrderRevision = typeof orderRevisions.$inferSelect & {
  discount_type?: string;
  original_percentage?: number | null;
};
export type InsertOrderRevision = z.infer<typeof insertOrderRevisionSchema>;

export type OrderRevisionItem = typeof orderRevisionItems.$inferSelect;
export type InsertOrderRevisionItem = z.infer<typeof insertOrderRevisionItemSchema>;

export type Coupon = typeof coupons.$inferSelect;
export type InsertCoupon = z.infer<typeof insertCouponSchema>;

export type CouponUsage = typeof couponUsage.$inferSelect;
export type InsertCouponUsage = z.infer<typeof insertCouponUsageSchema>;

export type OrderPayment = typeof orderPayments.$inferSelect;
export type InsertOrderPayment = z.infer<typeof insertOrderPaymentSchema>;

export type Subscription = typeof subscriptions.$inferSelect;
export type InsertSubscription = z.infer<typeof insertSubscriptionSchema>;

// Tipo para representar um recebimento de pagamento
export type Recebimento = {
  valor: number;
  data: Date;
  metodo: 'Pix' | 'Dinheiro' | 'Cartão' | 'Transferência' | 'Outro';
  observacao?: string;
};

// Função para atualizar status de pagamento automaticamente baseado nos recebimentos
export function atualizarStatusPagamento(
  totalPedido: number,
  recebimentos: OrderPayment[]
): 'pendente' | 'parcialmente_recebido' | 'recebido' | 'em_disputa' | 'estornado' | 'cancelado' {
  // Se não há recebimentos, status é pendente
  if (!recebimentos || recebimentos.length === 0) {
    return 'pendente';
  }

  // Calcular total recebido
  const totalRecebido = recebimentos.reduce((total, recebimento) => total + recebimento.valor, 0);

  // Verificar se há recebimentos com status especiais
  const temDisputa = recebimentos.some(r => r.metodo === 'Outro' && r.observacao?.toLowerCase().includes('disputa'));
  const temEstorno = recebimentos.some(r => r.valor < 0); // Valores negativos indicam estorno
  const temCancelamento = recebimentos.some(r => r.metodo === 'Outro' && r.observacao?.toLowerCase().includes('cancelado'));

  // Priorizar status especiais
  if (temCancelamento) {
    return 'cancelado';
  }

  if (temEstorno && totalRecebido <= 0) {
    return 'estornado';
  }

  if (temDisputa) {
    return 'em_disputa';
  }

  // Verificar status baseado no valor recebido
  const tolerancia = 0.01; // Tolerância para diferenças de centavos

  if (totalRecebido >= (totalPedido - tolerancia)) {
    return 'recebido';
  } else if (totalRecebido > 0) {
    return 'parcialmente_recebido';
  } else {
    return 'pendente';
  }
}

// Tipo para representar uma opção selecionada com quantidade (usado nos pedidos)
export type SelectedOption = {
  optionId: number;
  quantity: number;
};

// Tipos para as variações de produtos no formato JSONB (estrutura do JSON armazenado no banco)
export type VariationOption = {
  id: string;
  name: string;
  precoAdicional: number;
  price?: number; // Adicional para compatibilidade com frontend (mesma coisa que precoAdicional)
};

export type VariationGroup = {
  id: string;
  nomeGrupo: string;
  obrigatorio: boolean;
  minSelecionados: number;
  maxSelecionados: number;
  opcoes: VariationOption[];
};

// Tipo para representar períodos de indisponibilidade (férias, feriados, etc)
export type UnavailablePeriod = {
  id: string;        // ID único gerado para identificar o período
  startDate: string; // Data inicial no formato YYYY-MM-DD
  endDate: string;   // Data final no formato YYYY-MM-DD (pode ser igual à startDate para uma data única)
  reason?: string;   // Motivo opcional (ex: "Férias", "Feriado")
};

// Definindo um tipo para melhorar o acesso às variações no objeto Product
export interface ProductWithVariations extends Product {
  variations: VariationGroup[];
}

// Tipos e constantes para sistema de planos de assinatura
export type PlanType = 'free' | 'premium';
export type SubscriptionStatus = 'active' | 'past_due' | 'canceled' | 'unpaid' | 'incomplete';

export interface PlanLimits {
  maxProducts: number;
  maxOrdersPerMonth: number;
  allowPdfGeneration: boolean;
  allowAnalytics: boolean;
  allowWhatsappIntegration: boolean;
  allowCoupons: boolean;
  allowCustomization: boolean;
}

export interface PlanConfig {
  id: PlanType;
  name: string;
  price: number;
  currency: string;
  interval: 'month' | 'year';
  limits: PlanLimits;
  features: string[];
  popular?: boolean;
}

// Configuração dos planos disponíveis
export const PLAN_CONFIGS: Record<PlanType, PlanConfig> = {
  free: {
    id: 'free',
    name: 'Plano Gratuito',
    price: 0,
    currency: 'BRL',
    interval: 'month',
    limits: {
      maxProducts: 10,
      maxOrdersPerMonth: 5,
      allowPdfGeneration: false,
      allowAnalytics: false,
      allowWhatsappIntegration: false,
      allowCoupons: false,
      allowCustomization: false,
    },
    features: [
      'Até 10 produtos cadastrados',
      'Máximo 5 pedidos por mês',
      'Loja online básica',
      'Suporte por email',
    ],
  },
  premium: {
    id: 'premium',
    name: 'Plano Premium',
    price: 29.90,
    currency: 'BRL',
    interval: 'month',
    limits: {
      maxProducts: 50,
      maxOrdersPerMonth: -1, // Ilimitado
      allowPdfGeneration: true,
      allowAnalytics: true,
      allowWhatsappIntegration: true,
      allowCoupons: true,
      allowCustomization: true,
    },
    features: [
      'Até 50 produtos cadastrados',
      'Pedidos ilimitados',
      'Impressão de pedidos (PDF)',
      'Relatórios e analytics completos',
      'Integração WhatsApp',
      'Sistema de cupons de desconto',
      'Personalização visual avançada',
      'Suporte prioritário',
    ],
    popular: true,
  },
};

// Configuração específica para planos com diferentes intervalos
export const PLAN_PRICING_OPTIONS = {
  premium: {
    monthly: {
      price: 29.90,
      interval: 'month' as const,
      savings: 0,
    },
    yearly: {
      price: 299.00, // R$ 24.92/mês (economia de 16.6%)
      interval: 'year' as const,
      savings: 59.80, // Economia anual
      monthlyEquivalent: 24.92,
    },
  },
} as const;

// Função para verificar se uma funcionalidade está disponível no plano
export function isFeatureAvailable(planType: PlanType, feature: keyof PlanLimits): boolean {
  return PLAN_CONFIGS[planType].limits[feature] as boolean;
}

// Função para obter limite numérico de uma funcionalidade
export function getFeatureLimit(planType: PlanType, feature: 'maxProducts' | 'maxOrdersPerMonth'): number {
  return PLAN_CONFIGS[planType].limits[feature];
}

// Função para verificar se um limite foi excedido
export function isLimitExceeded(planType: PlanType, feature: 'maxProducts' | 'maxOrdersPerMonth', currentCount: number): boolean {
  const limit = getFeatureLimit(planType, feature);
  return limit !== -1 && currentCount >= limit;
}
