import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { AlertCircle, Plus, Trash, Pencil, Eye } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { categoryFormSchema, type CategoryFormValues } from '@/lib/schemas/category-schema';
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useTranslation } from '@/hooks/useTranslation';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Switch } from "@/components/ui/switch";

type CategoryFormValues = z.infer<typeof insertCategorySchema>;

export function Categories() {
  const { t } = useTranslation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isOpen, setIsOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<any>(null);
  const [isConfirmDeleteOpen, setIsConfirmDeleteOpen] = useState(false);
  const [categoryToDelete, setCategoryToDelete] = useState<number | null>(null);

  const form = useForm<CategoryFormValues>({
    resolver: zodResolver(categoryFormSchema),
    defaultValues: {
      name: "",
      description: "",
      storeId: 0,
    },
  });

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      form.reset();
      setSelectedCategory(null);
    }
    setIsOpen(open);
  };

  const { data: categories = [], isLoading: isLoadingCategories, error: categoriesError } = useQuery({
    queryKey: ['/api/categories'],
  });

  const createMutation = useMutation({
    mutationFn: (data: CategoryFormValues) => {
      return apiRequest('POST', '/api/categories', data);
    },
    onSuccess: () => {
      toast({
        title: t('admin.categoryCreated'),
        description: t('admin.categoryCreatedDesc'),
      });
      queryClient.invalidateQueries({ queryKey: ['/api/categories'] });
      setIsOpen(false);
      form.reset();
    },
    onError: (error) => {
      toast({
        title: t('common.error'),
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const updateMutation = useMutation({
    mutationFn: (data: { id: number; category: CategoryFormValues }) => {
      return apiRequest('PATCH', `/api/categories/${data.id}`, data.category);
    },
    onSuccess: () => {
      toast({
        title: t('admin.categoryUpdated'),
        description: t('admin.categoryUpdatedDesc'),
      });
      queryClient.invalidateQueries({ queryKey: ['/api/categories'] });
      setIsOpen(false);
      form.reset();
      setSelectedCategory(null);
    },
    onError: (error) => {
      toast({
        title: t('common.error'),
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const deleteMutation = useMutation({
    mutationFn: (id: number) => {
      return apiRequest('DELETE', `/api/categories/${id}`);
    },
    onSuccess: () => {
      toast({
        title: t('admin.categoryDeleted'),
        description: t('admin.categoryDeletedDesc'),
      });
      queryClient.invalidateQueries({ queryKey: ['/api/categories'] });
      setIsConfirmDeleteOpen(false);
      setCategoryToDelete(null);
    },
    onError: (error) => {
      toast({
        title: t('common.error'),
        description: error.message,
        variant: "destructive",
      });
    },
  });

  function onSubmit(values: CategoryFormValues) {
    if (selectedCategory) {
      updateMutation.mutate({ id: selectedCategory.id, category: values });
    } else {
      createMutation.mutate(values);
    }
  }

  const handleEditCategory = (category: any) => {
    setSelectedCategory(category);
    form.reset({
      name: category.name,
      description: category.description || "",
      storeId: category.storeId,
    });
    setIsOpen(true);
  };

  const handleDeleteClick = (id: number) => {
    setCategoryToDelete(id);
    setIsConfirmDeleteOpen(true);
  };

  const confirmDelete = () => {
    if (categoryToDelete) {
      deleteMutation.mutate(categoryToDelete);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">{t('admin.categories')}</h2>

        <Dialog open={isOpen} onOpenChange={handleOpenChange}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              {t('admin.newCategory')}
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {selectedCategory ? t('admin.editCategory') : t('admin.newCategory')}
              </DialogTitle>
              <DialogDescription>
                {t('admin.categoryFormDescription')}
              </DialogDescription>
            </DialogHeader>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('admin.categoryName')}</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('admin.categoryDescription')}</FormLabel>
                      <FormControl>
                        <Textarea {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <DialogFooter>
                  <Button type="submit" disabled={createMutation.isPending || updateMutation.isPending}>
                    {createMutation.isPending || updateMutation.isPending
                      ? t('common.loading')
                      : selectedCategory
                      ? t('common.save')
                      : t('common.create')
                    }
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      {categoriesError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>{t('common.error')}</AlertTitle>
          <AlertDescription>
            {(categoriesError as Error).message || t('common.unknownError')}
          </AlertDescription>
        </Alert>
      )}

      <div className="flex flex-col gap-2 w-full">
        {isLoadingCategories ? (
          <Card>
            <CardContent className="py-4">
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            </CardContent>
          </Card>
        ) : categories.length === 0 ? (
          <Card>
            <CardContent className="py-4">
              <div className="flex items-center justify-center text-center text-muted-foreground">
                {t('admin.noCategories')}
              </div>
            </CardContent>
          </Card>
        ) : (
          categories.map((category: any) => (
            <Card key={category.id} className="w-full hover:bg-accent/5">
              <CardContent className="p-4">
                <div className="flex items-center gap-4 w-full">
                  {category.logo && (
                    <img 
                      src={category.logo} 
                      alt={category.name}
                      className="w-12 h-12 rounded-lg object-cover"
                    />
                  )}
                  <div className="flex-1">
                    <h3 className="font-medium">{category.name}</h3>
                    <p className="text-sm text-muted-foreground line-clamp-1">
                      {category.description || t('admin.noDescription')}
                    </p>
                  </div>
                  <div className="flex space-x-2 items-center">
                    <div className="flex items-center gap-2 mr-2">
                      {category.visible ? 
                        <Eye className="h-4 w-4 text-green-500" /> : 
                        <Eye className="h-4 w-4 text-muted-foreground" />
                      }
                      <Switch
                        key={`visibility-switch-${category.id}-${Date.now()}`}
                        checked={category.visible}
                        onCheckedChange={(checked) => {
                          const updatedCategory = { ...category, visible: checked };

                          // Update the category locally for immediate visual feedback
                          category.visible = checked;

                          // Update React Query cache
                          queryClient.setQueryData(['/api/categories'], (oldData: any) => {
                            if (!oldData) return oldData;
                            return oldData.map((c: any) => 
                              c.id === category.id ? updatedCategory : c
                            );
                          });

                          // Make the API call
                          updateMutation.mutate({ 
                            id: category.id, 
                            category: updatedCategory
                          });
                        }}
                      />
                      <span className="text-sm text-muted-foreground">
                        {category.visible ? t('common.visible') : t('common.hidden')}
                      </span>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleEditCategory(category)}
                    >
                      <Pencil className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleDeleteClick(category.id)}
                    >
                      <Trash className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      <Dialog open={isConfirmDeleteOpen} onOpenChange={setIsConfirmDeleteOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('admin.confirmDelete')}</DialogTitle>
            <DialogDescription>{t('admin.confirmDeleteCategoryDesc')}</DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsConfirmDeleteOpen(false)}
            >
              {t('common.cancel')}
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDelete}
              disabled={deleteMutation.isPending}
            >
              {deleteMutation.isPending ? t('common.loading') : t('common.delete')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}