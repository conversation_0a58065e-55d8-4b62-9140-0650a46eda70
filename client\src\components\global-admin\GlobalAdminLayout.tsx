import { ReactNode } from 'react';
import { Link, useLocation } from 'wouter';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/context/FirebaseAuthContext';
import { useTranslation } from '@/hooks/useTranslation';
import {
  LayoutDashboard,
  Store,
  Users,
  CreditCard,
  Settings,
  ArrowLeft,
  LogOut,
  User,
  Crown
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';

interface GlobalAdminLayoutProps {
  children: ReactNode;
  title: string;
  description?: string;
}

export default function GlobalAdminLayout({ children, title, description }: GlobalAdminLayoutProps) {
  const [location] = useLocation();
  const { user, signOut } = useAuth();
  const { t } = useTranslation();

  const navItems = [
    {
      path: '/admin/global',
      label: t('globalAdmin.analytics.title'),
      icon: <LayoutDashboard className="h-5 w-5" />
    },
    {
      path: '/admin/global/stores',
      label: t('globalAdmin.stores.title'),
      icon: <Store className="h-5 w-5" />
    },
    {
      path: '/admin/global/users',
      label: t('globalAdmin.users.title'),
      icon: <Users className="h-5 w-5" />
    }
  ];

  const isActive = (path: string) => {
    if (path === '/admin/global') {
      return location === path;
    }
    return location.startsWith(path);
  };

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Erro ao fazer logout:', error);
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo e navegação */}
            <div className="flex items-center space-x-8">
              <div className="flex items-center space-x-3">
                <Crown className="h-8 w-8 text-yellow-500" />
                <div>
                  <h1 className="text-xl font-bold text-gray-900">Doce Menu</h1>
                  <Badge variant="secondary" className="text-xs">
                    Super Admin
                  </Badge>
                </div>
              </div>

              {/* Navegação desktop */}
              <nav className="hidden md:flex space-x-1">
                {navItems.map((item) => (
                  <Link
                    key={item.path}
                    href={item.path}
                    className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                      isActive(item.path)
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                    }`}
                  >
                    {item.icon}
                    <span>{item.label}</span>
                  </Link>
                ))}
              </nav>
            </div>

            {/* Ações do usuário */}
            <div className="flex items-center space-x-4">
              {/* Voltar para admin normal */}
              <Link href="/admin">
                <Button variant="outline" size="sm" className="hidden sm:flex">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  {t('common.back')} Admin
                </Button>
              </Link>

              {/* Menu do usuário */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="flex items-center space-x-2">
                    <User className="h-4 w-4" />
                    <span className="hidden sm:inline">{user?.email}</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuItem disabled>
                    <User className="h-4 w-4 mr-2" />
                    {user?.email}
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleSignOut}>
                    <LogOut className="h-4 w-4 mr-2" />
                    {t('common.logout')}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>

        {/* Navegação mobile */}
        <div className="md:hidden border-t bg-white">
          <div className="px-4 py-2">
            <div className="flex space-x-1 overflow-x-auto">
              {navItems.map((item) => (
                <Link
                  key={item.path}
                  href={item.path}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium whitespace-nowrap transition-colors ${
                    isActive(item.path)
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                >
                  {item.icon}
                  <span>{item.label}</span>
                </Link>
              ))}
            </div>
          </div>
        </div>
      </header>

      {/* Conteúdo principal */}
      <main className="flex-1">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Cabeçalho da página */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">{title}</h1>
            {description && (
              <p className="mt-2 text-gray-600">{description}</p>
            )}
          </div>

          {/* Conteúdo da página */}
          {children}
        </div>
      </main>
    </div>
  );
}
