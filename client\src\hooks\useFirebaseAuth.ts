import { useState, useEffect } from "react";
import { 
  User, 
  signInWithPopup,
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut as firebaseSignOut,
  onAuthStateChanged,
  AuthError
} from "firebase/auth";
import { auth, googleProvider } from "@/lib/firebase";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

export interface FirebaseAuthUser {
  id: string;
  email: string | null;
  displayName: string | null;
  photoURL: string | null;
  firebaseUser: User;
}

export function useFirebaseAuth() {
  const [user, setUser] = useState<FirebaseAuthUser | null>(null);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  // Function to convert Firebase user to our user format
  const formatUser = (firebaseUser: User): FirebaseAuthUser => {
    return {
      id: firebaseUser.uid,
      email: firebaseUser.email,
      displayName: firebaseUser.displayName,
      photoURL: firebaseUser.photoURL,
      firebaseUser
    };
  };

  // Sign in with Google
  const signInWithGoogle = async () => {
    try {
      setLoading(true);
      const result = await signInWithPopup(auth, googleProvider);
      
      // Get the Firebase user
      const firebaseUser = result.user;
      
      // Create a user in our database if it doesn't exist yet
      try {
        await apiRequest("POST", "/api/auth/sync-user", {
          id: firebaseUser.uid,
          email: firebaseUser.email,
          username: firebaseUser.displayName || firebaseUser.email?.split('@')[0] || `user-${firebaseUser.uid}`,
          profileImageUrl: firebaseUser.photoURL,
          displayName: firebaseUser.displayName
        });
      } catch (error) {
        console.error("Error syncing user with database:", error);
      }
      
      // Set the formatted user
      setUser(formatUser(firebaseUser));
      
      return firebaseUser;
    } catch (error) {
      console.error("Error signing in with Google:", error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Sign out
  const signOut = async () => {
    try {
      await firebaseSignOut(auth);
      setUser(null);
    } catch (error) {
      console.error("Error signing out:", error);
      throw error;
    }
  };

  // Listen for auth state changes
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      setLoading(true);
      try {
        if (firebaseUser) {
          // User is signed in
          setUser(formatUser(firebaseUser));
          
          // We can optionally refresh user data from our database here
          try {
            await apiRequest("POST", "/api/auth/sync-user", {
              id: firebaseUser.uid,
              email: firebaseUser.email,
              username: firebaseUser.displayName || firebaseUser.email?.split('@')[0] || `user-${firebaseUser.uid}`,
              profileImageUrl: firebaseUser.photoURL,
              displayName: firebaseUser.displayName
            });
          } catch (dbError) {
            console.error("Error syncing user with database:", dbError);
          }
        } else {
          // User is signed out
          setUser(null);
        }
      } catch (error) {
        console.error("Auth state change error:", error);
      } finally {
        setLoading(false);
      }
    });

    // Cleanup subscription on unmount
    return () => unsubscribe();
  }, []);

  // Sign up with email and password
  const signUpWithEmail = async (email: string, password: string, username?: string) => {
    try {
      setLoading(true);
      
      // Create user in Firebase Auth
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const firebaseUser = userCredential.user;
      
      // Set a display name in our database
      const displayName = username || email.split('@')[0];
      
      // Sync user with our database
      try {
        await apiRequest("POST", "/api/auth/sync-user", {
          id: firebaseUser.uid,
          email: firebaseUser.email,
          username: displayName,
          profileImageUrl: null,
          displayName: displayName
        });
      } catch (error) {
        console.error("Error syncing user with database:", error);
      }
      
      // Set the formatted user
      setUser(formatUser(firebaseUser));
      
      toast({
        title: "Conta criada com sucesso",
        description: "Bem-vindo ao Doce Menu!",
        variant: "default",
      });
      
      return firebaseUser;
    } catch (error) {
      const authError = error as AuthError;
      console.error("Error signing up with email:", authError);
      
      // Handle common Firebase auth errors
      const errorMessages: Record<string, string> = {
        'auth/email-already-in-use': 'Este email já está em uso. Tente fazer login.',
        'auth/invalid-email': 'Email inválido. Verifique o formato do email.',
        'auth/weak-password': 'Senha muito fraca. Use uma senha mais forte.',
        'auth/operation-not-allowed': 'Operação não permitida.',
      };
      
      toast({
        title: "Erro ao criar conta",
        description: errorMessages[authError.code] || authError.message || "Ocorreu um erro ao criar sua conta",
        variant: "destructive",
      });
      
      throw error;
    } finally {
      setLoading(false);
    }
  };
  
  // Sign in with email and password
  const signInWithEmail = async (email: string, password: string) => {
    try {
      setLoading(true);
      
      // Sign in with Firebase Auth
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const firebaseUser = userCredential.user;
      
      // Sync user with our database
      try {
        const displayName = firebaseUser.displayName || email.split('@')[0];
        await apiRequest("POST", "/api/auth/sync-user", {
          id: firebaseUser.uid,
          email: firebaseUser.email,
          username: displayName,
          profileImageUrl: firebaseUser.photoURL,
          displayName: displayName
        });
      } catch (error) {
        console.error("Error syncing user with database:", error);
      }
      
      // Set the formatted user
      setUser(formatUser(firebaseUser));
      
      toast({
        title: "Login realizado com sucesso",
        description: "Bem-vindo de volta!",
        variant: "default",
      });
      
      return firebaseUser;
    } catch (error) {
      const authError = error as AuthError;
      console.error("Error signing in with email:", authError);
      
      // Handle common Firebase auth errors
      const errorMessages: Record<string, string> = {
        'auth/user-not-found': 'Usuário não encontrado. Verifique seu email.',
        'auth/wrong-password': 'Senha incorreta. Tente novamente.',
        'auth/invalid-email': 'Email inválido. Verifique o formato do email.',
        'auth/user-disabled': 'Esta conta foi desativada.',
        'auth/too-many-requests': 'Muitas tentativas de login. Tente novamente mais tarde.',
      };
      
      toast({
        title: "Erro ao fazer login",
        description: errorMessages[authError.code] || authError.message || "Ocorreu um erro ao fazer login",
        variant: "destructive",
      });
      
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return {
    user,
    loading,
    signInWithGoogle,
    signInWithEmail,
    signUpWithEmail,
    signOut,
    isAuthenticated: !!user,
  };
}