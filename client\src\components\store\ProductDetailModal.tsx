import { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { formatCurrency, formatStoreCurrency } from '@/lib/utils';
import { useStore } from '@/context/StoreContext';
import { useToast } from '@/hooks/use-toast';
import { useTranslation } from '@/hooks/useTranslation';
import { useCart } from '@/context/CartContext';
import { Product, ProductVariation, VariationOption } from './ProductCard';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { ShoppingCart, ChevronLeft, ChevronRight } from 'lucide-react';

interface ProductDetailModalProps {
  product: Product | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

// Interface para rastrear as opções selecionadas
interface SelectedOptions {
  [variationId: number]: {
    options: number[];
    required: boolean;
    multipleChoice: boolean;
  };
}

export function ProductDetailModal({ product, open, onOpenChange }: ProductDetailModalProps) {
  const { t } = useTranslation();
  const { toast } = useToast();
  const { addItem } = useCart();
  const { store } = useStore();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [selectedOptions, setSelectedOptions] = useState<SelectedOptions>({});
  const [productPrice, setProductPrice] = useState(0);
  const [canAddToCart, setCanAddToCart] = useState(false);
  const [observation, setObservation] = useState('');

  // Quando o produto muda, resetamos o estado
  useEffect(() => {
    if (product) {
      // Inicializar o preço base
      setProductPrice(product.price);
      
      // Inicializar as opções selecionadas
      const initialOptions: SelectedOptions = {};
      if (product.variations && product.variations.length > 0) {
        product.variations.forEach(variation => {
          initialOptions[variation.id] = {
            options: [],
            required: variation.required,
            multipleChoice: variation.multipleChoice
          };
        });
      }
      setSelectedOptions(initialOptions);
      
      // Resetar o índice da imagem
      setCurrentImageIndex(0);
    }
  }, [product]);

  // Verificar se podemos adicionar ao carrinho (todas as variações obrigatórias selecionadas)
  useEffect(() => {
    if (!product) {
      setCanAddToCart(false);
      return;
    }

    // Se não tem variações, pode adicionar ao carrinho
    if (!product.variations || product.variations.length === 0) {
      setCanAddToCart(true);
      return;
    }

    // Verificar se todas as variações obrigatórias têm pelo menos uma opção selecionada
    const allRequiredSelected = Object.values(selectedOptions).every(
      option => !option.required || option.options.length > 0
    );
    
    setCanAddToCart(allRequiredSelected);
  }, [product, selectedOptions]);

  // Calcular o preço total com base nas opções selecionadas
  useEffect(() => {
    if (!product) return;

    let totalPrice = product.price;
    
    // Adicionar preços das opções selecionadas
    if (product.variations) {
      product.variations.forEach(variation => {
        if (selectedOptions[variation.id]) {
          const selectedIds = selectedOptions[variation.id].options;
          
          // Verificar se opcoes existe
          if (variation.opcoes) {
            variation.opcoes.forEach(option => {
              if (selectedIds.includes(option.id)) {
                const additionalPrice = option.precoAdicional || option.price || 0;
                totalPrice += additionalPrice;
              }
            });
          }
        }
      });
    }
    
    setProductPrice(totalPrice);
  }, [product, selectedOptions]);

  // Função para alternar para a próxima imagem
  const nextImage = () => {
    if (!product?.images) return;
    setCurrentImageIndex((prev) => (prev + 1) % product.images!.length);
  };

  // Função para alternar para a imagem anterior
  const prevImage = () => {
    if (!product?.images) return;
    setCurrentImageIndex((prev) => (prev - 1 + product.images!.length) % product.images!.length);
  };

  // Função para lidar com a seleção de opções de variação
  const handleOptionSelect = (variationId: number, optionId: number) => {
    setSelectedOptions(prev => {
      const variation = { ...prev[variationId] };
      
      // Se é múltipla escolha, adicionar ou remover da lista
      if (variation.multipleChoice) {
        if (variation.options.includes(optionId)) {
          variation.options = variation.options.filter(id => id !== optionId);
        } else {
          variation.options = [...variation.options, optionId];
        }
      } else {
        // Se não é múltipla escolha, substituir a seleção atual
        variation.options = [optionId];
      }
      
      return { ...prev, [variationId]: variation };
    });
  };

  // Função para adicionar ao carrinho
  const handleAddToCart = () => {
    if (!product || !canAddToCart) return;

    // Preparar as opções selecionadas para enviar ao carrinho
    const selectedVariations: any[] = [];
    
    try {
      if (product.variations && Array.isArray(product.variations)) {
        product.variations.forEach(variation => {
          // Verificação de segurança para garantir que selectedOptions[variation.id] existe
          if (!selectedOptions[variation.id] || 
              !selectedOptions[variation.id].options || 
              !Array.isArray(selectedOptions[variation.id].options) ||
              selectedOptions[variation.id].options.length === 0) {
            return;
          }
          
          // Para cada opção selecionada desta variação
          selectedOptions[variation.id].options.forEach(optionId => {
            // Verificar se optionId é válido antes de continuar
            if (!optionId) return;
            
            // Verificar se variation.opcoes existe e é um array
            if (!variation.opcoes || !Array.isArray(variation.opcoes)) return;
            
            const option = variation.opcoes.find(opt => opt && opt.id === optionId);
            if (option) {
              // Garantir que temos valores válidos para todos os campos
              const variationName = variation.nomeGrupo || variation.name || 'Opção';
              const optionName = option.name || 'Selecionada';
              const optionPrice = option.precoAdicional || option.price || 0;
              
              selectedVariations.push({
                variationId: variation.id,
                variationName: variationName,
                optionId: option.id,
                optionName: optionName,
                price: optionPrice,
                quantity: 1 // Adicionar quantidade para compatibilidade com o novo formato
              });
            }
          });
        });
      }

      // Adicionar o item ao carrinho
      addItem({
        productId: product.id,
        name: product.name,
        price: productPrice,
        image: product.images && product.images.length > 0 ? product.images[0] : undefined,
        selectedVariations,
        observation: observation.trim() || undefined
      });

      // Mostrar mensagem de sucesso
      toast({
        title: t('storefront.addedToCart'),
        description: t('storefront.productAddedToCart'),
        duration: 3000,
      });

      // Fechar o modal
      onOpenChange(false);
    } catch (error) {
      console.error("Erro ao adicionar item ao carrinho:", error);
      
      // Mostrar mensagem de erro
      toast({
        title: t('common.error'),
        description: t('storefront.errorAddingToCart'),
        variant: "destructive",
        duration: 3000,
      });
    }
  };

  // Se não há produto, não renderizar nada
  if (!product) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px] max-h-screen overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{product.name}</DialogTitle>
          {product.description && (
            <DialogDescription>{product.description}</DialogDescription>
          )}
        </DialogHeader>
        
        {/* Carrossel de imagens */}
        {product.images && product.images.length > 0 && (
          <div className="relative w-full h-48 bg-gray-100 rounded overflow-hidden">
            <img 
              src={product.images[currentImageIndex]} 
              alt={product.name} 
              className="w-full h-full object-cover"
            />
            
            {product.images.length > 1 && (
              <>
                <Button 
                  variant="secondary" 
                  size="icon" 
                  className="absolute left-2 top-1/2 transform -translate-y-1/2 rounded-full opacity-70 hover:opacity-100"
                  onClick={prevImage}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <Button 
                  variant="secondary" 
                  size="icon" 
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 rounded-full opacity-70 hover:opacity-100"
                  onClick={nextImage}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
                
                {/* Indicadores de imagem */}
                <div className="absolute bottom-2 left-0 right-0 flex justify-center space-x-1">
                  {product.images.map((_, index) => (
                    <div 
                      key={index} 
                      className={`w-2 h-2 rounded-full ${index === currentImageIndex ? 'bg-primary' : 'bg-gray-300'}`}
                      onClick={() => setCurrentImageIndex(index)}
                    />
                  ))}
                </div>
              </>
            )}
          </div>
        )}
        
        {/* Variações do produto */}
        {product.variations && product.variations.length > 0 && (
          <div className="space-y-4 mt-4">
            {product.variations.map((variation) => (
              <div key={variation.id} className="space-y-2">
                <div className="flex justify-between">
                  <Label className="text-base font-medium">
                    {variation.name}
                    {variation.required && <span className="text-red-500 ml-1">*</span>}
                  </Label>
                  {variation.multipleChoice && (
                    <span className="text-xs text-muted-foreground">
                      {t('storefront.selectMultiple')}
                    </span>
                  )}
                </div>
                
                {/* Opções da variação */}
                {variation.multipleChoice ? (
                  // Checkbox para múltipla escolha
                  <div className="grid grid-cols-2 gap-2">
                    {variation.opcoes && variation.opcoes.map((option) => (
                      <div key={option.id} className="flex items-start space-x-2 p-2 rounded hover:bg-muted/50 cursor-pointer w-full">
                        <Checkbox 
                          id={`option-${option.id}`}
                          checked={selectedOptions[variation.id]?.options.includes(option.id)}
                          onCheckedChange={() => handleOptionSelect(variation.id, option.id)}
                          className="mt-0.5"
                        />
                        <div className="grid gap-1 flex-1">
                          <label htmlFor={`option-${option.id}`} className="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer">
                            {option.name}
                          </label>
                          {(option.precoAdicional > 0 || option.price > 0) && (
                            <p className="text-xs text-muted-foreground">
                              + {formatStoreCurrency(option.precoAdicional || option.price || 0, store?.currency)}
                            </p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  // Radio para escolha única
                  <RadioGroup 
                    value={selectedOptions[variation.id]?.options[0]?.toString()}
                    onValueChange={(value) => handleOptionSelect(variation.id, value)}
                  >
                    <div className="grid grid-cols-2 gap-2">
                      {variation.opcoes && variation.opcoes.map((option) => (
                        <div key={option.id} className="flex items-center space-x-2 p-2 rounded hover:bg-muted/50 cursor-pointer w-full">
                          <RadioGroupItem value={option.id.toString()} id={`option-${option.id}`} />
                          <div className="grid gap-1 flex-1">
                            <label htmlFor={`option-${option.id}`} className="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer">
                              {option.name}
                            </label>
                            {(option.precoAdicional > 0 || option.price > 0) && (
                              <p className="text-xs text-muted-foreground">
                                + {formatStoreCurrency(option.precoAdicional || option.price || 0, store?.currency)}
                              </p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </RadioGroup>
                )}
              </div>
            ))}
          </div>
        )}
        
        {/* Observações do cliente */}
        <div className="mt-4">
          <Label htmlFor="observation" className="text-base font-medium">
            {t('storefront.observation') || 'Observações'}
          </Label>
          <Textarea 
            id="observation"
            placeholder={t('storefront.observationPlaceholder') || 'Ex: Sem glutén, sem lactose, etc.'}
            value={observation}
            onChange={(e) => setObservation(e.target.value)}
            className="mt-1"
          />
        </div>

        {/* Preço e botão de adicionar ao carrinho */}
        <div className="flex flex-col gap-2 mt-4">
          <div className="flex justify-between items-center">
            <span className="text-lg font-bold">{formatStoreCurrency(productPrice, store?.currency)}</span>
            <Button 
              onClick={handleAddToCart} 
              disabled={!canAddToCart || !product.inStock}
              className="gap-2 primary-button"
            >
              <ShoppingCart className="h-4 w-4" />
              {t('storefront.addToCart')}
            </Button>
          </div>
          
          {/* Mensagem de variações obrigatórias */}
          {!canAddToCart && product.variations && (
            <p className="text-xs text-red-500">
              {t('storefront.selectRequiredOptions')}
            </p>
          )}
          
          {/* Mensagem de fora de estoque */}
          {!product.inStock && (
            <p className="text-xs text-red-500">
              {t('storefront.outOfStock')}
            </p>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default ProductDetailModal;