import { But<PERSON> } from "@/components/ui/button";
import { <PERSON> } from "wouter";
import { useTranslation } from "@/hooks/useTranslation";
import { motion } from "framer-motion";
import { ArrowRight, Store, ShoppingCart, BarChart3, Users, Globe, Check, X, FileText, MessageCircle, Palette, Zap, TrendingUp, Clock, Star, DollarSign, CheckCircle, AlertTriangle, Crown, Eye } from "lucide-react";
import { useState } from "react";

export default function LandingPage() {
  const { t } = useTranslation();
  const [billingInterval, setBillingInterval] = useState<'monthly' | 'yearly'>('monthly');
  
  return (
    <div className="min-h-screen flex flex-col">
      {/* Navigation */}
      <nav className="container mx-auto py-6 px-4 flex justify-between items-center">
        <div className="flex items-center space-x-2">
          <span className="font-bold text-2xl bg-gradient-to-r from-pink-500 to-yellow-500 bg-clip-text text-transparent">
            Doce Menu
          </span>
        </div>
        <div className="hidden md:flex space-x-8 items-center">
          <a href="#features" className="text-gray-600 hover:text-pink-600 transition-colors">
            {t('landing.features')}
          </a>
          <a href="#how-it-works" className="text-gray-600 hover:text-pink-600 transition-colors">
            {t('landing.howItWorks')}
          </a>
          <a href="#pricing" className="text-gray-600 hover:text-pink-600 transition-colors">
            {t('landing.pricing')}
          </a>
          <div className="flex space-x-4">
            <Button variant="outline" asChild size="sm">
              <Link href="/login">{t('common.login')}</Link>
            </Button>
            <Button asChild size="sm" className="bg-gradient-to-r from-pink-500 to-yellow-500 hover:from-pink-600 hover:to-yellow-600">
              <Link href="/register">{t('common.register')}</Link>
            </Button>
          </div>
        </div>
        <div className="md:hidden">
          <Button variant="ghost" size="sm">
            <Link href="/login">{t('common.login')}</Link>
          </Button>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="py-20 px-4 bg-gradient-to-b from-pink-50 to-white">
        <div className="container mx-auto max-w-6xl">
          <div className="flex flex-col lg:flex-row items-center gap-12">
            <div className="lg:w-1/2">
              <motion.h1 
                className="text-4xl sm:text-5xl md:text-6xl font-bold leading-tight"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                {t('landing.heroTitle')}
                <span className="bg-gradient-to-r from-pink-500 to-yellow-500 bg-clip-text text-transparent">
                  {' '}{t('landing.heroTitleHighlight')}
                </span>
              </motion.h1>
              <motion.p 
                className="mt-6 text-xl text-gray-600 max-w-lg"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                {t('landing.heroSubtitle')}
              </motion.p>
              <motion.div 
                className="mt-8 flex flex-col sm:flex-row gap-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <Button asChild size="lg" className="bg-gradient-to-r from-pink-500 to-yellow-500 hover:from-pink-600 hover:to-yellow-600">
                  <Link href="/register">
                    {t('landing.getStarted')}
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
                <Button asChild variant="outline" size="lg">
                  <a href="#how-it-works">
                    {t('landing.learnMore')}
                  </a>
                </Button>
              </motion.div>
            </div>
            <div className="lg:w-1/2">
              <motion.div
                className="relative w-full rounded-3xl bg-white shadow-2xl border border-gray-100 overflow-hidden"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                {/* Header do Dashboard - Replica o real */}
                <div className="bg-gradient-to-br from-gray-50 to-gray-100/50 px-6 py-4 border-b border-gray-200">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="bg-gradient-to-r from-pink-500 to-yellow-500 rounded-lg p-2">
                        <BarChart3 className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <h3 className="font-bold text-gray-900 text-lg">{t('landing.demoDashboardTitle')}</h3>
                        <p className="text-gray-600 text-sm">{t('landing.demoStoreUrl')}</p>
                      </div>
                    </div>
                    <div className="bg-gradient-to-r from-pink-500 to-yellow-500 rounded-full px-3 py-1">
                      <span className="text-white text-xs font-medium">DEMO</span>
                    </div>
                  </div>
                </div>

                {/* Conteúdo do Dashboard - Replica estrutura real */}
                <div className="p-6 space-y-6 bg-gradient-to-br from-gray-50 to-gray-100/50">
                  {/* Resumo Rápido - 3 Cards como no dashboard real */}
                  <div className="grid grid-cols-3 gap-3">
                    {/* Pedidos Pendentes */}
                    <div className="bg-white/90 backdrop-blur-sm border-0 shadow-lg rounded-2xl p-4 hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex-1">
                          <p className="text-xs font-medium text-gray-600 mb-1">{t('landing.demoPendingOrders')}</p>
                          <p className="text-xl font-bold text-gray-900">3</p>
                        </div>
                        <div className="p-2 bg-blue-100 rounded-xl">
                          <ShoppingCart className="h-4 w-4 text-blue-600" />
                        </div>
                      </div>
                    </div>

                    {/* Pedidos Confirmados */}
                    <div className="bg-white/90 backdrop-blur-sm border-0 shadow-lg rounded-2xl p-4 hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex-1">
                          <p className="text-xs font-medium text-gray-600 mb-1">{t('landing.demoConfirmedOrders')}</p>
                          <p className="text-xl font-bold text-gray-900">8</p>
                        </div>
                        <div className="p-2 bg-green-100 rounded-xl">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                        </div>
                      </div>
                    </div>

                    {/* Receita Mensal */}
                    <div className="bg-white/90 backdrop-blur-sm border-0 shadow-lg rounded-2xl p-4 hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex-1">
                          <p className="text-xs font-medium text-gray-600 mb-1">{t('landing.demoMonthlyRevenue')}</p>
                          <p className="text-lg font-bold text-gray-900">R$ 2.840</p>
                        </div>
                        <div className="p-2 bg-green-100 rounded-xl">
                          <DollarSign className="h-4 w-4 text-green-600" />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Resumo Financeiro - Como no dashboard real */}
                  <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                    <h4 className="font-semibold text-gray-900 text-sm mb-3 flex items-center">
                      <DollarSign className="h-4 w-4 mr-2 text-green-600" />
                      {t('landing.demoFinancialSummary')}
                    </h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-xs text-gray-600">{t('landing.demoTotalRevenue')}</p>
                        <p className="text-lg font-bold text-gray-900">R$ 8.420</p>
                        <div className="flex items-center mt-1">
                          <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                          <span className="text-xs text-green-600">+12.5%</span>
                        </div>
                      </div>
                      <div>
                        <p className="text-xs text-gray-600">{t('landing.demoAverageTicket')}</p>
                        <p className="text-lg font-bold text-gray-900">R$ 35,50</p>
                        <div className="flex items-center mt-1">
                          <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                          <span className="text-xs text-green-600">+8.2%</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Top Produtos - Como no dashboard real */}
                  <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                    <div className="flex items-center mb-3">
                      <Crown className="h-4 w-4 text-yellow-600 mr-2" />
                      <h4 className="font-semibold text-gray-900 text-sm">{t('landing.demoTopProducts')}</h4>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                        <div className="flex items-center gap-2">
                          <div className="flex items-center justify-center w-6 h-6 rounded-full text-xs font-bold bg-yellow-500 text-white">1</div>
                          <div>
                            <p className="font-medium text-xs">{t('landing.demoBrigadeiro')}</p>
                            <p className="text-xs text-gray-600">24 vendidos</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <span className="font-semibold text-green-600 text-xs">R$ 84,00</span>
                        </div>
                      </div>
                      <div className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                        <div className="flex items-center gap-2">
                          <div className="flex items-center justify-center w-6 h-6 rounded-full text-xs font-bold bg-gray-400 text-white">2</div>
                          <div>
                            <p className="font-medium text-xs">{t('landing.demoBrownie')}</p>
                            <p className="text-xs text-gray-600">18 vendidos</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <span className="font-semibold text-green-600 text-xs">R$ 160,20</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Pedidos em Andamento - Como no dashboard real */}
                  <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-semibold text-gray-900 text-sm">{t('landing.demoPendingOrdersList')}</h4>
                      <span className="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full">3</span>
                    </div>
                    <div className="space-y-2">
                      <div className="border rounded-lg p-3 hover:bg-gray-50 transition-colors">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-1">
                              <h5 className="font-medium text-gray-900 text-xs">#1247 - Maria Silva</h5>
                              <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">{t('landing.demoOrderConfirmed')}</span>
                            </div>
                            <div className="flex items-center space-x-4 text-xs text-gray-600">
                              <span>R$ 45,50</span>
                              <span>Hoje 14:30</span>
                            </div>
                          </div>
                          <button className="p-1 hover:bg-gray-100 rounded">
                            <Eye className="h-3 w-3 text-gray-400" />
                          </button>
                        </div>
                      </div>
                      <div className="border border-red-200 bg-red-50 rounded-lg p-3">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-1">
                              <h5 className="font-medium text-gray-900 text-xs">#1245 - João Santos</h5>
                              <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full flex items-center gap-1">
                                <AlertTriangle className="w-3 h-3" />
                                {t('landing.demoOrderLate')}
                              </span>
                            </div>
                            <div className="flex items-center space-x-4 text-xs text-gray-600">
                              <span>R$ 67,80</span>
                              <span>Ontem 16:00</span>
                            </div>
                          </div>
                          <button className="p-1 hover:bg-gray-100 rounded">
                            <Eye className="h-3 w-3 text-gray-400" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 px-4">
        <div className="container mx-auto max-w-6xl">
          <div className="text-center max-w-3xl mx-auto">
            <h2 className="text-3xl font-bold mb-6">{t('landing.featuresTitle')}</h2>
            <p className="text-xl text-gray-600 mb-12">{t('landing.featuresSubtitle')}</p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Feature 1 */}
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
              <div className="bg-pink-100 w-14 h-14 rounded-lg flex items-center justify-center mb-4">
                <Store className="h-7 w-7 text-pink-500" />
              </div>
              <h3 className="text-xl font-semibold mb-2">{t('landing.feature1Title')}</h3>
              <p className="text-gray-600">{t('landing.feature1Desc')}</p>
            </div>
            
            {/* Feature 2 */}
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
              <div className="bg-yellow-100 w-14 h-14 rounded-lg flex items-center justify-center mb-4">
                <ShoppingCart className="h-7 w-7 text-yellow-500" />
              </div>
              <h3 className="text-xl font-semibold mb-2">{t('landing.feature2Title')}</h3>
              <p className="text-gray-600">{t('landing.feature2Desc')}</p>
            </div>
            
            {/* Feature 3 */}
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
              <div className="bg-blue-100 w-14 h-14 rounded-lg flex items-center justify-center mb-4">
                <BarChart3 className="h-7 w-7 text-blue-500" />
              </div>
              <h3 className="text-xl font-semibold mb-2">{t('landing.feature3Title')}</h3>
              <p className="text-gray-600">{t('landing.feature3Desc')}</p>
            </div>
            
            {/* Feature 4 */}
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
              <div className="bg-green-100 w-14 h-14 rounded-lg flex items-center justify-center mb-4">
                <Users className="h-7 w-7 text-green-500" />
              </div>
              <h3 className="text-xl font-semibold mb-2">{t('landing.feature4Title')}</h3>
              <p className="text-gray-600">{t('landing.feature4Desc')}</p>
            </div>
            
            {/* Feature 5 */}
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
              <div className="bg-purple-100 w-14 h-14 rounded-lg flex items-center justify-center mb-4">
                <Globe className="h-7 w-7 text-purple-500" />
              </div>
              <h3 className="text-xl font-semibold mb-2">{t('landing.feature5Title')}</h3>
              <p className="text-gray-600">{t('landing.feature5Desc')}</p>
            </div>
            
            {/* Feature 6 */}
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
              <div className="bg-red-100 w-14 h-14 rounded-lg flex items-center justify-center mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-2">{t('landing.feature6Title')}</h3>
              <p className="text-gray-600">{t('landing.feature6Desc')}</p>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section id="how-it-works" className="py-20 px-4 bg-gray-50">
        <div className="container mx-auto max-w-6xl">
          <div className="text-center max-w-3xl mx-auto">
            <h2 className="text-3xl font-bold mb-6">{t('landing.howItWorksTitle')}</h2>
            <p className="text-xl text-gray-600 mb-12">{t('landing.howItWorksSubtitle')}</p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {/* Step 1 */}
            <div className="bg-white rounded-xl p-6 shadow-sm relative">
              <div className="absolute -top-4 -left-4 w-12 h-12 rounded-full bg-gradient-to-r from-pink-500 to-yellow-500 flex items-center justify-center text-white font-bold text-xl">
                1
              </div>
              <h3 className="text-xl font-semibold mb-4 mt-4">{t('landing.step1Title')}</h3>
              <p className="text-gray-600">{t('landing.step1Desc')}</p>
            </div>
            
            {/* Step 2 */}
            <div className="bg-white rounded-xl p-6 shadow-sm relative">
              <div className="absolute -top-4 -left-4 w-12 h-12 rounded-full bg-gradient-to-r from-pink-500 to-yellow-500 flex items-center justify-center text-white font-bold text-xl">
                2
              </div>
              <h3 className="text-xl font-semibold mb-4 mt-4">{t('landing.step2Title')}</h3>
              <p className="text-gray-600">{t('landing.step2Desc')}</p>
            </div>
            
            {/* Step 3 */}
            <div className="bg-white rounded-xl p-6 shadow-sm relative">
              <div className="absolute -top-4 -left-4 w-12 h-12 rounded-full bg-gradient-to-r from-pink-500 to-yellow-500 flex items-center justify-center text-white font-bold text-xl">
                3
              </div>
              <h3 className="text-xl font-semibold mb-4 mt-4">{t('landing.step3Title')}</h3>
              <p className="text-gray-600">{t('landing.step3Desc')}</p>
            </div>
          </div>
        </div>
      </section>

      {/* System Features Section */}
      <section className="py-20 px-4 bg-gradient-to-b from-gray-50 to-white">
        <div className="container mx-auto max-w-6xl">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="text-3xl font-bold mb-6">{t('landing.systemFeaturesTitle')}</h2>
            <p className="text-xl text-gray-600">
              {t('landing.systemFeaturesSubtitle')}
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Gerenciamento de Produtos */}
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
              <div className="bg-pink-100 w-14 h-14 rounded-lg flex items-center justify-center mb-4">
                <Store className="h-7 w-7 text-pink-500" />
              </div>
              <h3 className="text-xl font-semibold mb-3">{t('landing.completeCatalog')}</h3>
              <p className="text-gray-600 mb-4">
                {t('landing.completeCatalogDesc')}
              </p>
              <ul className="text-sm text-gray-500 space-y-1">
                <li>• {t('landing.completeCatalogFeatures.variations')}</li>
                <li>• {t('landing.completeCatalogFeatures.images')}</li>
                <li>• {t('landing.completeCatalogFeatures.inventory')}</li>
              </ul>
            </div>

            {/* Gestão de Pedidos */}
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
              <div className="bg-blue-100 w-14 h-14 rounded-lg flex items-center justify-center mb-4">
                <ShoppingCart className="h-7 w-7 text-blue-500" />
              </div>
              <h3 className="text-xl font-semibold mb-3">{t('landing.smartOrders')}</h3>
              <p className="text-gray-600 mb-4">
                {t('landing.smartOrdersDesc')}
              </p>
              <ul className="text-sm text-gray-500 space-y-1">
                <li>• {t('landing.smartOrdersFeatures.revisions')}</li>
                <li>• {t('landing.smartOrdersFeatures.payments')}</li>
                <li>• {t('landing.smartOrdersFeatures.scheduling')}</li>
              </ul>
            </div>

            {/* Dashboard e Analytics */}
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
              <div className="bg-green-100 w-14 h-14 rounded-lg flex items-center justify-center mb-4">
                <BarChart3 className="h-7 w-7 text-green-500" />
              </div>
              <h3 className="text-xl font-semibold mb-3">{t('landing.advancedAnalytics')}</h3>
              <p className="text-gray-600 mb-4">
                {t('landing.advancedAnalyticsDesc')}
              </p>
              <ul className="text-sm text-gray-500 space-y-1">
                <li>• {t('landing.advancedAnalyticsFeatures.financial')}</li>
                <li>• {t('landing.advancedAnalyticsFeatures.products')}</li>
                <li>• {t('landing.advancedAnalyticsFeatures.customers')}</li>
              </ul>
            </div>

            {/* Base de Clientes */}
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
              <div className="bg-purple-100 w-14 h-14 rounded-lg flex items-center justify-center mb-4">
                <Users className="h-7 w-7 text-purple-500" />
              </div>
              <h3 className="text-xl font-semibold mb-3">{t('landing.customerManagementFull')}</h3>
              <p className="text-gray-600 mb-4">
                {t('landing.customerManagementFullDesc')}
              </p>
              <ul className="text-sm text-gray-500 space-y-1">
                <li>• {t('landing.customerManagementFullFeatures.history')}</li>
                <li>• {t('landing.customerManagementFullFeatures.contact')}</li>
                <li>• {t('landing.customerManagementFullFeatures.whatsapp')}</li>
              </ul>
            </div>

            {/* Sistema de Cupons */}
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
              <div className="bg-yellow-100 w-14 h-14 rounded-lg flex items-center justify-center mb-4">
                <Zap className="h-7 w-7 text-yellow-500" />
              </div>
              <h3 className="text-xl font-semibold mb-3">{t('landing.couponsPromotions')}</h3>
              <p className="text-gray-600 mb-4">
                {t('landing.couponsPromotionsDesc')}
              </p>
              <ul className="text-sm text-gray-500 space-y-1">
                <li>• {t('landing.couponsPromotionsFeatures.discount')}</li>
                <li>• {t('landing.couponsPromotionsFeatures.minimum')}</li>
                <li>• {t('landing.couponsPromotionsFeatures.expiry')}</li>
              </ul>
            </div>

            {/* Geração de PDF */}
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
              <div className="bg-red-100 w-14 h-14 rounded-lg flex items-center justify-center mb-4">
                <FileText className="h-7 w-7 text-red-500" />
              </div>
              <h3 className="text-xl font-semibold mb-3">{t('landing.professionalPrinting')}</h3>
              <p className="text-gray-600 mb-4">
                {t('landing.professionalPrintingDesc')}
              </p>
              <ul className="text-sm text-gray-500 space-y-1">
                <li>• {t('landing.professionalPrintingFeatures.layout')}</li>
                <li>• {t('landing.professionalPrintingFeatures.logo')}</li>
                <li>• {t('landing.professionalPrintingFeatures.formats')}</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-20 px-4">
        <div className="container mx-auto max-w-6xl">
          <div className="text-center max-w-3xl mx-auto">
            <h2 className="text-3xl font-bold mb-6">{t('landing.pricingTitle')}</h2>
            <p className="text-xl text-gray-600 mb-8">{t('landing.pricingSubtitle')}</p>

            {/* Billing Toggle */}
            <div className="flex items-center justify-center mb-12">
              <div className="bg-gray-100 rounded-lg p-1 flex">
                <button
                  onClick={() => setBillingInterval('monthly')}
                  className={`px-6 py-2 rounded-md text-sm font-medium transition-all ${
                    billingInterval === 'monthly'
                      ? 'bg-white text-gray-900 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  {t('landing.monthly')}
                </button>
                <button
                  onClick={() => setBillingInterval('yearly')}
                  className={`px-6 py-2 rounded-md text-sm font-medium transition-all ${
                    billingInterval === 'yearly'
                      ? 'bg-white text-gray-900 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  {t('landing.yearly')}
                  <span className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                    {t('landing.savePercentage')}
                  </span>
                </button>
              </div>
            </div>
          </div>

          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {/* Free Plan */}
            <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-100 flex flex-col">
              <div className="mb-8">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">{t('landing.freePlan')}</h3>
                <div className="flex items-baseline mb-4">
                  <span className="text-4xl font-bold text-gray-900">{t('landing.freePrice')}</span>
                </div>
                <p className="text-gray-600">{t('landing.freePlanDesc')}</p>
              </div>

              <ul className="space-y-4 mb-8 flex-grow">
                <li className="flex items-center">
                  <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                  <span className="text-gray-700">{t('landing.freeFeatures.products')}</span>
                </li>
                <li className="flex items-center">
                  <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                  <span className="text-gray-700">{t('landing.freeFeatures.orders')}</span>
                </li>
                <li className="flex items-center">
                  <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                  <span className="text-gray-700">{t('landing.freeFeatures.store')}</span>
                </li>
                <li className="flex items-center">
                  <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                  <span className="text-gray-700">{t('landing.freeFeatures.support')}</span>
                </li>
              </ul>

              <Button
                variant="outline"
                asChild
                className="w-full h-12 text-base font-medium border-2 hover:bg-gray-50"
              >
                <Link href="/register">{t('landing.getStarted')}</Link>
              </Button>
            </div>

            {/* Premium Plan */}
            <div className="bg-gradient-to-b from-pink-50 to-white rounded-xl p-8 shadow-lg border-2 border-pink-200 flex flex-col relative">
              <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-gradient-to-r from-pink-500 to-yellow-500 text-white text-sm font-bold py-2 px-6 rounded-full">
                {t('landing.popular')}
              </div>
              <div className="mb-8 pt-4">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">{t('landing.premiumPlan')}</h3>
                <div className="flex items-baseline mb-2">
                  <span className="text-4xl font-bold text-gray-900">
                    {billingInterval === 'monthly' ? t('landing.premiumPriceMonthly') : t('landing.premiumPriceYearly')}
                  </span>
                  <span className="text-gray-600 ml-1">
                    {billingInterval === 'monthly' ? t('landing.perMonth') : t('landing.perYear')}
                  </span>
                </div>
                {billingInterval === 'yearly' && (
                  <p className="text-sm text-green-600 font-medium mb-2">
                    {t('landing.monthlyEquivalent')} • {t('landing.savePercentage')}
                  </p>
                )}
                {billingInterval === 'monthly' && (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-4">
                    <p className="text-sm text-green-800 font-medium">{t('landing.trialOffer')}</p>
                    <p className="text-xs text-green-700">{t('landing.trialDescription')}</p>
                  </div>
                )}
                <p className="text-gray-600">{t('landing.premiumPlanDesc')}</p>
              </div>

              <ul className="space-y-4 mb-8 flex-grow">
                <li className="flex items-center">
                  <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                  <span className="text-gray-700">{t('landing.premiumFeatures.products')}</span>
                </li>
                <li className="flex items-center">
                  <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                  <span className="text-gray-700">{t('landing.premiumFeatures.orders')}</span>
                </li>
                <li className="flex items-center">
                  <FileText className="h-5 w-5 text-pink-500 mr-3 flex-shrink-0" />
                  <span className="text-gray-700 font-medium">{t('landing.premiumFeatures.pdf')}</span>
                </li>
                <li className="flex items-center">
                  <BarChart3 className="h-5 w-5 text-pink-500 mr-3 flex-shrink-0" />
                  <span className="text-gray-700 font-medium">{t('landing.premiumFeatures.analytics')}</span>
                </li>
                <li className="flex items-center">
                  <MessageCircle className="h-5 w-5 text-pink-500 mr-3 flex-shrink-0" />
                  <span className="text-gray-700 font-medium">{t('landing.premiumFeatures.whatsapp')}</span>
                </li>
                <li className="flex items-center">
                  <Zap className="h-5 w-5 text-pink-500 mr-3 flex-shrink-0" />
                  <span className="text-gray-700 font-medium">{t('landing.premiumFeatures.coupons')}</span>
                </li>
                <li className="flex items-center">
                  <Palette className="h-5 w-5 text-pink-500 mr-3 flex-shrink-0" />
                  <span className="text-gray-700 font-medium">{t('landing.premiumFeatures.customization')}</span>
                </li>
                <li className="flex items-center">
                  <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                  <span className="text-gray-700">{t('landing.premiumFeatures.support')}</span>
                </li>
              </ul>

              <Button
                asChild
                className="w-full h-12 text-base font-medium bg-gradient-to-r from-pink-500 to-yellow-500 hover:from-pink-600 hover:to-yellow-600 shadow-lg"
              >
                <Link href="/register">
                  {billingInterval === 'monthly' ? t('landing.startFreeTrial') : t('landing.upgradeToPremium')}
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Features Comparison Section */}
      <section className="py-20 px-4 bg-gray-50">
        <div className="container mx-auto max-w-6xl">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="text-3xl font-bold mb-6">{t('landing.featuresComparison')}</h2>
            <p className="text-xl text-gray-600">
              {t('landing.featuresComparisonSubtitle')}
            </p>
          </div>

          <div className="bg-white rounded-xl shadow-lg overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900 w-1/2">
                      {t('landing.feature')}
                    </th>
                    <th className="px-6 py-4 text-center text-sm font-semibold text-gray-900 w-1/4">
                      {t('landing.freePlan')}
                    </th>
                    <th className="px-6 py-4 text-center text-sm font-semibold text-gray-900 w-1/4 bg-pink-50">
                      {t('landing.premiumPlan')}
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  <tr>
                    <td className="px-6 py-4 text-sm text-gray-900">{t('landing.productManagement')}</td>
                    <td className="px-6 py-4 text-center">
                      <div className="flex items-center justify-center">
                        <Check className="h-5 w-5 text-green-500" />
                        <span className="ml-2 text-sm text-gray-600">10 produtos</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 text-center bg-pink-50">
                      <div className="flex items-center justify-center">
                        <Check className="h-5 w-5 text-green-500" />
                        <span className="ml-2 text-sm text-gray-600">50 produtos</span>
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td className="px-6 py-4 text-sm text-gray-900">{t('landing.orderManagement')}</td>
                    <td className="px-6 py-4 text-center">
                      <div className="flex items-center justify-center">
                        <Check className="h-5 w-5 text-green-500" />
                        <span className="ml-2 text-sm text-gray-600">5/mês</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 text-center bg-pink-50">
                      <div className="flex items-center justify-center">
                        <Check className="h-5 w-5 text-green-500" />
                        <span className="ml-2 text-sm text-gray-600">Ilimitado</span>
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td className="px-6 py-4 text-sm text-gray-900">{t('landing.customerManagement')}</td>
                    <td className="px-6 py-4 text-center">
                      <Check className="h-5 w-5 text-green-500" />
                    </td>
                    <td className="px-6 py-4 text-center bg-pink-50">
                      <Check className="h-5 w-5 text-green-500" />
                    </td>
                  </tr>
                  <tr>
                    <td className="px-6 py-4 text-sm text-gray-900">{t('landing.pdfGeneration')}</td>
                    <td className="px-6 py-4 text-center">
                      <X className="h-5 w-5 text-gray-400" />
                    </td>
                    <td className="px-6 py-4 text-center bg-pink-50">
                      <Check className="h-5 w-5 text-green-500" />
                    </td>
                  </tr>
                  <tr>
                    <td className="px-6 py-4 text-sm text-gray-900">{t('landing.dashboardAnalytics')}</td>
                    <td className="px-6 py-4 text-center">
                      <X className="h-5 w-5 text-gray-400" />
                    </td>
                    <td className="px-6 py-4 text-center bg-pink-50">
                      <Check className="h-5 w-5 text-green-500" />
                    </td>
                  </tr>
                  <tr>
                    <td className="px-6 py-4 text-sm text-gray-900">{t('landing.whatsappIntegration')}</td>
                    <td className="px-6 py-4 text-center">
                      <X className="h-5 w-5 text-gray-400" />
                    </td>
                    <td className="px-6 py-4 text-center bg-pink-50">
                      <Check className="h-5 w-5 text-green-500" />
                    </td>
                  </tr>
                  <tr>
                    <td className="px-6 py-4 text-sm text-gray-900">{t('landing.couponSystem')}</td>
                    <td className="px-6 py-4 text-center">
                      <X className="h-5 w-5 text-gray-400" />
                    </td>
                    <td className="px-6 py-4 text-center bg-pink-50">
                      <Check className="h-5 w-5 text-green-500" />
                    </td>
                  </tr>
                  <tr>
                    <td className="px-6 py-4 text-sm text-gray-900">{t('landing.visualCustomization')}</td>
                    <td className="px-6 py-4 text-center">
                      <X className="h-5 w-5 text-gray-400" />
                    </td>
                    <td className="px-6 py-4 text-center bg-pink-50">
                      <Check className="h-5 w-5 text-green-500" />
                    </td>
                  </tr>
                  <tr>
                    <td className="px-6 py-4 text-sm text-gray-900">{t('landing.prioritySupport')}</td>
                    <td className="px-6 py-4 text-center">
                      <X className="h-5 w-5 text-gray-400" />
                    </td>
                    <td className="px-6 py-4 text-center bg-pink-50">
                      <Check className="h-5 w-5 text-green-500" />
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-gradient-to-r from-pink-500 to-yellow-500 text-white">
        <div className="container mx-auto max-w-6xl text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">{t('landing.ctaTitle')}</h2>
          <p className="text-xl md:text-2xl opacity-90 mb-10 max-w-3xl mx-auto">{t('landing.ctaSubtitle')}</p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button
              asChild
              size="lg"
              className="bg-white text-pink-600 hover:bg-gray-100 shadow-lg h-14 px-8 text-lg font-semibold rounded-xl"
            >
              <Link href="/register">
                {t('landing.getStarted')}
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>

            <Button
              asChild
              size="lg"
              variant="outline"
              className="border-2 border-white text-white hover:bg-white hover:text-pink-600 h-14 px-8 text-lg font-semibold rounded-xl"
            >
              <Link href="/register">
                {t('landing.startFreeTrial')}
                <Zap className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>

          <p className="mt-6 text-sm opacity-80">
            {t('landing.ctaFinalText')}
          </p>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 px-4">
        <div className="container mx-auto max-w-6xl">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <h3 className="font-bold text-xl mb-4 bg-gradient-to-r from-pink-400 to-yellow-400 bg-clip-text text-transparent">Doce Menu</h3>
              <p className="text-gray-400">{t('landing.footerTagline')}</p>
            </div>
            <div>
              <h4 className="font-semibold mb-4">{t('landing.footerProduct')}</h4>
              <ul className="space-y-2">
                <li><a href="#features" className="text-gray-400 hover:text-white transition-colors">{t('landing.features')}</a></li>
                <li><a href="#pricing" className="text-gray-400 hover:text-white transition-colors">{t('landing.pricing')}</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">{t('landing.footerFaq')}</a></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">{t('landing.footerCompany')}</h4>
              <ul className="space-y-2">
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">{t('landing.footerAbout')}</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">{t('landing.footerBlog')}</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">{t('landing.footerCareers')}</a></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">{t('landing.footerLegal')}</h4>
              <ul className="space-y-2">
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">{t('landing.footerTerms')}</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">{t('landing.footerPrivacy')}</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">{t('landing.footerCookies')}</a></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-12 pt-8 text-center text-gray-400">
            <p>&copy; {new Date().getFullYear()} Doce Menu. {t('landing.footerRights')}</p>
          </div>
        </div>
      </footer>
    </div>
  );
}