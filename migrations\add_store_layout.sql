-- Adiciona colunas para tipos de layout na tabela de lojas
ALTER TABLE stores
ADD COLUMN IF NOT EXISTS layout_type INTEGER NOT NULL DEFAULT 1,
ADD COLUMN IF NOT EXISTS layout_settings JSONB NOT NULL DEFAULT '{}';

-- Comentários para documentação
COMMENT ON COLUMN stores.layout_type IS 'Tipo de layout da loja (1: Categorias horizontais, 2: Bottom nav, 3: Seções com carrossel)';
COMMENT ON COLUMN stores.layout_settings IS 'Configurações específicas do layout escolhido';

-- Atualiza as lojas existentes
UPDATE stores SET layout_type = 1 WHERE layout_type IS NULL;
UPDATE stores SET layout_settings = '{}' WHERE layout_settings IS NULL;