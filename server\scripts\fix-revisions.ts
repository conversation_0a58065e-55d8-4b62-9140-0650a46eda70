/**
 * <PERSON>ript para executar a correção de todos os cálculos de revisões existentes
 * Corrige inconsistências entre subtotal, desconto e total
 */

import { fixAllRevisionCalculations } from '../utils/fix-revision-calculations';

async function main() {
  console.log('===== INICIANDO CORREÇÃO DE REVISÕES =====');
  console.log('Este script irá corrigir problemas de cálculos em todas as revisões existentes');
  console.log('Incluindo inconsistências entre subtotal, desconto e total');
  console.log('--------------------------------------------------------');
  
  try {
    const correctedCount = await fixAllRevisionCalculations();
    
    console.log('--------------------------------------------------------');
    console.log(`Correção concluída com sucesso!`);
    console.log(`Total de revisões processadas: ${correctedCount}`);
    console.log('--------------------------------------------------------');
    console.log('As próximas revisões já utilizarão o cálculo correto.');
  } catch (error) {
    console.error('ERRO ao executar script de correção:', error);
  }
}

// Executar o script
main()
  .then(() => {
    console.log('Script finalizado.');
    process.exit(0);
  })
  .catch(error => {
    console.error('Erro fatal no script:', error);
    process.exit(1);
  });