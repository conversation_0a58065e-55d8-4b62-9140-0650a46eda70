import { createContext, useContext, ReactNode } from "react";
import { useFire<PERSON>Auth, FirebaseAuthUser } from "@/hooks/useFirebaseAuth";

interface FirebaseAuthContextType {
  user: FirebaseAuthUser | null;
  loading: boolean;
  signInWithGoogle: () => Promise<any>;
  signInWithEmail: (email: string, password: string) => Promise<any>;
  signUpWithEmail: (email: string, password: string, username?: string) => Promise<any>;
  signOut: () => Promise<void>;
  isAuthenticated: boolean;
}

const FirebaseAuthContext = createContext<FirebaseAuthContextType | undefined>(undefined);

export function FirebaseAuthProvider({ children }: { children: ReactNode }) {
  const auth = useFirebaseAuth();
  
  return (
    <FirebaseAuthContext.Provider value={auth}>
      {children}
    </FirebaseAuthContext.Provider>
  );
}

export function useAuth(): FirebaseAuthContextType {
  const context = useContext(FirebaseAuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within a FirebaseAuthProvider");
  }
  return context;
}