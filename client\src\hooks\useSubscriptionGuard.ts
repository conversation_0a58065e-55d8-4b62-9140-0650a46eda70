import { useSubscription } from '@/context/SubscriptionContext';
import { useToast } from '@/hooks/use-toast';
import { useTranslation } from 'react-i18next';
import { PLAN_CONFIGS } from '@shared/schema';

interface UseSubscriptionGuardOptions {
  showToast?: boolean;
  redirectToUpgrade?: boolean;
}

export function useSubscriptionGuard(options: UseSubscriptionGuardOptions = {}) {
  const { showToast = true, redirectToUpgrade = false } = options;
  const { 
    isFeatureAvailable, 
    isLimitExceeded, 
    usageInfo, 
    createCheckoutSession 
  } = useSubscription();
  const { toast } = useToast();
  const { t } = useTranslation();

  // Verificar se uma funcionalidade está disponível
  const checkFeature = (feature: keyof typeof PLAN_CONFIGS.free.limits): boolean => {
    const available = isFeatureAvailable(feature);
    
    if (!available && showToast) {
      toast({
        title: t(`subscription.feature_blocked.${feature}.title`),
        description: t(`subscription.feature_blocked.${feature}.description`),
        variant: "destructive",
      });
    }

    return available;
  };

  // Verificar se um limite foi excedido
  const checkLimit = (feature: 'maxProducts' | 'maxOrdersPerMonth'): boolean => {
    if (!usageInfo) return false;

    // Map feature names to actual usage keys
    const usageKey = feature === 'maxProducts' ? 'products' : 'orders';
    const usage = usageInfo.usage[usageKey];
    
    if (!usage) return false;
    
    const exceeded = usage.isLimitExceeded;
    
    if (exceeded && showToast) {
      toast({
        title: t(`subscription.limit_exceeded.${feature}.title`),
        description: t(`subscription.limit_exceeded.${feature}.description`, {
          current: usage.current,
          limit: usage.limit
        }),
        variant: "destructive",
      });
    }

    return !exceeded;
  };

  // Verificar se pode adicionar produto
  const canAddProduct = (): boolean => {
    return checkLimit('maxProducts');
  };

  // Verificar se pode criar pedido
  const canCreateOrder = (): boolean => {
    return checkLimit('maxOrdersPerMonth');
  };

  // Verificar se pode gerar PDF
  const canGeneratePdf = (): boolean => {
    return checkFeature('allowPdfGeneration');
  };

  // Verificar se pode usar analytics
  const canUseAnalytics = (): boolean => {
    return checkFeature('allowAnalytics');
  };

  // Verificar se pode usar WhatsApp
  const canUseWhatsapp = (): boolean => {
    return checkFeature('allowWhatsappIntegration');
  };

  // Verificar se pode usar cupons
  const canUseCoupons = (): boolean => {
    return checkFeature('allowCoupons');
  };

  // Verificar se pode usar personalização
  const canUseCustomization = (): boolean => {
    return checkFeature('allowCustomization');
  };

  // Função para executar ação com verificação
  const withFeatureCheck = <T extends any[]>(
    feature: keyof typeof PLAN_CONFIGS.free.limits
  ) => {
    return (action: (...args: T) => void | Promise<void>) => {
      return (...args: T) => {
        if (checkFeature(feature)) {
          return action(...args);
        }
      };
    };
  };

  // Função para executar ação com verificação de limite
  const withLimitCheck = <T extends any[]>(
    feature: 'maxProducts' | 'maxOrdersPerMonth'
  ) => {
    return (action: (...args: T) => void | Promise<void>) => {
      return (...args: T) => {
        if (checkLimit(feature)) {
          return action(...args);
        }
      };
    };
  };

  // Função para mostrar prompt de upgrade
  const promptUpgrade = async (): Promise<void> => {
    if (redirectToUpgrade) {
      try {
        const checkoutUrl = await createCheckoutSession();
        if (checkoutUrl) {
          window.location.href = checkoutUrl;
        }
      } catch (error) {
        console.error('Erro ao criar checkout:', error);
      }
    }
  };

  return {
    // Verificações básicas
    checkFeature,
    checkLimit,
    
    // Verificações específicas
    canAddProduct,
    canCreateOrder,
    canGeneratePdf,
    canUseAnalytics,
    canUseWhatsapp,
    canUseCoupons,
    canUseCustomization,
    
    // Wrappers para ações
    withFeatureCheck,
    withLimitCheck,
    
    // Utilitários
    promptUpgrade,
  };
}

// Hook específico para verificar antes de adicionar produto
export function useProductLimitGuard() {
  const guard = useSubscriptionGuard();
  
  return {
    canAdd: guard.canAddProduct,
    withCheck: guard.withLimitCheck('maxProducts'),
  };
}

// Hook específico para verificar antes de criar pedido
export function useOrderLimitGuard() {
  const guard = useSubscriptionGuard();
  
  return {
    canCreate: guard.canCreateOrder,
    withCheck: guard.withLimitCheck('maxOrdersPerMonth'),
  };
}

// Hook específico para funcionalidades premium
export function usePremiumFeatureGuard(feature: keyof typeof PLAN_CONFIGS.free.limits) {
  const guard = useSubscriptionGuard();
  
  return {
    canUse: () => guard.checkFeature(feature),
    withCheck: guard.withFeatureCheck(feature),
  };
}
