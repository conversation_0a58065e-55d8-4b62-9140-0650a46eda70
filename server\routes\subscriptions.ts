import type { Express, Request, Response } from "express";
import { subscriptionService } from "../subscription-service";
import { isAuthenticated } from "../firebaseAuth";
import { storage } from "../storage";
import { verifyWebhookSignature, STRIPE_CONFIG } from "../stripe-config";

export function registerSubscriptionRoutes(app: Express) {
  // Obter informações de uso e limites da loja
  app.get("/api/subscriptions/usage", isAuthenticated, async (req: Request, res: Response) => {
    try {
      const firebaseUid = (req as any).user?.uid;
      console.log('=== ROTA SUBSCRIPTION USAGE ===');
      console.log('firebaseUid recebido:', firebaseUid);
      console.log('req.user:', (req as any).user);

      if (!firebaseUid) {
        console.log('❌ firebaseUid está undefined na rota de subscription');
        return res.status(401).json({ message: "UID do Firebase não encontrado" });
      }

      // Obter loja do usuário
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        console.log('❌ Loja não encontrada para UID:', firebaseUid);
        return res.status(404).json({ message: "Loja não encontrada" });
      }

      const usageInfo = await subscriptionService.getUsageInfo(store.id);
      res.json(usageInfo);
    } catch (error) {
      console.error('Erro ao obter informações de uso:', error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  });

  // Criar sessão de checkout para upgrade para premium
  app.post("/api/subscriptions/create-checkout", isAuthenticated, async (req: Request, res: Response) => {
    try {
      const firebaseUid = (req as any).user?.uid;
      const { interval = 'month' } = req.body; // 'month' ou 'year'

      // Validar intervalo
      if (!['month', 'year'].includes(interval)) {
        return res.status(400).json({ message: "Intervalo inválido. Use 'month' ou 'year'" });
      }

      // Obter usuário e loja
      const user = await storage.getUserByFirebaseUid(firebaseUid);
      const store = await storage.getStoreByFirebaseUid(firebaseUid);

      if (!user || !store) {
        return res.status(404).json({ message: "Usuário ou loja não encontrados" });
      }

      const checkoutUrl = await subscriptionService.createPremiumCheckoutSession(
        store.id,
        user.email || '',
        user.fullName || user.username || 'Usuário',
        interval
      );

      if (!checkoutUrl) {
        return res.status(500).json({ message: "Erro ao criar sessão de checkout" });
      }

      res.json({ checkoutUrl });
    } catch (error) {
      console.error('Erro ao criar checkout:', error);
      res.status(500).json({ 
        message: error instanceof Error ? error.message : "Erro interno do servidor" 
      });
    }
  });

  // Obter URL do Customer Portal
  app.get("/api/subscriptions/portal", isAuthenticated, async (req: Request, res: Response) => {
    try {
      const firebaseUid = (req as any).user?.uid;
      
      // Obter loja do usuário
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(404).json({ message: "Loja não encontrada" });
      }

      const portalUrl = await subscriptionService.getCustomerPortalUrl(store.id);
      
      if (!portalUrl) {
        return res.status(400).json({ message: "Customer Portal não disponível" });
      }

      res.json({ portalUrl });
    } catch (error) {
      console.error('Erro ao obter URL do Customer Portal:', error);
      res.status(500).json({ 
        message: error instanceof Error ? error.message : "Erro interno do servidor" 
      });
    }
  });

  // Verificar se uma funcionalidade está disponível
  app.get("/api/subscriptions/feature/:feature", isAuthenticated, async (req: Request, res: Response) => {
    try {
      const firebaseUid = (req as any).user?.uid;
      const { feature } = req.params;
      
      // Obter loja do usuário
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(404).json({ message: "Loja não encontrada" });
      }

      const isAvailable = await subscriptionService.isFeatureAvailableForStore(
        store.id, 
        feature as any
      );

      res.json({ available: isAvailable });
    } catch (error) {
      console.error('Erro ao verificar funcionalidade:', error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  });

  // Webhook do Stripe para sincronizar eventos
  app.post("/api/subscriptions/webhook", async (req: Request, res: Response) => {
    try {
      const signature = req.headers['stripe-signature'] as string;
      
      if (!signature) {
        return res.status(400).json({ message: "Assinatura do webhook ausente" });
      }

      // Verificar assinatura do webhook
      const event = verifyWebhookSignature(req.body, signature);
      
      if (!event) {
        return res.status(400).json({ message: "Assinatura do webhook inválida" });
      }

      console.log(`Webhook recebido: ${event.type}`);

      // Processar eventos relevantes
      switch (event.type) {
        case 'customer.subscription.created':
        case 'customer.subscription.updated':
        case 'customer.subscription.deleted':
          const subscription = event.data.object as any;
          await subscriptionService.syncSubscriptionWithStripe(subscription.id);
          break;

        case 'invoice.payment_succeeded':
          const invoice = event.data.object as any;
          if (invoice.subscription) {
            await subscriptionService.syncSubscriptionWithStripe(invoice.subscription);
          }
          break;

        case 'invoice.payment_failed':
          const failedInvoice = event.data.object as any;
          if (failedInvoice.subscription) {
            await subscriptionService.syncSubscriptionWithStripe(failedInvoice.subscription);
          }
          break;

        default:
          console.log(`Evento não processado: ${event.type}`);
      }

      res.json({ received: true });
    } catch (error) {
      console.error('Erro no webhook:', error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  });

  // Verificar se um limite foi excedido
  app.get("/api/subscriptions/limit/:feature/:count", isAuthenticated, async (req: Request, res: Response) => {
    try {
      const firebaseUid = (req as any).user?.uid;
      const { feature, count } = req.params;
      
      // Obter loja do usuário
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(404).json({ message: "Loja não encontrada" });
      }

      const isExceeded = await subscriptionService.isLimitExceededForStore(
        store.id, 
        feature as any,
        parseInt(count)
      );

      res.json({ exceeded: isExceeded });
    } catch (error) {
      console.error('Erro ao verificar limite:', error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  });

  // Obter status da assinatura
  app.get("/api/subscriptions/status", isAuthenticated, async (req: Request, res: Response) => {
    try {
      const firebaseUid = (req as any).user?.uid;
      
      // Obter loja do usuário
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(404).json({ message: "Loja não encontrada" });
      }

      const subscription = await subscriptionService.getActiveSubscription(store.id);
      
      if (!subscription) {
        // Criar assinatura gratuita se não existir
        const newSubscription = await subscriptionService.createFreeSubscription(store.id);
        return res.json(newSubscription);
      }

      res.json(subscription);
    } catch (error) {
      console.error('Erro ao obter status da assinatura:', error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  });
}
