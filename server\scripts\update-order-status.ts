/**
 * Script para atualizar status de pedidos de 'processing'/'sent' para 'confirmed'
 * Execute este script para aplicar a migração de status
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Variáveis de ambiente SUPABASE_URL e SUPABASE_ANON_KEY são necessárias');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function updateOrderStatus() {
  try {
    console.log('🔄 Iniciando atualização de status de pedidos...');

    // Atualizar tabela orders
    const { data: ordersData, error: ordersError } = await supabase
      .from('orders')
      .update({ status: 'confirmed' })
      .in('status', ['processing', 'sent'])
      .select('id, status');

    if (ordersError) {
      console.error('❌ Erro ao atualizar orders:', ordersError);
      return;
    }

    console.log(`✅ ${ordersData?.length || 0} pedidos atualizados na tabela orders`);

    // Atualizar tabela order_revisions
    const { data: revisionsData, error: revisionsError } = await supabase
      .from('order_revisions')
      .update({ status: 'confirmed' })
      .in('status', ['processing', 'sent'])
      .select('id, status');

    if (revisionsError) {
      console.error('❌ Erro ao atualizar order_revisions:', revisionsError);
      return;
    }

    console.log(`✅ ${revisionsData?.length || 0} revisões atualizadas na tabela order_revisions`);
    console.log('✅ Migração de status concluída com sucesso!');

    // Exibir resumo
    console.log('\n📊 Resumo da migração:');
    console.log(`- Pedidos atualizados: ${ordersData?.length || 0}`);
    console.log(`- Revisões atualizadas: ${revisionsData?.length || 0}`);
    console.log('- Status antigos: processing, sent');
    console.log('- Novo status: confirmed');

  } catch (error) {
    console.error('❌ Erro durante a migração:', error);
  }
}

// Executar migração
updateOrderStatus();