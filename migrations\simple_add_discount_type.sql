-- Script simples para adicionar o campo discount_type às tabelas
-- Execute este script diretamente no console SQL do Supabase

-- Adicionar a coluna discount_type à tabela order_revisions
ALTER TABLE order_revisions 
ADD COLUMN IF NOT EXISTS discount_type VARCHAR(10) DEFAULT 'fixed';

-- Adicionar comentário para documentação
COMMENT ON COLUMN order_revisions.discount_type IS 'Tipo de desconto: fixed (valor fixo) ou percentage (percentual)';

-- Atualizar registros existentes para usar o tipo 'fixed' como padrão
UPDATE order_revisions
SET discount_type = 'fixed'
WHERE discount_type IS NULL AND discount > 0;

-- Adicionar a coluna discount_type à tabela orders se não existir
ALTER TABLE orders 
ADD COLUMN IF NOT EXISTS discount_type VARCHAR(10) DEFAULT 'fixed';

-- Adicionar comentário para documentação
COMMENT ON COLUMN orders.discount_type IS 'Tipo de desconto: fixed (valor fixo) ou percentage (percentual)';

-- Atualizar registros existentes na tabela orders para usar o tipo 'fixed' como padrão
UPDATE orders
SET discount_type = 'fixed'
WHERE discount_type IS NULL AND discount > 0;
