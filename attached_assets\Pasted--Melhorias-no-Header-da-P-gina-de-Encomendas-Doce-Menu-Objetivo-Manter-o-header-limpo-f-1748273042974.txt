✅ Melhorias no Header da Página de Encomendas - Doce Menu

🎯 Objetivo: 
Manter o header limpo, focado na identidade visual e na principal ação (fazer encomenda), deixando informações complementares acessíveis em uma nova página.

---

✅ Estrutura recomendada para o Header:

1. **Logo + Nome da Loja**: sempre visível no topo.
2. **Pequena descrição**: ex: “Doces e bolos sob encomenda”.
3. **Botão CTA principal**: "Monte sua encomenda" ou "Fazer encomenda".
4. **Botão de WhatsApp**: exclusivamente para suporte, com rótulo claro:
   - "❓ Dúvidas? Fale conosco"
   - Link direto para WhatsApp com mensagem pré-preenchida: 
     https://wa.me/5511999999999?text=Olá! Tenho uma dúvida sobre as encomendas.
5. **<PERSON>t<PERSON> “Saiba mais”**: 
   - <PERSON><PERSON><PERSON><PERSON>: "Como funcionam nossas encomendas?"
   - Direciona para uma **nova página** (não usar modal), com a URL: `/como-funciona`.

---

✅ Estrutura da nova página: `/como-funciona`

Conteúdo sugerido:

- 📅 **Prazos mínimos de encomenda** → ex: "Encomendas com 48h de antecedência".
- 🕒 **Horários de retirada** → ex: "Seg-Sex: 8h-18h".
- 📍 **Endereço para retirada** → com link "Como chegar" (Google Maps).
- 💳 **Formas de pagamento aceitas** → Pix, Cartão, Dinheiro.
- 📢 **Política de cancelamento ou alteração** → ex: "Alterações até 24h antes da retirada".
- 📞 **Canais de contato** → WhatsApp, telefone, e-mail.

---

✅ Benefícios desse modelo:

- Mantém o header **limpo e objetivo**.
- Evita **poluição visual** com excesso de informações.
- Garante que o cliente **entenda o funcionamento** das encomendas ao acessar a página específica.
- WhatsApp posicionado como **canal de suporte**, não como principal forma de pedido.

---

✅ Exemplos de rótulos:

- **Botão Encomenda:** "Monte sua encomenda"
- **Botão WhatsApp:** "❓ Dúvidas? Fale conosco"
- **Botão Saiba Mais:** "Como funcionam nossas encomendas?"

---

✅ Observações de UX:

- Não usar modais → pode prejudicar acessibilidade e causar confusão em dispositivos móveis.
- Nova página permite incluir imagens, exemplos e FAQs.
- Indicar o link “Como funcionam nossas encomendas?” também no rodapé, para reforçar o acesso.

---

✅ Conclusão:

Manter a jornada do cliente simples e clara:  
→ Encomenda → no site.  
→ Dúvidas → WhatsApp.  
→ Informações → página específica.
