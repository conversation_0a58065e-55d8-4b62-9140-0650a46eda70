import { initializeApp, cert } from 'firebase-admin/app';
import { getStorage } from 'firebase-admin/storage';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';
import fs from 'fs/promises';
import dotenv from 'dotenv';

// Carregar variáveis de ambiente
dotenv.config();

// Configuração do Firebase
const firebaseConfig = {
  apiKey: "AIzaSyD07MYd52ffihsx73Hh9kQWQzs6kPQw9bo",
  authDomain: "replitdocemenu.firebaseapp.com",
  projectId: "replitdocemenu",
  storageBucket: "replitdocemenu.firebasestorage.app",
  messagingSenderId: "************",
  appId: "1:************:web:1c12eedf4c823a0f3f2bd4",
  measurementId: "G-CNSDV69Y7J"
};

// Inicializar o Firebase Admin
// O Firebase Admin pode usar credenciais padrão ou explícitas
let app;
try {
  // Criar um arquivo de credenciais temporário se fornecido como variável de ambiente
  if (process.env.GOOGLE_APPLICATION_CREDENTIALS && 
      typeof process.env.GOOGLE_APPLICATION_CREDENTIALS === 'string' && 
      process.env.GOOGLE_APPLICATION_CREDENTIALS.startsWith('{')) {
    // Usar credenciais fornecidas na variável de ambiente
    const credentials = JSON.parse(process.env.GOOGLE_APPLICATION_CREDENTIALS);
    app = initializeApp({
      credential: cert(credentials),
      storageBucket: firebaseConfig.storageBucket,
    });
  } else {
    // Usar credenciais padrão do application default
    app = initializeApp({
      projectId: firebaseConfig.projectId,
      storageBucket: firebaseConfig.storageBucket
    });
  }
  
  console.log('Firebase Admin inicializado com sucesso');
} catch (error) {
  console.error('Erro ao inicializar Firebase Admin:', error);
  // Fornecer um valor padrão para a aplicação continuar funcionando
  // Se Firebase falhar, usaremos armazenamento local
  throw error;
}

// Obter instância do Storage
let storage;
let bucket: any;
try {
  storage = getStorage();
  bucket = storage.bucket();
  console.log('Bucket do Firebase Storage configurado:', bucket.name);
} catch (error) {
  console.error('Erro ao configurar Firebase Storage:', error);
  throw error;
}

/**
 * Faz upload de um arquivo para o Firebase Storage
 * @param buffer Buffer com o conteúdo do arquivo
 * @param mimeType Tipo MIME do arquivo
 * @param destinationPath Caminho de destino no Firebase Storage
 * @returns URL pública do arquivo
 */
export async function uploadToFirebaseStorage(buffer: Buffer, mimeType: string, destinationPath: string): Promise<string> {
  try {
    console.log(`Iniciando upload para Firebase Storage: ${destinationPath}`);
    
    // Gerar nome de arquivo único, se necessário
    if (!path.extname(destinationPath)) {
      const extension = mimeType.split('/')[1] || 'bin';
      destinationPath = `${destinationPath}-${uuidv4()}.${extension}`;
    }
    
    // Criar arquivo temporário
    const tempFilePath = path.join('/tmp', `temp-${uuidv4()}${path.extname(destinationPath)}`);
    await fs.writeFile(tempFilePath, buffer);
    
    // Fazer upload para o Firebase Storage
    const uploadResponse = await bucket.upload(tempFilePath, {
      destination: destinationPath,
      metadata: {
        contentType: mimeType,
        cacheControl: 'public, max-age=31536000', // Cache de 1 ano
      },
    });
    
    // Remover arquivo temporário
    await fs.unlink(tempFilePath);
    
    // Configurar arquivo para acesso público
    await uploadResponse[0].makePublic();
    
    // Obter URL pública
    const publicUrl = `https://storage.googleapis.com/${bucket.name}/${destinationPath}`;
    console.log(`Arquivo enviado com sucesso para o Firebase Storage. URL: ${publicUrl}`);
    
    return publicUrl;
  } catch (error) {
    console.error('Erro ao fazer upload para o Firebase Storage:', error);
    throw error;
  }
}

/**
 * Exclui um arquivo do Firebase Storage
 * @param filePath Caminho ou URL do arquivo no Firebase Storage
 * @returns true se o arquivo foi excluído com sucesso
 */
export async function deleteFromFirebaseStorage(filePath: string): Promise<boolean> {
  try {
    console.log(`Iniciando exclusão do arquivo no Firebase Storage: ${filePath}`);
    
    // Extrair o caminho do arquivo da URL, se for uma URL
    let storagePath = filePath;
    
    if (filePath.startsWith('https://storage.googleapis.com/')) {
      storagePath = filePath.replace(`https://storage.googleapis.com/${bucket.name}/`, '');
    }
    
    // Excluir o arquivo
    await bucket.file(storagePath).delete();
    
    console.log(`Arquivo excluído com sucesso do Firebase Storage: ${storagePath}`);
    return true;
  } catch (error) {
    console.error('Erro ao excluir arquivo do Firebase Storage:', error);
    return false;
  }
}

// Interface para o arquivo do Firebase Storage
interface FirebaseStorageFile {
  name: string;
  [key: string]: any;
}

/**
 * Lista todos os arquivos no bucket do Firebase Storage
 * @param prefix Prefixo opcional para filtrar os arquivos
 * @returns Array de URLs de arquivos
 */
export async function listFirebaseStorageFiles(prefix?: string): Promise<string[]> {
  try {
    console.log(`Listando arquivos no Firebase Storage${prefix ? ` com prefixo: ${prefix}` : ''}`);
    
    // Obter todos os arquivos no bucket (ou com prefixo específico)
    const [files] = await bucket.getFiles({ prefix });
    
    // Retornar as URLs públicas
    const fileUrls = files.map((file: FirebaseStorageFile) => {
      return `https://storage.googleapis.com/${bucket.name}/${file.name}`;
    });
    
    return fileUrls;
  } catch (error) {
    console.error('Erro ao listar arquivos do Firebase Storage:', error);
    return [];
  }
}

export { bucket };