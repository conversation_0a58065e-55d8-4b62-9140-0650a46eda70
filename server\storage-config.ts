import * as path from 'path';
import * as fs from 'fs/promises';
import { v4 as uuidv4 } from 'uuid';
import axios from 'axios';
import FormData from 'form-data';

// Importar as funções do Firebase Storage
import * as firebaseConfig from './firebase-config';
// Importar o cliente Supabase
import { supabase } from './supabase-config';

// Definir tipo de armazenamento
// Por padrão, tentamos usar Supabase, com fallback para Firebase e depois local
const STORAGE_TYPE = 'supabase'; // 'supabase', 'firebase' ou 'local'

// Funções auxiliares para verificar se o Supabase está disponível
function isSupabaseAvailable(): boolean {
  try {
    return !!supabase &&
           typeof supabase.storage !== 'undefined' &&
           typeof supabase.storage.from === 'function';
  } catch (error) {
    console.warn('Supabase Storage não está disponível:', error);
    return false;
  }
}

// Funções auxiliares para verificar se o Firebase está disponível
function isFirebaseAvailable(): boolean {
  try {
    return !!firebaseConfig &&
           typeof firebaseConfig.uploadToFirebaseStorage === 'function' &&
           typeof firebaseConfig.deleteFromFirebaseStorage === 'function' &&
           typeof firebaseConfig.listFirebaseStorageFiles === 'function';
  } catch (error) {
    console.warn('Firebase Storage não está disponível:', error);
    return false;
  }
}

// Nome do bucket para armazenamento local (compatibilidade retroativa)
const bucketName = 'doce-menu';

// Nome do diretório raiz para armazenamento local (compatibilidade)
const STORAGE_ROOT = 'storage';

// Nome do bucket do Supabase Storage
const SUPABASE_BUCKET = 'images';

/**
 * Upload de arquivo para o Supabase Storage
 * @param buffer Buffer com o conteúdo do arquivo
 * @param mimeType Tipo MIME do arquivo
 * @param destinationPath Caminho de destino
 * @returns URL pública do arquivo
 */
export async function uploadToSupabaseStorage(buffer: Buffer, mimeType: string, destinationPath: string): Promise<string> {
  try {
    console.log(`Iniciando upload para Supabase Storage: ${destinationPath}`);

    // Fazer upload para o Supabase Storage
    const { data, error } = await supabase
      .storage
      .from(SUPABASE_BUCKET)
      .upload(destinationPath, buffer, {
        contentType: mimeType,
        cacheControl: 'public, max-age=31536000', // Cache de 1 ano
        upsert: true
      });

    if (error) {
      console.error('Erro ao fazer upload para o Supabase Storage:', error);
      throw error;
    }

    // Obter a URL pública do arquivo
    const { data: { publicUrl } } = supabase
      .storage
      .from(SUPABASE_BUCKET)
      .getPublicUrl(destinationPath);

    console.log(`Arquivo enviado com sucesso para o Supabase Storage. URL: ${publicUrl}`);

    return publicUrl;
  } catch (error) {
    console.error('Erro ao fazer upload para o Supabase Storage:', error);
    throw error;
  }
}

/**
 * Upload de arquivo para o Firebase Storage ou armazenamento local
 * @param buffer Buffer com o conteúdo do arquivo
 * @param mimeType Tipo MIME do arquivo
 * @param destinationPath Caminho de destino
 * @returns URL pública do arquivo
 */
export async function uploadToBucket(buffer: Buffer, mimeType: string, destinationPath: string): Promise<string> {
  try {
    console.log(`Iniciando upload para ${STORAGE_TYPE}: ${destinationPath}`);

    // Tentar usar Supabase Storage se disponível
    if ((STORAGE_TYPE === 'supabase' || STORAGE_TYPE === 'auto') && isSupabaseAvailable()) {
      try {
        console.log(`Tentando utilizar Supabase Storage para ${destinationPath}`);

        // Fazer upload para o Supabase Storage
        const supabaseUrl = await uploadToSupabaseStorage(buffer, mimeType, destinationPath);
        console.log(`Arquivo enviado com sucesso para o Supabase Storage. URL: ${supabaseUrl}`);

        return supabaseUrl;
      } catch (supabaseError) {
        console.error('Erro ao fazer upload para o Supabase Storage:', supabaseError);
        console.log('Fazendo fallback para Firebase Storage');
        // Continua para a implementação do Firebase após falha do Supabase
      }
    }

    // Tentar usar Firebase Storage se disponível
    if ((STORAGE_TYPE === 'firebase' || STORAGE_TYPE === 'auto') && isFirebaseAvailable()) {
      try {
        console.log(`Tentando utilizar Firebase Storage para ${destinationPath}`);

        // Estruturar caminho do arquivo no Firebase Storage
        const storageDestination = destinationPath;

        // Fazer upload para o Firebase Storage
        const firebaseUrl = await firebaseConfig.uploadToFirebaseStorage(buffer, mimeType, storageDestination);
        console.log(`Arquivo enviado com sucesso para o Firebase Storage. URL: ${firebaseUrl}`);

        return firebaseUrl;
      } catch (firebaseError) {
        console.error('Erro ao fazer upload para o Firebase Storage:', firebaseError);
        console.log('Fazendo fallback para armazenamento local');
        // Continua para a implementação local após falha do Firebase
      }
    }

    // Implementação de armazenamento local
    console.log(`Usando armazenamento local para ${destinationPath}`);

    // Configurar caminhos de destino
    const storagePath = path.join(process.cwd(), STORAGE_ROOT);
    const bucketPath = path.join(storagePath, bucketName);
    const folderPath = path.dirname(path.join(bucketPath, destinationPath));
    const filePath = path.join(bucketPath, destinationPath);

    // Criar diretórios se não existirem
    await fs.mkdir(storagePath, { recursive: true });
    await fs.mkdir(bucketPath, { recursive: true });
    await fs.mkdir(folderPath, { recursive: true });

    // Salvar o arquivo
    await fs.writeFile(filePath, buffer);
    console.log(`Arquivo salvo localmente em: ${filePath}`);

    // Retornar URL pública do arquivo
    const publicUrl = `/${STORAGE_ROOT}/${bucketName}/${destinationPath}`;
    console.log(`Arquivo enviado com sucesso para armazenamento local. URL: ${publicUrl}`);

    return publicUrl;
  } catch (error) {
    console.error('Erro ao fazer upload de arquivo:', error);
    throw error;
  }
}

/**
 * Exclui um arquivo do Supabase Storage
 * @param filePath Caminho do arquivo no storage
 * @returns true se o arquivo foi excluído com sucesso
 */
export async function deleteFromSupabaseStorage(filePath: string): Promise<boolean> {
  try {
    console.log(`Iniciando exclusão do Supabase Storage: ${filePath}`);

    const { error } = await supabase
      .storage
      .from(SUPABASE_BUCKET)
      .remove([filePath]);

    if (error) {
      console.error('Erro ao excluir do Supabase Storage:', error);
      throw error;
    }

    console.log(`Arquivo excluído com sucesso do Supabase Storage: ${filePath}`);
    return true;
  } catch (error) {
    console.error('Erro ao excluir do Supabase Storage:', error);
    throw error;
  }
}

/**
 * Exclui um arquivo do Firebase Storage ou armazenamento local
 * @param filePath Caminho do arquivo no storage
 * @returns true se o arquivo foi excluído com sucesso
 */
export async function deleteFromBucket(filePath: string): Promise<boolean> {
  try {
    console.log(`Iniciando exclusão do arquivo: ${filePath}`);

    // Tentar usar Supabase Storage se disponível
    if ((STORAGE_TYPE === 'supabase' || STORAGE_TYPE === 'auto') && isSupabaseAvailable()) {
      try {
        console.log(`Tentando excluir do Supabase Storage: ${filePath}`);

        const success = await deleteFromSupabaseStorage(filePath);
        console.log(`Arquivo excluído do Supabase Storage: ${success}`);

        return success;
      } catch (supabaseError) {
        console.error('Erro ao excluir do Supabase Storage:', supabaseError);
        console.log('Fazendo fallback para Firebase Storage');
        // Continua para a implementação do Firebase após falha do Supabase
      }
    }

    // Verificar qual storage usar
    if ((STORAGE_TYPE === 'firebase' || STORAGE_TYPE === 'auto') && isFirebaseAvailable()) {
      // Usar Firebase Storage para exclusão
      try {
        console.log(`Usando Firebase Storage para excluir: ${filePath}`);

        // Fazer exclusão via Firebase Storage
        const success = await firebaseConfig.deleteFromFirebaseStorage(filePath);

        if (success) {
          console.log(`Arquivo excluído com sucesso do Firebase Storage: ${filePath}`);
        } else {
          console.error(`Falha ao excluir arquivo do Firebase Storage: ${filePath}`);
        }

        return success;
      } catch (firebaseError) {
        console.error('Erro ao excluir arquivo do Firebase Storage:', firebaseError);
        // Continue para tentar excluir localmente como fallback
      }
    }

    // Usar sistema de arquivos local (compatibilidade ou fallback)
    try {
      // Remover o prefixo da URL se existir para obter o caminho relativo
      let relativePath = filePath;
      if (filePath.startsWith(`/${STORAGE_ROOT}/`)) {
        relativePath = filePath.replace(`/${STORAGE_ROOT}/${bucketName}/`, '');
      }

      const fullPath = path.join(process.cwd(), STORAGE_ROOT, bucketName, relativePath);
      console.log(`Usando sistema de arquivos local para excluir: ${fullPath}`);

      // Verificar se o arquivo existe
      await fs.access(fullPath);

      // Excluir o arquivo
      await fs.unlink(fullPath);

      console.log(`Arquivo excluído com sucesso: ${fullPath}`);
      return true;
    } catch (err) {
      console.error('Erro ao excluir arquivo localmente:', err);
      return false;
    }
  } catch (error) {
    console.error('Erro ao excluir arquivo:', error);
    return false;
  }
}

/**
 * Lista todos os arquivos no Supabase Storage
 * @param prefix Prefixo opcional para filtrar os arquivos
 * @returns Array de URLs de arquivos
 */
export async function listSupabaseStorageFiles(prefix?: string): Promise<string[]> {
  try {
    console.log(`Listando arquivos do Supabase Storage com prefixo: ${prefix || 'todos'}`);

    const { data, error } = await supabase
      .storage
      .from(SUPABASE_BUCKET)
      .list(prefix || '', {
        limit: 1000,
        offset: 0
      });

    if (error) {
      console.error('Erro ao listar arquivos do Supabase Storage:', error);
      throw error;
    }

    // Converter para URLs públicas
    const fileUrls = data.map(file => {
      const filePath = prefix ? `${prefix}/${file.name}` : file.name;
      const { data: { publicUrl } } = supabase
        .storage
        .from(SUPABASE_BUCKET)
        .getPublicUrl(filePath);
      return publicUrl;
    });

    console.log(`Encontrados ${fileUrls.length} arquivos no Supabase Storage`);
    return fileUrls;
  } catch (error) {
    console.error('Erro ao listar arquivos do Supabase Storage:', error);
    throw error;
  }
}

/**
 * Lista todos os arquivos no Firebase Storage ou armazenamento local
 * @param prefix Prefixo opcional para filtrar os arquivos
 * @returns Array de URLs de arquivos
 */
export async function listBucketFiles(prefix?: string): Promise<string[]> {
  try {
    console.log(`Listando arquivos com prefixo: ${prefix || 'todos'}`);

    // Tentar usar Supabase Storage se disponível
    if ((STORAGE_TYPE === 'supabase' || STORAGE_TYPE === 'auto') && isSupabaseAvailable()) {
      try {
        console.log('Listando arquivos do Supabase Storage');

        // Listar arquivos via Supabase Storage
        const files = await listSupabaseStorageFiles(prefix);

        console.log(`Encontrados ${files.length} arquivos no Supabase Storage`);
        return files;
      } catch (supabaseError) {
        console.error('Erro ao listar arquivos do Supabase Storage:', supabaseError);
        console.log('Fazendo fallback para Firebase Storage');
        // Continue para tentar listar via Firebase como fallback
      }
    }

    // Verificar qual storage usar
    if ((STORAGE_TYPE === 'firebase' || STORAGE_TYPE === 'auto') && isFirebaseAvailable()) {
      // Usar Firebase Storage
      try {
        console.log('Listando arquivos do Firebase Storage');

        // Listar arquivos via Firebase Storage
        const files = await firebaseConfig.listFirebaseStorageFiles(prefix);

        console.log(`Encontrados ${files.length} arquivos no Firebase Storage`);
        return files;
      } catch (firebaseError) {
        console.error('Erro ao listar arquivos do Firebase Storage:', firebaseError);
        // Continue para tentar listar localmente como fallback
      }
    }

    // Usar sistema de arquivos local (compatibilidade ou fallback)
    try {
      console.log('Listando arquivos do armazenamento local');

      const bucketPath = path.join(process.cwd(), STORAGE_ROOT, bucketName);

      // Verificar se o diretório existe
      try {
        await fs.access(bucketPath);
      } catch (err) {
        console.log(`Diretório ${bucketPath} não existe, criando...`);
        await fs.mkdir(bucketPath, { recursive: true });
        return [];
      }

      // Lista todos os arquivos recursivamente
      const listFiles = async (dir: string): Promise<string[]> => {
        const entries = await fs.readdir(dir, { withFileTypes: true });
        const files = await Promise.all(
          entries.map(async (entry) => {
            const fullPath = path.join(dir, entry.name);
            return entry.isDirectory() ? listFiles(fullPath) : fullPath;
          })
        );
        return files.flat();
      };

      let allFiles = await listFiles(bucketPath);

      // Filtrar por prefixo se especificado
      if (prefix) {
        const prefixPath = path.join(bucketPath, prefix);
        allFiles = allFiles.filter(file => file.startsWith(prefixPath));
      }

      // Converter caminhos absolutos para URLs relativas
      const fileUrls = allFiles.map(file => {
        const relativePath = path.relative(bucketPath, file);
        return `/${STORAGE_ROOT}/${bucketName}/${relativePath.replace(/\\/g, '/')}`;
      });

      console.log(`Encontrados ${fileUrls.length} arquivos no armazenamento local`);
      return fileUrls;
    } catch (err) {
      console.error('Erro ao listar arquivos localmente:', err);
      return [];
    }
  } catch (error) {
    console.error('Erro ao listar arquivos:', error);
    return [];
  }
}