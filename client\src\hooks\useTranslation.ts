import { useTranslation as useI18nTranslation } from 'react-i18next';
import { useState, useEffect } from 'react';
import i18n from '@/lib/i18n';

export function useTranslation() {
  const { t, i18n: i18nInstance } = useI18nTranslation();
  const [currentLanguage, setCurrentLanguage] = useState(i18n.language);

  // Function to change language
  const changeLanguage = (language: string) => {
    i18nInstance.changeLanguage(language);
    setCurrentLanguage(language);
    localStorage.setItem('language', language);
  };

  // Load language from local storage on component mount
  useEffect(() => {
    const savedLanguage = localStorage.getItem('language');
    if (savedLanguage && savedLanguage !== currentLanguage) {
      changeLanguage(savedLanguage);
    }
  }, []);

  return {
    t,
    changeLanguage,
    currentLanguage
  };
}
