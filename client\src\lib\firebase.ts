import { initializeApp } from "firebase/app";
import { getAuth, GoogleAuthProvider } from "firebase/auth";

const firebaseConfig = {
  apiKey: "AIzaSyD07MYd52ffihsx73Hh9kQWQzs6kPQw9bo",
  authDomain: "replitdocemenu.firebaseapp.com",
  projectId: "replitdocemenu",
  storageBucket: "replitdocemenu.firebasestorage.app",
  messagingSenderId: "************",
  appId: "1:************:web:1c12eedf4c823a0f3f2bd4",
  measurementId: "G-CNSDV69Y7J"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Authentication
export const auth = getAuth(app);
export const googleProvider = new GoogleAuthProvider();

// Configure Google provider
googleProvider.setCustomParameters({
  prompt: 'select_account'
});

export default app;