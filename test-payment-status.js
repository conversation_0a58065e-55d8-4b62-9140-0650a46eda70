// Script de teste para verificar a lógica de cálculo do status de pagamento

// Simular a função atualizarStatusPagamento
function atualizarStatusPagamento(totalPedido, recebimentos) {
  // Se não há recebimentos, status é pendente
  if (!recebimentos || recebimentos.length === 0) {
    return 'pendente';
  }

  // Calcular total recebido
  const totalRecebido = recebimentos.reduce((total, recebimento) => total + recebimento.valor, 0);

  // Verificar se há recebimentos com status especiais
  const temDisputa = recebimentos.some(r => r.metodo === 'Outro' && r.observacao?.toLowerCase().includes('disputa'));
  const temEstorno = recebimentos.some(r => r.valor < 0); // Valores negativos indicam estorno
  const temCancelamento = recebimentos.some(r => r.metodo === 'Outro' && r.observacao?.toLowerCase().includes('cancelado'));

  // Priorizar status especiais
  if (temCancelamento) {
    return 'cancelado';
  }

  if (temEstorno && totalRecebido <= 0) {
    return 'estornado';
  }

  if (temDisputa) {
    return 'em_disputa';
  }

  // Verificar status baseado no valor recebido
  const tolerancia = 0.01; // Tolerância para diferenças de centavos

  if (totalRecebido >= (totalPedido - tolerancia)) {
    return 'recebido';
  } else if (totalRecebido > 0) {
    return 'parcialmente_recebido';
  } else {
    return 'pendente';
  }
}

// Casos de teste
console.log('=== TESTES DE STATUS DE PAGAMENTO ===\n');

// Teste 1: Pedido totalmente pago
console.log('Teste 1: Pedido totalmente pago');
const teste1 = {
  totalPedido: 100.00,
  recebimentos: [
    { valor: 50.00, metodo: 'Pix', observacao: null },
    { valor: 50.00, metodo: 'Dinheiro', observacao: null }
  ]
};
const resultado1 = atualizarStatusPagamento(teste1.totalPedido, teste1.recebimentos);
console.log(`Total: R$ ${teste1.totalPedido}`);
console.log(`Recebido: R$ ${teste1.recebimentos.reduce((sum, r) => sum + r.valor, 0)}`);
console.log(`Status: ${resultado1}`);
console.log(`Esperado: recebido`);
console.log(`✅ ${resultado1 === 'recebido' ? 'PASSOU' : 'FALHOU'}\n`);

// Teste 2: Pedido com valor exato (sem tolerância)
console.log('Teste 2: Pedido com valor exato');
const teste2 = {
  totalPedido: 100.00,
  recebimentos: [
    { valor: 100.00, metodo: 'Pix', observacao: null }
  ]
};
const resultado2 = atualizarStatusPagamento(teste2.totalPedido, teste2.recebimentos);
console.log(`Total: R$ ${teste2.totalPedido}`);
console.log(`Recebido: R$ ${teste2.recebimentos.reduce((sum, r) => sum + r.valor, 0)}`);
console.log(`Status: ${resultado2}`);
console.log(`Esperado: recebido`);
console.log(`✅ ${resultado2 === 'recebido' ? 'PASSOU' : 'FALHOU'}\n`);

// Teste 3: Pedido com valor ligeiramente maior (dentro da tolerância)
console.log('Teste 3: Pedido com valor ligeiramente maior (dentro da tolerância)');
const teste3 = {
  totalPedido: 100.00,
  recebimentos: [
    { valor: 100.01, metodo: 'Pix', observacao: null }
  ]
};
const resultado3 = atualizarStatusPagamento(teste3.totalPedido, teste3.recebimentos);
console.log(`Total: R$ ${teste3.totalPedido}`);
console.log(`Recebido: R$ ${teste3.recebimentos.reduce((sum, r) => sum + r.valor, 0)}`);
console.log(`Status: ${resultado3}`);
console.log(`Esperado: recebido`);
console.log(`✅ ${resultado3 === 'recebido' ? 'PASSOU' : 'FALHOU'}\n`);

// Teste 4: Pedido parcialmente pago
console.log('Teste 4: Pedido parcialmente pago');
const teste4 = {
  totalPedido: 100.00,
  recebimentos: [
    { valor: 50.00, metodo: 'Pix', observacao: null }
  ]
};
const resultado4 = atualizarStatusPagamento(teste4.totalPedido, teste4.recebimentos);
console.log(`Total: R$ ${teste4.totalPedido}`);
console.log(`Recebido: R$ ${teste4.recebimentos.reduce((sum, r) => sum + r.valor, 0)}`);
console.log(`Status: ${resultado4}`);
console.log(`Esperado: parcialmente_recebido`);
console.log(`✅ ${resultado4 === 'parcialmente_recebido' ? 'PASSOU' : 'FALHOU'}\n`);

// Teste 5: Pedido com valor ligeiramente menor (fora da tolerância)
console.log('Teste 5: Pedido com valor ligeiramente menor (fora da tolerância)');
const teste5 = {
  totalPedido: 100.00,
  recebimentos: [
    { valor: 99.98, metodo: 'Pix', observacao: null }
  ]
};
const resultado5 = atualizarStatusPagamento(teste5.totalPedido, teste5.recebimentos);
console.log(`Total: R$ ${teste5.totalPedido}`);
console.log(`Recebido: R$ ${teste5.recebimentos.reduce((sum, r) => sum + r.valor, 0)}`);
console.log(`Diferença: R$ ${teste5.totalPedido - teste5.recebimentos.reduce((sum, r) => sum + r.valor, 0)}`);
console.log(`Status: ${resultado5}`);
console.log(`Esperado: parcialmente_recebido`);
console.log(`✅ ${resultado5 === 'parcialmente_recebido' ? 'PASSOU' : 'FALHOU'}\n`);

// Teste 6: Pedido com valor dentro da tolerância (99.99)
console.log('Teste 6: Pedido com valor dentro da tolerância (99.99)');
const teste6 = {
  totalPedido: 100.00,
  recebimentos: [
    { valor: 99.99, metodo: 'Pix', observacao: null }
  ]
};
const resultado6 = atualizarStatusPagamento(teste6.totalPedido, teste6.recebimentos);
console.log(`Total: R$ ${teste6.totalPedido}`);
console.log(`Recebido: R$ ${teste6.recebimentos.reduce((sum, r) => sum + r.valor, 0)}`);
console.log(`Diferença: R$ ${teste6.totalPedido - teste6.recebimentos.reduce((sum, r) => sum + r.valor, 0)}`);
console.log(`Status: ${resultado6}`);
console.log(`Esperado: recebido`);
console.log(`✅ ${resultado6 === 'recebido' ? 'PASSOU' : 'FALHOU'}\n`);

// Teste adicional: Verificar se a tolerância está funcionando corretamente
console.log('=== TESTE ADICIONAL: TOLERÂNCIA ===');

function testTolerancia(totalPedido, valorRecebido, expectedStatus) {
  const recebimentos = [{ valor: valorRecebido, metodo: 'Pix', observacao: null }];
  const resultado = atualizarStatusPagamento(totalPedido, recebimentos);
  const diferenca = valorRecebido - totalPedido;
  const dentroDaTolerancia = diferenca >= -0.01;

  console.log(`\nTeste: Total R$ ${totalPedido.toFixed(2)}, Recebido R$ ${valorRecebido.toFixed(2)}`);
  console.log(`Diferença: R$ ${diferenca.toFixed(2)}`);
  console.log(`Dentro da tolerância (-0.01): ${dentroDaTolerancia}`);
  console.log(`Status calculado: ${resultado}`);
  console.log(`Status esperado: ${expectedStatus}`);
  console.log(`✅ ${resultado === expectedStatus ? 'PASSOU' : 'FALHOU'}`);

  return resultado === expectedStatus;
}

// Casos específicos de tolerância
testTolerancia(100.00, 100.00, 'recebido');    // Exato
testTolerancia(100.00, 100.01, 'recebido');    // Ligeiramente maior
testTolerancia(100.00, 99.99, 'recebido');     // Dentro da tolerância
testTolerancia(100.00, 99.98, 'parcialmente_recebido'); // Fora da tolerância

// Teste específico: Valor pendente zerado
console.log('\n=== TESTE ESPECÍFICO: VALOR PENDENTE ZERADO ===');

function testValorPendenteZerado() {
  console.log('\n🧪 Teste: Valor pendente zerado deve resultar em status "recebido"');

  const cenarios = [
    {
      nome: "Valor exato",
      totalPedido: 100.00,
      recebimentos: [{ valor: 100.00, metodo: 'Pix', observacao: null }]
    },
    {
      nome: "Múltiplos recebimentos que somam o total",
      totalPedido: 100.00,
      recebimentos: [
        { valor: 60.00, metodo: 'Pix', observacao: null },
        { valor: 40.00, metodo: 'Dinheiro', observacao: null }
      ]
    },
    {
      nome: "Valor ligeiramente maior (dentro da tolerância)",
      totalPedido: 100.00,
      recebimentos: [{ valor: 100.01, metodo: 'Pix', observacao: null }]
    },
    {
      nome: "Valor dentro da tolerância (99.99)",
      totalPedido: 100.00,
      recebimentos: [{ valor: 99.99, metodo: 'Pix', observacao: null }]
    }
  ];

  let todosPassed = true;

  cenarios.forEach((cenario, index) => {
    const totalRecebido = cenario.recebimentos.reduce((sum, r) => sum + r.valor, 0);
    const valorPendente = Math.max(0, cenario.totalPedido - totalRecebido);
    const resultado = atualizarStatusPagamento(cenario.totalPedido, cenario.recebimentos);
    const esperado = 'recebido';
    const passou = resultado === esperado;

    console.log(`\n${index + 1}. ${cenario.nome}:`);
    console.log(`   Total do pedido: R$ ${cenario.totalPedido.toFixed(2)}`);
    console.log(`   Total recebido: R$ ${totalRecebido.toFixed(2)}`);
    console.log(`   Valor pendente: R$ ${valorPendente.toFixed(2)}`);
    console.log(`   Status calculado: "${resultado}"`);
    console.log(`   Status esperado: "${esperado}"`);
    console.log(`   ${passou ? '✅ PASSOU' : '❌ FALHOU'}`);

    if (!passou) {
      todosPassed = false;
      console.log(`   🔍 Análise: totalRecebido (${totalRecebido}) >= (totalPedido (${cenario.totalPedido}) - tolerancia (0.01)) = ${totalRecebido >= (cenario.totalPedido - 0.01)}`);
    }
  });

  console.log(`\n📊 Resultado geral: ${todosPassed ? '✅ TODOS OS TESTES PASSARAM' : '❌ ALGUNS TESTES FALHARAM'}`);

  return todosPassed;
}

testValorPendenteZerado();

// Teste baseado nos logs reais do servidor
console.log('\n=== TESTE BASEADO NOS LOGS REAIS ===');

function testCenarioReal() {
  console.log('\n🔍 Cenário real do servidor (Pedido 62):');

  const totalRevisao = 437.20;
  const recebimentosAtuais = [
    { valor: 319.85, metodo: 'Transferência', observacao: null },
    { valor: 20.00, metodo: 'Pix', observacao: null },
    { valor: 20.00, metodo: 'Pix', observacao: null },
    { valor: 16.35, metodo: 'Pix', observacao: null }
  ];

  const totalRecebido = recebimentosAtuais.reduce((sum, r) => sum + r.valor, 0);
  const valorPendente = Math.max(0, totalRevisao - totalRecebido);
  const statusAtual = atualizarStatusPagamento(totalRevisao, recebimentosAtuais);

  console.log(`📊 Situação atual:`);
  console.log(`   Total da revisão: R$ ${totalRevisao.toFixed(2)}`);
  console.log(`   Total recebido: R$ ${totalRecebido.toFixed(2)}`);
  console.log(`   Valor pendente: R$ ${valorPendente.toFixed(2)}`);
  console.log(`   Status atual: "${statusAtual}"`);
  console.log(`   ✅ Status correto: ${statusAtual === 'parcialmente_recebido' ? 'SIM' : 'NÃO'} (ainda há valor pendente)`);

  // Agora vamos testar adicionando o valor pendente exato
  console.log(`\n💰 Testando com recebimento que completa o valor pendente:`);
  const recebimentosCompletos = [
    ...recebimentosAtuais,
    { valor: valorPendente, metodo: 'Pix', observacao: null }
  ];

  const totalRecebidoCompleto = recebimentosCompletos.reduce((sum, r) => sum + r.valor, 0);
  const valorPendenteCompleto = Math.max(0, totalRevisao - totalRecebidoCompleto);
  const statusCompleto = atualizarStatusPagamento(totalRevisao, recebimentosCompletos);

  console.log(`   Novo recebimento: R$ ${valorPendente.toFixed(2)}`);
  console.log(`   Total recebido: R$ ${totalRecebidoCompleto.toFixed(2)}`);
  console.log(`   Valor pendente: R$ ${valorPendenteCompleto.toFixed(2)}`);
  console.log(`   Novo status: "${statusCompleto}"`);
  console.log(`   ✅ Status correto: ${statusCompleto === 'recebido' ? 'SIM' : 'NÃO'} (valor pendente zerado)`);

  // Teste com tolerância
  console.log(`\n🎯 Testando com valor ligeiramente menor (dentro da tolerância):`);
  const recebimentosTolerancia = [
    ...recebimentosAtuais,
    { valor: valorPendente - 0.005, metodo: 'Pix', observacao: null } // 0.5 centavos a menos
  ];

  const totalRecebidoTolerancia = recebimentosTolerancia.reduce((sum, r) => sum + r.valor, 0);
  const valorPendenteTolerancia = Math.max(0, totalRevisao - totalRecebidoTolerancia);
  const statusTolerancia = atualizarStatusPagamento(totalRevisao, recebimentosTolerancia);

  console.log(`   Recebimento com tolerância: R$ ${(valorPendente - 0.005).toFixed(2)}`);
  console.log(`   Total recebido: R$ ${totalRecebidoTolerancia.toFixed(2)}`);
  console.log(`   Valor pendente: R$ ${valorPendenteTolerancia.toFixed(3)}`);
  console.log(`   Status: "${statusTolerancia}"`);
  console.log(`   ✅ Status correto: ${statusTolerancia === 'recebido' ? 'SIM' : 'NÃO'} (dentro da tolerância de 0.01)`);

  return {
    cenarioAtual: statusAtual === 'parcialmente_recebido',
    cenarioCompleto: statusCompleto === 'recebido',
    cenarioTolerancia: statusTolerancia === 'recebido'
  };
}

const resultados = testCenarioReal();
console.log(`\n📈 Resumo dos testes:`);
console.log(`   Cenário atual (pendente): ${resultados.cenarioAtual ? '✅' : '❌'}`);
console.log(`   Cenário completo (recebido): ${resultados.cenarioCompleto ? '✅' : '❌'}`);
console.log(`   Cenário tolerância (recebido): ${resultados.cenarioTolerancia ? '✅' : '❌'}`);

console.log('\n=== FIM DOS TESTES ===');
