import React, { useState, useRef, useEffect } from 'react';
import { ChevronLeft, ChevronRight, ShoppingCart, Phone, Info, Clock, MessageSquare } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { Category, Product, Store } from '@shared/schema';
import { formatCurrency } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

interface HorizontalCategoriesLayoutProps {
  categories: Category[];
  products: Product[];
  store: Store;
  onProductClick: (product: Product) => void;
  onAddToCart: (product: Product) => void;
}

// Definição do tipo de variação
interface VariationOption {
  id: string;
  name: string;
  precoAdicional: number;
}

interface VariationGroup {
  id: string;
  nomeGrupo: string;
  obrigatorio: boolean;
  minSelecionados: number;
  maxSelecionados: number;
  opcoes: VariationOption[];
}

// Interface para gerenciar seleções de variacões
interface ProductSelectionsState {
  [productId: number]: {
    variations: {
      [variationId: string]: string[] // IDs das opções selecionadas
    },
    obs: string;
  }
}

const HorizontalCategoriesLayout: React.FC<HorizontalCategoriesLayoutProps> = ({
  categories,
  products,
  store,
  onProductClick,
  onAddToCart
}) => {
  const { t } = useTranslation();
  const [selectedCategory, setSelectedCategory] = useState<number | null>(
    categories.length > 0 ? categories[0].id : null
  );
  const categoriesRef = useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  const [productSelections, setProductSelections] = useState<ProductSelectionsState>({});
  const [cartItems, setCartItems] = useState<{count: number, total: number}>({count: 0, total: 0});

  // Check scroll possibility
  useEffect(() => {
    const checkScroll = () => {
      if (categoriesRef.current) {
        setCanScrollLeft(categoriesRef.current.scrollLeft > 0);
        setCanScrollRight(
          categoriesRef.current.scrollLeft <
            categoriesRef.current.scrollWidth - categoriesRef.current.clientWidth - 10
        );
      }
    };

    const currentRef = categoriesRef.current;
    if (currentRef) {
      currentRef.addEventListener('scroll', checkScroll);
      // Initial check
      checkScroll();
    }

    return () => {
      if (currentRef) {
        currentRef.removeEventListener('scroll', checkScroll);
      }
    };
  }, [categories]);

  const handleScrollCategories = (direction: 'left' | 'right') => {
    if (categoriesRef.current) {
      const scrollAmount = 200;
      const currentScroll = categoriesRef.current.scrollLeft;
      categoriesRef.current.scrollTo({
        left: direction === 'left' ? currentScroll - scrollAmount : currentScroll + scrollAmount,
        behavior: 'smooth'
      });
    }
  };

  // Inicializar seleções de produto quando necessário
  useEffect(() => {
    const initialSelections: ProductSelectionsState = {};
    products.forEach(product => {
      if (!productSelections[product.id]) {
        initialSelections[product.id] = {
          variations: {},
          obs: ''
        };
        
        // Se o produto tiver variações, pré-inicializar
        if (product.hasVariations && product.variations) {
          const variations = product.variations as unknown as VariationGroup[];
          variations.forEach(variation => {
            initialSelections[product.id].variations[variation.id] = [];
          });
        }
      }
    });
    
    if (Object.keys(initialSelections).length > 0) {
      setProductSelections(prev => ({...prev, ...initialSelections}));
    }
  }, [products]);

  // Função para manipular seleção de variações
  const handleVariationChange = (
    productId: number, 
    variationId: string, 
    optionId: string, 
    isMultiple: boolean,
    checked?: boolean
  ) => {
    setProductSelections(prevState => {
      const productState = prevState[productId] || { variations: {}, obs: '' };
      const currentSelections = productState.variations[variationId] || [];
      
      let newSelections: string[];
      
      if (isMultiple) {
        // Para checkbox (múltipla seleção)
        if (checked) {
          newSelections = [...currentSelections, optionId];
        } else {
          newSelections = currentSelections.filter(id => id !== optionId);
        }
      } else {
        // Para radio (seleção única)
        newSelections = [optionId];
      }
      
      return {
        ...prevState,
        [productId]: {
          ...productState,
          variations: {
            ...productState.variations,
            [variationId]: newSelections
          }
        }
      };
    });
  };

  // Função para atualizar observações
  const handleObsChange = (productId: number, value: string) => {
    setProductSelections(prevState => {
      const productState = prevState[productId] || { variations: {}, obs: '' };
      return {
        ...prevState,
        [productId]: {
          ...productState,
          obs: value
        }
      };
    });
  };

  // Função para calcular o preço final com adicionais
  const calculateFinalPrice = (product: Product) => {
    let finalPrice = product.price;
    
    if (product.hasVariations && product.variations && productSelections[product.id]) {
      const variations = product.variations as unknown as VariationGroup[];
      const selections = productSelections[product.id].variations;
      
      variations.forEach(variation => {
        const selectedOptions = selections[variation.id] || [];
        
        selectedOptions.forEach(optionId => {
          const option = variation.opcoes.find(opt => opt.id === optionId);
          if (option) {
            finalPrice += option.precoAdicional;
          }
        });
      });
    }
    
    return finalPrice;
  };

  // Verificar se pode adicionar ao carrinho (todas variações obrigatórias selecionadas)
  const canAddToCart = (product: Product) => {
    if (!product.hasVariations) return true;
    if (!product.variations) return true;
    
    const selections = productSelections[product.id]?.variations;
    if (!selections) return false;
    
    const variations = product.variations as unknown as VariationGroup[];
    
    return variations.every(variation => {
      const selectedCount = (selections[variation.id] || []).length;
      
      if (variation.obrigatorio) {
        return selectedCount >= variation.minSelecionados && 
               selectedCount <= variation.maxSelecionados;
      }
      
      // Se não for obrigatório, só verifica se não excedeu o máximo
      return selectedCount <= variation.maxSelecionados;
    });
  };

  // Função para adicionar ao carrinho com validações
  const handleAddProductToCart = (product: Product) => {
    if (!canAddToCart(product)) {
      // Exibir mensagem de erro
      alert(t('storefront.selectRequiredVariations') || 'Por favor, selecione todas as opções obrigatórias');
      return;
    }
    
    // Adicionar ao carrinho com variações selecionadas
    const productWithSelections = {
      ...product,
      finalPrice: calculateFinalPrice(product),
      selectedVariations: productSelections[product.id]
    };
    
    // Atualizar carrinho
    setCartItems(prev => ({
      count: prev.count + 1,
      total: prev.total + calculateFinalPrice(product)
    }));
    
    // Chamar função de callback
    onAddToCart(productWithSelections as any);
  };

  // Filter products by selected category or show all if "All Products" is selected
  const filteredProducts = selectedCategory 
    ? products.filter((product) => product.categoryId === selectedCategory)
    : products;

  return (
    <div className="flex flex-col min-h-screen bg-background">
      {/* Header fixo */}
      <header className="sticky top-0 z-10 bg-background border-b p-4 shadow-sm">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            {store?.logo && (
              <img 
                src={store.logo} 
                alt={store?.name || "Store"} 
                className="h-10 w-10 object-contain mr-2 rounded-md"
              />
            )}
            <div>
              <h1 className="text-xl font-bold">{store?.name}</h1>
              <div className="flex items-center text-xs text-muted-foreground mt-1">
                <Badge variant="outline" className="mr-2 text-xs font-normal">
                  <Clock className="h-3 w-3 mr-1" />
                  Aberto
                </Badge>
                {store?.whatsapp && (
                  <a 
                    href={`https://wa.me/${store.whatsapp}`} 
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center text-xs text-muted-foreground hover:text-primary"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <Phone className="h-3 w-3 mr-1" />
                    Contato
                  </a>
                )}
              </div>
            </div>
          </div>
          <Button variant="outline" size="icon" className="relative">
            <ShoppingCart className="h-5 w-5" />
            {cartItems.count > 0 && (
              <span className="absolute -top-2 -right-2 bg-primary text-primary-foreground text-xs font-semibold rounded-full h-5 w-5 flex items-center justify-center">
                {cartItems.count}
              </span>
            )}
          </Button>
        </div>
      </header>

      {/* Menu de categorias horizontal */}
      <div className="relative mt-2 px-4">
        {canScrollLeft && (
          <button
            onClick={() => handleScrollCategories('left')}
            className="absolute left-0 top-1/2 -translate-y-1/2 bg-background/80 p-1 rounded-full shadow-md z-10"
          >
            <ChevronLeft className="h-6 w-6" />
          </button>
        )}
        
        <ScrollArea className="w-full">
          <div 
            ref={categoriesRef}
            className="flex space-x-2 py-3 w-full"
          >
            <Button
              variant={selectedCategory === null ? "default" : "outline"}
              className="whitespace-nowrap rounded-full px-4 py-2 text-sm"
              onClick={() => setSelectedCategory(null)}
            >
              {t('storefront.allProducts')}
            </Button>
            
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? "default" : "outline"}
                className="whitespace-nowrap rounded-full px-4 py-2 text-sm"
                onClick={() => setSelectedCategory(category.id)}
              >
                {category.name}
              </Button>
            ))}
          </div>
          <ScrollBar orientation="horizontal" />
        </ScrollArea>
        
        {canScrollRight && (
          <button
            onClick={() => handleScrollCategories('right')}
            className="absolute right-0 top-1/2 -translate-y-1/2 bg-background/80 p-1 rounded-full shadow-md z-10"
          >
            <ChevronRight className="h-6 w-6" />
          </button>
        )}
      </div>

      {/* Listagem de produtos */}
      <main className="flex-1 px-4 py-4">
        {/* Organização por categoria */}
        {filteredProducts.length > 0 ? (
          selectedCategory ? (
            // Produtos da categoria selecionada
            <div className="space-y-6">
              {filteredProducts.map((product) => (
                <div key={product.id} className="bg-white rounded-lg shadow-sm border overflow-hidden">
                  {/* Cabeçalho do produto */}
                  <div className="flex border-b">
                    <div className="relative w-1/3 h-24">
                      {product.images && Array.isArray(product.images) && product.images.length > 0 ? (
                        <img 
                          src={product.images[0] as string} 
                          alt={product.name} 
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-muted flex items-center justify-center">
                          <span className="text-muted-foreground">No image</span>
                        </div>
                      )}
                    </div>
                    <div className="p-3 flex-1">
                      <h3 className="font-semibold">{product.name}</h3>
                      <p className="text-sm text-muted-foreground line-clamp-2 mt-1">
                        {product.description || ""}
                      </p>
                      <p className="text-primary font-bold mt-1">
                        {formatCurrency(calculateFinalPrice(product), store?.currency)}
                      </p>
                    </div>
                  </div>
                  
                  {/* Variações do produto */}
                  {product.hasVariations && product.variations && (
                    <div className="p-3 space-y-4">
                      {(product.variations as unknown as VariationGroup[]).map((variation) => (
                        <div key={variation.id} className="space-y-2">
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium text-sm">
                              {variation.nomeGrupo} 
                              {variation.obrigatorio && (
                                <span className="text-red-500 ml-1">*</span>
                              )}
                            </h4>
                            <span className="text-xs text-muted-foreground">
                              {variation.minSelecionados === variation.maxSelecionados && variation.maxSelecionados === 1 
                                ? 'Escolha 1'
                                : `Min: ${variation.minSelecionados}, Max: ${variation.maxSelecionados}`
                              }
                            </span>
                          </div>
                          
                          {/* Opções de variação */}
                          {variation.maxSelecionados === 1 ? (
                            // Radio buttons para seleção única
                            <RadioGroup 
                              value={(productSelections[product.id]?.variations[variation.id]?.[0] || '')}
                              onValueChange={(value) => {
                                handleVariationChange(product.id, variation.id, value, false);
                              }}
                            >
                              <div className="grid grid-cols-1 gap-2">
                                {variation.opcoes.map((option) => (
                                  <div key={option.id} className="flex items-center space-x-2">
                                    <RadioGroupItem value={option.id} id={`${product.id}-${variation.id}-${option.id}`} />
                                    <Label 
                                      htmlFor={`${product.id}-${variation.id}-${option.id}`}
                                      className="flex items-center justify-between w-full text-sm"
                                    >
                                      <span>{option.name}</span>
                                      {option.precoAdicional > 0 && (
                                        <span className="text-primary text-xs">
                                          +{formatCurrency(option.precoAdicional, store?.currency || 'R$')}
                                        </span>
                                      )}
                                    </Label>
                                  </div>
                                ))}
                              </div>
                            </RadioGroup>
                          ) : (
                            // Checkboxes para múltipla seleção
                            <div className="grid grid-cols-1 gap-2">
                              {variation.opcoes.map((option) => {
                                const isSelected = (productSelections[product.id]?.variations[variation.id] || []).includes(option.id);
                                
                                // Verificar se chegou ao limite de seleções
                                const selections = productSelections[product.id]?.variations[variation.id] || [];
                                const reachedMax = selections.length >= variation.maxSelecionados;
                                const disableOption = reachedMax && !isSelected;
                                
                                return (
                                  <div key={option.id} className="flex items-center space-x-2">
                                    <Checkbox 
                                      id={`${product.id}-${variation.id}-${option.id}`}
                                      checked={isSelected}
                                      disabled={disableOption}
                                      onCheckedChange={(checked) => {
                                        handleVariationChange(
                                          product.id, 
                                          variation.id, 
                                          option.id, 
                                          true, 
                                          !!checked
                                        );
                                      }}
                                    />
                                    <Label 
                                      htmlFor={`${product.id}-${variation.id}-${option.id}`}
                                      className={`flex items-center justify-between w-full text-sm ${disableOption ? 'text-muted-foreground' : ''}`}
                                    >
                                      <span>{option.name}</span>
                                      {option.precoAdicional > 0 && (
                                        <span className="text-primary text-xs">
                                          +{formatCurrency(option.precoAdicional, store?.currency || 'R$')}
                                        </span>
                                      )}
                                    </Label>
                                  </div>
                                );
                              })}
                            </div>
                          )}
                        </div>
                      ))}
                      
                      {/* Campo de observações */}
                      <div className="mt-4">
                        <Label htmlFor={`obs-${product.id}`} className="text-sm font-medium">
                          Observações
                        </Label>
                        <Textarea 
                          id={`obs-${product.id}`}
                          placeholder="Alguma instrução adicional?"
                          className="mt-1"
                          value={productSelections[product.id]?.obs || ''}
                          onChange={(e) => handleObsChange(product.id, e.target.value)}
                        />
                      </div>
                      
                      {/* Botão adicionar ao carrinho */}
                      <Button 
                        className="w-full mt-4" 
                        size="lg"
                        onClick={() => handleAddProductToCart(product)}
                        disabled={!canAddToCart(product)}
                      >
                        {t('storefront.addToCart')} - {formatCurrency(calculateFinalPrice(product), store?.currency || 'R$')}
                      </Button>
                    </div>
                  )}
                  
                  {/* Produtos sem variações */}
                  {(!product.hasVariations || !product.variations) && product.inStock && (
                    <div className="p-3">
                      <Button 
                        className="w-full" 
                        size="sm"
                        onClick={() => onAddToCart(product)}
                      >
                        {t('storefront.addToCart')}
                      </Button>
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            // Todas as categorias
            <div className="space-y-8">
              {categories.map((category) => {
                const categoryProducts = products.filter(p => p.categoryId === category.id);
                if (categoryProducts.length === 0) return null;
                
                return (
                  <div key={category.id}>
                    <h2 className="text-lg font-bold mb-4">{category.name}</h2>
                    <div className="grid grid-cols-2 gap-4">
                      {categoryProducts.map((product) => (
                        <div 
                          key={product.id} 
                          className="border rounded-lg overflow-hidden flex flex-col bg-white shadow-sm"
                          onClick={() => setSelectedCategory(category.id)}
                        >
                          <div className="relative h-32">
                            {product.images && Array.isArray(product.images) && product.images.length > 0 ? (
                              <img 
                                src={product.images[0] as string} 
                                alt={product.name} 
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <div className="w-full h-full bg-muted flex items-center justify-center">
                                <span className="text-muted-foreground">No image</span>
                              </div>
                            )}
                            
                            {product.hasVariations && (
                              <Badge 
                                className="absolute top-2 right-2 bg-primary/70 hover:bg-primary/70"
                                variant="secondary"
                              >
                                {t('products.hasVariationsShort')}
                              </Badge>
                            )}
                            
                            {!product.inStock && (
                              <div className="absolute inset-0 bg-background/60 flex items-center justify-center">
                                <Badge variant="destructive">
                                  {t('storefront.outOfStock')}
                                </Badge>
                              </div>
                            )}
                          </div>
                          
                          <div className="p-3 flex-1 flex flex-col">
                            <h3 className="font-medium line-clamp-2 mb-1">{product.name}</h3>
                            <p className="text-primary font-bold mt-auto">
                              {formatCurrency(product.price, store?.currency || 'R$')}
                            </p>
                          </div>
                          
                          {product.inStock && (
                            <div className="px-3 pb-3 mt-auto">
                              <Button 
                                variant="outline" 
                                size="sm" 
                                className="w-full"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setSelectedCategory(category.id);
                                }}
                              >
                                Ver detalhes
                              </Button>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                );
              })}
            </div>
          )
        ) : (
          <div className="py-10 text-center text-muted-foreground">
            {t('products.noProducts')}
          </div>
        )}
      </main>
      
      {/* Rodapé flutuante com resumo do carrinho */}
      {cartItems.count > 0 && (
        <div className="fixed bottom-0 left-0 right-0 bg-background border-t shadow-md p-3 flex items-center justify-between">
          <div>
            <p className="text-sm font-medium">{cartItems.count} {cartItems.count === 1 ? 'item' : 'itens'}</p>
            <p className="text-primary font-bold">{formatCurrency(cartItems.total, store?.currency || 'R$')}</p>
          </div>
          <Button>
            Ver Carrinho
          </Button>
        </div>
      )}
    </div>
  );
};

export default HorizontalCategoriesLayout;