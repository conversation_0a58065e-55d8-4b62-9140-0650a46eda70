import { useState, useRef } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { AlertCircle, ImagePlus, Loader2, ArrowLeft } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { insertCategorySchema } from '@shared/schema';
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { useTranslation } from '@/hooks/useTranslation';
import { Link, useLocation } from 'wouter';
import { uploadCategoryLogo } from '@/lib/uploadUtils';
import { useStore } from '@/context/StoreContext';
import AdminLayout from '@/components/admin/AdminLayout';

type CategoryFormValues = z.infer<typeof insertCategorySchema>;

export default function NewCategoryPage() {
  const { t } = useTranslation();
  const { toast } = useToast();
  const { store } = useStore();
  const queryClient = useQueryClient();
  const [isUploading, setIsUploading] = useState(false);
  const [logoUrl, setLogoUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [, navigate] = useLocation();

  const form = useForm<CategoryFormValues>({
    resolver: zodResolver(insertCategorySchema),
    defaultValues: {
      name: "",
      description: "",
      storeId: 0,
      logo: "",
    },
  });

  const createMutation = useMutation({
    mutationFn: (data: CategoryFormValues) => {
      return apiRequest('POST', '/api/categories', data);
    },
    onSuccess: () => {
      toast({
        title: t('admin.categoryCreated'),
        description: t('admin.categoryCreatedDesc'),
      });
      queryClient.invalidateQueries({ queryKey: ['/api/categories'] });
      navigate('/admin/categories');
    },
    onError: (error) => {
      toast({
        title: t('common.error'),
        description: error.message,
        variant: "destructive",
      });
    },
  });

  function onSubmit(values: CategoryFormValues) {
    // Include logo URL in form data if it exists
    if (logoUrl) {
      values.logo = logoUrl;
    }
    
    // Add store ID from context
    if (store) {
      values.storeId = store.id;
    }
    
    createMutation.mutate(values);
  }

  const handleLogoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !store) return;
    
    try {
      setIsUploading(true);
      
      const imageUrl = await uploadCategoryLogo(file, store.id);
      setLogoUrl(imageUrl);
      
      // Update form with new logo URL
      form.setValue("logo", imageUrl);
      
      toast({
        title: t('settings.logoUploaded'),
        description: t('settings.logoUploadSuccess'),
      });
    } catch (error) {
      toast({
        title: t('common.error'),
        description: error instanceof Error ? error.message : String(error),
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
      
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };
  
  // Trigger file input click
  const triggerFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <AdminLayout title={t('admin.newCategory')}>
      <div className="container py-6 space-y-6 px-0">
        <div className="flex items-center mb-6">
          <Link href="/admin/categories">
            <Button variant="ghost" size="icon" className="mr-2">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
        </div>
      
      <div className="max-w-2xl mx-auto">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('admin.categoryName')}</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('admin.categoryDescription')}</FormLabel>
                  <FormControl>
                    <Textarea {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="logo"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('admin.categoryLogo')}</FormLabel>
                  <div>
                    <input
                      ref={fileInputRef}
                      type="file" 
                      className="hidden" 
                      accept="image/*"
                      onChange={handleLogoUpload}
                    />
                    
                    {/* Logo preview and upload button */}
                    <div className="flex flex-col gap-4 mt-2">
                      {logoUrl && (
                        <div className="relative w-full h-48 border rounded overflow-hidden bg-muted">
                          <img 
                            src={logoUrl} 
                            alt="Category logo"
                            className="object-cover w-full h-full"
                            onError={(e) => {
                              // Handle image loading error
                              (e.target as HTMLImageElement).style.display = 'none';
                            }}
                          />
                        </div>
                      )}
                      
                      <Button 
                        type="button"
                        variant="outline"
                        onClick={triggerFileInput}
                        disabled={isUploading}
                        className="flex items-center gap-2 w-full"
                      >
                        {isUploading ? (
                          <Loader2 className="w-4 h-4 animate-spin" />
                        ) : (
                          <ImagePlus className="w-4 h-4" />
                        )}
                        {logoUrl ? t('admin.changeImage') : t('admin.uploadLogo')}
                      </Button>
                    </div>
                    
                    <FormDescription>
                      {t('admin.logoDescription')}
                    </FormDescription>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <div className="flex justify-end pt-4">
              <Button 
                type="submit" 
                disabled={createMutation.isPending || isUploading}
                className="min-w-24"
              >
                {createMutation.isPending ? (
                  <Loader2 className="w-4 h-4 animate-spin mr-2" />
                ) : null}
                {createMutation.isPending ? t('common.saving') : t('common.save')}
              </Button>
            </div>
          </form>
        </Form>
      </div>
      </div>
    </AdminLayout>
  );
}