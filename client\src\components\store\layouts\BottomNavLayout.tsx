import React, { useState, useEffect } from 'react';
import { ShoppingCart, Menu, Home, Search, Package, Phone, Info, Clock, MessageSquare } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { Category, Product, Store, VariationGroup, VariationOption, ProductWithVariations } from '@shared/schema';
import { formatCurrency } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

interface BottomNavLayoutProps {
  categories: Category[];
  products: Product[];
  store: Store;
  onProductClick: (product: Product) => void;
  onAddToCart: (product: Product) => void;
}

// Interface para gerenciar seleções de variacões
interface ProductSelectionsState {
  [productId: number]: {
    variations: {
      [variationId: string]: string[] // IDs das opções selecionadas
    },
    obs: string;
  }
}

const BottomNavLayout: React.FC<BottomNavLayoutProps> = ({
  categories,
  products,
  store,
  onProductClick,
  onAddToCart
}) => {
  const { t } = useTranslation();
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [productSelections, setProductSelections] = useState<ProductSelectionsState>({});
  const [cartItems, setCartItems] = useState<{count: number, total: number}>({count: 0, total: 0});
  const [expandedProduct, setExpandedProduct] = useState<number | null>(null);

  // Filter products by selected category and search term
  const filteredProducts = products.filter((product) => {
    const matchesCategory = selectedCategory === null || product.categoryId === selectedCategory;
    const matchesSearch = searchTerm === '' || 
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
      (product.description && product.description.toLowerCase().includes(searchTerm.toLowerCase()));
    
    return matchesCategory && matchesSearch;
  });

  // Inicializar seleções de produto quando necessário
  useEffect(() => {
    const initialSelections: ProductSelectionsState = {};
    products.forEach(product => {
      if (!productSelections[product.id]) {
        initialSelections[product.id] = {
          variations: {},
          obs: ''
        };
        
        // Se o produto tiver variações, pré-inicializar
        if (product.hasVariations && product.variations) {
          const variations = product.variations as unknown as VariationGroup[];
          variations.forEach(variation => {
            initialSelections[product.id].variations[variation.id] = [];
          });
        }
      }
    });
    
    if (Object.keys(initialSelections).length > 0) {
      setProductSelections(prev => ({...prev, ...initialSelections}));
    }
  }, [products]);
  
  // Função para manipular seleção de variações
  const handleVariationChange = (
    productId: number, 
    variationId: string, 
    optionId: string, 
    isMultiple: boolean,
    checked?: boolean
  ) => {
    setProductSelections(prevState => {
      const productState = prevState[productId] || { variations: {}, obs: '' };
      const currentSelections = productState.variations[variationId] || [];
      
      let newSelections: string[];
      
      if (isMultiple) {
        // Para checkbox (múltipla seleção)
        if (checked) {
          newSelections = [...currentSelections, optionId];
        } else {
          newSelections = currentSelections.filter(id => id !== optionId);
        }
      } else {
        // Para radio (seleção única)
        newSelections = [optionId];
      }
      
      return {
        ...prevState,
        [productId]: {
          ...productState,
          variations: {
            ...productState.variations,
            [variationId]: newSelections
          }
        }
      };
    });
  };

  // Função para atualizar observações
  const handleObsChange = (productId: number, value: string) => {
    setProductSelections(prevState => {
      const productState = prevState[productId] || { variations: {}, obs: '' };
      return {
        ...prevState,
        [productId]: {
          ...productState,
          obs: value
        }
      };
    });
  };

  // Função para calcular o preço final com adicionais
  const calculateFinalPrice = (product: Product) => {
    let finalPrice = product.price;
    
    if (product.hasVariations && product.variations && productSelections[product.id]) {
      const variations = product.variations as unknown as VariationGroup[];
      const selections = productSelections[product.id].variations;
      
      variations.forEach(variation => {
        const selectedOptions = selections[variation.id] || [];
        
        selectedOptions.forEach(optionId => {
          const option = variation.opcoes.find(opt => opt.id === optionId);
          if (option) {
            finalPrice += option.precoAdicional;
          }
        });
      });
    }
    
    return finalPrice;
  };

  // Verificar se pode adicionar ao carrinho (todas variações obrigatórias selecionadas)
  const canAddToCart = (product: Product) => {
    if (!product.hasVariations) return true;
    if (!product.variations) return true;
    
    const selections = productSelections[product.id]?.variations;
    if (!selections) return false;
    
    const variations = product.variations as unknown as VariationGroup[];
    
    return variations.every(variation => {
      const selectedCount = (selections[variation.id] || []).length;
      
      if (variation.obrigatorio) {
        return selectedCount >= variation.minSelecionados && 
               selectedCount <= variation.maxSelecionados;
      }
      
      // Se não for obrigatório, só verifica se não excedeu o máximo
      return selectedCount <= variation.maxSelecionados;
    });
  };

  // Função para adicionar ao carrinho com validações
  const handleAddProductToCart = (product: Product) => {
    if (!canAddToCart(product)) {
      // Exibir mensagem de erro
      alert(t('storefront.selectRequiredVariations') || 'Por favor, selecione todas as opções obrigatórias');
      return;
    }
    
    // Adicionar ao carrinho com variações selecionadas
    const productWithSelections = {
      ...product,
      finalPrice: calculateFinalPrice(product),
      selectedVariations: productSelections[product.id]
    };
    
    // Atualizar carrinho
    setCartItems(prev => ({
      count: prev.count + 1,
      total: prev.total + calculateFinalPrice(product)
    }));
    
    // Depois de adicionar ao carrinho, fechar a visualização expandida
    setExpandedProduct(null);
    
    // Chamar função de callback
    onAddToCart(productWithSelections as any);
  };

  // Alternar exibição do produto expandido
  const toggleProductExpansion = (productId: number, e: React.MouseEvent) => {
    e.stopPropagation(); // Impedir propagação para não acionar o onProductClick
    setExpandedProduct(expandedProduct === productId ? null : productId);
  };

  // Get category name by id
  const getCategoryName = (categoryId: number | null) => {
    if (categoryId === null) return t('storefront.allProducts');
    const category = categories.find(c => c.id === categoryId);
    return category ? category.name : t('products.uncategorized');
  };

  return (
    <div className="flex flex-col min-h-screen bg-background pb-16">
      {/* Header */}
      <header className="sticky top-0 z-10 bg-background border-b p-4 shadow-sm">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            {store?.logo && (
              <img 
                src={store.logo} 
                alt={store?.name || "Store"} 
                className="h-10 w-10 object-contain mr-2 rounded-md"
              />
            )}
            <h1 className="text-xl font-bold">{store?.name}</h1>
          </div>
          <Button variant="outline" size="icon">
            <ShoppingCart className="h-5 w-5" />
          </Button>
        </div>
        
        {/* Search input */}
        <div className="mt-4">
          <Input
            placeholder={t('storefront.searchProducts')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full"
          />
        </div>
        
        {/* Selected category display */}
        <div className="flex items-center justify-between mt-4">
          <h2 className="text-lg font-semibold">
            {getCategoryName(selectedCategory)}
          </h2>
          
          {/* Category menu sheet */}
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline" size="sm">
                <Menu className="h-4 w-4 mr-2" />
                {t('common.categories')}
              </Button>
            </SheetTrigger>
            <SheetContent side="right">
              <div className="py-6">
                <h3 className="text-lg font-medium mb-4">{t('common.categories')}</h3>
                <div className="space-y-2">
                  <Button
                    variant={selectedCategory === null ? "default" : "outline"}
                    className="w-full justify-start"
                    onClick={() => {
                      setSelectedCategory(null);
                    }}
                  >
                    {t('storefront.allProducts')}
                  </Button>
                  
                  {categories.map((category) => (
                    <Button
                      key={category.id}
                      variant={selectedCategory === category.id ? "default" : "outline"}
                      className="w-full justify-start"
                      onClick={() => {
                        setSelectedCategory(category.id);
                      }}
                    >
                      {category.name}
                    </Button>
                  ))}
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </header>

      {/* Products list */}
      <main className="flex-1 px-4 py-4">
        <div className="space-y-4">
          {filteredProducts.length > 0 ? (
            filteredProducts.map((product) => (
              <div 
                key={product.id} 
                className={`border rounded-lg overflow-hidden bg-white shadow-sm ${expandedProduct === product.id ? 'flex flex-col' : 'flex'}`}
              >
                {/* Product Header - always visible */}
                <div 
                  className="flex w-full" 
                  onClick={(e) => product.hasVariations ? toggleProductExpansion(product.id, e) : onProductClick(product)}
                >
                  <div className="w-28 h-28 relative flex-shrink-0">
                    {product.images && Array.isArray(product.images) && product.images.length > 0 ? (
                      <img 
                        src={product.images[0] as string} 
                        alt={product.name} 
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-muted flex items-center justify-center">
                        <span className="text-muted-foreground">No image</span>
                      </div>
                    )}
                    
                    {!product.inStock && (
                      <div className="absolute inset-0 bg-background/60 flex items-center justify-center">
                        <Badge variant="destructive" className="text-xs">
                          {t('storefront.outOfStock')}
                        </Badge>
                      </div>
                    )}
                  </div>
                  
                  <div className="p-3 flex flex-col flex-1">
                    <div className="flex justify-between items-start mb-1">
                      <h3 className="font-medium line-clamp-2">{product.name}</h3>
                      {product.hasVariations && (
                        <Badge 
                          className="ml-2 bg-primary/70 hover:bg-primary/70"
                          variant="secondary"
                        >
                          {t('products.hasVariationsShort')}
                        </Badge>
                      )}
                    </div>
                    
                    {product.description && (
                      <p className="text-muted-foreground text-sm line-clamp-2 mb-2">
                        {product.description}
                      </p>
                    )}
                    
                    <div className="mt-auto flex items-center justify-between">
                      <p className="text-primary font-bold">
                        {product.hasVariations ? 
                          formatCurrency(calculateFinalPrice(product), store?.currency) :
                          formatCurrency(product.price, store?.currency || 'R$')
                        }
                      </p>
                      
                      {product.inStock && (
                        product.hasVariations ? (
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={(e) => toggleProductExpansion(product.id, e)}
                          >
                            {expandedProduct === product.id ? 
                              t('storefront.closeOptions') : 
                              t('storefront.selectOptions') || 'Selecionar opções'
                            }
                          </Button>
                        ) : (
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleAddProductToCart(product);
                            }}
                          >
                            {t('storefront.addToCart')}
                          </Button>
                        )
                      )}
                    </div>
                  </div>
                </div>
                
                {/* Product Variations - only visible when expanded */}
                {expandedProduct === product.id && product.hasVariations && product.variations && (
                  <div className="p-4 border-t pt-3">
                    <Accordion type="single" collapsible className="w-full">
                      {/* Variações do produto */}
                      {(product.variations as unknown as VariationGroup[]).map((variation) => (
                        <AccordionItem key={variation.id} value={variation.id}>
                          <AccordionTrigger className="text-sm font-medium">
                            {variation.nomeGrupo}
                            {variation.obrigatorio && (
                              <span className="text-red-500 ml-1">*</span>
                            )}
                          </AccordionTrigger>
                          <AccordionContent>
                            <div className="py-2">
                              {variation.maxSelecionados === 1 ? (
                                // Opções de seleção única (radio)
                                <RadioGroup
                                  value={
                                    productSelections[product.id]?.variations[variation.id]?.[0] || ""
                                  }
                                  onValueChange={(value) => 
                                    handleVariationChange(product.id, variation.id, value, false)
                                  }
                                >
                                  <div className="space-y-2">
                                    {variation.opcoes.map((option) => (
                                      <div key={option.id} className="flex items-center justify-between p-2 rounded hover:bg-muted/50 cursor-pointer w-full">
                                        <div className="flex items-center space-x-2">
                                          <RadioGroupItem value={option.id} id={`${variation.id}-${option.id}`} />
                                          <Label htmlFor={`${variation.id}-${option.id}`} className="cursor-pointer">{option.name}</Label>
                                        </div>
                                        {option.precoAdicional > 0 && (
                                          <span className="text-sm text-primary">
                                            + {formatCurrency(option.precoAdicional, store?.currency || 'R$')}
                                          </span>
                                        )}
                                      </div>
                                    ))}
                                  </div>
                                </RadioGroup>
                              ) : (
                                // Opções de múltipla seleção (checkbox)
                                <div className="space-y-2">
                                  {variation.opcoes.map((option) => {
                                    const isSelected = productSelections[product.id]?.variations[variation.id]?.includes(option.id) || false;
                                    
                                    return (
                                      <div key={option.id} className="flex items-center justify-between p-2 rounded hover:bg-muted/50 cursor-pointer w-full">
                                        <div className="flex items-center space-x-2">
                                          <Checkbox 
                                            id={`${variation.id}-${option.id}`}
                                            checked={isSelected}
                                            onCheckedChange={(checked) => {
                                              handleVariationChange(
                                                product.id, 
                                                variation.id, 
                                                option.id, 
                                                true, 
                                                checked as boolean
                                              );
                                            }}
                                            className="mt-0.5"
                                          />
                                          <Label htmlFor={`${variation.id}-${option.id}`} className="cursor-pointer">{option.name}</Label>
                                        </div>
                                        {option.precoAdicional > 0 && (
                                          <span className="text-sm text-primary">
                                            + {formatCurrency(option.precoAdicional, store?.currency || 'R$')}
                                          </span>
                                        )}
                                      </div>
                                    );
                                  })}
                                </div>
                              )}
                              
                              {/* Informação sobre seleção mínima e máxima */}
                              {variation.minSelecionados > 0 && (
                                <p className="mt-2 text-xs text-muted-foreground">
                                  {variation.minSelecionados === variation.maxSelecionados ? (
                                    t('products.selectExactOptions', {
                                      count: variation.minSelecionados
                                    }) || `Selecione exatamente ${variation.minSelecionados} ${variation.minSelecionados === 1 ? 'opção' : 'opções'}`
                                  ) : (
                                    t('products.selectMinMaxOptions', {
                                      min: variation.minSelecionados,
                                      max: variation.maxSelecionados
                                    }) || `Selecione de ${variation.minSelecionados} a ${variation.maxSelecionados} opções`
                                  )}
                                </p>
                              )}
                            </div>
                          </AccordionContent>
                        </AccordionItem>
                      ))}
                      
                      {/* Observações */}
                      <AccordionItem value="observacoes">
                        <AccordionTrigger className="text-sm font-medium">
                          {t('products.notes') || 'Observações'}
                        </AccordionTrigger>
                        <AccordionContent>
                          <div className="py-2">
                            <Textarea
                              placeholder={t('products.enterNotes') || 'Digite suas observações aqui...'}
                              value={productSelections[product.id]?.obs || ''}
                              onChange={(e) => handleObsChange(product.id, e.target.value)}
                              className="w-full"
                              rows={3}
                            />
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    </Accordion>
                    
                    {/* Resumo e botão adicionar ao carrinho */}
                    <div className="mt-4 border-t pt-4">
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-semibold">{t('common.total')}:</span>
                        <span className="text-primary text-lg font-bold">
                          {formatCurrency(calculateFinalPrice(product), store?.currency || 'R$')}
                        </span>
                      </div>
                      
                      <Button 
                        className="w-full"
                        disabled={!canAddToCart(product)}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleAddProductToCart(product);
                        }}
                      >
                        {t('storefront.addToCart')}
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            ))
          ) : (
            <div className="py-10 text-center text-muted-foreground">
              {t('products.noProducts')}
            </div>
          )}
        </div>
      </main>

      {/* Bottom navigation */}
      <nav className="fixed bottom-0 left-0 right-0 bg-background border-t p-2 flex justify-around items-center z-20">
        <Button variant="ghost" size="sm" className="flex flex-col items-center" onClick={() => setSelectedCategory(null)}>
          <Home className="h-5 w-5" />
          <span className="text-xs mt-1">{t('storefront.allProducts')}</span>
        </Button>
        
        <Button variant="ghost" size="sm" className="flex flex-col items-center" onClick={() => setSearchTerm('')}>
          <Search className="h-5 w-5" />
          <span className="text-xs mt-1">{t('common.search')}</span>
        </Button>
        
        <Button variant="ghost" size="sm" className="flex flex-col items-center">
          <ShoppingCart className="h-5 w-5" />
          <span className="text-xs mt-1">{t('storefront.cart')}</span>
        </Button>
        
        <Sheet>
          <SheetTrigger asChild>
            <Button variant="ghost" size="sm" className="flex flex-col items-center">
              <Package className="h-5 w-5" />
              <span className="text-xs mt-1">{t('common.categories')}</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="bottom" className="h-[50vh]">
            <div className="py-6">
              <h3 className="text-lg font-medium mb-4">{t('common.categories')}</h3>
              <div className="space-y-2">
                <Button
                  variant={selectedCategory === null ? "default" : "outline"}
                  className="w-full justify-start"
                  onClick={() => {
                    setSelectedCategory(null);
                  }}
                >
                  {t('storefront.allProducts')}
                </Button>
                
                {categories.map((category) => (
                  <Button
                    key={category.id}
                    variant={selectedCategory === category.id ? "default" : "outline"}
                    className="w-full justify-start"
                    onClick={() => {
                      setSelectedCategory(category.id);
                    }}
                  >
                    {category.name}
                  </Button>
                ))}
              </div>
            </div>
          </SheetContent>
        </Sheet>
      </nav>
    </div>
  );
};

export default BottomNavLayout;