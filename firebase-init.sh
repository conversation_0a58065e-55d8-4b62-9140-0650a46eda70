#!/bin/bash

echo "=== Firebase Hosting Setup for Doce Menu ==="
echo "This script will help you initialize Firebase for deployment"
echo ""

# Login to Firebase
echo "1. First, let's login to Firebase:"
npx firebase login

# Initialize Firebase project
echo ""
echo "2. Now, let's initialize your Firebase project."
echo "   When prompted, select the following options:"
echo "   - Select Hosting"
echo "   - Select 'Use an existing project' and choose 'docemenu'"
echo "   - For public directory, enter: dist/public"
echo "   - Configure as a single-page app: Yes"
echo "   - Set up automatic builds and deploys with GitHub: No"
echo ""
npx firebase init

echo ""
echo "=== Firebase Setup Complete ==="
echo ""
echo "To deploy your application to Firebase, run:"
echo "./deploy.sh"
echo ""
echo "Your site will be available at: https://app.docemenu.com.br"