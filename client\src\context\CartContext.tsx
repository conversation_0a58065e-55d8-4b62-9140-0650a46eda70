import { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { useTranslation } from '@/hooks/useTranslation';

interface SelectedVariation {
  variationId: number;
  variationName: string;
  optionId: number;
  optionName: string;
  price: number;
}

interface CartItem {
  id: number;
  productId: number;
  name: string;
  price: number;
  image?: string;
  quantity: number;
  selectedVariations?: SelectedVariation[];
  observation?: string; // Customer notes/observations about this item
}

interface AppliedCoupon {
  id: number;
  code: string;
  tipo: 'valor_fixo' | 'percentual';
  valor: number;
  minimoCompra?: number;
}

interface CartContextType {
  items: CartItem[];
  addItem: (item: Omit<CartItem, 'id' | 'quantity'>, quantity?: number) => void;
  removeItem: (itemId: number) => void;
  updateQuantity: (itemId: number, quantity: number) => void;
  clearCart: () => void;
  totalItems: number;
  subtotal: number;

  // Cupom
  coupon: AppliedCoupon | null;
  applyCoupon: (storeSlug: string, code: string) => Promise<boolean>;
  removeCoupon: () => void;
  discount: number;
  total: number;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export function CartProvider({ children }: { children: ReactNode }) {
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [coupon, setCoupon] = useState<AppliedCoupon | null>(null);
  const { toast } = useToast();
  const { t } = useTranslation();

  // Load cart and coupon from localStorage on mount
  useEffect(() => {
    // Carregar carrinho
    const savedCart = localStorage.getItem('cart');
    if (savedCart) {
      try {
        const parsedCart = JSON.parse(savedCart);
        setCartItems(parsedCart);
      } catch (error) {
        console.error('Error parsing cart from localStorage:', error);
      }
    }

    // Carregar cupom
    const savedCoupon = localStorage.getItem('cart_coupon');
    if (savedCoupon) {
      try {
        const parsedCoupon = JSON.parse(savedCoupon);
        setCoupon(parsedCoupon);
      } catch (error) {
        console.error('Error parsing coupon from localStorage:', error);
      }
    }
  }, []);

  // Save cart to localStorage when it changes
  useEffect(() => {
    localStorage.setItem('cart', JSON.stringify(cartItems));
  }, [cartItems]);

  // Save coupon to localStorage when it changes
  useEffect(() => {
    if (coupon) {
      localStorage.setItem('cart_coupon', JSON.stringify(coupon));
    } else {
      localStorage.removeItem('cart_coupon');
    }
  }, [coupon]);

  // Add item to cart
  const addItem = (item: Omit<CartItem, 'id' | 'quantity'>, quantity = 1) => {
    setCartItems(prev => {
      // Não agrupar itens com variações - cada item é adicionado individualmente
      // Criar um novo item sempre para mostrar cada item separadamente no carrinho
      const newItem = {
        ...item,
        id: Date.now(), // Use timestamp as unique id
        quantity
      };

      toast({
        title: 'Item adicionado',
        description: `${item.name} foi adicionado ao carrinho`,
        duration: 2000,
      });

      return [...prev, newItem];
    });
  };

  // Remove item from cart
  const removeItem = (itemId: number) => {
    setCartItems(prev => {
      const itemToRemove = prev.find(item => item.id === itemId);
      if (itemToRemove) {
        toast({
          title: 'Item removido',
          description: `${itemToRemove.name} foi removido do carrinho`,
          duration: 2000,
        });

      }

      return prev.filter(item => item.id !== itemId);
    });
  };

  // Update item quantity
  const updateQuantity = (itemId: number, quantity: number) => {
    if (quantity < 1) {
      removeItem(itemId);
      return;
    }

    setCartItems(prev => {
      const updatedItems = prev.map(item => {
        if (item.id === itemId) {
          const updatedItem = { ...item, quantity };
          return updatedItem;
        }
        return item;
      });
      return updatedItems;
    });
  };

  // Clear cart
  const clearCart = () => {
    setCartItems([]);
    setCoupon(null); // Limpar também o cupom quando o carrinho for limpo
    toast({
      title: t('storefront.cartCleared'),
      description: t('storefront.allItemsRemoved'),
      duration: 2000,
    });
  };

  // Apply coupon
  const applyCoupon = async (storeSlug: string, code: string): Promise<boolean> => {
    try {
      console.log(`Aplicando cupom: ${code} para loja: ${storeSlug}, subtotal: ${subtotal}`);

      // Validar o cupom no backend
      const url = `/api/public/stores/${storeSlug}/coupons/validate?code=${encodeURIComponent(code)}&subtotal=${subtotal}`;
      console.log('URL da requisição:', url);

      try {
        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Accept': 'application/json'
          }
        });

        console.log('Resposta da API:', response.status, response.statusText);

        if (!response.ok) {
          const errorData = await response.json();
          console.error('Erro na validação do cupom:', errorData);
          toast({
            title: t('storefront.invalidCoupon'),
            description: errorData.message || t('storefront.couponNotValid'),
            variant: "destructive",
          });
          return false;
        }

        const couponData = await response.json();
        console.log('Dados do cupom recebidos:', couponData);
        setCoupon(couponData);

        toast({
          title: t('storefront.couponApplied'),
          description: t('storefront.discountApplied'),
        });

        return true;
      } catch (fetchError) {
        console.error('Erro na requisição fetch:', fetchError);
        throw fetchError;
      }
    } catch (error) {
      console.error('Erro ao aplicar cupom:', error);
      toast({
        title: t('common.error'),
        description: t('storefront.errorApplyingCoupon'),
        variant: "destructive",
      });
      return false;
    }
  };

  // Remove coupon
  const removeCoupon = () => {
    setCoupon(null);
    toast({
      title: t('storefront.couponRemoved'),
      description: t('storefront.discountRemoved'),
    });
  };

  // Calculate total items
  const totalItems = cartItems.reduce((total, item) => total + item.quantity, 0);

  // Calculate subtotal (preço base + variações)
  const subtotal = cartItems.reduce((total, item) => {
    let itemSubtotal = item.price * item.quantity; // Preço base × quantidade

    // Adicionar preço das variações selecionadas
    if (item.selectedVariations && item.selectedVariations.length > 0) {
      const variationsTotal = item.selectedVariations.reduce(
        (varTotal, variation) => varTotal + (variation.price || 0),
        0
      );
      itemSubtotal += variationsTotal * item.quantity;
    }

    return total + itemSubtotal;
  }, 0);

  // Calculate discount
  const discount = coupon ? calculateDiscount(coupon, subtotal) : 0;

  // Calculate total (subtotal - discount)
  const total = Math.max(0, subtotal - discount);

  // Helper function to calculate discount based on coupon type
  function calculateDiscount(coupon: AppliedCoupon, subtotal: number): number {
    // Verificar se o valor mínimo de compra foi atingido
    if (coupon.minimoCompra && subtotal < coupon.minimoCompra) {
      return 0;
    }

    if (coupon.tipo === 'valor_fixo') {
      // Para cupons de valor fixo, o desconto é o valor do cupom
      // Mas não pode ser maior que o subtotal
      return Math.min(coupon.valor, subtotal);
    } else {
      // Para cupons percentuais, o desconto é o percentual do subtotal
      // Usar toFixed(2) para garantir precisão matemática e depois converter de volta para número
      return parseFloat(((coupon.valor / 100) * subtotal).toFixed(2));
    }
  }

  return (
    <CartContext.Provider
      value={{
        items: cartItems,
        addItem,
        removeItem,
        updateQuantity,
        clearCart,
        totalItems,
        subtotal,
        coupon,
        applyCoupon,
        removeCoupon,
        discount,
        total
      }}
    >
      {children}
    </CartContext.Provider>
  );
}

export function useCart() {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
}
