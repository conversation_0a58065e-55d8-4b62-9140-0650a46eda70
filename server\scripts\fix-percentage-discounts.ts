/**
 * Script para corrigir descontos percentuais em revisões existentes
 *
 * Este script:
 * 1. Busca todas as revisões com discount_type='percentage'
 * 2. Calcula o percentual original com base no subtotal e valor do desconto
 * 3. Atualiza o campo original_percentage
 */

import { supabase } from '../db';
import { calculateSubtotalFromItems } from '../utils/discount-helpers';

async function fixPercentageDiscounts() {
  console.log('Iniciando correção de descontos percentuais...');

  try {
    // Buscar revisões com descontos do tipo percentual
    const { data: revisions, error } = await supabase
      .from('order_revisions')
      .select('*')
      .eq('discount_type', 'percentage')
      .is('original_percentage', null); // Apenas aquelas sem o percentual original definido

    if (error) {
      throw error;
    }

    console.log(`Encontradas ${revisions?.length || 0} revisões com desconto percentual sem percentual original`);

    if (!revisions || revisions.length === 0) {
      console.log('Nenhuma revisão para corrigir.');
      return;
    }

    let fixedCount = 0;

    // Para cada revisão, calcular o percentual original
    for (const revision of revisions) {
      try {
        // Buscar os itens da revisão para calcular o subtotal correto
        const { data: items } = await supabase
          .from('order_revision_items')
          .select('*')
          .eq('revision_id', revision.id);

        if (!items || items.length === 0) {
          console.log(`Revisão ${revision.id}: Sem itens, pulando.`);
          continue;
        }

        // Calcular o subtotal real baseado nos itens
        const calculatedSubtotal = calculateSubtotalFromItems(items);

        // Se o subtotal for zero, não é possível calcular o percentual
        if (calculatedSubtotal <= 0) {
          console.log(`Revisão ${revision.id}: Subtotal zero ou negativo, pulando.`);
          continue;
        }

        // Calcular o percentual original (desconto/subtotal * 100)
        const originalPercentage = (revision.discount / calculatedSubtotal) * 100;

        // Arredondar para 2 casas decimais
        const roundedPercentage = parseFloat(originalPercentage.toFixed(2));

        console.log(`Revisão ${revision.id}: Calculado percentual original de ${roundedPercentage}%`);

        // Atualizar a revisão com o percentual original
        const { error: updateError } = await supabase
          .from('order_revisions')
          .update({
            original_percentage: roundedPercentage
          })
          .eq('id', revision.id);

        if (updateError) {
          console.error(`Erro ao atualizar revisão ${revision.id}:`, updateError);
          continue;
        }

        fixedCount++;
        console.log(`Revisão ${revision.id}: Atualizada com sucesso.`);
      } catch (err) {
        console.error(`Erro ao processar revisão ${revision.id}:`, err);
      }
    }

    console.log(`Correção concluída. Atualizadas ${fixedCount} de ${revisions.length} revisões.`);
    return fixedCount;
  } catch (err) {
    console.error('Erro ao corrigir descontos percentuais:', err);
    throw err;
  }
}

// Script disponível apenas para execução manual via endpoint
// Para executar manualmente, use: POST /api/admin/fix-percentage-discounts

export { fixPercentageDiscounts };