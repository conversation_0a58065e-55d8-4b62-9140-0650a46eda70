import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import PaymentForm from './PaymentForm';
import { type CreatePaymentData } from '@/hooks/useOrderPayments';

interface PaymentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: CreatePaymentData) => void;
  isSubmitting?: boolean;
  orderTotal: number;
  totalReceived: number;
  currency?: string;
}

export default function PaymentDialog({
  open,
  onOpenChange,
  onSubmit,
  isSubmitting = false,
  orderTotal,
  totalReceived,
  currency = 'R$'
}: PaymentDialogProps) {
  const handleCancel = () => {
    onOpenChange(false);
  };

  const handleSubmit = (data: CreatePaymentData) => {
    onSubmit(data);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="sr-only">
            Registrar Pagamento
          </DialogTitle>
        </DialogHeader>
        
        <PaymentForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isSubmitting={isSubmitting}
          orderTotal={orderTotal}
          totalReceived={totalReceived}
          currency={currency}
        />
      </DialogContent>
    </Dialog>
  );
}
