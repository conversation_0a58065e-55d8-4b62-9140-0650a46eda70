
import { useEffect, useState } from 'react';
import { useLocation } from 'wouter';
import { useQuery } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ArrowLeft, Instagram, Mail, Phone, Clock, MapPin, CreditCard, Navigation } from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import { openStoreLocation, hasValidAddress } from '@/lib/locationUtils';

interface StoreInfoPageProps {
  slug: string;
}

export default function StoreInfoPage() {
  const [location] = useLocation();
  const slug = location.split('/')[1]; // Extrai o slug da URL
  const { t } = useTranslation();
  const [, navigate] = useLocation();

  // Fetch store data
  const { data: store, isLoading } = useQuery({
    queryKey: [`/api/public/stores/${slug}`],
  });

  const navigateBack = () => {
    navigate(`/${slug}`);
  };

  const openWhatsApp = () => {
    if (store?.whatsapp) {
      const phoneNumber = store.whatsapp.replace(/\D/g, '');
      const message = encodeURIComponent(`Olá! Vim através da sua loja online.`);
      window.open(`https://wa.me/${phoneNumber}?text=${message}`, '_blank');
    }
  };

  const openInstagram = () => {
    if (store?.instagram) {
      const instagramUrl = store.instagram.startsWith('@')
        ? `https://instagram.com/${store.instagram.slice(1)}`
        : `https://instagram.com/${store.instagram}`;
      window.open(instagramUrl, '_blank');
    }
  };

  const openEmail = () => {
    if (store?.contactEmail) {
      window.open(`mailto:${store.contactEmail}`, '_blank');
    }
  };

  const handleOpenLocation = () => {
    if (store && hasValidAddress(store)) {
      openStoreLocation(store);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Carregando informações...</p>
        </div>
      </div>
    );
  }

  if (!store) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-muted-foreground">Loja não encontrada</p>
          <Button onClick={navigateBack} className="mt-4">
            Voltar
          </Button>
        </div>
      </div>
    );
  }

  // CSS Variables for store colors
  const cssColorVariables = {
    '--primary': store.colors?.primary || '#2ECC71',
    '--secondary': store.colors?.secondary || '#3498DB',
    '--accent': store.colors?.accent || '#F39C12',
  } as React.CSSProperties;

  const formatDays = (days: string[]) => {
    const dayNames: { [key: string]: string } = {
      '0': 'Domingo',
      '1': 'Segunda-feira',
      '2': 'Terça-feira',
      '3': 'Quarta-feira',
      '4': 'Quinta-feira',
      '5': 'Sexta-feira',
      '6': 'Sábado'
    };
    return days.map(day => dayNames[day]).join(', ');
  };

  const formatTimeSlots = (slots: string[]) => {
    return slots.join(', ');
  };

  const getPaymentMethodsText = () => {
    const methods = [];
    if (store.paymentMethods?.cash) methods.push('Dinheiro');
    if (store.paymentMethods?.creditCard) methods.push('Cartão de Crédito');
    if (store.paymentMethods?.debitCard) methods.push('Cartão de Débito');
    if (store.paymentMethods?.pix) methods.push('PIX');
    if (store.paymentMethods?.bankTransfer) methods.push('Transferência Bancária');

    if (store.paymentMethods?.customMethods?.length > 0) {
      methods.push(...store.paymentMethods.customMethods);
    }

    return methods.length > 0 ? methods.join(', ') : 'Não informado';
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4" style={cssColorVariables}>
      {/* Header */}
      <div className="max-w-2xl mx-auto">
        {/* Back button */}
        <div className="flex items-center mb-6">
          <Button
            variant="ghost"
            onClick={navigateBack}
            className="mr-2 text-primary hover:text-primary/90 hover:bg-primary/10"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Voltar
          </Button>
        </div>

        {/* Store Header */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex items-center space-x-4">
              {store.logo && (
                <img
                  src={store.logo}
                  alt={store.name}
                  className="w-16 h-16 rounded-full object-cover"
                />
              )}
              <div>
                <h1 className="text-2xl font-bold text-gray-900">{store.name}</h1>
                {store.description && (
                  <p className="text-muted-foreground mt-1">{store.description}</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Contact Information */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              <Phone className="h-5 w-5 mr-2" />
              {t('storeInfo.contact')}
            </h2>
            <div className="space-y-3">
              {store.whatsapp && (
                <Button
                  variant="outline"
                  onClick={openWhatsApp}
                  className="w-full justify-start"
                  style={{ borderColor: 'var(--primary)', color: 'var(--primary)' }}
                >
                  <Phone className="h-4 w-4 mr-2" />
                  WhatsApp: {store.whatsapp}
                </Button>
              )}

              {store.instagram && (
                <Button
                  variant="outline"
                  onClick={openInstagram}
                  className="w-full justify-start"
                  style={{ borderColor: 'var(--primary)', color: 'var(--primary)' }}
                >
                  <Instagram className="h-4 w-4 mr-2" />
                  Instagram: @{store.instagram}
                </Button>
              )}

              {store.contactEmail && (
                <Button
                  variant="outline"
                  onClick={openEmail}
                  className="w-full justify-start"
                  style={{ borderColor: 'var(--primary)', color: 'var(--primary)' }}
                >
                  <Mail className="h-4 w-4 mr-2" />
                  Email: {store.contactEmail}
                </Button>
              )}

              {/* Endereço na seção de contato */}
              {(store.addressStreet || store.addressCity || store.addressNeighborhood) && (
                <div className="border rounded-lg p-3" style={{ borderColor: 'var(--primary)' }}>
                  <div className="flex items-start justify-between">
                    <div className="flex items-start flex-1">
                      <MapPin className="h-4 w-4 mr-2 mt-1 flex-shrink-0" style={{ color: 'var(--primary)' }} />
                      <div className="text-sm flex-1">
                        <p className="font-medium text-gray-900 mb-1">{t('storeInfo.address')}</p>
                        {store.addressStreet && (
                          <p className="text-gray-900">
                            {store.addressStreet}
                            {store.addressNumber && `, ${store.addressNumber}`}
                            {store.addressComplement && `, ${store.addressComplement}`}
                          </p>
                        )}
                        {store.addressNeighborhood && (
                          <p className="text-gray-700">{store.addressNeighborhood}</p>
                        )}
                        {(store.addressCity || store.addressState) && (
                          <p className="text-gray-700">
                            {store.addressCity}
                            {store.addressCity && store.addressState && ' - '}
                            {store.addressState}
                          </p>
                        )}
                      </div>
                    </div>
                    {hasValidAddress(store) && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handleOpenLocation}
                        className="ml-2 flex-shrink-0"
                        style={{ borderColor: 'var(--primary)', color: 'var(--primary)' }}
                      >
                        <Navigation className="h-4 w-4 mr-1" />
                        {t('storeInfo.openLocation')}
                      </Button>
                    )}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Delivery & Pickup Information */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              <Clock className="h-5 w-5 mr-2" />
              Entrega e Retirada
            </h2>
            <div className="space-y-4">
              {/* Antecedência */}
              <div>
                <h3 className="font-medium text-gray-900">Antecedência de Pedidos</h3>
                <p className="text-gray-700">
                  {store.deliverySettings?.minAdvanceDays || 0} dias de antecedência
                </p>
              </div>

              {/* Retirada */}
              {store.deliverySettings?.allowPickup && (
                <div>
                  <h3 className="font-medium text-gray-900">Retirada no Local</h3>
                  <div className="text-gray-700">
                    {store.deliverySettings.pickupDays?.length > 0 && (
                      <p><strong>Dias:</strong> {formatDays(store.deliverySettings.pickupDays)}</p>
                    )}
                    {store.deliverySettings.pickupTimeSlots?.length > 0 && (
                      <p><strong>Horários:</strong> {formatTimeSlots(store.deliverySettings.pickupTimeSlots)}</p>
                    )}
                  </div>
                </div>
              )}

              {/* Entrega */}
              {store.deliverySettings?.allowDelivery && (
                <div>
                  <h3 className="font-medium text-gray-900">Entrega</h3>
                  <div className="text-gray-700">
                    {store.deliverySettings.deliveryFee > 0 && (
                      <p><strong>Taxa:</strong> {store.currency}{store.deliverySettings.deliveryFee.toFixed(2)}</p>
                    )}
                    {store.deliverySettings.deliveryDays?.length > 0 && (
                      <p><strong>Dias:</strong> {formatDays(store.deliverySettings.deliveryDays)}</p>
                    )}
                    {store.deliverySettings.deliveryTimeSlots?.length > 0 && (
                      <p><strong>Horários:</strong> {formatTimeSlots(store.deliverySettings.deliveryTimeSlots)}</p>
                    )}
                  </div>
                </div>
              )}

              {/* Mensagem personalizada */}
              {store.deliverySettings?.customMessage && (
                <div>
                  <h3 className="font-medium text-gray-900">Informações Adicionais</h3>
                  <p className="text-gray-700">{store.deliverySettings.customMessage}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Payment Methods */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <h2 className="text-xl font-semibold mb-4 flex items-center">
              <CreditCard className="h-5 w-5 mr-2" />
              Formas de Pagamento
            </h2>
            <div className="space-y-2">
              {store.paymentMethods?.cash && (
                <div className="flex items-center text-gray-700">
                  <span className="w-2 h-2 bg-primary rounded-full mr-3"></span>
                  Dinheiro
                </div>
              )}
              {store.paymentMethods?.creditCard && (
                <div className="flex items-center text-gray-700">
                  <span className="w-2 h-2 bg-primary rounded-full mr-3"></span>
                  Cartão de Crédito
                </div>
              )}
              {store.paymentMethods?.debitCard && (
                <div className="flex items-center text-gray-700">
                  <span className="w-2 h-2 bg-primary rounded-full mr-3"></span>
                  Cartão de Débito
                </div>
              )}
              {store.paymentMethods?.pix && (
                <div className="flex items-center text-gray-700">
                  <span className="w-2 h-2 bg-primary rounded-full mr-3"></span>
                  PIX
                </div>
              )}
              {store.paymentMethods?.bankTransfer && (
                <div className="flex items-center text-gray-700">
                  <span className="w-2 h-2 bg-primary rounded-full mr-3"></span>
                  Transferência Bancária
                </div>
              )}
              {store.paymentMethods?.customMethods?.map((method, index) => (
                <div key={index} className="flex items-start text-gray-700">
                  <span className="w-2 h-2 bg-primary rounded-full mr-3 mt-2 flex-shrink-0"></span>
                  <span>{method}</span>
                </div>
              ))}
              {(!store.paymentMethods ||
                (!store.paymentMethods.cash &&
                 !store.paymentMethods.creditCard &&
                 !store.paymentMethods.debitCard &&
                 !store.paymentMethods.pix &&
                 !store.paymentMethods.bankTransfer &&
                 (!store.paymentMethods.customMethods || store.paymentMethods.customMethods.length === 0)
                )) && (
                <p className="text-gray-500 italic">Não informado</p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
