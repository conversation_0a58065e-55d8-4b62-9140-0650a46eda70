import { and, desc, eq, isNotNull, lt, gte, lte } from 'drizzle-orm';
import { db } from './db';
import { 
  users, categories, products, customers, orders, orderItems, stores, storeVisits,
  productVariations, variationOptions,
  type User, type InsertUser, type UpsertUser, type Store, type InsertStore, type Category, type InsertCategory,
  type Product, type InsertProduct, type Customer, type InsertCustomer, type Order, type InsertOrder,
  type OrderItem, type InsertOrderItem, type StoreVisit, type InsertStoreVisit,
  type ProductVariation, type InsertProductVariation, type VariationOption, type InsertVariationOption 
} from '@shared/schema';
import { DatabaseStorage } from './storage.db';

export interface IStorage {
  // User methods - updated for Replit Auth
  getUser(id: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  upsertUser(user: UpsertUser): Promise<User>;

  // Store methods
  getStore(id: number): Promise<Store | undefined>;
  getStoreBySlug(slug: string): Promise<Store | undefined>;
  getStoreByUserId(userId: string): Promise<Store | undefined>;
  createStore(store: InsertStore): Promise<Store>;
  updateStore(id: number, store: Partial<Store>): Promise<Store | undefined>;

  // Product methods
  getProduct(id: number): Promise<Product | undefined>;
  getProductsByStoreId(storeId: number): Promise<Product[]>;
  getProductsByCategoryId(categoryId: number): Promise<Product[]>;
  createProduct(product: InsertProduct): Promise<Product>;
  updateProduct(id: number, product: Partial<Product>): Promise<Product | undefined>;
  deleteProduct(id: number): Promise<boolean>;

  // Product Variation methods
  getProductVariationsByProductId(productId: number): Promise<ProductVariation[]>;
  createProductVariation(variation: InsertProductVariation): Promise<ProductVariation>;
  updateProductVariation(id: number, variation: Partial<ProductVariation>): Promise<ProductVariation | undefined>;
  deleteProductVariation(id: number): Promise<boolean>;
  
  // Variation Option methods
  getVariationOptionsByVariationId(variationId: number): Promise<VariationOption[]>;
  createVariationOption(option: InsertVariationOption): Promise<VariationOption>;
  updateVariationOption(id: number, option: Partial<VariationOption>): Promise<VariationOption | undefined>;
  deleteVariationOption(id: number): Promise<boolean>;

  // Category methods
  getCategory(id: number): Promise<Category | undefined>;
  getCategoriesByStoreId(storeId: number): Promise<Category[]>;
  createCategory(category: InsertCategory): Promise<Category>;
  updateCategory(id: number, category: Partial<Category>): Promise<Category | undefined>;
  deleteCategory(id: number): Promise<boolean>;

  // Customer methods
  getCustomer(id: number): Promise<Customer | undefined>;
  getCustomersByStoreId(storeId: number): Promise<Customer[]>;
  createCustomer(customer: InsertCustomer): Promise<Customer>;

  // Order methods
  getOrder(id: number): Promise<Order | undefined>;
  getOrdersByStoreId(storeId: number): Promise<Order[]>;
  getOrdersByCustomerId(customerId: number): Promise<Order[]>;
  createOrder(order: InsertOrder): Promise<Order>;
  updateOrderStatus(id: number, status: string): Promise<Order | undefined>;

  // Order item methods
  getOrderItemsByOrderId(orderId: number): Promise<OrderItem[]>;
  createOrderItem(orderItem: InsertOrderItem): Promise<OrderItem>;

  // Analytics methods
  recordStoreVisit(storeVisit: InsertStoreVisit): Promise<StoreVisit>;
  getStoreVisitsByStoreId(storeId: number, startDate?: Date, endDate?: Date): Promise<StoreVisit[]>;
  getMonthlyVisitCount(storeId: number): Promise<number>;
  getMonthlyOrderCount(storeId: number): Promise<number>;
}

// Use DatabaseStorage for production
export const storage = new DatabaseStorage();