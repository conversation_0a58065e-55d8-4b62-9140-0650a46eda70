import React, { useState } from 'react';
import { useLocation } from 'wouter';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { apiRequest } from '@/lib/queryClient';
import { toast } from '@/hooks/use-toast';

export default function TestIOSLayout() {
  const [, navigate] = useLocation();
  const [isLoading, setIsLoading] = useState(false);
  const [layout, setLayout] = useState<string>('4');
  const [storeName, setStoreName] = useState<string>('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Fetch the store first
      const storeResponse = await fetch(`/api/stores/me`);
      if (!storeResponse.ok) {
        throw new Error('Could not fetch store');
      }
      
      const storeData = await storeResponse.json();
      setStoreName(storeData.name || 'Your store');
      
      // Update the store with the new layout
      await apiRequest(`/api/stores/${storeData.id}`, 'PATCH', {
        layout: parseInt(layout)
      });

      toast({
        title: 'Layout atualizado',
        description: `O layout foi alterado para ${layout}`,
      });
      
      // Navigate to store page
      if (storeData.slug) {
        navigate(`/${storeData.slug}`);
      }
      
    } catch (error) {
      console.error('Error updating layout:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update layout',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">Teste do Layout Estilo iOS</h1>
      
      <div className="max-w-md mx-auto bg-white p-6 rounded-lg shadow-md">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <label className="text-sm font-medium">Selecione o layout da loja</label>
            <Select
              value={layout}
              onValueChange={(value) => setLayout(value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Selecione um layout" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1">Layout 1: Grade tradicional</SelectItem>
                <SelectItem value="2">Layout 2: Lista vertical</SelectItem>
                <SelectItem value="3">Layout 3: Seções de categorias</SelectItem>
                <SelectItem value="4">Layout 4: Estilo iOS</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <Button 
            type="submit" 
            className="w-full" 
            disabled={isLoading}
          >
            {isLoading ? 'Atualizando layout...' : 'Aplicar layout e abrir loja'}
          </Button>
        </form>
      </div>
      
      {storeName && (
        <p className="text-center mt-4 text-muted-foreground">
          Loja: {storeName}
        </p>
      )}
    </div>
  );
}
