-- <PERSON><PERSON><PERSON> para corrigir a estrutura da tabela store_visits
-- <PERSON><PERSON> script resolve o problema do erro "column store_visits.created_at does not exist"

-- <PERSON><PERSON>, vamos verificar se a tabela existe
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'store_visits') THEN
        -- 1. Renomear a coluna visit_date para created_at se ela existir
        IF EXISTS (SELECT FROM information_schema.columns 
                  WHERE table_name = 'store_visits' AND column_name = 'visit_date') 
           AND NOT EXISTS (SELECT FROM information_schema.columns 
                          WHERE table_name = 'store_visits' AND column_name = 'created_at') THEN
            ALTER TABLE store_visits RENAME COLUMN visit_date TO created_at;
            RAISE NOTICE 'Coluna visit_date renomeada para created_at';
        END IF;

        -- 2. Adicionar a coluna created_at se não existir
        IF NOT EXISTS (SELECT FROM information_schema.columns 
                      WHERE table_name = 'store_visits' AND column_name = 'created_at') THEN
            ALTER TABLE store_visits ADD COLUMN created_at TIMESTAMP NOT NULL DEFAULT NOW();
            RAISE NOTICE 'Coluna created_at adicionada';
        END IF;

        -- 3. Garantir que todas as outras colunas necessárias existam
        -- Adicionar session_id se não existir
        IF NOT EXISTS (SELECT FROM information_schema.columns 
                      WHERE table_name = 'store_visits' AND column_name = 'session_id') THEN
            ALTER TABLE store_visits ADD COLUMN session_id VARCHAR(255);
            RAISE NOTICE 'Coluna session_id adicionada';
        END IF;

        -- Adicionar user_id se não existir
        IF NOT EXISTS (SELECT FROM information_schema.columns 
                      WHERE table_name = 'store_visits' AND column_name = 'user_id') THEN
            ALTER TABLE store_visits ADD COLUMN user_id INTEGER REFERENCES users(id);
            RAISE NOTICE 'Coluna user_id adicionada';
        END IF;

        -- Adicionar page se não existir
        IF NOT EXISTS (SELECT FROM information_schema.columns 
                      WHERE table_name = 'store_visits' AND column_name = 'page') THEN
            ALTER TABLE store_visits ADD COLUMN page VARCHAR(255);
            RAISE NOTICE 'Coluna page adicionada';
        END IF;

        -- Adicionar user_agent se não existir
        IF NOT EXISTS (SELECT FROM information_schema.columns 
                      WHERE table_name = 'store_visits' AND column_name = 'user_agent') THEN
            ALTER TABLE store_visits ADD COLUMN user_agent TEXT;
            RAISE NOTICE 'Coluna user_agent adicionada';
        END IF;

        -- Adicionar referrer se não existir
        IF NOT EXISTS (SELECT FROM information_schema.columns 
                      WHERE table_name = 'store_visits' AND column_name = 'referrer') THEN
            ALTER TABLE store_visits ADD COLUMN referrer TEXT;
            RAISE NOTICE 'Coluna referrer adicionada';
        END IF;

        -- 4. Garantir que a coluna visitor_ip seja do tipo correto
        -- Primeiro verificamos o tipo atual
        IF EXISTS (SELECT FROM information_schema.columns 
                  WHERE table_name = 'store_visits' AND column_name = 'visitor_ip' 
                  AND data_type = 'character varying') THEN
            -- Já está no formato correto, não precisa fazer nada
            RAISE NOTICE 'Coluna visitor_ip já está no formato correto';
        ELSIF EXISTS (SELECT FROM information_schema.columns 
                     WHERE table_name = 'store_visits' AND column_name = 'visitor_ip') THEN
            -- Alterar o tipo para VARCHAR(50)
            ALTER TABLE store_visits ALTER COLUMN visitor_ip TYPE VARCHAR(50);
            RAISE NOTICE 'Tipo da coluna visitor_ip alterado para VARCHAR(50)';
        ELSE
            -- Adicionar a coluna se não existir
            ALTER TABLE store_visits ADD COLUMN visitor_ip VARCHAR(50);
            RAISE NOTICE 'Coluna visitor_ip adicionada';
        END IF;

        -- 5. Adicionar índices para melhorar a performance
        -- Verificar se o índice já existe antes de criar
        IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_store_visits_store_id') THEN
            CREATE INDEX idx_store_visits_store_id ON store_visits(store_id);
            RAISE NOTICE 'Índice idx_store_visits_store_id criado';
        END IF;

        IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_store_visits_created_at') THEN
            CREATE INDEX idx_store_visits_created_at ON store_visits(created_at);
            RAISE NOTICE 'Índice idx_store_visits_created_at criado';
        END IF;

        -- 6. Adicionar comentários para documentação
        COMMENT ON TABLE store_visits IS 'Armazena informações sobre visitas às lojas para análise';
        COMMENT ON COLUMN store_visits.id IS 'Identificador único da visita';
        COMMENT ON COLUMN store_visits.store_id IS 'ID da loja visitada';
        COMMENT ON COLUMN store_visits.visitor_ip IS 'Endereço IP do visitante';
        COMMENT ON COLUMN store_visits.created_at IS 'Data e hora da visita';
        COMMENT ON COLUMN store_visits.session_id IS 'ID da sessão do visitante';
        COMMENT ON COLUMN store_visits.user_id IS 'ID do usuário se estiver logado';
        COMMENT ON COLUMN store_visits.page IS 'Página visitada';
        COMMENT ON COLUMN store_visits.user_agent IS 'User agent do navegador do visitante';
        COMMENT ON COLUMN store_visits.referrer IS 'URL de referência de onde o visitante veio';

        RAISE NOTICE 'Tabela store_visits atualizada com sucesso!';
    ELSE
        -- Criar a tabela se não existir
        CREATE TABLE store_visits (
            id SERIAL PRIMARY KEY,
            store_id INTEGER NOT NULL REFERENCES stores(id) ON DELETE CASCADE,
            visitor_ip VARCHAR(50),
            created_at TIMESTAMP NOT NULL DEFAULT NOW(),
            session_id VARCHAR(255),
            user_id INTEGER REFERENCES users(id),
            page VARCHAR(255),
            user_agent TEXT,
            referrer TEXT
        );

        -- Criar índices
        CREATE INDEX idx_store_visits_store_id ON store_visits(store_id);
        CREATE INDEX idx_store_visits_created_at ON store_visits(created_at);

        -- Adicionar comentários
        COMMENT ON TABLE store_visits IS 'Armazena informações sobre visitas às lojas para análise';
        COMMENT ON COLUMN store_visits.id IS 'Identificador único da visita';
        COMMENT ON COLUMN store_visits.store_id IS 'ID da loja visitada';
        COMMENT ON COLUMN store_visits.visitor_ip IS 'Endereço IP do visitante';
        COMMENT ON COLUMN store_visits.created_at IS 'Data e hora da visita';
        COMMENT ON COLUMN store_visits.session_id IS 'ID da sessão do visitante';
        COMMENT ON COLUMN store_visits.user_id IS 'ID do usuário se estiver logado';
        COMMENT ON COLUMN store_visits.page IS 'Página visitada';
        COMMENT ON COLUMN store_visits.user_agent IS 'User agent do navegador do visitante';
        COMMENT ON COLUMN store_visits.referrer IS 'URL de referência de onde o visitante veio';

        RAISE NOTICE 'Tabela store_visits criada com sucesso!';
    END IF;
END
$$;
