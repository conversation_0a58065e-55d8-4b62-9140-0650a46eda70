import { ReactNode } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { TrendingUp, TrendingDown } from 'lucide-react';

interface MetricCardProps {
  title: string;
  value: string | number;
  icon: ReactNode;
  isLoading?: boolean;
  trend?: {
    value: number;
    label: string;
  };
  description?: string;
  className?: string;
}

export default function MetricCard({
  title,
  value,
  icon,
  isLoading = false,
  trend,
  description,
  className = ''
}: MetricCardProps) {
  const formatTrend = (trendValue: number) => {
    return `${trendValue >= 0 ? '+' : ''}${trendValue.toFixed(1)}%`;
  };

  return (
    <Card className={`hover:shadow-md transition-shadow ${className}`}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {title}
        </CardTitle>
        <div className="text-muted-foreground">
          {icon}
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-2">
            <Skeleton className="h-8 w-20" />
            {(trend || description) && <Skeleton className="h-4 w-24" />}
          </div>
        ) : (
          <div className="space-y-1">
            <div className="text-2xl font-bold text-foreground">
              {typeof value === 'number' ? value.toLocaleString() : value}
            </div>
            
            {trend && (
              <div className="flex items-center text-xs">
                {trend.value > 0 ? (
                  <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                ) : trend.value < 0 ? (
                  <TrendingDown className="h-3 w-3 text-red-500 mr-1" />
                ) : null}
                <span className={
                  trend.value > 0 
                    ? 'text-green-600' 
                    : trend.value < 0 
                      ? 'text-red-600' 
                      : 'text-muted-foreground'
                }>
                  {formatTrend(trend.value)}
                </span>
                <span className="text-muted-foreground ml-1">{trend.label}</span>
              </div>
            )}
            
            {description && !trend && (
              <p className="text-xs text-muted-foreground">{description}</p>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
