import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { formatCurrency } from './utils';

/**
 * Gera um PDF a partir de um elemento HTML
 * @param element Elemento HTML a ser convertido em PDF
 * @param fileName Nome do arquivo PDF
 */
export const generatePdfFromElement = async (element: HTMLElement, fileName: string): Promise<void> => {
  try {
    console.log('[PDF] Iniciando geração de PDF...');

    // Salvar estilos originais do elemento
    const originalStyles = {
      width: element.style.width,
      height: element.style.height,
      transform: element.style.transform,
      position: element.style.position,
      left: element.style.left,
      top: element.style.top,
      zIndex: element.style.zIndex,
      visibility: element.style.visibility
    };

    // Aplicar estilos temporários para garantir formato A4 consistente
    element.style.width = '794px'; // A4 width em pixels (210mm * 3.78 pixels/mm)
    element.style.height = 'auto';
    element.style.transform = 'scale(1)';
    element.style.position = 'relative';
    element.style.left = '0';
    element.style.top = '0';
    element.style.zIndex = '9999';
    element.style.visibility = 'visible';

    // Adicionar classe temporária para estilos específicos de PDF
    element.classList.add('pdf-generation');

    // Aguardar um frame para garantir que os estilos sejam aplicados
    await new Promise(resolve => requestAnimationFrame(resolve));

    // Pré-carregar imagens para garantir que estejam disponíveis
    const images = Array.from(element.querySelectorAll('img'));
    console.log(`[PDF] Pré-carregando ${images.length} imagens...`);

    await Promise.all(
      images.map(img => {
        return new Promise((resolve) => {
          if (img.complete) {
            resolve(true);
          } else {
            img.onload = () => resolve(true);
            img.onerror = () => {
              console.error('Erro ao carregar imagem:', img.src);
              resolve(false);
            };
          }
        });
      })
    );

    console.log('[PDF] Capturando elemento com html2canvas...');

    // Configurações otimizadas do canvas para diferentes dispositivos
    const devicePixelRatio = window.devicePixelRatio || 1;
    const scale = Math.min(2, Math.max(1, devicePixelRatio)); // Escala entre 1 e 2

    const canvas = await html2canvas(element, {
      scale: scale,
      useCORS: true,
      allowTaint: true,
      logging: false,
      backgroundColor: '#ffffff',
      imageTimeout: 15000,
      width: 794, // Forçar largura A4 em pixels
      height: element.scrollHeight, // Altura dinâmica baseada no conteúdo
      windowWidth: 794, // Largura da janela virtual
      windowHeight: element.scrollHeight,
      scrollX: 0,
      scrollY: 0,
      foreignObjectRendering: false, // Desabilitar para melhor compatibilidade
      removeContainer: true
    });

    console.log(`[PDF] Canvas capturado: ${canvas.width}x${canvas.height}`);

    // Configurações do PDF - sempre A4 com fundo branco
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4',
      compress: true
    });

    // Dimensões A4 em mm
    const a4Width = 210;
    const a4Height = 297;

    // Usar margens mínimas para maximizar a área de conteúdo
    const margin = 5; // Margem reduzida para 5mm
    const contentWidth = a4Width - (margin * 2);
    const contentHeight = a4Height - (margin * 2);

    console.log(`[PDF] Dimensões A4: ${a4Width}x${a4Height}mm, Área de conteúdo: ${contentWidth}x${contentHeight}mm`);

    // Garantir fundo branco em toda a página
    pdf.setFillColor(255, 255, 255); // Branco
    pdf.rect(0, 0, a4Width, a4Height, 'F'); // Preencher toda a página

    // Calcular dimensões da imagem para cobrir toda a área disponível
    const imgAspectRatio = canvas.height / canvas.width;

    // Usar toda a largura disponível
    let imgWidth = contentWidth;
    let imgHeight = contentWidth * imgAspectRatio;

    // Se a altura calculada for menor que a área disponível, ajustar para usar toda a altura
    if (imgHeight < contentHeight) {
      imgHeight = contentHeight;
      imgWidth = contentHeight / imgAspectRatio;

      // Se a largura exceder a área disponível, voltar ao cálculo original
      if (imgWidth > contentWidth) {
        imgWidth = contentWidth;
        imgHeight = contentWidth * imgAspectRatio;
      }
    }

    // Centralizar o conteúdo
    const xOffset = margin + (contentWidth - imgWidth) / 2;
    const yOffset = margin;

    console.log(`[PDF] Dimensões da imagem no PDF: ${imgWidth}x${imgHeight}mm, Posição: ${xOffset}x${yOffset}mm`);

    console.log(`[PDF] Adicionando imagem ao PDF: ${imgWidth}mm x ${imgHeight}mm`);

    // Verificar se precisa de múltiplas páginas
    const totalPages = Math.ceil(imgHeight / contentHeight);

    console.log(`[PDF] Total de páginas necessárias: ${totalPages}`);

    for (let page = 0; page < totalPages; page++) {
      if (page > 0) {
        pdf.addPage();
        // Garantir fundo branco em cada nova página
        pdf.setFillColor(255, 255, 255);
        pdf.rect(0, 0, a4Width, a4Height, 'F');
      }

      const sourceY = page * contentHeight * (canvas.height / imgHeight);
      const sourceHeight = Math.min(contentHeight * (canvas.height / imgHeight), canvas.height - sourceY);
      const destHeight = Math.min(contentHeight, imgHeight - (page * contentHeight));

      console.log(`[PDF] Página ${page + 1}: sourceY=${sourceY}, sourceHeight=${sourceHeight}, destHeight=${destHeight}`);

      // Criar canvas temporário para esta página
      const pageCanvas = document.createElement('canvas');
      const pageCtx = pageCanvas.getContext('2d');

      pageCanvas.width = canvas.width;
      pageCanvas.height = sourceHeight;

      if (pageCtx) {
        // Garantir fundo branco no canvas da página
        pageCtx.fillStyle = '#ffffff';
        pageCtx.fillRect(0, 0, pageCanvas.width, pageCanvas.height);

        // Desenhar o conteúdo da página
        pageCtx.drawImage(canvas, 0, sourceY, canvas.width, sourceHeight, 0, 0, canvas.width, sourceHeight);

        const pageImgData = pageCanvas.toDataURL('image/png', 1.0); // Qualidade máxima

        // Adicionar a imagem cobrindo toda a área de conteúdo
        pdf.addImage(pageImgData, 'PNG', xOffset, yOffset, imgWidth, destHeight);

        console.log(`[PDF] Página ${page + 1} adicionada com sucesso`);
      }
    }

    console.log('[PDF] PDF gerado com sucesso');

    // Restaurar estilos originais
    Object.keys(originalStyles).forEach(key => {
      element.style[key as any] = originalStyles[key as keyof typeof originalStyles];
    });

    // Remover classe temporária
    element.classList.remove('pdf-generation');

    // Salvar o PDF
    pdf.save(fileName);
  } catch (error) {
    console.error('Erro ao gerar PDF:', error);

    // Garantir que os estilos sejam restaurados mesmo em caso de erro
    element.classList.remove('pdf-generation');

    throw error;
  }
};

/**
 * Gera um PDF de pedido diretamente a partir dos dados
 * @param orderData Dados do pedido
 * @param fileName Nome do arquivo PDF
 * @param storeName Nome da loja
 * @param currency Símbolo da moeda
 * @param revisionData Dados da revisão (opcional)
 */
export const generateOrderPdf = (
  orderData: any,
  fileName: string,
  storeName: string,
  currency: string,
  revisionData?: any
): void => {
  // Usar dados da revisão se disponíveis, caso contrário usar dados do pedido original
  const data = revisionData || orderData;

  // Criar um novo documento PDF
  const pdf = new jsPDF({
    orientation: 'portrait',
    unit: 'mm',
    format: 'a4'
  });

  // Configurações de fonte e margens
  const margin = 15;
  const pageWidth = 210 - (margin * 2); // A4 width - margins
  let y = margin;

  // Cores da loja (usar valores padrão se não estiverem disponíveis)
  const storeColors = orderData.store?.colors || {
    primary: "#6082e6",
    secondary: "#577590",
    accent: "#F4A261"
  };

  // Converter cores hex para RGB para uso no PDF
  const primaryColor = hexToRgb(storeColors.primary);
  const secondaryColor = hexToRgb(storeColors.secondary);
  const accentColor = hexToRgb(storeColors.accent);

  // ===== CABEÇALHO =====

  // Adicionar retângulo colorido no topo
  pdf.setFillColor(primaryColor.r, primaryColor.g, primaryColor.b);
  pdf.rect(0, 0, 210, 30, 'F');

  // Verificar se a loja tem logo
  const hasLogo = orderData.store?.logo;

  if (hasLogo) {
    // Adicionar logo da loja no topo esquerdo
    try {
      // Adicionar logo como imagem
      const logoX = 15;
      const logoY = 5;
      const logoWidth = 20;
      const logoHeight = 20;

      // Carregar e adicionar logo como imagem
      const img = new Image();
      img.crossOrigin = 'Anonymous';  // Permitir carregamento cross-origin

      console.log('[PDF] Tentando carregar logo da loja:', orderData.store.logo);
      console.log('[PDF] Tipo da URL da logo:', typeof orderData.store.logo);
      console.log('[PDF] Comprimento da URL da logo:', orderData.store.logo?.length || 0);
      console.log('[PDF] A URL começa com data:image?', orderData.store.logo?.startsWith('data:image') || false);
      console.log('[PDF] A URL começa com http?', orderData.store.logo?.startsWith('http') || false);
      console.log('[PDF] Dimensões planejadas - X:', logoX, 'Y:', logoY, 'Largura:', logoWidth, 'Altura:', logoHeight);

      // Remover await - usar Promise diretamente
      new Promise((resolve, reject) => {
        img.onload = () => {
          console.log('[PDF] Logo carregada com sucesso. Dimensões originais:', img.width, 'x', img.height);
          console.log('[PDF] Propriedades da imagem - naturalWidth:', img.naturalWidth, 'naturalHeight:', img.naturalHeight);
          console.log('[PDF] Imagem completa?', img.complete);

          try {
            // Criar canvas para processar a imagem
            console.log('[PDF] Criando canvas para processar a imagem...');
            const canvas = document.createElement('canvas');
            canvas.width = img.width;
            canvas.height = img.height;
            console.log('[PDF] Canvas criado com dimensões:', canvas.width, 'x', canvas.height);

            // Obter contexto 2D
            console.log('[PDF] Obtendo contexto 2D do canvas...');
            const ctx = canvas.getContext('2d');
            if (!ctx) {
              console.error('[PDF] Erro: Não foi possível obter contexto 2D do canvas');
              reject(new Error('Contexto 2D não disponível'));
              return;
            }
            console.log('[PDF] Contexto 2D obtido com sucesso');

            // Desenhar imagem no canvas
            console.log('[PDF] Desenhando imagem no canvas...');
            try {
              ctx.drawImage(img, 0, 0);
              console.log('[PDF] Imagem desenhada no canvas com sucesso');
            } catch (drawError) {
              console.error('[PDF] Erro ao desenhar imagem no canvas:', drawError);
              console.log('[PDF] Estado da imagem no momento do erro - completa:', img.complete, 'largura:', img.width, 'altura:', img.height);
              reject(drawError);
              return;
            }

            // Converter canvas para Data URL
            console.log('[PDF] Convertendo canvas para Data URL...');
            try {
              const imgData = canvas.toDataURL('image/jpeg');
              const imgDataSize = Math.round(imgData.length / 1024);
              console.log('[PDF] Imagem convertida para Data URL com sucesso');
              console.log('[PDF] Tamanho da Data URL:', imgDataSize, 'KB');
              console.log('[PDF] Primeiros 100 caracteres da Data URL:', imgData.substring(0, 100) + '...');

              // Adicionar imagem ao PDF
              console.log('[PDF] Adicionando imagem ao PDF...');
              console.log('[PDF] Parâmetros para addImage - formato: JPEG, X:', logoX, 'Y:', logoY, 'Largura:', logoWidth, 'Altura:', logoHeight);

              try {
                pdf.addImage(imgData, 'JPEG', logoX, logoY, logoWidth, logoHeight);
                console.log('[PDF] Imagem adicionada ao PDF com sucesso');
                resolve(true);
              } catch (addImageError) {
                console.error('[PDF] Erro ao adicionar imagem ao PDF:', addImageError);
                console.log('[PDF] Detalhes do erro:', addImageError.message || 'Sem mensagem de erro');
                console.log('[PDF] Stack trace:', addImageError.stack || 'Sem stack trace');

                // Tentar com outro formato
                try {
                  console.log('[PDF] Tentando adicionar como PNG em vez de JPEG...');
                  const pngData = canvas.toDataURL('image/png');
                  pdf.addImage(pngData, 'PNG', logoX, logoY, logoWidth, logoHeight);
                  console.log('[PDF] Imagem adicionada como PNG com sucesso');
                  resolve(true);
                } catch (pngError) {
                  console.error('[PDF] Também falhou como PNG:', pngError);
                  reject(addImageError);
                }
              }
            } catch (dataUrlError) {
              console.error('[PDF] Erro ao converter canvas para Data URL:', dataUrlError);
              console.log('[PDF] Detalhes do erro:', dataUrlError.message || 'Sem mensagem de erro');
              console.log('[PDF] Stack trace:', dataUrlError.stack || 'Sem stack trace');
              console.log('[PDF] Possível erro CORS ou tainted canvas');
              console.log('[PDF] Possível erro CORS ou tainted canvas');
              reject(dataUrlError);
            }
          } catch (error) {
            console.error('[PDF] Erro ao processar logo:', error);
            console.log('[PDF] Detalhes do erro:', error.message || 'Sem mensagem de erro');
            console.log('[PDF] Stack trace:', error.stack || 'Sem stack trace');
            reject(error);
          }
        };

        img.onerror = (error) => {
          console.error('[PDF] Erro ao carregar logo:', error);
          console.log('[PDF] URL da imagem:', orderData.store.logo);
          console.log('[PDF] Tipo do erro:', typeof error);
          console.log('[PDF] Mensagem de erro:', error instanceof Error ? error.message : 'Erro desconhecido');
          console.log('[PDF] Verificando se a URL é válida e acessível...');

          // Tentar fazer uma solicitação fetch para verificar a URL
          fetch(orderData.store.logo, { method: 'HEAD' })
            .then(response => {
              console.log('[PDF] Resposta da verificação da URL:', response.status, response.statusText);
              console.log('[PDF] Headers da resposta:', JSON.stringify([...response.headers]));
              console.log('[PDF] Content-Type:', response.headers.get('content-type'));
            })
            .catch(fetchError => {
              console.error('[PDF] Erro ao verificar URL com fetch:', fetchError);
            })
            .finally(() => {
              reject(error);
            });
        };

        // Adicionar timeout para evitar espera infinita
        setTimeout(() => {
          if (!img.complete) {
            console.error('[PDF] Timeout ao carregar a imagem após 5 segundos');
            console.log('[PDF] Estado da imagem no timeout - src definido:', !!img.src, 'naturalWidth:', img.naturalWidth);
            reject(new Error('Timeout ao carregar imagem'));
          }
        }, 5000);

        // Definir src para iniciar o carregamento
        console.log('[PDF] Definindo src da imagem para iniciar carregamento...');
        img.src = orderData.store.logo;
        console.log('[PDF] Solicitação de carregamento da imagem iniciada');

        // Verificar se a imagem já está em cache
        if (img.complete) {
          console.log('[PDF] Imagem já está em cache ou carregada instantaneamente');
          console.log('[PDF] Dimensões da imagem em cache:', img.width, 'x', img.height);
        }
      });

      // Adicionar nome da loja ao lado do logo
      pdf.setTextColor(255, 255, 255); // Texto branco
      pdf.setFontSize(18);
      pdf.setFont('helvetica', 'bold');
      pdf.text(storeName, logoX + logoWidth + 5, 15);

      // Adicionar número do pedido abaixo do nome da loja
      pdf.setFontSize(14);
      const orderNumberText = `Pedido #${orderData.id}`;
      pdf.text(orderNumberText, logoX + logoWidth + 5, 25);
    } catch (e) {
      console.error('Erro ao adicionar logo:', e);

      // Fallback para texto centralizado se houver erro com a logo
      pdf.setTextColor(255, 255, 255); // Texto branco
      pdf.setFontSize(22);
      pdf.setFont('helvetica', 'bold');
      const storeNameWidth = pdf.getStringUnitWidth(storeName) * 22 / pdf.internal.scaleFactor;
      const storeNameX = (210 - storeNameWidth) / 2;
      pdf.text(storeName, storeNameX, 15);

      // Adicionar número do pedido centralizado abaixo do nome da loja
      pdf.setFontSize(16);
      const orderNumberText = `Pedido #${orderData.id}`;
      const orderNumberWidth = pdf.getStringUnitWidth(orderNumberText) * 16 / pdf.internal.scaleFactor;
      const orderNumberX = (210 - orderNumberWidth) / 2;
      pdf.text(orderNumberText, orderNumberX, 25);
    }
  } else {
    // Adicionar nome da loja centralizado no topo (sem logo)
    pdf.setTextColor(255, 255, 255); // Texto branco
    pdf.setFontSize(22);
    pdf.setFont('helvetica', 'bold');
    const storeNameWidth = pdf.getStringUnitWidth(storeName) * 22 / pdf.internal.scaleFactor;
    const storeNameX = (210 - storeNameWidth) / 2;
    pdf.text(storeName, storeNameX, 15);

    // Adicionar número do pedido centralizado abaixo do nome da loja
    pdf.setFontSize(16);
    const orderNumberText = `Pedido #${orderData.id}`;
    const orderNumberWidth = pdf.getStringUnitWidth(orderNumberText) * 16 / pdf.internal.scaleFactor;
    const orderNumberX = (210 - orderNumberWidth) / 2;
    pdf.text(orderNumberText, orderNumberX, 25);
  }

  // Resetar posição Y após o cabeçalho
  y = 40;

  // Resetar cor do texto para preto
  pdf.setTextColor(0, 0, 0);

  // ===== INFORMAÇÕES DO PEDIDO =====

  // Adicionar caixa de informações do pedido
  pdf.setFillColor(245, 245, 245); // Cinza claro
  pdf.roundedRect(margin, y, pageWidth, 30, 3, 3, 'F');

  pdf.setFontSize(10);
  pdf.setFont('helvetica', 'normal');

  // Data do pedido
  const orderDate = new Date(orderData.createdAt).toLocaleDateString('pt-BR');
  pdf.text(`Data: ${orderDate}`, margin + 5, y + 8);

  // Status do pedido
  pdf.setFont('helvetica', 'bold');
  pdf.text(`Status: ${data.status || 'Pendente'}`, margin + 5, y + 16);
  pdf.setFont('helvetica', 'normal');

  // Se for uma revisão, adicionar informação da revisão
  if (revisionData) {
    const revisionDate = new Date(revisionData.createdAt).toLocaleDateString('pt-BR');
    pdf.text(`Revisão #${revisionData.revisionNumber || '1'} (${revisionDate})`, margin + 5, y + 24);
  }

  // Método de pagamento (lado direito)
  const paymentMethod = data.paymentMethodName || data.paymentMethod || 'Não especificado';
  const paymentText = `Pagamento: ${paymentMethod}`;
  const paymentWidth = pdf.getStringUnitWidth(paymentText) * 10 / pdf.internal.scaleFactor;
  pdf.text(paymentText, 210 - margin - 5 - paymentWidth, y + 8);

  // Método de recebimento (lado direito)
  const receivingMethod = data.receivingMethod === 'delivery' ? 'Entrega' : 'Retirada';
  const receivingText = `Método: ${receivingMethod}`;
  const receivingWidth = pdf.getStringUnitWidth(receivingText) * 10 / pdf.internal.scaleFactor;
  pdf.text(receivingText, 210 - margin - 5 - receivingWidth, y + 16);

  // Data de recebimento (lado direito)
  if (data.receivingDate) {
    try {
      const receivingDate = new Date(data.receivingDate).toLocaleDateString('pt-BR');
      const timeText = data.receivingTime ? ` às ${data.receivingTime}` : '';
      const dateText = `Data de ${receivingMethod}: ${receivingDate}${timeText}`;
      const dateWidth = pdf.getStringUnitWidth(dateText) * 10 / pdf.internal.scaleFactor;
      pdf.text(dateText, 210 - margin - 5 - dateWidth, y + 24);
    } catch (e) {
      console.error('Erro ao formatar data de recebimento:', e);
    }
  }

  y += 35;

  // ===== INFORMAÇÕES DO CLIENTE =====

  if (data.customer) {
    pdf.setFillColor(secondaryColor.r, secondaryColor.g, secondaryColor.b, 0.1); // Cor secundária com transparência
    pdf.roundedRect(margin, y, pageWidth / 2 - 5, 35, 3, 3, 'F');

    pdf.setFont('helvetica', 'bold');
    pdf.setFontSize(12);
    pdf.text('INFORMAÇÕES DO CLIENTE', margin + 5, y + 8);
    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(10);

    pdf.text(`Nome: ${data.customer.name || 'Não informado'}`, margin + 5, y + 16);

    if (data.customer.email) {
      pdf.text(`Email: ${data.customer.email}`, margin + 5, y + 24);
    }

    if (data.customer.phone) {
      pdf.text(`Telefone: ${data.customer.phone}`, margin + 5, y + 32);
    }
  }

  // ===== ENDEREÇO DE ENTREGA =====

  if (data.receivingMethod === 'delivery' && data.deliveryAddress) {
    pdf.setFillColor(secondaryColor.r, secondaryColor.g, secondaryColor.b, 0.1); // Cor secundária com transparência
    pdf.roundedRect(margin + pageWidth / 2 + 5, y, pageWidth / 2 - 5, 35, 3, 3, 'F');

    pdf.setFont('helvetica', 'bold');
    pdf.setFontSize(12);
    pdf.text('ENDEREÇO DE ENTREGA', margin + pageWidth / 2 + 10, y + 8);
    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(10);

    const address = data.deliveryAddress;
    let addressText = '';

    if (address.street) {
      addressText += `${address.street}${address.number ? `, ${address.number}` : ''}`;
    }

    if (address.complement) {
      addressText += `\n${address.complement}`;
    }

    if (address.neighborhood) {
      addressText += `\n${address.neighborhood}`;
    }

    if (address.city) {
      addressText += `\n${address.city}${address.state ? ` - ${address.state}` : ''}`;
    }

    pdf.text(addressText, margin + pageWidth / 2 + 10, y + 16);
  }

  y += 40;

  // ===== ITENS DO PEDIDO =====

  // Título da seção
  pdf.setFillColor(primaryColor.r, primaryColor.g, primaryColor.b);
  pdf.rect(margin, y, pageWidth, 8, 'F');

  pdf.setTextColor(255, 255, 255);
  pdf.setFont('helvetica', 'bold');
  pdf.setFontSize(12);
  pdf.text('ITENS DO PEDIDO', margin + 5, y + 5.5);

  y += 12;

  // Resetar cor do texto para preto
  pdf.setTextColor(0, 0, 0);

  // Cabeçalho da tabela
  pdf.setFillColor(240, 240, 240);
  pdf.rect(margin, y, pageWidth, 8, 'F');

  pdf.setFont('helvetica', 'bold');
  pdf.setFontSize(10);
  pdf.text('Produto', margin + 5, y + 5.5);
  pdf.text('Qtd', margin + 100, y + 5.5);
  pdf.text('Preço Unit.', margin + 120, y + 5.5);
  pdf.text('Total', margin + 160, y + 5.5);

  y += 8;

  // Itens
  pdf.setFont('helvetica', 'normal');

  if (data.items && data.items.length > 0) {
    let rowColor = false; // Alternar cores das linhas

    data.items.forEach((item: any) => {
      // Nome do produto (usar o nome do produto do item ou do produto relacionado)
      const productName = item.productName || (item.product ? item.product.name : 'Produto');

      // Verificar se o texto cabe na página atual, senão adicionar nova página
      if (y > 250) {
        pdf.addPage();
        y = margin;

        // Adicionar cabeçalho da tabela na nova página
        pdf.setFillColor(primaryColor.r, primaryColor.g, primaryColor.b);
        pdf.rect(margin, y, pageWidth, 8, 'F');

        pdf.setTextColor(255, 255, 255);
        pdf.setFont('helvetica', 'bold');
        pdf.setFontSize(12);
        pdf.text('ITENS DO PEDIDO (CONTINUAÇÃO)', margin + 5, y + 5.5);

        y += 12;

        // Resetar cor do texto para preto
        pdf.setTextColor(0, 0, 0);

        // Cabeçalho da tabela
        pdf.setFillColor(240, 240, 240);
        pdf.rect(margin, y, pageWidth, 8, 'F');

        pdf.setFont('helvetica', 'bold');
        pdf.setFontSize(10);
        pdf.text('Produto', margin + 5, y + 5.5);
        pdf.text('Qtd', margin + 100, y + 5.5);
        pdf.text('Preço Unit.', margin + 120, y + 5.5);
        pdf.text('Total', margin + 160, y + 5.5);

        y += 8;
        pdf.setFont('helvetica', 'normal');
      }

      // Altura da linha do item
      const itemHeight = 8;

      // Cor de fundo alternada
      if (rowColor) {
        pdf.setFillColor(245, 245, 245);
        pdf.rect(margin, y, pageWidth, itemHeight, 'F');
      }
      rowColor = !rowColor;

      // Truncar nome do produto se for muito longo
      const maxProductNameLength = 45;
      const displayProductName = productName.length > maxProductNameLength
        ? productName.substring(0, maxProductNameLength) + '...'
        : productName;

      // Informações do item
      pdf.text(displayProductName, margin + 5, y + 5.5);
      pdf.text(item.quantity.toString(), margin + 100, y + 5.5);

      // Preço unitário - tratar NaN
      const unitPrice = isNaN(item.unitPrice) ? 0 : item.unitPrice;
      pdf.text(formatCurrency(unitPrice, currency), margin + 120, y + 5.5);

      // Subtotal - tratar NaN
      const subtotal = isNaN(item.subtotal) ? 0 : item.subtotal;
      pdf.text(formatCurrency(subtotal, currency), margin + 160, y + 5.5);

      y += itemHeight;

      // Variações selecionadas
      if (item.selectedVariations && item.selectedVariations.length > 0) {
        item.selectedVariations.forEach((variation: any) => {
          if (y > 250) {
            pdf.addPage();
            y = margin;

            // Adicionar cabeçalho da tabela na nova página
            pdf.setFillColor(primaryColor.r, primaryColor.g, primaryColor.b);
            pdf.rect(margin, y, pageWidth, 8, 'F');

            pdf.setTextColor(255, 255, 255);
            pdf.setFont('helvetica', 'bold');
            pdf.setFontSize(12);
            pdf.text('ITENS DO PEDIDO (CONTINUAÇÃO)', margin + 5, y + 5.5);

            y += 12;

            // Resetar cor do texto para preto
            pdf.setTextColor(0, 0, 0);

            // Cabeçalho da tabela
            pdf.setFillColor(240, 240, 240);
            pdf.rect(margin, y, pageWidth, 8, 'F');

            pdf.setFont('helvetica', 'bold');
            pdf.setFontSize(10);
            pdf.text('Produto', margin + 5, y + 5.5);
            pdf.text('Qtd', margin + 100, y + 5.5);
            pdf.text('Preço Unit.', margin + 120, y + 5.5);
            pdf.text('Total', margin + 160, y + 5.5);

            y += 8;
            pdf.setFont('helvetica', 'normal');
          }

          const variationText = `  - ${variation.variationName}: ${variation.optionName}`;
          pdf.setTextColor(100, 100, 100); // Cinza para variações
          pdf.text(variationText, margin + 10, y + 5);

          if (variation.price > 0) {
            pdf.text(`(+${formatCurrency(variation.price, currency)})`, margin + 120, y + 5);
          }

          y += 6;
          pdf.setTextColor(0, 0, 0); // Voltar para preto
        });
      }

      // Observação do item
      if (item.observation) {
        if (y > 250) {
          pdf.addPage();
          y = margin;
        }

        pdf.setTextColor(100, 100, 100); // Cinza para observações
        pdf.text(`  Obs: ${item.observation}`, margin + 10, y + 5);
        y += 6;
        pdf.setTextColor(0, 0, 0); // Voltar para preto
      }
    });
  } else {
    pdf.text('Nenhum item encontrado', margin + 5, y + 10);
    y += 15;
  }

  // ===== TOTAIS =====

  // Adicionar caixa para os totais
  pdf.setFillColor(245, 245, 245);
  const totalsBoxHeight = data.deliveryFee > 0 ? 30 : 20;
  pdf.roundedRect(margin + 100, y + 5, pageWidth - 100, totalsBoxHeight, 3, 3, 'F');

  y += 10;

  // Subtotal
  pdf.setFont('helvetica', 'normal');
  pdf.setFontSize(10);
  pdf.text('Subtotal:', margin + 105, y + 5);

  // Tratar NaN no subtotal
  const subtotal = isNaN(data.subtotal) ? 0 : data.subtotal;
  pdf.text(formatCurrency(subtotal, currency), margin + 160, y + 5);

  y += 8;

  // Taxa de entrega (se aplicável)
  if (data.deliveryFee > 0) {
    pdf.text('Taxa de entrega:', margin + 105, y + 5);

    // Tratar NaN na taxa de entrega
    const deliveryFee = isNaN(data.deliveryFee) ? 0 : data.deliveryFee;
    pdf.text(formatCurrency(deliveryFee, currency), margin + 160, y + 5);

    y += 8;
  }

  // Total
  pdf.setFont('helvetica', 'bold');
  pdf.setFontSize(12);
  pdf.text('TOTAL:', margin + 105, y + 5);

  // Tratar NaN no total
  const total = isNaN(data.total) ? 0 : data.total;
  pdf.text(formatCurrency(total, currency), margin + 160, y + 5);

  y += 15;

  // ===== OBSERVAÇÕES =====

  if (data.notes) {
    // Verificar se cabe na página atual
    if (y > 240) {
      pdf.addPage();
      y = margin;
    }

    pdf.setFillColor(accentColor.r, accentColor.g, accentColor.b, 0.1);
    pdf.roundedRect(margin, y, pageWidth, 25, 3, 3, 'F');

    pdf.setFont('helvetica', 'bold');
    pdf.setFontSize(11);
    pdf.text('OBSERVAÇÕES', margin + 5, y + 8);

    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(10);
    pdf.text(data.notes, margin + 5, y + 16);

    y += 30;
  }

  // ===== RODAPÉ =====

  // Verificar se cabe na página atual
  if (y > 260) {
    pdf.addPage();
    y = margin;
  }

  // Linha separadora
  pdf.setDrawColor(200, 200, 200);
  pdf.line(margin, y, 210 - margin, y);

  y += 10;

  // Mensagem de agradecimento
  pdf.setFont('helvetica', 'bold');
  pdf.setFontSize(12);
  pdf.setTextColor(primaryColor.r, primaryColor.g, primaryColor.b);

  const thankYouMessage = "Obrigado por comprar conosco!";
  const thankYouWidth = pdf.getStringUnitWidth(thankYouMessage) * 12 / pdf.internal.scaleFactor;
  const thankYouX = (210 - thankYouWidth) / 2;
  pdf.text(thankYouMessage, thankYouX, y);

  y += 8;

  // Informações de contato da loja
  pdf.setFont('helvetica', 'normal');
  pdf.setFontSize(10);
  pdf.setTextColor(100, 100, 100);

  const contactMessage = `${storeName} - Pedido gerado em ${new Date().toLocaleDateString('pt-BR')}`;
  const contactWidth = pdf.getStringUnitWidth(contactMessage) * 10 / pdf.internal.scaleFactor;
  const contactX = (210 - contactWidth) / 2;
  pdf.text(contactMessage, contactX, y);

  // Salvar o PDF
  pdf.save(fileName);
};

// Função auxiliar para converter cor hexadecimal para RGB
function hexToRgb(hex: string) {
  // Remover o # se existir
  hex = hex.replace(/^#/, '');

  // Verificar se é um valor hex válido
  if (!/^[0-9A-F]{6}$/i.test(hex)) {
    return { r: 0, g: 0, b: 0 };
  }

  // Converter para RGB
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  return { r, g, b };
}