import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { Plus, Check, X, Edit, Trash2, Eye, EyeOff, Search } from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import { useTranslation } from "@/hooks/useTranslation";
import { apiRequest } from "@/lib/queryClient";
import { useLocation } from "wouter";
import { useProductLimitGuard } from "@/hooks/useSubscriptionGuard";
import { UpgradePrompt } from "@/components/subscription/UpgradePrompt";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Input } from "@/components/ui/input";

export default function ProductManagement() {
  const { t } = useTranslation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [, navigate] = useLocation();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<any>(null);
  const [isMobile, setIsMobile] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const { canAdd, withCheck } = useProductLimitGuard();

  // Detectar se é mobile
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Verificar inicialmente
    checkIfMobile();

    // Adicionar listener para redimensionamento
    window.addEventListener('resize', checkIfMobile);

    // Forçar verificação após um curto período
    const timer = setTimeout(checkIfMobile, 500);

    // Cleanup
    return () => {
      window.removeEventListener('resize', checkIfMobile);
      clearTimeout(timer);
    };
  }, []);

  // Fetch products
  const { data: products, isLoading } = useQuery({
    queryKey: ['/api/products'],
  });

  // Fetch categories
  const { data: categories } = useQuery({
    queryKey: ['/api/categories'],
  });

  // Delete product mutation
  const deleteMutation = useMutation({
    mutationFn: (id: number) =>
      apiRequest('DELETE', `/api/products/${id}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/products'] });
      setIsDeleteDialogOpen(false);
      toast({
        title: t('products.productDeleted'),
        description: t('common.success'),
      });
    },
    onError: (error) => {
      toast({
        title: t('common.error'),
        description: error instanceof Error ? error.message : String(error),
        variant: "destructive",
      });
    }
  });

  // Toggle product visibility mutation
  const toggleVisibilityMutation = useMutation({
    mutationFn: async ({ id, visible, product }: { id: number, visible: boolean, product: any }) => {
      // Update local state immediately for better UX
      queryClient.setQueryData(['/api/products'], (oldData: any) => {
        if (!oldData) return oldData;
        return oldData.map((p: any) =>
          p.id === id ? { ...p, visible } : p
        );
      });

      return apiRequest('PATCH', `/api/products/${id}`, { visible });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/products'] });
      toast({
        title: t('products.visibilityUpdated'),
        description: t('common.success'),
      });
    },
    onError: (error, variables) => {
      // Revert the local state change on error
      const { id, visible, product } = variables;

      queryClient.setQueryData(['/api/products'], (oldData: any) => {
        if (!oldData) return oldData;
        return oldData.map((p: any) =>
          p.id === id ? { ...p, visible: !visible } : p
        );
      });

      toast({
        title: t('common.error'),
        description: error instanceof Error ? error.message : String(error),
        variant: "destructive",
      });
    }
  });

  // Open edit dialog and populate form
  const handleEdit = (product: any) => {
    // Navigate to the edit page with the product ID
    navigate(`/admin/products/edit/${product.id}`);
  };

  // Open delete dialog
  const handleDelete = (product: any) => {
    setSelectedProduct(product);
    setIsDeleteDialogOpen(true);
  };

  // Confirm delete
  const confirmDelete = () => {
    if (selectedProduct) {
      deleteMutation.mutate(selectedProduct.id);
    }
  };

  // Group products by category and filter by search query
  const groupProductsByCategory = () => {
    if (!products || !categories || !Array.isArray(products) || !Array.isArray(categories)) {
      return { uncategorized: [] as any[] };
    }

    // Create a map of sorted categories
    const sortedCategories = [...categories].sort((a, b) => a.displayOrder - b.displayOrder);
    const categoryMap = sortedCategories.reduce((acc: Record<number, any>, category: any) => {
      acc[category.id] = category;
      return acc;
    }, {} as Record<number, any>);

    // Filter products by search query
    const filteredProducts = searchQuery.trim() === ""
      ? products
      : products.filter((product: any) =>
          product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          (product.description && product.description.toLowerCase().includes(searchQuery.toLowerCase()))
        );

    // Group products by category
    const groupedProducts: Record<string, any[]> = {};

    // Products with categories
    filteredProducts.forEach((product: any) => {
      if (product.categoryId && categoryMap[product.categoryId]) {
        const categoryName = categoryMap[product.categoryId].name;
        if (!groupedProducts[categoryName]) {
          groupedProducts[categoryName] = [];
        }
        groupedProducts[categoryName].push(product);
      } else {
        // Products without a category
        if (!groupedProducts['uncategorized']) {
          groupedProducts['uncategorized'] = [];
        }
        groupedProducts['uncategorized'].push(product);
      }
    });

    return groupedProducts;
  };

  // Render a product item in desktop style
  const renderProductItem = (product: any) => (
    <div
      key={product.id}
      className="flex flex-col sm:flex-row items-start sm:items-center justify-between p-4 border-b hover:bg-muted/30 transition-colors"
    >
      <div className="flex items-center space-x-4 mb-3 sm:mb-0 w-full sm:w-auto">
        <div className="w-16 h-16 relative flex-shrink-0">
          {product.images && product.images.length > 0 ? (
            <img
              src={product.images[0]}
              alt={product.name}
              className="w-full h-full object-cover rounded-md"
              onError={(e) => {
                e.currentTarget.onerror = null;
                e.currentTarget.src = `https://via.placeholder.com/400x300?text=Image+Error`;
              }}
            />
          ) : (
            <div className="w-full h-full bg-muted flex items-center justify-center rounded-md">
              <span className="text-xs text-muted-foreground">No image</span>
            </div>
          )}
          {product.images && product.images.length > 1 && (
            <div className="absolute -top-2 -right-2 bg-primary text-white text-xs px-1.5 py-0.5 rounded-full">
              {product.images.length}
            </div>
          )}
        </div>
        <div className="flex-grow">
          <h3 className="text-base font-medium text-neutral-dark">{product.name}</h3>
          <p className="text-neutral-dark text-xs mt-1 line-clamp-1">{product.description}</p>
          <div className="flex items-center mt-1 space-x-4">
            <span className="text-primary font-semibold">{formatCurrency(product.price)}</span>
            <span className="inline-flex items-center text-xs text-neutral-dark">
              {product.inStock ? (
                <>
                  <Check className="text-success mr-1 h-3 w-3" />
                  {t('products.inStock')}
                </>
              ) : (
                <>
                  <X className="text-error mr-1 h-3 w-3" />
                  {t('products.outOfStock')}
                </>
              )}
            </span>
            {product.hasVariations && (
              <span className="text-xs bg-secondary/20 text-secondary px-2 py-0.5 rounded-full">
                {t('products.hasVariationsShort') || 'Variações'}
              </span>
            )}
          </div>
        </div>
      </div>
      <div className="flex items-center space-x-2 ml-auto">
        {/* Visibility toggle button */}
        <Button
          variant="ghost"
          size="icon"
          onClick={() => toggleVisibilityMutation.mutate({
            id: product.id,
            visible: !product.visible,
            product
          })}
          title={product.visible ? t('admin.visible') : t('admin.hidden')}
          disabled={toggleVisibilityMutation.isPending}
          className={product.visible ? "text-green-500 hover:text-green-600" : "text-gray-400 hover:text-gray-500"}
        >
          {toggleVisibilityMutation.isPending ? (
            <div className="animate-spin h-4 w-4 border-b-2 border-current"></div>
          ) : product.visible ? (
            <Eye className="h-4 w-4" />
          ) : (
            <EyeOff className="h-4 w-4" />
          )}
        </Button>
        <Button variant="outline" size="sm" onClick={() => handleEdit(product)}>
          <Edit className="h-3.5 w-3.5 mr-1" />
          {t('common.edit')}
        </Button>
        <Button variant="outline" size="sm" className="text-error border-error hover:bg-error/10" onClick={() => handleDelete(product)}>
          <Trash2 className="h-3.5 w-3.5 mr-1" />
          {t('common.delete')}
        </Button>
      </div>
    </div>
  );

  // Render a product item in mobile card style
  const renderMobileProductItem = (product: any) => (
    <div
      key={product.id}
      className="bg-white rounded-lg shadow p-4 mb-4"
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center">
          <div className="w-14 h-14 relative flex-shrink-0 mr-3">
            {product.images && product.images.length > 0 ? (
              <img
                src={product.images[0]}
                alt={product.name}
                className="w-full h-full object-cover rounded-md"
                onError={(e) => {
                  e.currentTarget.onerror = null;
                  e.currentTarget.src = `https://via.placeholder.com/400x300?text=Image+Error`;
                }}
              />
            ) : (
              <div className="w-full h-full bg-muted flex items-center justify-center rounded-md">
                <span className="text-xs text-muted-foreground">No image</span>
              </div>
            )}
            {product.images && product.images.length > 1 && (
              <div className="absolute -top-2 -right-2 bg-primary text-white text-xs px-1.5 py-0.5 rounded-full">
                {product.images.length}
              </div>
            )}
          </div>
          <div>
            <h3 className="font-medium text-neutral-dark">{product.name}</h3>
            <span className="text-primary font-semibold">{formatCurrency(product.price)}</span>
          </div>
        </div>

        <Button
          variant="ghost"
          size="icon"
          onClick={() => toggleVisibilityMutation.mutate({
            id: product.id,
            visible: !product.visible,
            product
          })}
          title={product.visible ? t('admin.visible') : t('admin.hidden')}
          disabled={toggleVisibilityMutation.isPending}
          className={product.visible ? "text-green-500 hover:text-green-600" : "text-gray-400 hover:text-gray-500"}
        >
          {toggleVisibilityMutation.isPending ? (
            <div className="animate-spin h-4 w-4 border-b-2 border-current"></div>
          ) : product.visible ? (
            <Eye className="h-4 w-4" />
          ) : (
            <EyeOff className="h-4 w-4" />
          )}
        </Button>
      </div>

      <p className="text-neutral-dark text-xs mb-3 line-clamp-2">{product.description}</p>

      <div className="flex items-center mb-3 space-x-3">
        <span className="inline-flex items-center text-xs text-neutral-dark">
          {product.inStock ? (
            <>
              <Check className="text-success mr-1 h-3 w-3" />
              {t('products.inStock')}
            </>
          ) : (
            <>
              <X className="text-error mr-1 h-3 w-3" />
              {t('products.outOfStock')}
            </>
          )}
        </span>
        {product.hasVariations && (
          <span className="text-xs bg-secondary/20 text-secondary px-2 py-0.5 rounded-full">
            {t('products.hasVariationsShort') || 'Variações'}
          </span>
        )}
      </div>

      <div className="flex justify-end space-x-2">
        <Button variant="outline" size="sm" onClick={() => handleEdit(product)}>
          <Edit className="h-3.5 w-3.5 mr-1" />
          {t('common.edit')}
        </Button>
        <Button variant="outline" size="sm" className="text-error border-error hover:bg-error/10" onClick={() => handleDelete(product)}>
          <Trash2 className="h-3.5 w-3.5 mr-1" />
          {t('common.delete')}
        </Button>
      </div>
    </div>
  );

  return (
    <div>
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
        {/* Search bar */}
        <div className="relative w-full sm:w-80">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder={`${t('common.search')} ${t('products.title').toLowerCase()}...`}
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <Button
          onClick={withCheck(() => navigate('/admin/products/new'))}
          className="flex items-center"
        >
          <Plus className="mr-1 h-4 w-4" />
          {t('products.addProduct')}
        </Button>
      </div>

      {/* Products by Category */}
      {isLoading ? (
        // Loading skeletons
        isMobile ? (
          // Mobile loading skeletons
          <div className="space-y-4">
            {Array(3).fill(0).map((_, index) => (
              <div key={index} className="bg-white rounded-lg shadow p-4 mb-4">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center">
                    <Skeleton className="h-14 w-14 rounded-md mr-3" />
                    <div className="space-y-2">
                      <Skeleton className="h-5 w-32" />
                      <Skeleton className="h-4 w-20" />
                    </div>
                  </div>
                  <Skeleton className="h-8 w-8 rounded-full" />
                </div>
                <Skeleton className="h-3 w-full mb-3" />
                <div className="flex space-x-3 mb-3">
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-4 w-16" />
                </div>
                <div className="flex justify-end space-x-2">
                  <Skeleton className="h-8 w-16" />
                  <Skeleton className="h-8 w-16" />
                </div>
              </div>
            ))}
          </div>
        ) : (
          // Desktop loading skeletons
          <div className="bg-white rounded-md shadow divide-y">
            {Array(4).fill(0).map((_, index) => (
              <div key={index} className="p-4">
                <div className="flex items-center space-x-4">
                  <Skeleton className="h-16 w-16 rounded-md" />
                  <div className="space-y-2 flex-grow">
                    <Skeleton className="h-5 w-1/3" />
                    <Skeleton className="h-3 w-2/3" />
                    <div className="flex space-x-4">
                      <Skeleton className="h-4 w-16" />
                      <Skeleton className="h-4 w-16" />
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Skeleton className="h-8 w-16" />
                    <Skeleton className="h-8 w-16" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        )
      ) : products && Array.isArray(products) && products.length > 0 ? (
        <div className="space-y-8">
          {/* Check if there are any filtered products */}
          {Object.keys(groupProductsByCategory()).length === 0 && searchQuery.trim() !== "" ? (
            <div className="text-center py-12 bg-white rounded-md shadow">
              <p className="text-muted-foreground">
                {t('products.noSearchResults', 'Nenhum produto encontrado para') + ` "${searchQuery}"`}
              </p>
              <Button onClick={() => setSearchQuery("")} className="mt-4">
                {t('common.clearSearch', 'Limpar busca')}
              </Button>
            </div>
          ) : (
            Object.entries(groupProductsByCategory())
              .sort(([nameA, _], [nameB, __]) => {
                // Find categories to compare their displayOrder
                const categoryA = categories.find(c => c.name === nameA);
                const categoryB = categories.find(c => c.name === nameB);

                // Handle uncategorized
                if (nameA === 'uncategorized') return 1;
                if (nameB === 'uncategorized') return -1;

                // Sort by displayOrder
                return (categoryA?.displayOrder || 0) - (categoryB?.displayOrder || 0);
              })
              .map(([categoryName, categoryProducts]) => (
              <div key={categoryName} className="space-y-4">
                <h2 className="text-xl font-semibold text-neutral-dark font-heading px-1 border-l-4 border-primary pl-2">
                  {categoryName === 'uncategorized' ? t('products.uncategorized') || 'Sem categoria' : categoryName}
                  <span className="ml-2 text-sm font-normal text-muted-foreground">
                    ({categoryProducts.length})
                  </span>
                </h2>
                {isMobile ? (
                  // Mobile product cards
                  <div className="space-y-4">
                    {categoryProducts.map((product: any) => renderMobileProductItem(product))}
                  </div>
                ) : (
                  // Desktop product table
                  <div className="bg-white rounded-md shadow divide-y">
                    {categoryProducts.map((product: any) => renderProductItem(product))}
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      ) : (
        <div className="text-center py-12 bg-white rounded-md shadow">
          <p className="text-muted-foreground">{t('products.noProducts') || 'Sem produtos cadastrados'}</p>
          <Button
            onClick={withCheck(() => navigate('/admin/products/new'))}
            className="mt-4"
          >
            {t('products.addProduct')}
          </Button>
        </div>
      )}

      {/* Upgrade prompt when limit is reached */}
      {!canAdd() && (
        <div className="mt-6">
          <UpgradePrompt
            title="Limite de produtos atingido"
            description="Você atingiu o limite de produtos do plano gratuito. Faça upgrade para adicionar mais produtos."
            variant="card"
          />
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('products.deleteProduct')}</AlertDialogTitle>
            <AlertDialogDescription>
              {t('products.confirmDelete')}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t('common.cancel')}</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete} className="bg-error text-white hover:bg-error/90">
              {deleteMutation.isPending ? t('common.deleting') : t('common.delete')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}