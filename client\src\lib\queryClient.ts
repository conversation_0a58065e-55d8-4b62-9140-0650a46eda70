import { QueryClient, QueryFunction } from "@tanstack/react-query";

async function throwIfResNotOk(res: Response) {
  if (!res.ok) {
    const text = (await res.text()) || res.statusText;
    throw new Error(`${res.status}: ${text}`);
  }
}

// Com o novo esquema de autenticação Firebase, precisamos enviar o UID como parâmetro de consulta
async function getAuthHeaders(): Promise<Record<string, string>> {
  try {
    // Verificar se temos o Firebase inicializado e o usuário está logado
    const firebaseModule = await import('./firebase');
    const { auth } = firebaseModule;

    const currentUser = auth.currentUser;
    if (currentUser && currentUser.uid) {
      return {};  // Retornamos um objeto vazio, pois o UID será adicionado como query param
    }
  } catch (error) {
    console.error("Error getting auth user:", error);
  }

  return {};
}

export async function apiRequest(
  method: string,
  url: string,
  data?: unknown | undefined,
): Promise<Response> {
  console.log(`API Request: ${method} ${url}`);

  // Get auth headers and check if we need to add UID
  let finalUrl = url;
  let authHeaders = {};

  try {
    // Verificar se temos o Firebase inicializado e o usuário está logado
    const firebaseModule = await import('./firebase');
    const { auth } = firebaseModule;

    const currentUser = auth.currentUser;

    if (currentUser && currentUser.uid) {
      // Adicionar o UID como parâmetro de consulta
      const separator = url.includes('?') ? '&' : '?';
      finalUrl = `${url}${separator}uid=${currentUser.uid}`;

      // Tentar obter o token de autenticação
      try {
        const token = await currentUser.getIdToken();
        authHeaders = {
          'Authorization': `Bearer ${token}`
        };
      } catch (tokenError) {
        console.warn('Could not get ID token:', tokenError);
      }
    }
  } catch (error) {
    console.error("Error getting auth user for request:", error);
  }

  // Prepare headers
  const headers: Record<string, string> = {
    ...(data ? { "Content-Type": "application/json" } : {}),
    ...authHeaders,
    // Desabilitar cache HTTP para garantir dados atualizados
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0'
  };

  const res = await fetch(finalUrl, {
    method,
    headers,
    body: data ? JSON.stringify(data) : undefined,
    credentials: "include",
  });

  console.log(`API Response: ${res.status} ${res.statusText}`);

  await throwIfResNotOk(res);
  return res;
}

type UnauthorizedBehavior = "returnNull" | "throw";
export const getQueryFn: <T>(options: {
  on401: UnauthorizedBehavior;
}) => QueryFunction<T> =
  ({ on401: unauthorizedBehavior }) =>
  async ({ queryKey }) => {
    // Processar queryKey para lidar com parâmetros de rota
    let url = queryKey[0] as string;

    // Verificamos se há mais elementos no queryKey e o primeiro é uma string
    // que termina com um ID (ex: ['/api/products', 1])
    if (queryKey.length > 1 && typeof url === 'string' && typeof queryKey[1] !== 'undefined') {
      url = `${url}/${queryKey[1]}`;
    }

    console.log(`Query request: ${url}`);
    console.log(`Query key:`, queryKey);

    // Adicionar o UID ao URL se o usuário estiver autenticado
    let finalUrl = url;
    try {
      // Verificar se temos o Firebase inicializado e o usuário está logado
      const firebaseModule = await import('./firebase');
      const { auth } = firebaseModule;

      const currentUser = auth.currentUser;
      if (currentUser && currentUser.uid) {
        // Adicionar o UID como parâmetro de consulta
        const separator = url.includes('?') ? '&' : '?';
        finalUrl = `${url}${separator}uid=${currentUser.uid}`;
        console.log('Added UID to query URL:', finalUrl);
      }
    } catch (error) {
      console.error("Error getting auth user for query:", error);
    }

    // Get auth headers if user is logged in
    const authHeaders = await getAuthHeaders();
    console.log('Query auth headers:', authHeaders);

    const res = await fetch(finalUrl, {
      credentials: "include",
      headers: {
        ...authHeaders,
        // Desabilitar cache HTTP para garantir dados atualizados
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      } as HeadersInit
    });

    console.log(`Query response: ${res.status} ${res.statusText}`);

    if (unauthorizedBehavior === "returnNull" && res.status === 401) {
      console.log('Returning null due to 401 status');
      return null;
    }

    await throwIfResNotOk(res);
    const data = await res.json();
    console.log('Query response data:', data);
    return data;
  };

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      queryFn: getQueryFn({ on401: "throw" }),
      refetchInterval: false,
      refetchOnWindowFocus: false,
      staleTime: 0, // Temporariamente removendo cache para testar
      retry: false,
    },
    mutations: {
      retry: false,
    },
  },
});
