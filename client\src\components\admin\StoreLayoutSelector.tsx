import React from 'react';
import { useTranslation } from 'react-i18next';
import { Layout, LayoutGrid, Menu, Smartphone } from 'lucide-react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';

interface StoreLayoutSelectorProps {
  value: number;
  onChange: (layoutType: number) => void;
}

/**
 * Componente para seleção de layout nas configurações da loja
 */
const StoreLayoutSelector: React.FC<StoreLayoutSelectorProps> = ({
  value,
  onChange
}) => {
  const { t } = useTranslation();

  const layoutOptions = [
    {
      id: 1,
      title: t('settings.layoutType1') || 'Exibição em Grade',
      description: t('settings.layoutType1Desc') || 'Produtos exibidos em formato de grade',
      icon: <LayoutGrid className="h-6 w-6 mb-2" />
    },
    {
      id: 2,
      title: t('settings.layoutType2') || 'Categorias horizontais + Lista de produtos',
      description: t('settings.layoutType2Desc') || 'Navegação por categorias deslizantes, produtos em formato de listtile',
      icon: <Menu className="h-6 w-6 mb-2" />
    }
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('settings.layoutOptions')}</CardTitle>
        <CardDescription>{t('settings.layoutOptionsDesc')}</CardDescription>
      </CardHeader>
      <CardContent>
        <RadioGroup 
          value={String(value)} 
          onValueChange={(val) => onChange(Number(val))}
          className="grid gap-6 sm:grid-cols-3"
        >
          {layoutOptions.map((option) => (
            <div key={option.id} className="relative">
              <RadioGroupItem
                value={String(option.id)}
                id={`layout-${option.id}`}
                className="sr-only peer"
              />
              <Label
                htmlFor={`layout-${option.id}`}
                className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary cursor-pointer h-full"
              >
                {option.icon}
                <div className="flex flex-col items-center space-y-1 text-center">
                  <span className="text-sm font-medium leading-none">{option.title}</span>
                  <span className="text-xs text-muted-foreground leading-tight">
                    {option.description}
                  </span>
                </div>
              </Label>
            </div>
          ))}
        </RadioGroup>
      </CardContent>
    </Card>
  );
};

export default StoreLayoutSelector;