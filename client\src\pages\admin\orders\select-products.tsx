import { useState, useEffect } from 'react';
import { useLocation } from 'wouter';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useTranslation } from '@/hooks/useTranslation';
import { useStore } from '@/context/StoreContext';
import { useAdminOrder } from '@/context/AdminOrderContext';
import { useToast } from '@/hooks/use-toast';
import AdminLayout from '@/components/admin/AdminLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Search, Plus, Minus, ShoppingCart, Package, Check, FileText } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';

export default function SelectProductsPage() {
  const { t } = useTranslation();
  const [, navigate] = useLocation();
  const { store } = useStore();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedProducts, setSelectedProducts] = useState<{[key: number]: number}>({});
  const [isRevisionMode, setIsRevisionMode] = useState<boolean>(false);
  const [revisionId, setRevisionId] = useState<string | null>(null);
  const [orderId, setOrderId] = useState<string | null>(null);

  const { addItem } = useAdminOrder();

  // Verificar se estamos adicionando a uma revisão
  useEffect(() => {
    const addingToRevision = localStorage.getItem('addingToRevision');
    const currentRevisionId = localStorage.getItem('currentRevisionId');
    const currentOrderId = localStorage.getItem('currentOrderId');

    if (addingToRevision === 'true' && currentRevisionId) {
      setIsRevisionMode(true);
      setRevisionId(currentRevisionId);
      setOrderId(currentOrderId);
      console.log('Modo de revisão ativado:', { revisionId: currentRevisionId, orderId: currentOrderId });
    }
  }, []);

  // Buscar produtos
  const { data: products, isLoading } = useQuery({
    queryKey: ['/api/products'],
    enabled: !!store,
  });

  // Filtrar produtos com base na busca
  const filteredProducts = products
    ? products.filter((product: any) =>
        product.name.toLowerCase().includes(searchQuery.toLowerCase()))
    : [];

  // Adicionar produto ao pedido ou à revisão
  const handleAddProduct = (product: any) => {
    // Se o produto tem variações, navegar para a página de detalhes do produto
    if (product.hasVariations) {
      // Salvar informações no localStorage para recuperar na página de detalhes
      if (isRevisionMode) {
        // Modo de revisão - adicionar a uma revisão existente
        localStorage.setItem('addingToRevision', 'true');
        localStorage.setItem('currentProductId', product.id.toString());
      } else {
        // Modo normal - adicionar a um novo pedido
        localStorage.setItem('adminOrderCreationProduct', 'true');
        localStorage.setItem('currentProductId', product.id.toString());
      }

      // Navegar para a página de detalhes do produto
      navigate(`/admin/orders/product-details/${product.id}`);
      return;
    }

    if (isRevisionMode && revisionId) {
      // Adicionar produto simples diretamente à revisão
      // Criar um item de revisão via API
      addProductToRevision(product);
    } else {
      // Adicionar produto simples diretamente ao novo pedido
      addItem({
        productId: product.id,
        name: product.name,
        price: product.price,
        image: product.images && product.images.length > 0 ? product.images[0] : undefined,
        quantity: 1
      });

      toast({
        title: t('products.productAddedToast') || 'Produto adicionado',
        description: t('products.regularProductAddedToOrder') || 'O produto foi adicionado ao pedido',
        variant: "success",
      });

      // Redirecionar para a página de novo pedido
      console.log('Produto simples adicionado ao pedido administrativo, redirecionando para /admin/orders/new');
      navigate('/admin/orders/new');
    }
  };

  // Função para adicionar produto à revisão via API
  const addProductToRevision = async (product: any) => {
    if (!revisionId) return;

    try {
      // Preparar os dados do item
      const itemData = {
        productId: product.id,
        productName: product.name,
        productImage: product.images && product.images.length > 0 ? product.images[0] : null,
        quantity: 1,
        unitPrice: product.price,
        subtotal: product.price,
        selectedVariations: [],
        observation: null
      };

      // Chamar a API para adicionar o item à revisão
      const response = await apiRequest('POST', `/api/orders/revisions/${revisionId}/items`, { item: itemData });

      toast({
        title: t('products.productAddedToast') || 'Produto adicionado',
        description: t('products.productAddedToRevision') || 'O produto foi adicionado à revisão do pedido',
        variant: "success",
      });

      // Invalidar consultas para atualizar os dados
      if (orderId) {
        queryClient.invalidateQueries({ queryKey: [`/api/orders/${orderId}`] });
        queryClient.invalidateQueries({ queryKey: [`/api/orders/${orderId}/revisions`] });
        queryClient.invalidateQueries({ queryKey: [`/api/orders/revisions/${revisionId}`] });
      }

      // Redirecionar de volta para a página de detalhes do pedido
      if (orderId) {
        navigate(`/admin/orders/${orderId}`);
      } else {
        navigate('/admin/orders');
      }
    } catch (error) {
      console.error('Erro ao adicionar produto à revisão:', error);
      toast({
        title: t('common.error') || 'Erro',
        description: t('products.errorAddingProduct') || 'Ocorreu um erro ao adicionar o produto à revisão',
        variant: "destructive",
      });
    }
  };

  // Atualizar quantidade do produto selecionado
  const handleUpdateQuantity = (productId: number, quantity: number) => {
    setSelectedProducts(prev => {
      const updated = { ...prev };
      if (quantity <= 0) {
        delete updated[productId];
      } else {
        updated[productId] = quantity;
      }
      return updated;
    });
  };

  // Voltar para a página apropriada
  const handleGoBack = () => {
    if (isRevisionMode && orderId) {
      // Limpar flags do localStorage
      localStorage.removeItem('addingToRevision');

      // Voltar para a página de detalhes do pedido
      navigate(`/admin/orders/${orderId}`);
    } else {
      // Voltar para a página de criação de pedido
      navigate('/admin/orders/new');
    }
  };

  // Navegar para a página de criação de produto personalizado
  const handleCreateCustomProduct = () => {
    if (isRevisionMode) {
      // Configurar para adicionar produto personalizado à revisão
      localStorage.setItem('isRevision', 'true');
      localStorage.setItem('revisionId', revisionId || '');
      localStorage.setItem('orderId', orderId || '');
    }

    navigate('/admin/orders/custom-product');
  };

  return (
    <AdminLayout title={isRevisionMode
      ? (t('orders.addProductToRevision') || 'Adicionar Produto à Revisão')
      : (t('orders.selectProducts') || 'Selecionar Produtos')
    }>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold">
            {isRevisionMode
              ? (t('orders.addProductToRevision') || 'Adicionar Produto à Revisão')
              : (t('orders.selectProducts') || 'Selecionar Produtos')
            }
          </h2>
          <div className="flex gap-2">
            <Button variant="default" onClick={handleCreateCustomProduct}>
              <FileText className="h-4 w-4 mr-2" />
              {t('orders.createCustomProduct') || "Criar produto personalizado"}
            </Button>
            <Button variant="outline" onClick={handleGoBack}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              {t('common.goBack') || "Voltar"}
            </Button>
          </div>
        </div>

        {/* Campo de busca */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder={t('products.searchProducts') || 'Buscar produtos...'}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Lista de produtos */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {isLoading ? (
            // Esqueletos de carregamento
            Array(6).fill(0).map((_, index) => (
              <Card key={index} className="animate-pulse">
                <CardContent className="p-4">
                  <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                  <div className="h-4 bg-muted rounded w-1/2 mb-4"></div>
                  <div className="h-8 bg-muted rounded"></div>
                </CardContent>
              </Card>
            ))
          ) : filteredProducts.length > 0 ? (
            filteredProducts.map((product: any) => (
              <Card key={product.id} className="overflow-hidden">
                <CardContent className="p-0">
                  <div className="flex items-center p-4">
                    {product.images && product.images.length > 0 ? (
                      <div className="w-16 h-16 rounded-md overflow-hidden mr-4 flex-shrink-0">
                        <img
                          src={product.images[0]}
                          alt={product.name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    ) : (
                      <div className="w-16 h-16 rounded-md bg-muted flex items-center justify-center mr-4 flex-shrink-0">
                        <Package className="h-8 w-8 text-muted-foreground" />
                      </div>
                    )}
                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium truncate">{product.name}</h3>
                      <p className="text-primary font-medium">{formatCurrency(product.price)}</p>
                      {!product.inStock && (
                        <Badge variant="destructive" className="mt-1">
                          {t('products.outOfStock') || 'Fora de estoque'}
                        </Badge>
                      )}
                      {product.hasVariations && (
                        <Badge variant="secondary" className="mt-1">
                          {t('products.hasVariations') || 'Com variações'}
                        </Badge>
                      )}
                    </div>
                    <Button
                      onClick={() => handleAddProduct(product)}
                      disabled={!product.inStock}
                      size="sm"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      {t('common.add') || 'Adicionar'}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            <div className="col-span-full flex flex-col items-center justify-center p-8 text-center">
              <Package className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">
                {searchQuery
                  ? t('products.noProductsFound') || 'Nenhum produto encontrado'
                  : t('products.noProducts') || 'Nenhum produto disponível'}
              </h3>
              <p className="text-sm text-muted-foreground">
                {searchQuery
                  ? t('products.tryAnotherSearch') || 'Tente outra busca'
                  : t('products.addProductsFirst') || 'Adicione produtos primeiro'}
              </p>
            </div>
          )}
        </div>

        {/* Botão para continuar */}
        <div className="flex justify-end">
          <Button onClick={handleGoBack}>
            <ShoppingCart className="h-4 w-4 mr-2" />
            {t('orders.continueToOrder') || 'Continuar para o pedido'}
          </Button>
        </div>
      </div>
    </AdminLayout>
  );
}
