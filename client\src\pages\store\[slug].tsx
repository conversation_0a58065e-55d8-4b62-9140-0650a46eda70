import { useEffect, useState } from 'react';
import { useLocation } from 'wouter';
import { useToast } from '@/hooks/use-toast';
import { useTranslation } from '@/hooks/useTranslation';
import StoreLayout from '@/components/store/StoreLayout';
import ProductList from '@/components/store/ProductList';
import { Skeleton } from '@/components/ui/skeleton';

interface StorePageProps {
  slug: string;
}

export default function StorePage({ slug }: StorePageProps) {
  const { t } = useTranslation();
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const [store, setStore] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch store data
  useEffect(() => {
    const fetchStore = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/public/stores/${slug}`, {
          credentials: 'include'
        });
        
        if (!response.ok) {
          if (response.status === 404) {
            setError('Store not found');
            return;
          }
          throw new Error(`Failed to fetch store: ${response.statusText}`);
        }
        
        const storeData = await response.json();
        setStore(storeData);
      } catch (err) {
        console.error('Error fetching store:', err);
        setError(err instanceof Error ? err.message : 'Failed to load store');
        toast({
          title: t('common.error'),
          description: err instanceof Error ? err.message : 'Failed to load store',
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };
    
    fetchStore();
  }, [slug, toast, t]);

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-neutral-light">
        <div className="h-16 bg-primary"></div>
        <div className="h-48 bg-gradient-to-r from-primary to-secondary">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <Skeleton className="h-8 w-64 bg-white/20" />
            <Skeleton className="h-4 w-96 mt-4 bg-white/20" />
          </div>
        </div>
        <div className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
          <div className="flex overflow-x-auto space-x-4 pb-2">
            {Array(4).fill(0).map((_, i) => (
              <Skeleton key={i} className="h-10 w-28 rounded-full" />
            ))}
          </div>
          <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 lg:grid-cols-4 mt-8">
            {Array(8).fill(0).map((_, i) => (
              <div key={i} className="bg-white rounded-lg shadow overflow-hidden">
                <Skeleton className="h-40 w-full" />
                <div className="p-4">
                  <Skeleton className="h-5 w-3/4 mb-2" />
                  <div className="mt-2 flex justify-between items-center">
                    <Skeleton className="h-4 w-16" />
                    <Skeleton className="h-8 w-8 rounded-full" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error || !store) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-neutral-light p-4">
        <h1 className="text-2xl font-bold text-error mb-4">Store Not Found</h1>
        <p className="text-neutral-dark mb-8">
          The store you're looking for doesn't exist or might have been moved.
        </p>
        <button 
          onClick={() => setLocation("/")}
          className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90"
        >
          Go back to home
        </button>
      </div>
    );
  }

  return (
    <StoreLayout store={store}>
      <ProductList storeSlug={slug} />
    </StoreLayout>
  );
}
