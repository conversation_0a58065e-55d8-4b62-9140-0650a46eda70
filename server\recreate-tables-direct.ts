import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import 'dotenv/config';

// Obter variáveis do Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Supabase URL ou Service Key não encontrados nas variáveis de ambiente!');
  process.exit(1);
}

console.log(`Conectando ao Supabase: ${supabaseUrl}`);

// Inicializar cliente Supabase com service key
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Ler o arquivo SQL
const sqlFilePath = path.join(process.cwd(), 'recreate_tables.sql');
let sqlContent: string;

try {
  sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
  console.log('Arquivo SQL lido com sucesso!');
} catch (error) {
  console.error('Erro ao ler o arquivo SQL:', error);
  process.exit(1);
}

// Função principal para recriar tabelas
async function recreateTables() {
  console.log('Iniciando recriação das tabelas no Supabase...');
  
  try {
    // Verificar se podemos acessar o Supabase e suas tabelas (um teste básico)
    const { data: tables, error: tablesError } = await supabase.from('users').select('count');
    
    if (tablesError?.code !== 'PGRST116') { // PGRST116 = relação não encontrada, oque é esperado
      console.log('Teste de conexão com Supabase bem-sucedido!');
    } else {
      console.log('Tabela "users" não existe ainda, vamos criar as tabelas.');
    }

    // Como alternativa, vamos gerar um arquivo SQL para o usuário executar no console do Supabase
    const outputPath = path.join(process.cwd(), 'migrations', 'execute_me_in_supabase.sql');
    fs.writeFileSync(outputPath, sqlContent);
    
    console.log(`\n\n====== INSTRUÇÕES IMPORTANTES ======\n`);
    console.log(`Não foi possível executar o SQL diretamente via API.`);
    console.log(`Um arquivo SQL foi gerado para você em: ${outputPath}`);
    console.log(`\nPara criar as tabelas no Supabase:\n`);
    console.log(`1. Acesse o painel do Supabase: ${supabaseUrl}`);
    console.log(`2. Vá para "SQL Editor" no menu lateral`);
    console.log(`3. Clique em "New Query"`);
    console.log(`4. Copie e cole o conteúdo do arquivo SQL gerado`);
    console.log(`5. Clique em "Run" para executar o script SQL`);
    console.log(`\n====================================\n`);
  } catch (error) {
    console.error('Erro durante a recriação das tabelas:', error);
  }
}

// Executar a função principal
recreateTables();
