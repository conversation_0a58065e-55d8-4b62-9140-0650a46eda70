import { useEffect } from 'react';
import { useLocation } from 'wouter';
import GlobalAdminLayout from '@/components/global-admin/GlobalAdminLayout';
import MetricCard from '@/components/global-admin/MetricCard';
import { useGlobalAdminGuard, useGlobalAnalytics } from '@/hooks/useGlobalAdmin';
import { useTranslation } from '@/hooks/useTranslation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Store,
  Users,
  DollarSign,
  ShoppingCart,
  Crown,
  CreditCard
} from 'lucide-react';

export default function GlobalDashboard() {
  const [, setLocation] = useLocation();
  const { t } = useTranslation();
  const { data: adminUser, isLoading: isLoadingUser, error: userError } = useGlobalAdminGuard();
  const { data: analytics, isLoading: isLoadingAnalytics, error: analyticsError } = useGlobalAnalytics();

  // Redirecionar se não for admin global
  useEffect(() => {
    if (!isLoadingUser && (!adminUser || !adminUser.isGlobalAdmin)) {
      setLocation('/admin');
    }
  }, [adminUser, isLoadingUser, setLocation]);

  // Mostrar loading enquanto verifica permissões
  if (isLoadingUser) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Crown className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
          <p className="text-gray-600">{t('common.loading')}</p>
        </div>
      </div>
    );
  }

  // Mostrar erro se não conseguir verificar usuário
  if (userError || !adminUser?.isGlobalAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Alert className="max-w-md">
          <AlertDescription>
            Acesso negado. Permissões de super-administrador necessárias.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(1)}%`;
  };

  return (
    <GlobalAdminLayout
      title={t('globalAdmin.title')}
      description={t('globalAdmin.subtitle')}
    >
      {/* Cards de métricas principais */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <MetricCard
          title={t('globalAdmin.analytics.totalStores')}
          value={analytics?.summary.totalStores || 0}
          icon={<Store className="h-4 w-4" />}
          isLoading={isLoadingAnalytics}
          description={analytics ? `${analytics.summary.activeStores} ativas` : undefined}
        />

        <MetricCard
          title={t('globalAdmin.analytics.premiumStores')}
          value={analytics?.summary.premiumStores || 0}
          icon={<Crown className="h-4 w-4 text-yellow-500" />}
          isLoading={isLoadingAnalytics}
          description={analytics ? `${analytics.subscriptionDistribution.free} gratuitas` : undefined}
        />

        <MetricCard
          title={t('globalAdmin.analytics.totalRevenue')}
          value={formatCurrency(analytics?.summary.totalRevenue || 0)}
          icon={<DollarSign className="h-4 w-4" />}
          isLoading={isLoadingAnalytics}
          trend={analytics && analytics.summary.revenueGrowth !== 0 ? {
            value: analytics.summary.revenueGrowth,
            label: 'vs mês anterior'
          } : undefined}
        />

        <MetricCard
          title={t('globalAdmin.analytics.totalOrders')}
          value={analytics?.summary.totalOrders || 0}
          icon={<ShoppingCart className="h-4 w-4" />}
          isLoading={isLoadingAnalytics}
          trend={analytics && analytics.summary.orderGrowth !== 0 ? {
            value: analytics.summary.orderGrowth,
            label: 'vs mês anterior'
          } : undefined}
        />
      </div>

      {/* Top Lojas */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Top por Pedidos */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <ShoppingCart className="h-5 w-5" />
              <span>{t('globalAdmin.topStores.byOrders')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoadingAnalytics ? (
              <div className="space-y-3">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="flex items-center justify-between">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-4 w-16" />
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-3">
                {analytics?.topStoresByOrders.slice(0, 5).map((store, index) => (
                  <div key={store.storeId} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center">
                        {index + 1}
                      </Badge>
                      <div>
                        <p className="font-medium text-sm">{store.storeName}</p>
                        <p className="text-xs text-muted-foreground">/{store.storeSlug}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{store.orderCount}</p>
                      <p className="text-xs text-muted-foreground">{t('globalAdmin.topStores.orders')}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Top por Receita */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <DollarSign className="h-5 w-5" />
              <span>{t('globalAdmin.topStores.byRevenue')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoadingAnalytics ? (
              <div className="space-y-3">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="flex items-center justify-between">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-4 w-20" />
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-3">
                {analytics?.topStoresByRevenue.slice(0, 5).map((store, index) => (
                  <div key={store.storeId} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center">
                        {index + 1}
                      </Badge>
                      <div>
                        <p className="font-medium text-sm">{store.storeName}</p>
                        <p className="text-xs text-muted-foreground">/{store.storeSlug}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{formatCurrency(store.revenue)}</p>
                      <p className="text-xs text-muted-foreground">{t('globalAdmin.topStores.revenue')}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Distribuição de Assinaturas */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <CreditCard className="h-5 w-5" />
            <span>{t('globalAdmin.subscriptions.distribution')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoadingAnalytics ? (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="text-center">
                  <Skeleton className="h-8 w-16 mx-auto mb-2" />
                  <Skeleton className="h-4 w-20 mx-auto" />
                </div>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {analytics?.subscriptionDistribution.free || 0}
                </div>
                <p className="text-sm text-muted-foreground">{t('globalAdmin.subscriptions.free')}</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">
                  {analytics?.subscriptionDistribution.premium || 0}
                </div>
                <p className="text-sm text-muted-foreground">{t('globalAdmin.subscriptions.premium')}</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">
                  {analytics?.subscriptionDistribution.canceled || 0}
                </div>
                <p className="text-sm text-muted-foreground">{t('globalAdmin.subscriptions.canceled')}</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {analytics?.subscriptionDistribution.pastDue || 0}
                </div>
                <p className="text-sm text-muted-foreground">{t('globalAdmin.subscriptions.pastDue')}</p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {analyticsError && (
        <Alert className="mt-6">
          <AlertDescription>
            Erro ao carregar analytics: {analyticsError.message}
          </AlertDescription>
        </Alert>
      )}
    </GlobalAdminLayout>
  );
}
