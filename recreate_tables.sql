-- <PERSON><PERSON><PERSON> para recriar todas as tabelas do sistema

-- <PERSON><PERSON>, remover todas as tabelas existentes (na ordem correta para evitar problemas de referência)
DROP TABLE IF EXISTS cart_items CASCADE;
DROP TABLE IF EXISTS order_items CASCADE;
DROP TABLE IF EXISTS orders CASCADE;
DROP TABLE IF EXISTS customers CASCADE;
DROP TABLE IF EXISTS store_visits CASCADE;
DROP TABLE IF EXISTS variation_options CASCADE;
DROP TABLE IF EXISTS product_variations CASCADE;
DROP TABLE IF EXISTS products CASCADE;
DROP TABLE IF EXISTS categories CASCADE;
DROP TABLE IF EXISTS stores CASCADE;
DROP TABLE IF EXISTS users CASCADE;

-- Criar tabela de usuários
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  username VARCHAR(100) NOT NULL,
  email VARCHAR(255) UNIQUE,
  password VARCHAR(255),
  full_name <PERSON><PERSON><PERSON><PERSON>(255),
  first_name <PERSON><PERSON><PERSON><PERSON>(100),
  last_name <PERSON><PERSON><PERSON><PERSON>(100),
  bio TEXT,
  profile_image_url TEXT,
  firebase_uid VARCHAR(255) UNIQUE,
  created_at TIMES<PERSON>MP NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Criar tabela de lojas
CREATE TABLE stores (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id),
  name VARCHAR(100) NOT NULL,
  slug VARCHAR(100) UNIQUE NOT NULL,
  description TEXT,
  logo TEXT,
  header_image TEXT,
  country_code VARCHAR(5) DEFAULT '+55',
  whatsapp VARCHAR(20),
  instagram VARCHAR(100),
  currency VARCHAR(3) DEFAULT 'BRL',
  colors JSONB DEFAULT '{"primary": "#6082e6", "secondary": "#577590", "accent": "#F4A261"}',
  payment_methods JSONB DEFAULT '{"pix": true, "cash": true, "creditCard": false, "debitCard": false, "bankTransfer": false, "customMethods": []}',
  delivery_settings JSONB DEFAULT '{"allowDelivery": true, "allowPickup": true, "deliveryFee": 0, "minAdvanceDays": 0, "deliveryDays": [], "pickupDays": [], "deliveryTimeSlots": [], "pickupTimeSlots": [], "unavailablePeriods": [], "customMessage": ""}',
  layout_type INTEGER DEFAULT 1,
  layout_settings JSONB DEFAULT '{}',
  created_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Criar tabela de categorias
CREATE TABLE categories (
  id SERIAL PRIMARY KEY,
  store_id INTEGER NOT NULL REFERENCES stores(id) ON DELETE CASCADE,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  logo TEXT,
  display_order INTEGER DEFAULT 0,
  visible BOOLEAN DEFAULT true
);

-- Criar tabela de produtos
CREATE TABLE products (
  id SERIAL PRIMARY KEY,
  store_id INTEGER NOT NULL REFERENCES stores(id) ON DELETE CASCADE,
  category_id INTEGER REFERENCES categories(id) ON DELETE SET NULL,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  price REAL NOT NULL,
  images TEXT[] DEFAULT '{}',
  in_stock BOOLEAN DEFAULT true,
  has_variations BOOLEAN DEFAULT false,
  variations JSONB DEFAULT '[]',
  visible BOOLEAN DEFAULT true,
  created_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Criar tabela de variações de produtos
CREATE TABLE product_variations (
  id SERIAL PRIMARY KEY,
  product_id INTEGER NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  name VARCHAR(100) NOT NULL,
  required BOOLEAN DEFAULT false,
  multiple_choice BOOLEAN DEFAULT false
);

-- Criar tabela de opções de variação
CREATE TABLE variation_options (
  id SERIAL PRIMARY KEY,
  variation_id INTEGER NOT NULL REFERENCES product_variations(id) ON DELETE CASCADE,
  name VARCHAR(100) NOT NULL,
  price REAL DEFAULT 0
);

-- Criar tabela de clientes
CREATE TABLE customers (
  id SERIAL PRIMARY KEY,
  store_id INTEGER NOT NULL REFERENCES stores(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255),
  country_code VARCHAR(5) DEFAULT '+55',
  whatsapp VARCHAR(20) NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Criar tabela de pedidos
CREATE TABLE orders (
  id SERIAL PRIMARY KEY,
  store_id INTEGER NOT NULL REFERENCES stores(id) ON DELETE CASCADE,
  customer_id INTEGER NOT NULL REFERENCES customers(id),
  status VARCHAR(50) NOT NULL DEFAULT 'pending',
  receiving_method VARCHAR(50) NOT NULL,
  receiving_date TIMESTAMP NOT NULL,
  receiving_time VARCHAR(50),
  delivery_address JSONB,
  payment_method VARCHAR(100) NOT NULL,
  subtotal REAL NOT NULL,
  delivery_fee REAL DEFAULT 0,
  total REAL NOT NULL,
  notes TEXT,
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Criar tabela de itens de pedido
CREATE TABLE order_items (
  id SERIAL PRIMARY KEY,
  order_id INTEGER NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
  product_id INTEGER NOT NULL REFERENCES products(id),
  product_name VARCHAR(255) NOT NULL,
  quantity INTEGER NOT NULL,
  unit_price REAL NOT NULL,
  selected_variations JSONB DEFAULT '[]',
  observation TEXT,
  subtotal REAL NOT NULL
);

-- Criar tabela de visitas à loja
CREATE TABLE store_visits (
  id SERIAL PRIMARY KEY,
  store_id INTEGER NOT NULL REFERENCES stores(id) ON DELETE CASCADE,
  session_id VARCHAR(255),
  user_id INTEGER REFERENCES users(id),
  page VARCHAR(255),
  visitor_ip VARCHAR(50),
  user_agent TEXT,
  visit_date TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Criar tabela de itens do carrinho
CREATE TABLE cart_items (
  id SERIAL PRIMARY KEY,
  store_id INTEGER NOT NULL REFERENCES stores(id) ON DELETE CASCADE,
  user_id VARCHAR,
  session_id VARCHAR NOT NULL,
  product_id INTEGER NOT NULL REFERENCES products(id),
  quantity INTEGER NOT NULL DEFAULT 1,
  price REAL NOT NULL,
  selected_options JSONB NOT NULL DEFAULT '[]',
  observation TEXT,
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP NOT NULL DEFAULT NOW()
);

-- Adicionar tabela de cupons de desconto
CREATE TABLE coupons (
  id SERIAL PRIMARY KEY,
  store_id INTEGER NOT NULL REFERENCES stores(id) ON DELETE CASCADE,
  code VARCHAR(50) NOT NULL,
  type VARCHAR(10) CHECK (type IN ('%', 'R$')) NOT NULL,
  value NUMERIC NOT NULL,
  min_purchase NUMERIC,
  expiration_date DATE NOT NULL,
  single_use BOOLEAN DEFAULT FALSE,
  active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  UNIQUE(store_id, code)
);

-- Tabela para rastrear uso de cupons
CREATE TABLE coupon_usage (
  id SERIAL PRIMARY KEY,
  coupon_id INTEGER NOT NULL REFERENCES coupons(id) ON DELETE CASCADE,
  customer_id INTEGER NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
  order_id INTEGER NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
  used_at TIMESTAMP NOT NULL DEFAULT NOW(),
  UNIQUE(coupon_id, order_id)
);

-- Adicionar índices para melhor performance
CREATE INDEX idx_stores_user_id ON stores(user_id);
CREATE INDEX idx_stores_slug ON stores(slug);
CREATE INDEX idx_products_store_id ON products(store_id);
CREATE INDEX idx_products_category_id ON products(category_id);
CREATE INDEX idx_categories_store_id ON categories(store_id);
CREATE INDEX idx_product_variations_product_id ON product_variations(product_id);
CREATE INDEX idx_variation_options_variation_id ON variation_options(variation_id);
CREATE INDEX idx_orders_store_id ON orders(store_id);
CREATE INDEX idx_orders_customer_id ON orders(customer_id);
CREATE INDEX idx_order_items_order_id ON order_items(order_id);
CREATE INDEX idx_store_visits_store_id ON store_visits(store_id);
CREATE INDEX idx_cart_items_store_session ON cart_items(store_id, session_id);
CREATE INDEX idx_cart_items_user_id ON cart_items(user_id) WHERE user_id IS NOT NULL;
CREATE INDEX idx_coupons_store_id ON coupons(store_id);
CREATE INDEX idx_coupons_code ON coupons(code);
CREATE INDEX idx_coupon_usage_coupon_id ON coupon_usage(coupon_id);
CREATE INDEX idx_coupon_usage_customer_id ON coupon_usage(customer_id);
