-- Script para adicionar o campo discount_type à tabela order_revisions
-- Execute este script diretamente no console SQL do Supabase ou em qualquer banco PostgreSQL

-- 1. Adicionar a coluna discount_type à tabela order_revisions se não existir
ALTER TABLE order_revisions 
ADD COLUMN IF NOT EXISTS discount_type VARCHAR(10) DEFAULT 'fixed';

-- 2. Adicionar comentário para documentação
COMMENT ON COLUMN order_revisions.discount_type IS 'Tipo de desconto: fixed (valor fixo) ou percentage (percentual)';

-- 3. Atualizar a função create_order_revision para incluir o campo discount_type
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'create_order_revision') THEN
    DROP FUNCTION IF EXISTS create_order_revision;
    
    CREATE OR REPLACE FUNCTION create_order_revision(order_id_param INTEGER)
    RETURNS INTEGER AS $$
    DECLARE
      new_revision_id INTEGER;
      new_revision_number INTEGER;
      order_record RECORD;
    BEGIN
      -- Get the next revision number for this order
      SELECT COALESCE(MAX(revision_number), 0) + 1 INTO new_revision_number
      FROM order_revisions
      WHERE order_id = order_id_param;
      
      -- Get the order data
      SELECT * INTO order_record
      FROM orders
      WHERE id = order_id_param;
      
      -- Create the new revision
      INSERT INTO order_revisions (
        order_id,
        revision_number,
        status,
        receiving_method,
        receiving_date,
        receiving_time,
        delivery_address,
        payment_method,
        subtotal,
        delivery_fee,
        discount,
        discount_type,
        coupon_id,
        coupon_code,
        coupon_type,
        total,
        notes,
        is_current,
        customer_id
      ) VALUES (
        order_id_param,
        new_revision_number,
        order_record.status,
        order_record.receiving_method,
        order_record.receiving_date,
        order_record.receiving_time,
        order_record.delivery_address,
        order_record.payment_method,
        order_record.subtotal,
        order_record.delivery_fee,
        order_record.discount,
        order_record.discount_type,
        order_record.coupon_id,
        order_record.coupon_code,
        order_record.coupon_type,
        order_record.total,
        order_record.notes,
        FALSE, -- New revision is not current by default
        order_record.customer_id -- Copia o ID do cliente do pedido original
      ) RETURNING id INTO new_revision_id;
      
      -- Copy order items to revision items
      INSERT INTO order_revision_items (
        revision_id,
        product_id,
        product_name,
        product_description,
        product_image,
        quantity,
        unit_price,
        subtotal,
        selected_variations,
        observation
      )
      SELECT
        new_revision_id,
        product_id,
        product_name,
        product_description,
        product_image,
        quantity,
        unit_price,
        subtotal,
        selected_variations,
        observation
      FROM order_items
      WHERE order_id = order_id_param;
      
      RETURN new_revision_id;
    END;
    $$ LANGUAGE plpgsql;
  END IF;
END $$;

-- 4. Atualizar registros existentes para usar o tipo 'fixed' como padrão
UPDATE order_revisions
SET discount_type = 'fixed'
WHERE discount_type IS NULL AND discount > 0;

-- 5. Adicionar a coluna discount_type à tabela orders se não existir
ALTER TABLE orders 
ADD COLUMN IF NOT EXISTS discount_type VARCHAR(10) DEFAULT 'fixed';

-- 6. Adicionar comentário para documentação
COMMENT ON COLUMN orders.discount_type IS 'Tipo de desconto: fixed (valor fixo) ou percentage (percentual)';

-- 7. Atualizar registros existentes na tabela orders para usar o tipo 'fixed' como padrão
UPDATE orders
SET discount_type = 'fixed'
WHERE discount_type IS NULL AND discount > 0;
