import { useState, useMemo } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { OrderFilters, OrderFilters as OrderFiltersComponent } from "./OrderFilters";
import { Clock, Phone, Eye, AlertTriangle } from "lucide-react";

interface PendingOrder {
  id: number;
  customerName: string;
  customerPhone: string;
  products: string;
  total: number;
  status: string;
  deliveryTime: string;
  isLate: boolean;
}

interface PendingOrdersListProps {
  orders: PendingOrder[];
  onViewOrder?: (orderId: number) => void;
}

export function PendingOrdersList({ orders, onViewOrder }: PendingOrdersListProps) {
  const [filters, setFilters] = useState<OrderFilters>({
    status: '',
    paymentStatus: '',
    deliveryDate: '',
    isLate: null,
    customerSearch: ''
  });

  // Filtrar pedidos baseado nos filtros aplicados
  const filteredOrders = useMemo(() => {
    return orders.filter(order => {
      // Filtro por status
      if (filters.status && order.status !== filters.status) {
        return false;
      }

      // Filtro por situação de entrega (atrasado)
      if (filters.isLate !== null && order.isLate !== filters.isLate) {
        return false;
      }

      // Filtro por busca de cliente
      if (filters.customerSearch) {
        const searchTerm = filters.customerSearch.toLowerCase();
        const customerMatch = order.customerName.toLowerCase().includes(searchTerm) ||
                             order.customerPhone.includes(searchTerm);
        if (!customerMatch) {
          return false;
        }
      }

      // Filtro por data de entrega
      if (filters.deliveryDate) {
        const orderDate = order.deliveryTime.split(' ')[0]; // Extrai a data
        if (orderDate !== filters.deliveryDate) {
          return false;
        }
      }

      return true;
    });
  }, [orders, filters]);

  const getStatusBadge = (status: string, isLate: boolean) => {
    if (isLate) {
      return <Badge variant="destructive" className="gap-1">
        <AlertTriangle className="w-3 h-3" />
        Atrasado
      </Badge>;
    }

    switch (status) {
      case 'pending':
        return <Badge variant="secondary">Pendente</Badge>;
      case 'confirmed':
        return <Badge variant="default">Confirmado</Badge>;
      case 'delivered':
        return <Badge variant="secondary">Entregue</Badge>;
      case 'cancelled':
        return <Badge variant="destructive">Cancelado</Badge>;
      // Support legacy statuses temporarily
      case 'preparing':
      case 'processing':
        return <Badge variant="default">Confirmado</Badge>;
      case 'ready':
      case 'completed':
        return <Badge variant="secondary">Entregue</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const formatDeliveryTime = (deliveryTime: string) => {
    try {
      const [dateStr, timeStr] = deliveryTime.split(' ');
      const [year, month, day] = dateStr.split('-');
      return `${day}/${month}/${year} ${timeStr}`;
    } catch {
      return deliveryTime;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Pedidos em Andamento</span>
          <Badge variant="outline">{filteredOrders.length}</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Componente de filtros */}
        <OrderFiltersComponent
          onFiltersChange={setFilters}
          totalCount={orders.length}
          filteredCount={filteredOrders.length}
        />

        {/* Lista de pedidos */}
        <div className="space-y-3">
          {filteredOrders.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              {orders.length === 0 ? (
                "Nenhum pedido em andamento"
              ) : (
                "Nenhum pedido encontrado com os filtros aplicados"
              )}
            </div>
          ) : (
            filteredOrders.map((order) => (
              <div
                key={order.id}
                className={`border rounded-lg p-4 hover:bg-gray-50 transition-colors ${
                  order.isLate ? 'border-red-200 bg-red-50' : 'border-gray-200'
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 space-y-2">
                    {/* Cabeçalho do pedido */}
                    <div className="flex items-center space-x-3">
                      <h4 className="font-medium text-gray-900">
                        #{order.id} - {order.customerName}
                      </h4>
                      {getStatusBadge(order.status, order.isLate)}
                    </div>

                    {/* Informações do pedido */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                      <div className="flex items-center space-x-2">
                        <Phone className="w-4 h-4" />
                        <span>{order.customerPhone}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Clock className="w-4 h-4" />
                        <span>{formatDeliveryTime(order.deliveryTime)}</span>
                      </div>
                      <div className="font-medium text-gray-900">
                        {formatCurrency(order.total)}
                      </div>
                    </div>

                    {/* Produtos (se disponível) */}
                    {order.products && (
                      <div className="text-sm text-gray-600">
                        <span className="font-medium">Produtos:</span> {order.products}
                      </div>
                    )}
                  </div>

                  {/* Ações */}
                  <div className="flex items-center space-x-2">
                    {onViewOrder && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onViewOrder(order.id)}
                      >
                        <Eye className="w-4 h-4 mr-2" />
                        Ver Detalhes
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
}