/* Estilos para impressão de pedidos */

/* Configurações gerais */
.print-order {
  font-family: 'Arial', sans-serif;
  color: #333;
  line-height: 1.4;
  max-width: 100%;
  margin: 0 auto;
  padding: 0;
  box-sizing: border-box;
}

/* Container para preview com scroll horizontal - manter dimensões A4 fixas */
.print-preview-container {
  width: 100%;
  overflow-x: auto;
  overflow-y: visible;
  padding: 20px 0;
  background-color: #f5f5f5;
  border-radius: 8px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Estilos específicos para preview (não para geração de PDF) */
.print-order.print-preview {
  width: 794px !important; /* A4 width em pixels - FIXO */
  max-width: 794px !important;
  min-width: 794px !important;
  min-height: 1123px !important; /* A4 height em pixels - FIXO */
  margin: 0 auto; /* Centralizar horizontalmente */
  background-color: #ffffff;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  overflow: visible;
  position: relative;
  font-size: 12px;
  line-height: 1.4;
  color: #333;
  padding: 19px; /* 5mm de margem em pixels */
}

/* Garantir que o preview seja idêntico ao PDF em todos os dispositivos */
@media screen and (max-width: 768px) {
  .print-preview-container {
    padding: 10px 0;
  }

  .print-order.print-preview {
    width: 794px !important; /* Manter A4 fixo mesmo em mobile */
    max-width: 794px !important;
    min-width: 794px !important;
    min-height: 1123px !important;
    margin: 0 auto;
    /* Permitir scroll horizontal quando necessário */
  }
}

@media screen and (min-width: 768px) and (max-width: 1024px) {
  .print-order.print-preview {
    width: 794px !important; /* Manter A4 fixo em tablets */
    max-width: 794px !important;
    min-width: 794px !important;
    min-height: 1123px !important;
    margin: 0 auto;
  }
}

@media screen and (min-width: 1024px) {
  .print-order.print-preview {
    width: 794px !important; /* Manter A4 fixo em desktop */
    max-width: 794px !important;
    min-width: 794px !important;
    min-height: 1123px !important;
    margin: 0 auto;
  }
}

/* Estilos específicos para geração de PDF - garantem formato A4 consistente */
.print-order.pdf-generation {
  width: 794px !important; /* A4 width em pixels (210mm * 3.78 pixels/mm) */
  max-width: 794px !important;
  min-width: 794px !important;
  min-height: 1123px !important; /* A4 height em pixels (297mm * 3.78 pixels/mm) */
  padding: 19px !important; /* 5mm de margem em pixels (reduzida) */
  margin: 0 !important;
  background-color: #ffffff !important;
  font-size: 12px !important;
  line-height: 1.4 !important;
  box-sizing: border-box !important;
  transform: none !important;
  position: relative !important;
  left: 0 !important;
  top: 0 !important;
  z-index: 9999 !important;
  visibility: visible !important;
  overflow: visible !important;
  border: none !important;
  box-shadow: none !important;
  display: block !important;
}

/* Ajustes para elementos dentro do PDF */
.print-order.pdf-generation * {
  box-sizing: border-box !important;
}

/* Cabeçalho otimizado para PDF */
.print-order.pdf-generation .print-header {
  display: grid !important;
  grid-template-columns: 120px 1fr 180px !important;
  gap: 20px !important;
  margin-bottom: 20px !important;
  padding-bottom: 10px !important;
  align-items: start !important;
}

/* Logo otimizada para PDF */
.print-order.pdf-generation .print-header-logo {
  width: 120px !important;
  height: auto !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background-color: #f5f5f5 !important;
  padding: 10px !important;
  border-radius: 4px !important;
}

.print-order.pdf-generation .print-header-logo .logo {
  max-width: 100px !important;
  max-height: 60px !important;
  object-fit: contain !important;
}

.print-order.pdf-generation .logo-fallback {
  width: 60px !important;
  height: 60px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background-color: #f0f0f0 !important;
  border-radius: 8px !important;
  font-size: 24px !important;
  font-weight: bold !important;
  color: #666 !important;
}

/* Títulos otimizados para PDF */
.print-order.pdf-generation .print-header-center h1 {
  font-size: 20px !important;
  margin: 0 0 5px 0 !important;
  font-weight: bold !important;
}

.print-order.pdf-generation .print-header-center h2 {
  font-size: 16px !important;
  margin: 0 0 10px 0 !important;
  font-weight: bold !important;
}

/* Informações do cabeçalho */
.print-order.pdf-generation .print-header-info {
  font-size: 11px !important;
  line-height: 1.3 !important;
}

.print-order.pdf-generation .print-header-right {
  text-align: right !important;
  font-size: 11px !important;
  width: 180px !important;
}

/* Tabela de produtos otimizada para PDF */
.print-order.pdf-generation .print-products-table {
  width: 100% !important;
  border-collapse: collapse !important;
  margin-bottom: 15px !important;
  font-size: 11px !important;
  table-layout: fixed !important;
}

.print-order.pdf-generation .print-products-table th {
  background-color: #f5f5f5 !important;
  padding: 6px !important;
  text-align: left !important;
  font-weight: bold !important;
  border-bottom: 2px solid #ddd !important;
  font-size: 11px !important;
}

.print-order.pdf-generation .print-products-table td {
  padding: 6px !important;
  border-bottom: 1px solid #eee !important;
  vertical-align: top !important;
  font-size: 10px !important;
}

/* Seções otimizadas para PDF */
.print-order.pdf-generation .print-section {
  margin-bottom: 15px !important;
  padding: 10px !important;
  border-radius: 4px !important;
  background-color: #f9f9f9 !important;
  border: 1px solid #eee !important;
  page-break-inside: avoid !important;
}

.print-order.pdf-generation .print-section-title {
  font-size: 12px !important;
  font-weight: bold !important;
  margin-bottom: 8px !important;
  padding-bottom: 5px !important;
  border-bottom: 1px solid #eee !important;
  display: flex !important;
  align-items: center !important;
}

/* Grid de informações do cliente otimizado para PDF */
.print-order.pdf-generation .print-customer-details-grid {
  display: grid !important;
  grid-template-columns: 1fr 1fr !important;
  gap: 15px !important;
}

.print-order.pdf-generation .print-info-row {
  display: flex !important;
  align-items: flex-start !important;
  margin-bottom: 3px !important;
  line-height: 1.3 !important;
  font-size: 10px !important;
}

.print-order.pdf-generation .print-info-label {
  font-weight: bold !important;
  min-width: 80px !important;
  display: flex !important;
  align-items: center !important;
  font-size: 10px !important;
}

/* Totais otimizados para PDF */
.print-order.pdf-generation .print-totals {
  margin-top: 15px !important;
  padding: 10px !important;
  background-color: #f9f9f9 !important;
  border-radius: 4px !important;
  border: 1px solid #eee !important;
  page-break-inside: avoid !important;
}

.print-order.pdf-generation .print-total-row {
  display: flex !important;
  justify-content: space-between !important;
  margin-bottom: 5px !important;
  font-size: 11px !important;
}

/* Rodapé otimizado para PDF */
.print-order.pdf-generation .print-footer {
  margin-top: 20px !important;
  padding-top: 15px !important;
  border-top: 1px solid #ddd !important;
  text-align: center !important;
  font-size: 10px !important;
  color: #666 !important;
  page-break-inside: avoid !important;
}

/* Ícones otimizados para PDF */
.print-order.pdf-generation svg {
  width: 12px !important;
  height: 12px !important;
  margin-right: 4px !important;
}

/* Quebras de página */
.print-order.pdf-generation .page-break {
  page-break-before: always !important;
}

.print-order.pdf-generation .no-page-break {
  page-break-inside: avoid !important;
}

/* Estilos responsivos para dispositivos móveis durante geração de PDF */
@media screen and (max-width: 768px) {
  .print-order.pdf-generation {
    width: 794px !important; /* Manter largura A4 mesmo em mobile */
    max-width: 794px !important;
    min-width: 794px !important;
    min-height: 1123px !important; /* A4 height em pixels */
    padding: 19px !important; /* 5mm de margem em pixels */
    margin: 0 !important;
    transform: none !important;
    position: relative !important;
    left: 0 !important;
    top: 0 !important;
    overflow: visible !important;
    background-color: #ffffff !important;
    border: none !important;
    box-shadow: none !important;
  }

  /* Garantir que o cabeçalho mantenha o layout em mobile */
  .print-order.pdf-generation .print-header {
    display: grid !important;
    grid-template-columns: 120px 1fr 180px !important;
    gap: 15px !important;
    margin-bottom: 20px !important;
  }

  /* Ajustar grid de informações do cliente para mobile */
  .print-order.pdf-generation .print-customer-details-grid {
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    gap: 10px !important;
  }

  /* Manter tabela de produtos com layout fixo em mobile */
  .print-order.pdf-generation .print-products-table {
    width: 100% !important;
    table-layout: fixed !important;
    font-size: 10px !important;
  }

  .print-order.pdf-generation .print-products-table th,
  .print-order.pdf-generation .print-products-table td {
    padding: 4px !important;
    font-size: 9px !important;
  }
}

/* Estilos para dispositivos com alta densidade de pixels */
@media screen and (-webkit-min-device-pixel-ratio: 2),
       screen and (min-resolution: 192dpi) {
  .print-order.pdf-generation {
    width: 794px !important;
    max-width: 794px !important;
    min-width: 794px !important;
    padding: 38px !important;
    font-size: 12px !important;
    line-height: 1.4 !important;
  }

  .print-order.pdf-generation .print-header-logo .logo {
    max-width: 100px !important;
    max-height: 60px !important;
    object-fit: contain !important;
  }

  .print-order.pdf-generation svg {
    width: 12px !important;
    height: 12px !important;
  }
}

/* Estilos específicos para tablets */
@media screen and (min-width: 768px) and (max-width: 1024px) {
  .print-order.pdf-generation {
    width: 794px !important;
    max-width: 794px !important;
    min-width: 794px !important;
    min-height: 1123px !important; /* A4 height em pixels */
    padding: 19px !important; /* 5mm de margem em pixels */
    margin: 0 !important;
    background-color: #ffffff !important;
    border: none !important;
    box-shadow: none !important;
  }

  .print-order.pdf-generation .print-header {
    grid-template-columns: 120px 1fr 180px !important;
    gap: 20px !important;
  }
}

/* Estilos específicos para desktop - corrigir distorção e cobertura */
@media screen and (min-width: 1024px) {
  .print-order.pdf-generation {
    width: 794px !important;
    max-width: 794px !important;
    min-width: 794px !important;
    min-height: 1123px !important; /* A4 height em pixels */
    padding: 19px !important; /* 5mm de margem em pixels */
    margin: 0 !important;
    transform: none !important;
    position: relative !important;
    left: 0 !important;
    top: 0 !important;
    overflow: visible !important;
    background-color: #ffffff !important;
    border: none !important;
    box-shadow: none !important;
    display: block !important;
  }

  /* Garantir que o cabeçalho mantenha o layout no desktop */
  .print-order.pdf-generation .print-header {
    display: grid !important;
    grid-template-columns: 120px 1fr 180px !important;
    gap: 20px !important;
    margin-bottom: 20px !important;
    align-items: start !important;
  }

  /* Ajustar grid de informações do cliente para desktop */
  .print-order.pdf-generation .print-customer-details-grid {
    display: grid !important;
    grid-template-columns: 1fr 1fr !important;
    gap: 20px !important;
  }

  /* Manter tabela de produtos com layout fixo no desktop */
  .print-order.pdf-generation .print-products-table {
    width: 100% !important;
    table-layout: fixed !important;
    font-size: 11px !important;
    border-collapse: collapse !important;
  }

  .print-order.pdf-generation .print-products-table th,
  .print-order.pdf-generation .print-products-table td {
    padding: 6px !important;
    font-size: 10px !important;
    border-bottom: 1px solid #eee !important;
  }

  .print-order.pdf-generation .print-products-table th {
    background-color: #f5f5f5 !important;
    font-weight: bold !important;
    border-bottom: 2px solid #ddd !important;
  }

  /* Garantir que seções mantenham layout correto */
  .print-order.pdf-generation .print-section {
    margin-bottom: 15px !important;
    padding: 10px !important;
    border-radius: 4px !important;
    background-color: #f9f9f9 !important;
    border: 1px solid #eee !important;
  }

  /* Totais otimizados para desktop */
  .print-order.pdf-generation .print-totals {
    margin-top: 15px !important;
    padding: 10px !important;
    background-color: #f9f9f9 !important;
    border-radius: 4px !important;
    border: 1px solid #eee !important;
  }
}

/* Garantir que elementos não sejam cortados durante a geração */
.print-order.pdf-generation .print-section,
.print-order.pdf-generation .print-totals,
.print-order.pdf-generation .print-footer {
  page-break-inside: avoid !important;
  break-inside: avoid !important;
}

/* Otimizações para variações de produtos */
.print-order.pdf-generation .print-variations-container {
  margin-top: 5px !important;
  margin-bottom: 5px !important;
}

.print-order.pdf-generation .print-product-variation {
  font-size: 9px !important;
  line-height: 1.2 !important;
  margin-bottom: 1px !important;
}

.print-order.pdf-generation .print-variation-name {
  font-weight: bold !important;
  font-size: 9px !important;
}

.print-order.pdf-generation .print-option-price {
  font-size: 9px !important;
  font-weight: 600 !important;
}

/* Versão para impressora térmica */
@media print and (max-width: 80mm) {
  .print-order {
    width: 80mm;
    font-size: 10px;
  }

  .print-order h1 {
    font-size: 14px;
  }

  .print-order h2 {
    font-size: 12px;
  }
}

/* Versão para A4 */
@media print and (min-width: 210mm) {
  .print-order {
    width: 210mm;
    padding: 10mm;
  }
}

/* Cabeçalho */
.print-header {
  display: grid;
  grid-template-columns: min-content 3fr 1fr;
  gap: 0 20px;
  margin-bottom: 20px;
  padding-bottom: 10px;
}

.print-header-logo {
  text-align: left;
  display: flex;
  align-items: center;
  height: fit-content;
  width: fit-content;
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
}

.print-header-logo .logo {
  max-height: 60px;
  max-width: 120px;
  object-fit: contain;
}

.logo-fallback {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  border-radius: 8px;
  font-size: 32px;
  font-weight: bold;
  color: #666;
}

.print-header-center {
  text-align: left;
}

.print-header-center h1 {
  margin: 0 0 5px;
  font-size: 24px;
  font-weight: bold;
  text-transform: uppercase;
  text-align: left;
}

.print-header-center h2 {
  margin: 0 0 10px;
  font-size: 18px;
  font-weight: bold;
  text-align: left;
}

.print-header-info {
  font-size: 12px;
  line-height: 1.4;
  margin: 5px 0;
  text-align: left;
}

.print-header-info p {
  text-align: left;
}

.print-header-right {
  text-align: right;
  font-size: 12px;
}

.print-header-order-info {
  margin-bottom: 10px;
  font-weight: bold;
}

.print-status-container {
  display: flex;
  flex-direction: column;
  margin-bottom: 10px;
}

.print-status-label {
  margin-bottom: 5px;
}

.print-status-value {
  margin-left: 5px;
}

.print-info-block {
  margin-bottom: 10px;
}

.print-info-block .print-info-value {
  margin-top: 5px;
  margin-left: 5px;
}

.print-header hr {
  border: none;
  border-top: 1px solid #ddd;
  margin: 8px 0;
}

.print-order-info {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 10px;
  margin: 5px 0;
}

.print-order-info-item {
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 13px;
  font-weight: bold;
}

/* Informações do cliente e endereço - layout de duas colunas */
.print-customer-info-container {
  margin: 10px 0;
  font-size: 12px;
}

/* Grid para layout de duas colunas */
.print-customer-details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

/* Estilo para cada coluna */
.print-customer-column {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

/* Título da coluna */
.print-column-title {
  font-size: 12px;
  font-weight: bold;
  margin-bottom: 5px;
  padding-bottom: 3px;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: center;
}

.print-info-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 3px;
  line-height: 1.4;
}

.print-info-label {
  font-weight: bold;
  min-width: 90px;
  display: flex;
  align-items: center;
}

.print-info-value {
  flex: 1;
}

/* Ajustes para impressão */
@media print {
  .print-customer-info-container {
    font-size: 10px;
  }

  .print-customer-details-grid {
    gap: 10px;
  }

  .print-column-title {
    font-size: 11px;
    margin-bottom: 3px;
  }

  .print-info-label {
    min-width: 80px;
  }

  .print-info-row {
    margin-bottom: 2px;
  }
}

/* Versão para impressora térmica */
@media print and (max-width: 80mm) {
  .print-customer-info-container {
    font-size: 8px;
  }

  /* Mudar para uma coluna em impressoras térmicas */
  .print-customer-details-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .print-column-title {
    font-size: 9px;
    margin-bottom: 2px;
  }

  .print-info-label {
    min-width: 60px;
  }

  .print-info-row {
    margin-bottom: 1px;
  }

  .print-info-label .h-3,
  .print-info-label .w-3 {
    height: 0.5rem !important;
    width: 0.5rem !important;
  }
}

.print-order-details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  margin: 10px 0;
  font-size: 12px;
  text-align: left;
}

.print-order-details-item {
  display: flex;
  align-items: center;
}

.print-order-details-item svg {
  margin-right: 5px;
  width: 14px;
  height: 14px;
}

.print-status-badge {
  display: inline-flex;
  align-items: center;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 12px;
  font-weight: bold;
}

/* Versão para impressora térmica */
@media print and (max-width: 80mm) {
  .print-header .logo {
    max-height: 40px !important;
    max-width: 80px !important;
  }

  .print-header-logo {
    padding: 5px !important;
  }

  .print-header h1 {
    font-size: 14px;
  }

  .print-header-order-info {
    font-size: 10px;
  }

  .print-status-container {
    margin-bottom: 5px;
  }

  .print-status-label {
    margin-bottom: 2px;
  }

  .print-info-block {
    margin-bottom: 5px;
  }

  .print-info-block .print-info-value {
    margin-top: 2px;
    margin-left: 3px;
  }

  .print-order-info-item {
    font-size: 11px;
  }

  .print-order-details {
    grid-template-columns: 1fr;
  }

  /* Ajustes para seção de informações do cliente e endereço */
  .print-section {
    padding: 5px !important;
  }

  /* Reduzir margens e espaçamentos */
  .mb-4, .mb-6 {
    margin-bottom: 0.5rem !important;
  }

  .gap-4 {
    gap: 0.25rem !important;
  }
}

/* Seções de informações */
.print-section {
  margin-bottom: 15px;
  padding: 10px;
  border-radius: 4px;
  background-color: #f9f9f9;
  border: 1px solid #eee;
}

.print-section-title {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 8px;
  padding-bottom: 5px;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: center;
}

.print-section-title svg {
  margin-right: 5px;
}

.print-info-row {
  display: flex;
  margin-bottom: 5px;
  font-size: 12px;
}

.print-info-label {
  font-weight: bold;
  margin-right: 5px;
  min-width: 100px;
}

/* Status do pedido */
.print-status {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 3px;
  font-size: 12px;
  font-weight: bold;
}

.print-status-pending, .print-status-badge.print-status-pending {
  background-color: #e3f2fd;
  color: #1976d2;
}

.print-status-processing, .print-status-badge.print-status-processing {
  background-color: #fff8e1;
  color: #ff8f00;
}

.print-status-completed, .print-status-badge.print-status-completed {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.print-status-cancelled, .print-status-badge.print-status-cancelled {
  background-color: #ffebee;
  color: #c62828;
}

/* Tabela de produtos */
.print-products-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 15px;
  font-size: 12px;
}

.print-products-table th {
  background-color: #f5f5f5;
  padding: 8px;
  text-align: left;
  font-weight: bold;
  border-bottom: 2px solid #ddd;
}

.print-products-table td {
  padding: 8px;
  border-bottom: 1px solid #eee;
  vertical-align: top;
}

.print-products-table tr:nth-child(even) {
  background-color: #fafafa;
}

.print-product-name {
  font-weight: bold;
  margin-bottom: 5px;
}

.print-product-description {
  font-size: 11px;
  color: #666;
  font-style: italic;
  margin-bottom: 8px;
  padding-left: 5px;
}

.print-variations-container {
  margin-top: 8px;
  margin-bottom: 5px;
}

.print-variations-title {
  font-size: 11px;
  font-weight: bold;
  color: #555;
  margin-bottom: 3px;
}

.print-variations-list {
  padding-left: 10px;
}

.print-product-variation {
  font-size: 11px;
  color: #666;
  margin-bottom: 2px;
  line-height: 1.3;
  display: block;
}

.print-variation-name {
  font-weight: bold;
  color: #555;
}

.print-option-name {
  margin-left: 3px;
}

.print-option-quantity {
  color: #888;
  font-weight: bold;
}

.print-option-price {
  color: #2563eb;
  font-weight: 600;
}

.print-product-observation {
  font-size: 11px;
  font-style: italic;
  color: #666;
  margin-top: 8px;
  padding: 5px;
  background-color: #f8f9fa;
  border-left: 3px solid #dee2e6;
  border-radius: 3px;
}

/* Breakdown de preços */
.print-price-breakdown {
  text-align: right;
}

.print-base-price {
  font-weight: bold;
  margin-bottom: 2px;
}

.print-variations-prices {
  margin-top: 2px;
}

.print-variation-price {
  font-size: 10px;
  color: #2563eb;
  line-height: 1.2;
  margin-bottom: 1px;
}

.print-multiplication-info {
  font-size: 9px;
  color: #666;
  font-style: italic;
}

.print-text-right {
  text-align: right;
}

.print-text-center {
  text-align: center;
}

/* Totais */
.print-totals {
  margin-top: 15px;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
  border: 1px solid #eee;
}

.print-total-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 12px;
}

.print-total-label {
  font-weight: bold;
}

.print-discount {
  color: #2e7d32;
}

.print-coupon-code {
  font-size: 10px;
  color: #666;
  text-align: right;
}

.print-grand-total {
  margin-top: 10px;
  padding: 8px;
  background-color: #f5f5f5;
  border-radius: 4px;
  font-size: 14px;
  font-weight: bold;
  display: flex;
  justify-content: space-between;
}

/* Rodapé */
.print-footer {
  margin-top: 20px;
  text-align: center;
  font-size: 12px;
  color: #666;
  padding-top: 10px;
  border-top: 1px dashed #ccc;
}

.print-thank-you {
  font-weight: bold;
  font-size: 14px;
  margin-bottom: 5px;
}

.print-store-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 5px;
}

.print-store-name {
  font-weight: bold;
  margin-bottom: 5px;
}

.print-social-links {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 5px;
}

.print-social-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 11px;
}

/* Versão para impressora térmica */
@media print and (max-width: 80mm) {
  .print-social-links {
    flex-direction: column;
    gap: 5px;
  }
}

.print-generation-info {
  margin-bottom: 20px;
  text-align: right;
}

/* Utilitários */
.print-notes {
  font-style: italic;
  padding: 10px;
  background-color: #f9f9f9;
  border-left: 3px solid #ddd;
  margin-bottom: 15px;
  font-size: 12px;
}

/* Estilos para impressão */
@media print {
  body * {
    visibility: hidden;
  }

  .print-order, .print-order * {
    visibility: visible;
  }

  .print-order {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
  }

  .no-print {
    display: none !important;
  }
}