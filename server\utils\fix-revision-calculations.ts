/**
 * Utilitário para corrigir cálculos de revisões
 * Este script recalcula subtotais, descontos e totais 
 * para garantir consistência matemática
 */

import { supabase } from '../db';
import { storage } from '../storage';

/**
 * Recalcula os valores de uma revisão específica
 * @param revisionId ID da revisão a ser corrigida
 * @returns true se a revisão foi corrigida com sucesso
 */
export async function recalculateRevisionValues(revisionId: number): Promise<boolean> {
  try {
    console.log(`Recalculando valores para revisão ID: ${revisionId}`);
    
    // Buscar a revisão
    const revision = await storage.getOrderRevision(revisionId);
    if (!revision) {
      console.error(`Revisão ID ${revisionId} não encontrada`);
      return false;
    }
    
    // Buscar os itens da revisão
    const items = await storage.getOrderRevisionItems(revisionId);
    if (!items || items.length === 0) {
      console.warn(`Revisão ID ${revisionId} não possui itens`);
      return false;
    }
    
    // Calcular o subtotal baseado nos itens
    const calculatedSubtotal = items.reduce((sum, item) => sum + item.subtotal, 0);
    
    // Determinar o valor do desconto baseado no tipo
    let finalDiscount = revision.discount !== null ? revision.discount : 0;
    const discountType = revision.discountType || 'fixed';
    
    if (discountType === 'percentage' && finalDiscount > 0) {
      // Se for desconto percentual, recalcular o valor
      // Neste caso, o valor armazenado é a porcentagem, não o valor
      const percentage = Math.min(finalDiscount, 100);
      finalDiscount = (percentage / 100) * calculatedSubtotal;
      finalDiscount = parseFloat(finalDiscount.toFixed(2));
    }
    
    // Garantir que o desconto não seja maior que o subtotal
    finalDiscount = Math.min(finalDiscount, calculatedSubtotal);
    
    // Calcular o total
    const deliveryFee = revision.deliveryFee || 0;
    const total = Math.max(0, calculatedSubtotal - finalDiscount + deliveryFee);
    
    // Verificar se há discrepância nos valores
    const subtotalDiff = Math.abs((revision.subtotal || 0) - calculatedSubtotal);
    const discountDiff = Math.abs((revision.discount || 0) - finalDiscount);
    const totalDiff = Math.abs((revision.total || 0) - total);
    
    const needsUpdate = subtotalDiff > 0.01 || discountDiff > 0.01 || totalDiff > 0.01;
    
    if (needsUpdate) {
      console.log(`Corrigindo valores para revisão ID ${revisionId}:`, {
        subtotal: { antigo: revision.subtotal || 0, novo: calculatedSubtotal },
        discount: { antigo: revision.discount || 0, novo: finalDiscount },
        total: { antigo: revision.total || 0, novo: total }
      });
      
      // Atualizar a revisão
      const { data, error } = await supabase
        .from('order_revisions')
        .update({
          subtotal: calculatedSubtotal,
          discount: finalDiscount,
          total: total
        })
        .eq('id', revisionId);
      
      if (error) {
        console.error(`Erro ao atualizar revisão ID ${revisionId}:`, error);
        return false;
      }
      
      console.log(`Revisão ID ${revisionId} atualizada com sucesso`);
      return true;
    } else {
      console.log(`Revisão ID ${revisionId} já possui valores corretos`);
      return true;
    }
  } catch (error) {
    console.error(`Erro ao recalcular valores para revisão ID ${revisionId}:`, error);
    return false;
  }
}

/**
 * Corrige os cálculos de todas as revisões no banco de dados
 * @returns O número de revisões corrigidas
 */
export async function fixAllRevisionCalculations(): Promise<number> {
  try {
    console.log('Iniciando correção de cálculos para todas as revisões...');
    
    // Buscar todas as revisões
    const { data: revisions, error } = await supabase
      .from('order_revisions')
      .select('id')
      .order('id');
    
    if (error) {
      console.error('Erro ao buscar revisões:', error);
      return 0;
    }
    
    if (!revisions || revisions.length === 0) {
      console.log('Nenhuma revisão encontrada para corrigir');
      return 0;
    }
    
    console.log(`Encontradas ${revisions.length} revisões para verificar`);
    
    let correctedCount = 0;
    
    // Processar cada revisão
    for (const revision of revisions) {
      const success = await recalculateRevisionValues(revision.id);
      if (success) {
        correctedCount++;
      }
    }
    
    console.log(`Processamento concluído: ${correctedCount} revisões corrigidas de ${revisions.length}`);
    return correctedCount;
  } catch (error) {
    console.error('Erro ao processar correção de cálculos:', error);
    return 0;
  }
}

/**
 * Este utilitário pode ser chamado diretamente ou importado como módulo
 * para corrigir os cálculos de revisões de pedidos.
 */
// Código de execução direta removido para compatibilidade com ES modules