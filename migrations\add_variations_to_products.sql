-- Add variations column to products table
ALTER TABLE products
ADD COLUMN IF NOT EXISTS variations JSONB DEFAULT '[]'::jsonb NOT NULL;

-- Comment to explain the structure
COMMENT ON COLUMN products.variations IS 'Array of variation groups with their options, format: 
[
  {
    "id": "string_id",
    "nomeGrupo": "Nome do grupo de variações",
    "obrigatorio": boolean,
    "minSelecionados": number,
    "maxSelecionados": number,
    "opcoes": [
      {
        "id": "string_id",
        "name": "Nome da opção",
        "precoAdicional": number
      }
    ]
  }
]';