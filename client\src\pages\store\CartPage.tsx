import { useState, useEffect } from 'react';
import { useLocation, useParams } from 'wouter';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useMutation } from '@tanstack/react-query';
import { format } from 'date-fns';
import { pt, enUS } from 'date-fns/locale';
import { countryCodes } from '@/lib/countryCodes';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { useCart } from '@/context/CartContext';
import { useTranslation } from '@/hooks/useTranslation';
import { formatCurrency, formatStoreCurrency, formatPhoneWithCountryCode } from '@/lib/utils';

// Chaves para armazenamento no localStorage
const CUSTOMER_DATA_KEY = 'doceMenu_customerData';
const DELIVERY_ADDRESS_KEY = 'doceMenu_deliveryAddress';
const RECEIVING_METHOD_KEY = 'doceMenu_receivingMethod';
const PAYMENT_METHOD_KEY = 'doceMenu_paymentMethod';
import { apiRequest } from '@/lib/queryClient';
import { StoreThemedCalendar } from '@/components/store/StoreThemedCalendar';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { cn } from '@/lib/utils';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogAction,
} from '@/components/ui/alert-dialog';
import { X, Minus, Plus, ShoppingBag, Check, ArrowLeft, CalendarIcon, Clock, MapPin, User, CreditCard, Truck, Store } from 'lucide-react';
import { useStore } from '@/context/StoreContext';

// Funções para gerenciar dados no localStorage
const saveCustomerData = (data: any) => {
  try {
    localStorage.setItem(CUSTOMER_DATA_KEY, JSON.stringify(data));
    console.log('Dados do cliente salvos no localStorage:', data);
  } catch (error) {
    console.error('Erro ao salvar dados do cliente no localStorage:', error);
  }
};

const getCustomerData = () => {
  try {
    const data = localStorage.getItem(CUSTOMER_DATA_KEY);
    if (data) {
      const parsedData = JSON.parse(data);
      console.log('Dados do cliente recuperados do localStorage:', parsedData);
      return parsedData;
    }
  } catch (error) {
    console.error('Erro ao recuperar dados do cliente do localStorage:', error);
  }
  return null;
};

const saveDeliveryAddress = (data: any) => {
  try {
    localStorage.setItem(DELIVERY_ADDRESS_KEY, JSON.stringify(data));
    console.log('Endereço de entrega salvo no localStorage:', data);
  } catch (error) {
    console.error('Erro ao salvar endereço de entrega no localStorage:', error);
  }
};

const getDeliveryAddress = () => {
  try {
    const data = localStorage.getItem(DELIVERY_ADDRESS_KEY);
    if (data) {
      const parsedData = JSON.parse(data);
      console.log('Endereço de entrega recuperado do localStorage:', parsedData);
      return parsedData;
    }
  } catch (error) {
    console.error('Erro ao recuperar endereço de entrega do localStorage:', error);
  }
  return null;
};

const saveReceivingMethod = (method: string) => {
  try {
    localStorage.setItem(RECEIVING_METHOD_KEY, method);
    console.log('Método de recebimento salvo no localStorage:', method);
  } catch (error) {
    console.error('Erro ao salvar método de recebimento no localStorage:', error);
  }
};

const getReceivingMethod = () => {
  try {
    const method = localStorage.getItem(RECEIVING_METHOD_KEY);
    if (method) {
      console.log('Método de recebimento recuperado do localStorage:', method);
      return method;
    }
  } catch (error) {
    console.error('Erro ao recuperar método de recebimento do localStorage:', error);
  }
  return null;
};

const savePaymentMethod = (method: string) => {
  try {
    localStorage.setItem(PAYMENT_METHOD_KEY, method);
    console.log('Método de pagamento salvo no localStorage:', method);
  } catch (error) {
    console.error('Erro ao salvar método de pagamento no localStorage:', error);
  }
};

const getPaymentMethod = () => {
  try {
    const method = localStorage.getItem(PAYMENT_METHOD_KEY);
    if (method) {
      console.log('Método de pagamento recuperado do localStorage:', method);
      return method;
    }
  } catch (error) {
    console.error('Erro ao recuperar método de pagamento do localStorage:', error);
  }
  return null;
};

// Checkout form schema
const checkoutSchema = z.object({
  customer: z.object({
    name: z.string().min(1, { message: "Name is required" }),
    email: z.string().min(1, { message: "Email is required" }).email({ message: "Invalid email address" }),
    countryCode: z.string().default("+55"),
    whatsapp: z.string()
      .min(8, { message: "WhatsApp number must be at least 8 digits" })
      .max(15, { message: "WhatsApp number is too long" })
      .regex(/^[0-9]+$/, { message: "WhatsApp must contain only numbers" })
      .refine(val => val.length >= 8, {
        message: "WhatsApp number must be at least 8 digits"
      }),
  }).refine(data => !!data.name && !!data.email && !!data.whatsapp, {
    message: "All customer information is required",
    path: ["incomplete"]
  }),
  // Novo campo para o tipo de recebimento
  receivingMethod: z.string().min(1, { message: "Receiving method is required" }),
  // Campos para entrega
  deliveryAddress: z.object({
    street: z.string().min(1, { message: "Street is required" }).optional(),
    number: z.string().min(1, { message: "Number is required" }).optional(),
    neighborhood: z.string().min(1, { message: "Neighborhood is required" }).optional(),
    cityState: z.string().min(1, { message: "City and state are required" }).optional(),
    reference: z.string().optional(),
  }).optional(),
  // Campos para data de recebimento
  receivingDate: z.date({
    required_error: "Date is required",
  }),
  suggestedTime: z.string().optional(),
  paymentMethod: z.string().min(1, { message: "Payment method is required" }),
  notes: z.string().optional(),
});

type CheckoutFormValues = z.infer<typeof checkoutSchema>;

export function CartPage() {
  const { slug } = useParams<{ slug: string }>();
  const [, navigate] = useLocation();
  const { t } = useTranslation();
  const { toast } = useToast();

  const { store, refreshStoreData } = useStore();

  const { items, addItem, removeItem, updateQuantity, clearCart, subtotal, coupon, applyCoupon, removeCoupon, discount, total } = useCart();
  const [isCheckingOut, setIsCheckingOut] = useState(false);
  const [isOrderSuccess, setIsOrderSuccess] = useState(false);
  const [orderNumber, setOrderNumber] = useState<number | null>(null);
  const [isLoadingStoreData, setIsLoadingStoreData] = useState(false);
  const [couponCode, setCouponCode] = useState('');
  const [isApplyingCoupon, setIsApplyingCoupon] = useState(false);

  // Carregar dados da loja apenas uma vez quando a página do carrinho for aberta
  useEffect(() => {
    const loadStoreData = async () => {
      if (slug && (!store || store.slug !== slug)) {
        console.log('[CartPage] Carregando dados da loja...');
        setIsLoadingStoreData(true);
        try {
          await refreshStoreData(slug);
          console.log('[CartPage] Store object loaded:', {
            storeId: store?.id,
            slug: store?.slug,
            hasPaymentMethods: !!store?.paymentMethods,
            hasDeliverySettings: !!store?.deliverySettings
          });
        } catch (error) {
          console.error('[CartPage] Erro ao carregar dados da loja:', error);
        } finally {
          setIsLoadingStoreData(false);
        }
      } else if (store && store.slug === slug) {
        console.log('[CartPage] Dados da loja já carregados:', store.slug);
      }
    };

    loadStoreData();
  }, [slug, refreshStoreData, store]);

  // Adicionar log quando o store mudar
  useEffect(() => {
    console.log('[CartPage] Store object updated:', {
      storeId: store?.id,
      slug: store?.slug,
      hasPaymentMethods: !!store?.paymentMethods,
      paymentMethodsCount: store?.paymentMethods ? Object.keys(store.paymentMethods).length : 0,
      hasDeliverySettings: !!store?.deliverySettings,
      deliverySettingsCount: store?.deliverySettings ? Object.keys(store.deliverySettings).length : 0
    });
  }, [store]);

  // Define CSS variables for store colors
  useEffect(() => {
    if (store?.colors?.primary) {
      document.documentElement.style.setProperty('--store-primary', store.colors.primary);
      document.documentElement.style.setProperty('--store-primary-light', `${store.colors.primary}20`);
    }

    // Clean up when component unmounts
    return () => {
      document.documentElement.style.removeProperty('--store-primary');
      document.documentElement.style.removeProperty('--store-primary-light');
    };
  }, [store?.colors?.primary]);

  // Calcula o subtotal de um item incluindo o preço base e as variações
  const calculateItemSubtotal = (item: any) => {
    let itemSubtotal = item.price * item.quantity; // Preço base × quantidade

    // Adicionar preço das variações
    if (item.selectedVariations && item.selectedVariations.length > 0) {
      const variationsTotal = item.selectedVariations.reduce(
        (total: number, variation: any) => total + (variation.price || 0),
        0
      );
      itemSubtotal += variationsTotal * item.quantity;
    }

    return itemSubtotal;
  };

  // Calcula o preço total das variações de um item
  const calculateVariationsTotal = (item: any) => {
    if (!item.selectedVariations || item.selectedVariations.length === 0) {
      return 0;
    }

    return item.selectedVariations.reduce(
      (total: number, variation: any) => total + (variation.price || 0),
      0
    ) * item.quantity;
  };

  // Recuperar dados salvos do localStorage
  const savedCustomerData = getCustomerData();
  const savedDeliveryAddress = getDeliveryAddress();
  const savedReceivingMethod = getReceivingMethod();
  const savedPaymentMethod = getPaymentMethod();

  // Verificar se há dados salvos para mostrar notificação
  useEffect(() => {
    if (savedCustomerData || savedDeliveryAddress) {
      // Mostrar notificação apenas se houver dados salvos
      toast({
        title: t('storefront.dataLoaded'),
        description: t('storefront.customerDataSaved'),
        duration: 3000,
      });
    }
  }, []);

  // Set up checkout form
  const form = useForm<CheckoutFormValues>({
    resolver: zodResolver(checkoutSchema),
    defaultValues: {
      customer: savedCustomerData || {
        name: '',
        email: '',
        countryCode: 'br', // Brasil selecionado por padrão
        whatsapp: ''
      },
      receivingMethod: savedReceivingMethod || '',
      deliveryAddress: savedDeliveryAddress || {
        street: '',
        number: '',
        neighborhood: '',
        cityState: '',
        reference: ''
      },
      receivingDate: undefined,
      suggestedTime: '',
      paymentMethod: savedPaymentMethod || '',
      notes: ''
    }
  });

  // Auto-select receiving method if only one option is available
  useEffect(() => {
    if (store?.deliverySettings) {
      const availableMethods = getAvailableReceivingMethods();
      console.log('Métodos de recebimento disponíveis:', availableMethods);
      console.log('Método de recebimento atualizado:', form.getValues('receivingMethod'));

      // If there's only one available method and no method is currently selected, auto-select it
      if (availableMethods.length === 1 && !form.getValues('receivingMethod')) {
        const onlyMethod = availableMethods[0].id;
        form.setValue('receivingMethod', onlyMethod, { shouldValidate: true });
        console.log(`Auto-selecionado método de recebimento: ${onlyMethod}, único disponível`);
      } else if (availableMethods.length === 0) {
        // Reset the receiving method if no methods are available
        form.setValue('receivingMethod', '', { shouldValidate: false });
        console.log('Nenhum método de recebimento disponível, campo limpo');
      }
    }
  }, [store?.deliverySettings, form]);

  // Gerar link de WhatsApp formatado
  const generateWhatsAppLink = (orderData: any) => {
    if (!store?.whatsapp) return '';

    const items = orderData.items.map((item: any) => {
      let itemText = `${item.quantity}x ${item.name}`;

      // Adicionar variações se houver
      if (item.selectedVariations && item.selectedVariations.length > 0) {
        const variations = item.selectedVariations.map((v: any) =>
          `${v.variationName}: ${v.optionName}${v.price > 0 ? ` (+${formatStoreCurrency(v.price, store?.currency)})` : ''}`
        ).join(', ');

        itemText += ` (${variations})`;
      }

      // Adicionar observação se houver
      if (item.observation) {
        itemText += ` - Obs: ${item.observation}`;
      }

      return itemText;
    }).join('\n');

    // Informações de entrega/retirada
    const receivingMethod = orderData.receivingMethod === 'delivery' ? t('storefront.delivery') : t('storefront.pickup');
    let receivingInfo = `\n\n${t('storefront.receivingMethodTitle')}: ${receivingMethod}`;

    if (orderData.receivingDate) {
      const date = new Date(orderData.receivingDate);
      receivingInfo += `\nData: ${format(date, 'dd/MM/yyyy')}`;
    }

    if (orderData.suggestedTime || orderData.receivingTime) {
      receivingInfo += `\nHorário: ${orderData.suggestedTime || orderData.receivingTime}`;
    }

    // Adicionar endereço de entrega se aplicar
    if (orderData.receivingMethod === 'delivery' && orderData.deliveryAddress) {
      const address = orderData.deliveryAddress;
      receivingInfo += `\nEndereço: ${address.street}, ${address.number}, ${address.neighborhood}, ${address.cityState}`;
      if (address.reference) {
        receivingInfo += `\nReferência: ${address.reference}`;
      }
    }

    // Informações de pagamento
    const paymentMethodId = orderData.paymentMethod;

    // Converter o ID do método de pagamento para o nome descritivo
    let paymentMethodName = paymentMethodId;
    if (paymentMethodId && paymentMethodId.startsWith('custom_')) {
      // Para métodos personalizados, obter o índice e buscar o texto correspondente
      const customIndex = parseInt(paymentMethodId.replace('custom_', ''), 10);
      if (!isNaN(customIndex) &&
          store?.paymentMethods?.customMethods &&
          Array.isArray(store.paymentMethods.customMethods) &&
          customIndex < store.paymentMethods.customMethods.length) {
        paymentMethodName = store.paymentMethods.customMethods[customIndex];
      }
    } else {
      // Para métodos padrão, buscar o nome traduzido
      const methods = getAvailablePaymentMethods();
      const method = methods.find(m => m.id === paymentMethodId);
      if (method) {
        paymentMethodName = method.name;
      }
    }

    // Informações do cliente
    const customer = orderData.customer;

    // Converter código do país (br) para o formato de exibição (+55)
    const countryObj = countryCodes.find(c => c.id === customer.countryCode);
    const displayCountryCode = countryObj ? countryObj.code : customer.countryCode;

    const customerInfo = `\n\nNome: ${customer.name}\nWhatsApp: ${displayCountryCode} ${customer.whatsapp}\nEmail: ${customer.email}`;

    // Valores
    const total = orderData.total;
    const subtotalInfo = `Subtotal: ${formatStoreCurrency(subtotal, store?.currency)}`;
    let valuesInfo = `\n\n${subtotalInfo}`;

    // Adicionar desconto do cupom, se aplicável
    if (discount > 0 && coupon) {
      valuesInfo += `\nCupom aplicado: ${coupon.code}`;
      valuesInfo += `\nDesconto: -${formatStoreCurrency(discount, store?.currency)}`;
    }

    if (orderData.receivingMethod === 'delivery' && store.deliverySettings?.deliveryFee > 0) {
      valuesInfo += `\nTaxa de entrega: ${formatStoreCurrency(store.deliverySettings.deliveryFee, store?.currency)}`;
    }

    valuesInfo += `\nTotal: ${formatStoreCurrency(total, store?.currency)}`;
    valuesInfo += `\nForma de pagamento: ${paymentMethodName}`;

    // Observações gerais
    let notesInfo = '';
    if (orderData.notes) {
      notesInfo = `\n\nObservações: ${orderData.notes}`;
    }

    // Mensagem completa
    const message = `*Novo Pedido*\n\n*Itens:*\n${items}${receivingInfo}${customerInfo}${valuesInfo}${notesInfo}`;

    // Gerar link
    const encodedMessage = encodeURIComponent(message);
    return `https://wa.me/${store.countryCode}${store.whatsapp}?text=${encodedMessage}`;
  };

  // Place order mutation
  const orderMutation = useMutation({
    mutationFn: (data: CheckoutFormValues) => {
      // Calcular o total com taxa de entrega se aplicável
      const finalTotal = data.receivingMethod === 'delivery' ? totalWithDelivery : total;

      // Formatar a data para envio
      const receivingDateString = data.receivingDate ? format(data.receivingDate, 'yyyy-MM-dd') : undefined;

      const orderData = {
        ...data,
        total: finalTotal, // Enviar o total correto incluindo taxa de entrega e desconto
        subtotal, // Subtotal sem desconto
        discount, // Valor do desconto
        receivingDate: receivingDateString,
        // Mapear suggestedTime para receivingTime que é o campo esperado no backend
        receivingTime: data.suggestedTime,
        // Remover suggestedTime para evitar duplicidade
        suggestedTime: undefined,
        // Incluir o cupom aplicado, se houver
        coupon: coupon ? {
          id: coupon.id,
          code: coupon.code,
          tipo: coupon.tipo,
          valor: coupon.valor
        } : undefined,
        items: items.map(item => ({
          productId: item.productId,
          quantity: item.quantity,
          price: item.price,
          selectedVariations: item.selectedVariations || [],
          observation: item.observation || undefined
        }))
      };
      return apiRequest('POST', `/api/public/stores/${store?.slug}/orders`, orderData);
    },
    onSuccess: async (response) => {
      const data = await response.json();
      setOrderNumber(data.orderId);
      setIsOrderSuccess(true);

      // Gerar link do WhatsApp com o resumo do pedido
      const formData = form.getValues();
      const orderData = {
        ...formData,
        total: formData.receivingMethod === 'delivery' ? totalWithDelivery : subtotal,
        items: items
      };

      // Gerar link de WhatsApp
      const whatsappLink = generateWhatsAppLink(orderData);

      // Abrir o link em uma nova janela/aba
      if (whatsappLink) {
        window.open(whatsappLink, '_blank');
      }

      clearCart();
    },
    onError: (error) => {
      toast({
        title: t('storefront.orderError'),
        description: error instanceof Error ? error.message : String(error),
        variant: "destructive",
      });
    }
  });

  // Adicionar efeito para salvar dados quando o usuário preencher o formulário
  useEffect(() => {
    const subscription = form.watch((value, { name, type }) => {
      // Salvar dados do cliente quando qualquer campo do cliente for alterado
      if (name?.startsWith('customer.') && value.customer) {
        saveCustomerData(value.customer);
      }

      // Salvar endereço de entrega quando qualquer campo de endereço for alterado
      if (name?.startsWith('deliveryAddress.') && value.deliveryAddress) {
        saveDeliveryAddress(value.deliveryAddress);
      }

      // Salvar método de recebimento quando for alterado
      if (name === 'receivingMethod' && value.receivingMethod) {
        saveReceivingMethod(value.receivingMethod);
      }

      // Salvar método de pagamento quando for alterado
      if (name === 'paymentMethod' && value.paymentMethod) {
        savePaymentMethod(value.paymentMethod);
      }
    });

    return () => subscription.unsubscribe();
  }, [form]);

  // Handle checkout form submission
  const onSubmit = (values: CheckoutFormValues) => {
    console.log('Form submission triggered', { values, items });

    if (items.length === 0) {
      console.log('Cart is empty, submission blocked');
      toast({
        title: t('common.error'),
        description: t('storefront.emptyCart'),
        variant: "destructive",
      });
      return;
    }

    // Verificar se os dados do cliente estão preenchidos
    if (!values.customer.name || !values.customer.email || !values.customer.whatsapp) {
      toast({
        title: t('common.error'),
        description: t('landing.pleaseCompleteCustomerInfo'),
        variant: "destructive",
      });
      console.log('Customer data incomplete:', values.customer);
      return;
    }

    // Salvar dados do cliente e endereço no localStorage
    saveCustomerData(values.customer);
    if (values.receivingMethod === 'delivery' && values.deliveryAddress) {
      saveDeliveryAddress(values.deliveryAddress);
    }
    saveReceivingMethod(values.receivingMethod);
    savePaymentMethod(values.paymentMethod);

    // Validar formato do e-mail
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(values.customer.email)) {
      toast({
        title: t('common.error'),
        description: t('landing.invalidEmailFormat'),
        variant: "destructive",
      });
      return;
    }

    // Validar número de WhatsApp (mínimo 8 dígitos)
    if (values.customer.whatsapp.length < 8) {
      toast({
        title: t('common.error'),
        description: t('landing.invalidWhatsAppNumber'),
        variant: "destructive",
      });
      return;
    }

    // Validação final (Bloco 6)
    const receivingMethod = values.receivingMethod;

    // Verificar se o método de recebimento está selecionado
    if (!receivingMethod) {
      toast({
        title: t('common.error'),
        description: t('landing.pleaseSelectReceivingMethod'),
        variant: "destructive",
      });
      return;
    }

    // Verificar se a data está disponível
    if (!values.receivingDate || isDateDisabled(values.receivingDate)) {
      toast({
        title: t('common.error'),
        description: t('landing.pleaseSelectValidDate'),
        variant: "destructive",
      });
      return;
    }

    // Verificar se os campos de endereço estão preenchidos para entregas
    if (receivingMethod === 'delivery') {
      if (!values.deliveryAddress?.street || !values.deliveryAddress?.number ||
          !values.deliveryAddress?.neighborhood || !values.deliveryAddress?.cityState) {
        toast({
          title: t('common.error'),
          description: t('landing.pleaseCompleteAddress'),
          variant: "destructive",
        });
        return;
      }
    }

    // Verificar se o método de pagamento foi selecionado
    if (!values.paymentMethod) {
      toast({
        title: t('common.error'),
        description: t('landing.pleaseSelectPaymentMethod'),
        variant: "destructive",
      });
      return;
    }

    // Se todas as validações passarem, enviar o pedido
    orderMutation.mutate(values);
  };

  // Start checkout process
  const handleCheckout = () => {
    if (items.length === 0) {
      toast({
        description: t('storefront.emptyCart'),
        variant: "destructive",
      });
      return;
    }
    setIsCheckingOut(true);
  };

  // Go back to cart from checkout
  const handleBackToCart = () => {
    setIsCheckingOut(false);
  };

  // Return to store
  const returnToStore = () => {
    navigate(`/${slug}`);
  };

  // Get available payment methods from store
  const getAvailablePaymentMethods = (): Method[] => {
    const methods: Method[] = [];

    console.log("Store payment methods data:", store?.paymentMethods);

    // Usar apenas dados diretos da configuração de métodos de pagamento da loja
    const paymentMethods = store?.paymentMethods;
    if (paymentMethods) {
      // Adicionar métodos padrão apenas se estiverem habilitados na configuração
      if (paymentMethods.cash) methods.push({ id: 'cash', name: t('storefront.cash') });
      if (paymentMethods.creditCard) methods.push({ id: 'creditCard', name: t('storefront.creditCard') });
      if (paymentMethods.debitCard) methods.push({ id: 'debitCard', name: t('storefront.debitCard') });
      if (paymentMethods.pix) methods.push({ id: 'pix', name: t('storefront.pix') });
      if (paymentMethods.bankTransfer) methods.push({ id: 'bankTransfer', name: t('storefront.bankTransfer') });

      // Adicionar métodos personalizados
      if (paymentMethods.customMethods && Array.isArray(paymentMethods.customMethods)) {
        paymentMethods.customMethods.forEach((method, index) => {
          methods.push({
            id: `custom_${index}`,
            name: method
          });
        });
      }
    }

    // Não adicionamos mais métodos padrão - apenas usamos métodos configurados no banco
    console.log("Métodos de pagamento disponíveis:", methods);
    return methods;
  };

  // Get available receiving methods from store
  // Definindo interface para métodos de recebimento/pagamento
  interface Method {
    id: string;
    name: string;
  }

  const getAvailableReceivingMethods = (): Method[] => {
    const methods: Method[] = [];

    console.log("Store delivery settings data:", store?.deliverySettings);

    // Usar apenas dados diretos da configuração de entrega da loja
    const deliverySettings = store?.deliverySettings;
    if (deliverySettings) {
      console.log("Valores específicos do deliverySettings:", JSON.stringify({
        allowDelivery: deliverySettings.allowDelivery,
        allowPickup: deliverySettings.allowPickup,
        pickupDays: deliverySettings.pickupDays,
        deliveryDays: deliverySettings.deliveryDays
      }));

      if (deliverySettings.allowDelivery) {
        methods.push({ id: 'delivery', name: t('storefront.delivery') });
      }

      if (deliverySettings.allowPickup) {
        methods.push({ id: 'pickup', name: t('storefront.pickup') });
      }
    }

    // Não fornecemos mais valores padrão para garantir que apenas valores do banco de dados sejam usados
    console.log("Métodos de recebimento disponíveis após todas as verificações:", methods);

    // Se houver apenas um método disponível, selecionar automaticamente
    if (methods.length === 1 && !form.getValues('receivingMethod')) {
      console.log('Auto-selecionado método de recebimento:', methods[0].id, 'único disponível');
      setTimeout(() => {
        form.setValue('receivingMethod', methods[0].id);
      }, 100);
    }

    return methods;
  };

  // Function to check if a date is valid for delivery/pickup
  const isDateDisabled = (date: Date) => {
    // Se não houver configurações de entrega, não permitimos nenhuma data
    if (!store?.deliverySettings) return true;

    // Verificar prazo mínimo de antecedência
    const minAdvanceDays = store.deliverySettings.minAdvanceDays;
    // Se minAdvanceDays existir, aplicamos a regra
    if (minAdvanceDays !== undefined) {
      const minDate = new Date();
      minDate.setDate(minDate.getDate() + minAdvanceDays);
      minDate.setHours(0, 0, 0, 0);

      if (date < minDate) return true;
    }

    const receivingMethod = form.getValues('receivingMethod');
    const dayOfWeek = date.getDay().toString(); // 0 = domingo, 6 = sábado

    // Verificar dias permitidos com base no método de recebimento
    if (receivingMethod === 'delivery') {
      // Verificar se o dia da semana está permitido para entregas
      const allowedDays = store.deliverySettings.deliveryDays;
      // Se houver dias configurados, verificamos se o dia atual está incluído
      if (allowedDays && allowedDays.length > 0 && !allowedDays.includes(dayOfWeek)) {
        return true;
      }
    } else if (receivingMethod === 'pickup') {
      // Verificar se o dia da semana está permitido para retiradas
      const allowedDays = store.deliverySettings.pickupDays;
      // Se houver dias configurados, verificamos se o dia atual está incluído
      if (allowedDays && allowedDays.length > 0 && !allowedDays.includes(dayOfWeek)) {
        return true;
      }
    }

    // Verificar períodos de indisponibilidade
    const unavailablePeriods = store.deliverySettings.unavailablePeriods;

    // Se houver períodos de indisponibilidade configurados
    if (unavailablePeriods && unavailablePeriods.length > 0) {
      for (const period of unavailablePeriods) {
        if (!period || !period.startDate || !period.endDate) continue;

        const startDate = new Date(period.startDate);
        const endDate = new Date(period.endDate);

        startDate.setHours(0, 0, 0, 0);
        endDate.setHours(23, 59, 59, 999);

        if (date >= startDate && date <= endDate) {
          return true;
        }
      }
    }

    return false;
  };

  // Watch receiving method to reload date validations when it changes
  const receivingMethod = form.watch('receivingMethod');

  // Adicionar um efeito para monitorar quando o método de recebimento mudar
  useEffect(() => {
    console.log('Método de recebimento atualizado:', receivingMethod);
  }, [receivingMethod]);

  // Função para aplicar cupom
  const handleApplyCoupon = async () => {
    if (!couponCode.trim()) {
      toast({
        title: t('storefront.invalidCoupon'),
        description: t('storefront.enterCouponCode'),
        variant: "destructive",
      });
      return;
    }

    setIsApplyingCoupon(true);
    try {
      const success = await applyCoupon(slug, couponCode.trim());
      if (success) {
        setCouponCode(''); // Limpar o campo após aplicar com sucesso
      }
    } catch (error) {
      console.error('Erro ao aplicar cupom:', error);
      toast({
        title: t('common.error'),
        description: t('storefront.errorApplyingCoupon'),
        variant: "destructive",
      });
    } finally {
      setIsApplyingCoupon(false);
    }
  };

  // Calculate delivery fee based on delivery settings, sem valores padrão
  const deliveryFee = store?.deliverySettings?.deliveryFee;
  const totalWithDelivery = total + (form.watch('receivingMethod') === 'delivery' && deliveryFee ? deliveryFee : 0);

  return (
    <div className="container max-w-4xl mx-auto px-4 pb-8">
      {/* Header para a página do carrinho com cores dinâmicas */}
      <div
        className="text-white w-full py-6 px-4 mb-6 rounded-b-lg shadow-md"
        style={{
          backgroundColor: store?.colors?.primary || '#6082e6',
          color: '#FFFFFF'
        }}
      >
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold">{t('storefront.yourCart')}</h1>
            <p className="text-sm opacity-90 mt-1">{store?.name || 'Loja'}</p>
          </div>
          <ShoppingBag className="h-8 w-8" />
        </div>
      </div>

      <Button
        variant="ghost"
        onClick={returnToStore}
        className="mb-4 flex items-center text-muted-foreground hover:text-foreground"
      >
        <ArrowLeft className="h-4 w-4 mr-2" />
        {t('storefront.returnToStore')}
      </Button>

      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        <div className="p-6">
          <h1 className="text-2xl font-bold text-neutral-dark mb-2">{t('storefront.cart')}</h1>
          <p className="text-muted-foreground mb-6">
            {items.length === 0 ? t('storefront.emptyCart') : ''}
          </p>

          {isOrderSuccess ? (
            // Order success view
            <div className="py-12 text-center">
              <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100 mb-4">
                <Check className="h-6 w-6 text-green-600" />
              </div>
              <h2 className="text-lg font-medium text-neutral-dark">
                {t('storefront.orderSuccess')}
              </h2>
              {orderNumber && (
                <p className="mt-2 text-muted-foreground">
                  {t('storefront.orderNumber')}: #{orderNumber}
                </p>
              )}
              <div className="mt-6">
                <Button
                  style={{
                    backgroundColor: store?.colors?.primary || '#6082e6',
                    color: '#FFFFFF'
                  }}
                  className="hover:opacity-90"
                  onClick={returnToStore}
                >
                  {t('storefront.returnToStore')}
                </Button>
              </div>
            </div>
          ) : !isCheckingOut ? (
            // Cart View
            <>
              {items.length > 0 ? (
                <div className="flex-1">
                  <ul className="divide-y divide-border">
                    {items.map((item) => (
                      <li key={item.id} className="py-6 flex">
                        {item.image && (
                          <div className="flex-shrink-0 w-20 h-20 rounded-md overflow-hidden">
                            <img src={item.image} alt={item.name} className="w-full h-full object-cover" />
                          </div>
                        )}
                        <div className="ml-4 flex-1 flex flex-col">
                          <div>
                            <div className="flex justify-between text-base font-medium text-neutral-dark">
                              <h3 className="line-clamp-1">{item.name}</h3>
                              <p className="ml-4">{formatStoreCurrency(item.price, store?.currency)}</p>
                            </div>
                            {item.selectedVariations && item.selectedVariations.length > 0 && (
                              <div className="mt-1 text-sm text-muted-foreground">
                                {item.selectedVariations.map((variation, i) => (
                                  <p key={i} className="line-clamp-1">
                                    {variation.variationName}: {variation.optionName}
                                    {variation.price > 0 && ` (+${formatStoreCurrency(variation.price * item.quantity, store?.currency)})`}
                                  </p>
                                ))}

                              </div>
                            )}
                            {item.observation && (
                              <div className="mt-1 text-sm text-muted-foreground">
                                <p className="line-clamp-2">{item.observation}</p>
                              </div>
                            )}
                          </div>
                          <div className="flex-1 flex items-end justify-between text-sm">
                            <div className="flex flex-col items-start">
                              <div className="flex items-center">
                                <button
                                  type="button"
                                  className="text-neutral-dark p-1"
                                  onClick={() => updateQuantity(item.id, item.quantity - 1)}
                                >
                                  <Minus className="h-4 w-4" />
                                </button>
                                <span className="mx-2 text-neutral-dark">{item.quantity}</span>
                                <button
                                  type="button"
                                  className="text-neutral-dark p-1"
                                  onClick={() => updateQuantity(item.id, item.quantity + 1)}
                                >
                                  <Plus className="h-4 w-4" />
                                </button>
                              </div>
                              <div className="text-sm font-medium text-primary mt-1">
                                {t('storefront.itemTotal')}: {formatStoreCurrency(calculateItemSubtotal(item), store?.currency)}
                              </div>
                            </div>
                            <div className="flex">
                              <button
                                type="button"
                                className="font-medium text-error hover:text-error-dark"
                                onClick={() => removeItem(item.id)}
                              >
                                {t('storefront.removeItem')}
                              </button>
                            </div>
                          </div>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              ) : (
                <div className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <ShoppingBag className="mx-auto h-12 w-12 text-muted-foreground" />
                    <h3 className="mt-2 text-sm font-medium text-neutral-dark">
                      {t('storefront.emptyCart')}
                    </h3>
                  </div>
                </div>
              )}

              {items.length > 0 && (
                <div className="border-t border-border py-6 mt-6">
                  {/* Campo de cupom */}
                  <div className="mb-4">
                    <h3 className="text-sm font-medium mb-2">{t('storefront.haveCoupon')}</h3>
                    <div className="flex space-x-2">
                      <Input
                        placeholder={t('storefront.enterCouponCode')}
                        value={couponCode}
                        onChange={(e) => setCouponCode(e.target.value)}
                        className="flex-1"
                      />
                      <Button
                        onClick={handleApplyCoupon}
                        disabled={isApplyingCoupon}
                        variant="outline"
                        style={{
                          borderColor: store?.colors?.primary || '#6082e6',
                          color: store?.colors?.primary || '#6082e6'
                        }}
                      >
                        {isApplyingCoupon ? t('common.loading') : t('storefront.apply')}
                      </Button>
                    </div>
                  </div>

                  {/* Cupom aplicado */}
                  {coupon && (
                    <div className="bg-muted p-3 rounded-md mb-4">
                      <div className="flex justify-between items-center">
                        <div>
                          <p className="font-medium text-sm">{t('storefront.couponApplied')}: {coupon.code}</p>
                          <p className="text-xs text-muted-foreground">
                            {coupon.tipo === 'valor_fixo'
                              ? t('storefront.fixedDiscount', { value: formatStoreCurrency(coupon.valor, store?.currency) })
                              : t('storefront.percentDiscount', { value: coupon.valor })}
                          </p>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={removeCoupon}
                          className="h-8 text-error hover:text-error-dark hover:bg-background"
                        >
                          <X className="h-4 w-4 mr-1" />
                          {t('storefront.remove')}
                        </Button>
                      </div>
                    </div>
                  )}

                  {/* Resumo de valores */}
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm text-muted-foreground">
                      <p>{t('storefront.subtotal')}</p>
                      <p>{formatStoreCurrency(subtotal, store?.currency)}</p>
                    </div>

                    {discount > 0 && coupon && (
                      <div className="flex justify-between text-sm text-green-600">
                        <div className="flex flex-col">
                          <p>{t('storefront.discount')}</p>
                          <p className="text-xs text-muted-foreground">
                            {coupon.code} - {coupon.tipo === 'valor_fixo'
                              ? t('storefront.fixedDiscount', { value: formatStoreCurrency(coupon.valor, store?.currency) })
                              : t('storefront.percentDiscount', { value: coupon.valor })}
                          </p>
                        </div>
                        <p>-{formatStoreCurrency(discount, store?.currency)}</p>
                      </div>
                    )}

                    <div className="flex justify-between text-base font-medium text-neutral-dark pt-2 border-t">
                      <p>{t('storefront.total')}</p>
                      <p>{formatStoreCurrency(total, store?.currency)}</p>
                    </div>
                  </div>

                  <p className="mt-0.5 text-sm text-neutral-dark">
                    {t('storefront.shippingCalc')}
                  </p>
                  <div className="mt-6">
                    <Button
                      className="w-full hover:opacity-90"
                      style={{
                        backgroundColor: store?.colors?.primary || '#6082e6',
                        color: '#FFFFFF'
                      }}
                      onClick={handleCheckout}
                    >
                      {t('storefront.checkout')}
                    </Button>
                  </div>
                  <div className="mt-6 flex justify-center text-sm text-center text-neutral-dark">
                    <Button variant="link" onClick={returnToStore}>
                      {t('storefront.continueShopping')}
                    </Button>
                  </div>
                </div>
              )}
            </>
          ) : (
            // Checkout Form
            <div className="py-6">
              <Button
                variant="ghost"
                onClick={handleBackToCart}
                className="mb-4 flex items-center text-muted-foreground hover:text-foreground"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                {t('storefront.backToCart')}
              </Button>

              <Form {...form}>
                <form
                  onSubmit={(e) => {
                    e.preventDefault();
                    console.log('Form submitted via standard handler');
                    form.handleSubmit(onSubmit)(e);
                  }}
                  className="space-y-6"
                >
                  {/* Informações do cliente */}
                  <div>
                    <h3 className="text-lg font-medium flex items-center">
                      <User className="h-5 w-5 mr-2" style={{ color: store?.colors?.primary || '#6082e6' }} />
                      {t('storefront.customerInfo')}
                    </h3>
                    <div className="mt-4 space-y-4">
                      <FormField
                        control={form.control}
                        name="customer.name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('storefront.name')}</FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="customer.email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>{t('storefront.email')}</FormLabel>
                            <FormControl>
                              <Input type="email" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      {/* Código de país e Whatsapp na mesma linha */}
                      <div className="grid grid-cols-12 gap-4">
                        <div className="col-span-4">
                          <FormField
                            control={form.control}
                            name="customer.countryCode"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>{t('storefront.countryCode')}</FormLabel>
                                <FormControl>
                                  <Select
                                    value={field.value || "br"}
                                    defaultValue="br"
                                    onValueChange={field.onChange}
                                  >
                                    <SelectTrigger className="w-full">
                                      <SelectValue placeholder="🇧🇷 +55" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      {countryCodes.map((country) => (
                                        <SelectItem key={country.id} value={country.id}>
                                          <div className="flex items-center">
                                            <span className="mr-2">{country.flag}</span>
                                            <span className="ml-1 text-muted-foreground">{country.code}</span>
                                            <span className="ml-2 text-xs">{country.name}</span>
                                          </div>
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <div className="col-span-8">
                          <FormField
                            control={form.control}
                            name="customer.whatsapp"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>{t('storefront.whatsapp')}</FormLabel>
                                <FormControl>
                                  <Input {...field} placeholder={t('storefront.whatsappPlaceholder')} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  {/* Método de recebimento */}
                  <div>
                    <h3 className="text-lg font-medium flex items-center">
                      <ShoppingBag className="h-5 w-5 mr-2" style={{ color: store?.colors?.primary || '#6082e6' }} />
                      {t('storefront.receivingMethod')}
                    </h3>
                    <div className="mt-4  pb-2">
                      {/* Botões Segmentados para método de recebimento (Toggle) */}
                      <div className="flex rounded-md shadow-sm mt-2">
                        {getAvailableReceivingMethods().map((method, index) => {
                          const isActive = form.getValues('receivingMethod') === method.id;
                          const availableMethods = getAvailableReceivingMethods();
                          const isFirst = index === 0;
                          const isLast = index === availableMethods.length - 1;

                          return (
                            <button
                              type="button"
                              key={`receiving-method-${method.id}`}
                              className={`
                                flex-1 relative inline-flex items-center justify-center px-4 py-3 text-sm font-medium
                                ${isActive ? 'text-white z-10' : 'text-gray-700 hover:bg-gray-50'}
                                border border-gray-300
                                ${isFirst ? 'rounded-l-md' : ''}
                                ${isLast ? 'rounded-r-md' : ''}
                                ${!isFirst ? '-ml-px' : ''}
                                transition-colors duration-200
                              `}
                              style={{
                                backgroundColor: isActive ? store?.colors?.primary || '#6082e6' : undefined,
                                borderColor: isActive ? store?.colors?.primary || '#6082e6' : undefined
                              }}
                              onClick={() => {
                                console.log('Clicando para alterar método para:', method.id);

                                // Se estiver mudando de pickup para delivery, limpar os horários de pickup
                                if (method.id === 'delivery') {
                                  form.setValue('suggestedTime', '');
                                }

                                // Atualizar o valor do campo
                                form.setValue('receivingMethod', method.id, { shouldValidate: true });

                                // Quando selecionar delivery, rolar para a seção de endereço de entrega
                                if (method.id === 'delivery') {
                                  // Dar um pequeno delay para garantir que o estado foi atualizado
                                  setTimeout(() => {
                                    const deliveryAddressSection = document.getElementById('delivery-address-section');
                                    if (deliveryAddressSection) {
                                      deliveryAddressSection.scrollIntoView({ behavior: 'smooth' });
                                    }
                                  }, 100);
                                }
                              }}
                            >
                              {isActive && method.id === 'delivery' && <Truck className="h-4 w-4 mr-2" />}
                              {isActive && method.id === 'pickup' && <Store className="h-4 w-4 mr-2" />}
                              {t(method.name)}
                            </button>
                          );
                        })}
                      </div>
                    </div>
                  </div>

                  {/* Endereço de entrega (apenas se o método for entrega) */}
                  {form.getValues('receivingMethod') === 'delivery' && (
                    <div id="delivery-address-section">
                      <h3 className="text-lg font-medium flex items-center">
                        <ShoppingBag className="h-5 w-5 mr-2" style={{ color: store?.colors?.primary || '#6082e6' }} />
                        {t('storefront.deliveryAddress')}
                      </h3>
                      <div className="mt-4 space-y-4">
                        <FormField
                          control={form.control}
                          name="deliveryAddress.street"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>{t('storefront.street')}</FormLabel>
                              <FormControl>
                                <Input {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <div className="grid grid-cols-2 gap-4">
                          <FormField
                            control={form.control}
                            name="deliveryAddress.number"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>{t('storefront.number')}</FormLabel>
                                <FormControl>
                                  <Input {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="deliveryAddress.neighborhood"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>{t('storefront.neighborhood')}</FormLabel>
                                <FormControl>
                                  <Input {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                        <FormField
                          control={form.control}
                          name="deliveryAddress.cityState"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>{t('storefront.cityState')}</FormLabel>
                              <FormControl>
                                <Input {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="deliveryAddress.reference"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>{t('storefront.reference')}</FormLabel>
                              <FormControl>
                                <Input {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {deliveryFee && (
                        <div className="mt-4 p-3 bg-muted rounded-md">
                          <div className="flex justify-between text-base font-medium text-neutral-dark">
                            <p>{t('storefront.deliveryFee')}:</p>
                            <p>{formatStoreCurrency(deliveryFee, store?.currency)}</p>
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Data de recebimento */}
                  {form.getValues('receivingMethod') && (
                    <div>
                      <h3 className="text-lg font-medium flex items-center">
                        <CalendarIcon className="h-5 w-5 mr-2" style={{ color: store?.colors?.primary || '#6082e6' }} />
                        {form.getValues('receivingMethod') === 'delivery' ? t('storefront.deliveryDate') : t('storefront.pickupDate')}
                      </h3>
                      <div className="mt-4 space-y-4">
                        <FormField
                          control={form.control}
                          name="receivingDate"
                          render={({ field }) => (
                            <FormItem className="flex flex-col">
                              <FormControl>
                                <StoreThemedCalendar
                                  mode="single"
                                  selected={field.value}
                                  onSelect={field.onChange}
                                  disabled={isDateDisabled}
                                  className="border rounded-xl p-0 shadow-sm ios-style-calendar"
                                  initialFocus
                                  usePrimaryAsSelected={true}
                                  useAccentAsToday={true}
                                />
                              </FormControl>
                              <div className="text-xs text-muted-foreground mt-2">
                                <p>
                                  {store?.deliverySettings?.minAdvanceDays && store.deliverySettings.minAdvanceDays > 0
                                    ? t('storefront.minAdvanceDaysHint', { count: store.deliverySettings.minAdvanceDays })
                                    : t('storefront.selectAvailableDate')}
                                </p>
                              </div>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        {/* Campo de horário para entrega ou retirada */}
                        <FormField
                          control={form.control}
                          name="suggestedTime"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>
                                {form.getValues('receivingMethod') === 'pickup'
                                  ? t('storefront.suggestedPickupTime')
                                  : t('storefront.suggestedDeliveryTime')}
                              </FormLabel>
                              <FormControl>
                                {form.getValues('receivingMethod') === 'delivery' && store?.deliverySettings?.deliveryTimeSlots &&
                                 store.deliverySettings.deliveryTimeSlots.length > 0 ? (
                                  <Select
                                    value={field.value}
                                    onValueChange={field.onChange}
                                  >
                                    <SelectTrigger className="w-full">
                                      <SelectValue placeholder={t('storefront.selectAvailableTime') || 'Selecione um horário disponível'} />
                                    </SelectTrigger>
                                    <SelectContent>
                                      {store.deliverySettings.deliveryTimeSlots.map((timeSlot) => (
                                        <SelectItem key={`time-slot-${timeSlot}`} value={timeSlot}>
                                          {timeSlot}
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                ) : form.getValues('receivingMethod') === 'pickup' && store?.deliverySettings?.pickupTimeSlots &&
                                   store.deliverySettings.pickupTimeSlots.length > 0 ? (
                                  <Select
                                    value={field.value}
                                    onValueChange={field.onChange}
                                  >
                                    <SelectTrigger className="w-full">
                                      <SelectValue placeholder={t('storefront.selectAvailableTime') || 'Selecione um horário disponível'} />
                                    </SelectTrigger>
                                    <SelectContent>
                                      {store.deliverySettings.pickupTimeSlots.map((timeSlot) => (
                                        <SelectItem key={`time-slot-${timeSlot}`} value={timeSlot}>
                                          {timeSlot}
                                        </SelectItem>
                                      ))}
                                    </SelectContent>
                                  </Select>
                                ) : (
                                  <Input
                                    {...field}
                                    type="time"
                                    placeholder="Ex: 14:30"
                                  />
                                )}
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        {/* Mensagem informativa sobre disponibilidade */}
                        {store?.deliverySettings?.customMessage && (
                          <div className="mt-2 text-sm text-muted-foreground">
                            <p>{store.deliverySettings.customMessage}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  <Separator />

                  <div>
                    <h3 className="text-lg font-medium flex items-center">
                      <CreditCard className="h-5 w-5 mr-2" style={{ color: store?.colors?.primary || '#6082e6' }} />
                      {t('storefront.paymentMethod')}
                    </h3>
                    <div className="mt-4  pb-2">
                      <FormField
                        control={form.control}
                        name="paymentMethod"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <div className="grid grid-cols-1 gap-3 mt-2">
                                {getAvailablePaymentMethods().map((method) => {
                                  // Determinar qual ícone usar com base no método
                                  let Icon;
                                  switch(method.id) {
                                    case 'cash':
                                      Icon = () => <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect width="20" height="12" x="2" y="6" rx="2"/><circle cx="12" cy="12" r="2"/><path d="M6 12h.01M18 12h.01"/></svg>;
                                      break;
                                    case 'creditCard':
                                      Icon = () => <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect width="20" height="14" x="2" y="5" rx="2"/><line x1="2" x2="22" y1="10" y2="10"/></svg>;
                                      break;
                                    case 'debitCard':
                                      Icon = () => <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect width="20" height="14" x="2" y="5" rx="2"/><line x1="2" x2="22" y1="10" y2="10"/></svg>;
                                      break;
                                    case 'pix':
                                      Icon = () => <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M12 2v20M2 12h20"/></svg>;
                                      break;
                                    case 'bankTransfer':
                                      Icon = () => <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M2 9c0-1.1.9-2 2-2h16a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V9Z"/><path d="M2 14h20"/></svg>;
                                      break;
                                    default:
                                      Icon = () => <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z"/><path d="m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z"/></svg>;
                                  }

                                  return (
                                    <button
                                      key={method.id}
                                      type="button"
                                      className={`
                                        flex items-center p-4 border-2 rounded-lg text-left
                                        transition-all duration-200 overflow-hidden
                                        ${field.value === method.id
                                          ? 'border-primary bg-primary/10'
                                          : 'border-gray-200 hover:border-gray-300 bg-white'
                                        }
                                      `}
                                      style={{
                                        borderColor: field.value === method.id ? store?.colors?.primary || '#6082e6' : undefined,
                                        backgroundColor: field.value === method.id ? `${store?.colors?.primary}10` || '#6082e610' : undefined,
                                      }}
                                      onClick={() => field.onChange(method.id)}
                                    >
                                      <div className="mr-3" style={{ color: store?.colors?.primary || '#6082e6' }}>
                                        <Icon />
                                      </div>
                                      <div className="font-medium text-base">{t(method.name)}</div>
                                      {field.value === method.id && (
                                        <div className="ml-auto">
                                          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{ color: store?.colors?.primary || '#6082e6' }}><path d="M20 6 9 17l-5-5"/></svg>
                                        </div>
                                      )}
                                    </button>
                                  );
                                })}
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  <div>
                    <FormField
                      control={form.control}
                      name="notes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t('storefront.notes')}</FormLabel>
                          <FormControl>
                            <Textarea
                              {...field}
                              placeholder={t('storefront.notes')}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Resumo do pedido antes do envio */}
                  {form.getValues('receivingMethod') && (
                    <div className="border border-border rounded-md p-4 mt-6 bg-muted/20">
                      <h3 className="text-lg font-medium mb-4 flex items-center">
                        <ShoppingBag className="h-5 w-5 mr-2" style={{ color: store?.colors?.primary || '#6082e6' }} />
                        {t('storefront.orderSummary')}
                      </h3>

                      {/* Lista de produtos */}
                      <div className="space-y-2 mb-4">
                        <h4 className="text-sm font-medium">{t('storefront.productsLabel')}</h4>
                        {items.map((item) => (
                          <div key={item.id} className="flex justify-between text-sm">
                            <div className="flex-1 pr-2">
                              <p>{item.quantity}x {item.name}</p>
                              {item.selectedVariations && item.selectedVariations.length > 0 && (
                                <span className="block text-xs text-muted-foreground">
                                  {item.selectedVariations.map(v => `${v.variationName}: ${v.optionName}`).join(', ')}
                                </span>
                              )}
                              {item.observation && (
                                <span className="block text-xs text-muted-foreground italic mt-1">
                                  {t('storefront.observation')}: {item.observation}
                                </span>
                              )}
                            </div>
                            <p>{formatStoreCurrency(calculateItemSubtotal(item), store?.currency)}</p>
                          </div>
                        ))}
                      </div>

                      <Separator className="my-4" />

                      {/* Dados do cliente */}
                      <div className="mb-4">
                        <h4 className="text-sm font-medium mb-2">{t('storefront.customerInfoSummary')}</h4>
                        <div className="flex flex-col space-y-1 text-sm">
                          <p>{form.getValues('customer.name')}</p>
                          <p className="text-muted-foreground">{t('storefront.customerContact')}: {formatPhoneWithCountryCode(form.getValues('customer.whatsapp'), form.getValues('customer.countryCode'))}</p>
                          {form.getValues('customer.email') && (
                            <p className="text-muted-foreground">{form.getValues('customer.email')}</p>
                          )}
                        </div>
                      </div>

                      <Separator className="my-4" />

                      {/* Dados de entrega/retirada */}
                      <div className="mb-4">
                        <h4 className="text-sm font-medium mb-2">
                          {form.getValues('receivingMethod') === 'delivery'
                            ? t('storefront.deliveryDetails')
                            : t('storefront.pickupDetails')
                          }
                        </h4>
                        <div className="bg-white/80 p-3 rounded-md shadow-sm border">
                          <div className="flex items-center text-sm mb-3">
                            {form.getValues('receivingMethod') === 'delivery' ? (
                              <Truck className="h-5 w-5 mr-2" style={{ color: store?.colors?.primary || '#6082e6' }} />
                            ) : (
                              <Store className="h-5 w-5 mr-2" style={{ color: store?.colors?.primary || '#6082e6' }} />
                            )}
                            <p className="font-medium">
                              {form.getValues('receivingMethod') === 'delivery'
                                ? t('storefront.delivery') || 'Entrega'
                                : t('storefront.pickup') || 'Retirada'
                              }
                            </p>
                          </div>

                          {/* Data e horário em destaque */}
                          <div className="bg-gray-100 p-2 rounded-md mb-3">
                            {form.getValues('receivingDate') && (
                              <div className="flex items-center text-sm">
                                <CalendarIcon className="h-4 w-4 mr-2" style={{ color: store?.colors?.primary || '#6082e6' }} />
                                <p><strong>{t('storefront.date')}:</strong> {format(form.getValues('receivingDate'), 'dd/MM/yyyy')}</p>
                              </div>
                            )}

                            {form.getValues('suggestedTime') && (
                              <div className="flex items-center text-sm mt-1">
                                <Clock className="h-4 w-4 mr-2" style={{ color: store?.colors?.primary || '#6082e6' }} />
                                <p><strong>{t('storefront.time')}:</strong> {form.getValues('suggestedTime')}</p>
                              </div>
                            )}
                          </div>

                          {/* Endereço de entrega */}
                          {form.getValues('receivingMethod') === 'delivery' && (
                            <div className="flex items-start text-sm">
                              <MapPin className="h-4 w-4 mr-2 mt-0.5" style={{ color: store?.colors?.primary || '#6082e6' }} />
                              <div>
                                <p><strong>{t('storefront.deliveryAddressTitle')}:</strong></p>
                                <p className="text-muted-foreground">
                                  {form.getValues('deliveryAddress.street')}, {form.getValues('deliveryAddress.number')}
                                </p>
                                <p className="text-muted-foreground">
                                  {form.getValues('deliveryAddress.neighborhood')}, {form.getValues('deliveryAddress.cityState')}
                                </p>
                                {form.getValues('deliveryAddress.reference') && (
                                  <p className="text-muted-foreground italic mt-1">
                                    {t('storefront.reference')}: {form.getValues('deliveryAddress.reference')}
                                  </p>
                                )}
                              </div>
                            </div>
                          )}

                          {/* Local de retirada para pickup */}
                          {form.getValues('receivingMethod') === 'pickup' && (
                            <div className="flex items-start text-sm">
                              <Store className="h-4 w-4 mr-2 mt-0.5" style={{ color: store?.colors?.primary || '#6082e6' }} />
                              <div>
                                <p><strong>{t('storefront.pickupLocation')}:</strong></p>
                                <p className="text-muted-foreground">{store?.name}</p>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>

                      <Separator className="my-4" />

                      {/* Valores */}
                      <div className="space-y-2">
                        <div className="flex justify-between text-base font-medium text-neutral-dark">
                          <p>{t('storefront.subtotal')}</p>
                          <p>{formatStoreCurrency(subtotal, store?.currency)}</p>
                        </div>

                        {discount > 0 && coupon && (
                          <div className="flex justify-between text-base font-medium text-green-600">
                            <div className="flex flex-col">
                              <p>{t('storefront.discount')}</p>
                              <p className="text-xs text-muted-foreground">
                                {coupon.code} - {coupon.tipo === 'valor_fixo'
                                  ? t('storefront.fixedDiscount', { value: formatStoreCurrency(coupon.valor, store?.currency) })
                                  : t('storefront.percentDiscount', { value: coupon.valor })}
                              </p>
                            </div>
                            <p>-{formatStoreCurrency(discount, store?.currency)}</p>
                          </div>
                        )}

                        {form.getValues('receivingMethod') === 'delivery' && deliveryFee && (
                          <div className="flex justify-between text-base font-medium text-neutral-dark">
                            <p>{t('storefront.deliveryFee')}</p>
                            <p>{formatStoreCurrency(deliveryFee, store?.currency)}</p>
                          </div>
                        )}

                        <div className="flex justify-between text-base font-medium pt-2 border-t border-border mt-2" style={{ color: store?.colors?.primary || '#6082e6' }}>
                          <p>{t('storefront.total')}</p>
                          <p>{formatStoreCurrency(totalWithDelivery, store?.currency)}</p>
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="pt-6">
                    <Button
                      type="button"
                      className="w-full hover:opacity-90 text-lg py-6 font-medium"
                      style={{
                        backgroundColor: store?.colors?.primary || '#6082e6',
                        color: '#FFFFFF'
                      }}
                      disabled={orderMutation.isPending}
                      onClick={() => {
                        console.log('Order button clicked (manual submit)');
                        // Validar o formulário manualmente
                        const formData = form.getValues();
                        console.log('Form data:', formData);

                        // Verificar se há itens no carrinho
                        if (items.length === 0) {
                          toast({
                            title: t('common.error'),
                            description: t('storefront.emptyCart'),
                            variant: "destructive",
                          });
                          return;
                        }

                        // Verificar se o método de recebimento está selecionado
                        if (!formData.receivingMethod) {
                          toast({
                            title: t('common.error'),
                            description: t('landing.pleaseSelectReceivingMethod'),
                            variant: "destructive",
                          });
                          return;
                        }

                        // Verificar se a data está disponível
                        if (!formData.receivingDate || isDateDisabled(formData.receivingDate)) {
                          toast({
                            title: t('common.error'),
                            description: t('landing.pleaseSelectValidDate'),
                            variant: "destructive",
                          });
                          return;
                        }

                        // Verificar se o método de pagamento foi selecionado
                        if (!formData.paymentMethod) {
                          toast({
                            title: t('common.error'),
                            description: t('landing.pleaseSelectPaymentMethod'),
                            variant: "destructive",
                          });
                          return;
                        }

                        // Chamar a função de envio diretamente
                        onSubmit(formData);
                      }}
                    >
                      {orderMutation.isPending ? (
                        <span className="flex items-center">
                          <span className="mr-2 h-4 w-4 animate-spin rounded-full border-t-2 border-b-2 border-white"></span>
                          {t('common.loading')}
                        </span>
                      ) : (
                        t('storefront.placeOrder')
                      )}
                    </Button>
                  </div>
                </form>
              </Form>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default CartPage;