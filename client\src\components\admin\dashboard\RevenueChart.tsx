import { Card } from "@/components/ui/card";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

interface RevenueChartProps {
  data: Array<{
    month: string;
    revenue: number;
    orders: number;
  }>;
}

export function RevenueChart({ data }: RevenueChartProps) {
  return (
    <Card className="p-6">
      <h3 className="text-lg font-semibold mb-4">Receita dos Últimos 6 Meses</h3>
      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip 
              formatter={(value, name) => [
                `R$ ${Number(value).toFixed(2)}`, 
                name === 'revenue' ? 'Receita' : 'Pedidos'
              ]}
              labelFormatter={(label) => `Mês: ${label}`}
            />
            <Bar dataKey="revenue" fill="#4361EE" name="revenue" />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </Card>
  );
}