import i18n from "i18next";
import { initReactI18next } from "react-i18next";

// Portuguese translations
const ptBrTranslations = {
  admin: {
    categories: "Categorias",
    newCategory: "Nova Categoria",
    noCategories: "Nenhuma categoria encontrada",
    editCategory: "Editar Categoria",
    noDescription: "Sem descrição",
    categoryName: "Nome da categoria",
    categoryDescription: "Descrição da categoria",
    categoryLogo: "Logo da categoria",
    categoryVisible: "Visível na loja",
    logoDescription:
      "Carregue uma imagem para esta categoria (tamanho recomendado: 800x600px)",
    uploadLogo: "Carregar logo",
    reorderCategories: "Reordenar Categorias",
    changeImage: "Alterar imagem",
    visibilityUpdated: "Visibilidade da categoria atualizada com sucesso!",
    visible: "Visível",
    hidden: "Oculta",
    categoryAdded: "Categoria adicionada com sucesso!",
    categoryUpdated: "Categoria atualizada com sucesso!",
    categoryDeleted: "Categoria excluída com sucesso!",
    categoryCreated: "Categoria criada",
    categoryCreatedDesc: "A categoria foi criada com sucesso.",
    categoryUpdatedDesc: "A categoria foi atualizada com sucesso.",
    categoryDeletedDesc: "A categoria foi excluída com sucesso.",
  },
  coupons: {
    title: "Cupons",
    addCoupon: "Adicionar Cupom",
    editCoupon: "Editar Cupom",
    deleteCoupon: "Excluir Cupom",
    couponCode: "Código do cupom",
    couponType: "Tipo de desconto",
    couponValue: "Valor do desconto",
    minPurchase: "Valor mínimo de compra",
    expirationDate: "Data de validade",
    singleUse: "Uso único",
    active: "Ativo",
    inactive: "Inativo",
    status: "Status",
    actions: "Ações",
    fixedValue: "Valor fixo",
    percentage: "Percentual",
    couponAdded: "Cupom adicionado com sucesso!",
    couponUpdated: "Cupom atualizado com sucesso!",
    couponDeleted: "Cupom excluído com sucesso!",
    couponActivated: "Cupom ativado com sucesso!",
    couponDeactivated: "Cupom desativado com sucesso!",
    confirmDelete: "Tem certeza que deseja excluir este cupom?",
    confirmDeleteDesc: "Esta ação não pode ser desfeita",
    noCoupons: "Nenhum cupom encontrado",
    confirmRemove: "Tem certeza que deseja remover o cupom desta revisão?",
    couponRemoved: "Cupom removido",
    couponRemovedFromRevision: "O cupom foi removido desta revisão",
    errorRemovingCoupon: "Erro ao remover cupom",
    codeExists: "Já existe um cupom com este código",
    invalidCode: "Código de cupom inválido. Use apenas letras, números e traços.",
    invalidValue: "O valor deve ser maior que zero",
    invalidMinPurchase: "O valor mínimo de compra deve ser maior ou igual a zero",
    invalidExpirationDate: "A data de validade deve ser futura",
    formTitle: "Detalhes do cupom",
    formDescription: "Preencha os detalhes do cupom de desconto",
    discountPercentage: "Desconto de {{value}}%",
  },
  common: {
    login: "Entrar",
    register: "Registrar",
    email: "Email",
    password: "Senha",
    forgotPassword: "Esqueceu a senha?",
    noAccount: "Não tem uma conta?",
    hasAccount: "Já tem uma conta?",
    fullName: "Nome completo",
    submit: "Enviar",
    save: "Salvar",
    saving: "Salvando...",
    update: "Atualizar",
    cancel: "Cancelar",
    delete: "Excluir",
    deleting: "Excluindo...",
    edit: "Editar",
    change: "Alterar",
    add: "Adicionar",
    search: "Buscar",
    clearSearch: "Limpar busca",
    clear: "Limpar",
    apply: "Aplicar",
    error: "Erro",
    success: "Sucesso",
    loading: "Carregando...",
    creating: "Criando...",
    required: "Este campo é obrigatório",
    invalidEmail: "Email inválido",
    minLength: "Digite pelo menos {{count}} caracteres",
    logout: "Sair",
    view: "Visualizar",
    viewDetails: "Ver detalhes",
    back: "Voltar",
    goBack: "Voltar",
    next: "Próximo",
    finish: "Finalizar",
    refresh: "Atualizar",
    refreshing: "Atualizando dados",
    confirmDelete: "Tem certeza que deseja excluir?",
    yes: "Sim",
    no: "Não",
    actions: "Ações",
    status: "Status",
    date: "Data do pedido",
    welcome: "Bem-vindo(a)",
    home: "Início",
    settings: "Configurações",
    productsNav: "Produtos",
    products: "Produtos",
    categories: "Categorias",
    orders: "Pedidos",
    customers: "Clientes",
    dashboard: "Dashboard",
    viewStore: "Ver loja",
    pageNotFound: "Página não encontrada",
    pageNotFoundDescription:
      "A página que você está procurando não existe ou foi movida.",
  },
  landing: {
    features: "Recursos",
    howItWorks: "Como Funciona",
    pricing: "Preços",
    getStarted: "Começar Grátis",
    learnMore: "Saiba mais",
    heroTitle: "Crie sua loja online em",
    heroTitleHighlight: "poucos minutos",
    heroSubtitle:
      "Plataforma completa para criar catálogos de produtos online e gerenciar seus pedidos com facilidade.",
    demoStore: "Loja de demonstração",
    productsDemo: "Produtos",
    orders: "Pedidos",
    recentVisits: "Visitas recentes",
    thisMonth: "Este mês",

    // Demo Store Card - Dashboard Real
    demoStoreTitle: "Doce Encanto",
    demoStoreUrl: "app.docemenu.com.br/doceencanto",
    demoDashboardTitle: "Dashboard",

    // Métricas principais do dashboard real
    demoPendingOrders: "Pedidos Pendentes",
    demoConfirmedOrders: "Pedidos Confirmados",
    demoMonthlyRevenue: "Receita Mensal",
    demoTotalRevenue: "Receita Total",
    demoAverageTicket: "Ticket Médio",
    demoVisitsMonth: "Visitas no Mês",
    demoConversionRate: "Taxa de Conversão",

    // Seções do dashboard
    demoFinancialSummary: "Resumo Financeiro",
    demoTopProducts: "Top Produtos",
    demoPendingOrdersList: "Pedidos em Andamento",
    demoSiteActivity: "Atividade do Site",

    // Dados realistas de confeitaria
    demoBrigadeiro: "Brigadeiro Gourmet",
    demoBrownie: "Brownie de Chocolate",
    demoCupcake: "Cupcake Red Velvet",
    demoTorta: "Torta de Morango",
    demoBemCasado: "Bem Casado",
    demoTrufas: "Trufas Sortidas",

    // Status dos pedidos
    demoOrderConfirmed: "Confirmado",
    demoOrderPending: "Pendente",
    demoOrderDelivered: "Entregue",
    demoOrderLate: "Atrasado",

    // Valores e métricas
    demoThisMonth: "Este mês",
    demoToday: "Hoje",
    demoGrowth: "Crescimento",
    featuresTitle: "Tudo o que você precisa para vender online",
    featuresSubtitle:
      "Recursos essenciais para gerenciar seu catálogo de produtos e vender mais.",
    feature1Title: "Catálogo de Produtos",
    feature1Desc:
      "Crie um catálogo completo com categorias, imagens e descrições detalhadas.",
    feature2Title: "Gestão de Pedidos",
    feature2Desc:
      "Acompanhe todos os pedidos e atualize seus status com facilidade.",
    feature3Title: "Painel de Controle",
    feature3Desc: "Visualize estatísticas e gerencie sua loja com um painel intuitivo.",

    delivery: "Entrega",
    pickup: "Retirada",
    deliveryToAddress: "Entrega no endereço",
    pickupAtStore: "Retirada no local",
    receivingMethod: "Como deseja receber seu pedido?",
    deliveryAddress: "Endereço de entrega",
    deliveryAddressTitle: "Entrega no endereço",
    receivingMethodTitle: "Método de Recebimento",
    pickupLocation: "Retirada no local",
    street: "Rua",
    number: "Número",
    neighborhood: "Bairro",
    productsLabel: "Produtos",
    customerInfoSummary: "Dados do Cliente",
    customerContact: "Contato",
    cityState: "Cidade e Estado",
    reference: "Ponto de referência (opcional)",
    deliveryFee: "Taxa de entrega",
    deliveryDate: "Data para entrega",
    pickupDate: "Data para retirada",
    suggestedDeliveryTime: "Horário de entrega",
    suggestedPickupTime: "Horário de retirada",
    selectAvailableTime: "Selecione um horário disponível",
    pleaseSelectReceivingMethod: "Por favor, selecione como deseja receber seu pedido (entrega ou retirada).",
    pleaseSelectValidDate: "Por favor, selecione uma data válida de recebimento.",
    pleaseCompleteAddress: "Por favor, complete todos os campos de endereço para entrega.",
    pleaseSelectPaymentMethod: "Por favor, selecione um método de pagamento.",
    pleaseCompleteCustomerInfo: "Por favor, preencha todos os dados do cliente.",
    invalidEmailFormat: "Por favor, forneça um e-mail válido.",
    invalidWhatsAppNumber: "O número de WhatsApp deve ter pelo menos 8 dígitos.",
    minAdvanceDaysRequired: "É necessário solicitar com pelo menos {{days}} dia(s) de antecedência.",
    selectAvailableDate: "Selecione um dia disponível no calendário.",
    orderSummary: "Resumo do Pedido",
    productsInOrder: "Produtos",
    time: "Horário",

    feature4Title: "Base de Clientes",
    feature4Desc:
      "Mantenha um registro dos seus clientes e suas preferências de compra.",
    feature5Title: "Multilíngue",
    feature5Desc:
      "Disponível em português e inglês para atender um público mais amplo.",
    feature6Title: "Rápido e Fácil",
    feature6Desc:
      "Configure sua loja em minutos e comece a vender imediatamente.",
    howItWorksTitle: "Como Funciona",
    howItWorksSubtitle:
      "Três passos simples para começar a vender seus produtos online.",
    step1Title: "Crie sua loja",
    step1Desc:
      "Registre-se e configure sua loja com nome, logo e configurações personalizadas.",
    step2Title: "Adicione seus produtos",
    step2Desc:
      "Cadastre seus produtos com fotos, descrições e preços para montar seu catálogo.",
    step3Title: "Comece a vender",
    step3Desc:
      "Compartilhe o link da sua loja e comece a receber pedidos imediatamente.",
    pricingTitle: "Planos e Preços",
    pricingSubtitle:
      "Escolha o plano ideal para o seu negócio. Comece grátis e faça upgrade quando precisar.",

    // Planos atualizados
    freePlan: "Plano Gratuito",
    freePlanDesc: "Ideal para quem está começando.",
    premiumPlan: "Plano Premium",
    premiumPlanDesc: "Para negócios que querem crescer.",
    popular: "MAIS POPULAR",

    // Preços
    freePrice: "Gratuito",
    premiumPriceMonthly: "R$ 29,90",
    premiumPriceYearly: "R$ 299,00",
    perMonth: "/mês",
    perYear: "/ano",
    monthlyEquivalent: "R$ 24,92/mês",
    savePercentage: "Economize 16,6%",

    // Toggle de preços
    monthly: "Mensal",
    yearly: "Anual",
    billingToggle: "Faturamento",

    // Trial
    trialOffer: "7 dias grátis",
    trialDescription: "Teste todas as funcionalidades premium por 7 dias",
    // Funcionalidades por plano
    freeFeatures: {
      products: "Até 10 produtos",
      orders: "Máximo 5 pedidos/mês",
      store: "Loja online básica",
      support: "Suporte por email"
    },
    premiumFeatures: {
      products: "Até 50 produtos",
      orders: "Pedidos ilimitados",
      pdf: "Geração de PDF de pedidos",
      analytics: "Dashboard e relatórios completos",
      whatsapp: "Integração WhatsApp",
      coupons: "Sistema de cupons de desconto",
      customization: "Personalização visual avançada",
      support: "Suporte prioritário"
    },

    // Comparação de funcionalidades
    featuresComparison: "Comparação de Funcionalidades",
    featuresComparisonSubtitle: "Compare todas as funcionalidades disponíveis em cada plano",
    feature: "Funcionalidade",
    included: "Incluído",
    notIncluded: "Não incluído",

    // CTA final
    ctaFinalText: "Sem cartão de crédito • Cancele a qualquer momento",

    // Funcionalidades específicas
    productManagement: "Gerenciamento de Produtos",
    orderManagement: "Gestão de Pedidos",
    customerManagement: "Base de Clientes",
    pdfGeneration: "Geração de PDF",
    dashboardAnalytics: "Dashboard e Analytics",
    whatsappIntegration: "Integração WhatsApp",
    couponSystem: "Sistema de Cupons",
    visualCustomization: "Personalização Visual",
    prioritySupport: "Suporte Prioritário",

    // System Features Section
    systemFeaturesTitle: "Sistema Completo para Confeitarias",
    systemFeaturesSubtitle: "Todas as ferramentas que você precisa para gerenciar e fazer crescer seu negócio de doces",

    completeCatalog: "Catálogo Completo",
    completeCatalogDesc: "Organize seus produtos em categorias, adicione variações (tamanhos, sabores), gerencie estoque e preços com facilidade.",
    completeCatalogFeatures: {
      variations: "Produtos com variações",
      images: "Múltiplas imagens",
      inventory: "Controle de estoque"
    },

    smartOrders: "Pedidos Inteligentes",
    smartOrdersDesc: "Sistema completo de pedidos com revisões, controle de pagamentos, agendamento de entrega e retirada.",
    smartOrdersFeatures: {
      revisions: "Revisão de pedidos",
      payments: "Controle de pagamentos",
      scheduling: "Agendamento flexível"
    },

    advancedAnalytics: "Analytics Avançado",
    advancedAnalyticsDesc: "Acompanhe o desempenho do seu negócio com relatórios detalhados e insights sobre vendas e clientes.",
    advancedAnalyticsFeatures: {
      financial: "Relatórios financeiros",
      products: "Análise de produtos",
      customers: "Métricas de clientes"
    },

    customerManagementFull: "Gestão de Clientes",
    customerManagementFullDesc: "Mantenha um cadastro completo dos seus clientes com histórico de pedidos e preferências.",
    customerManagementFullFeatures: {
      history: "Histórico completo",
      contact: "Dados de contato",
      whatsapp: "Integração WhatsApp"
    },

    couponsPromotions: "Cupons e Promoções",
    couponsPromotionsDesc: "Crie campanhas promocionais com cupons de desconto personalizados para aumentar suas vendas.",
    couponsPromotionsFeatures: {
      discount: "Desconto fixo ou %",
      minimum: "Valor mínimo",
      expiry: "Data de validade"
    },

    professionalPrinting: "Impressão Profissional",
    professionalPrintingDesc: "Gere PDFs profissionais dos pedidos para impressão em formato A4 ou térmico (80mm).",
    professionalPrintingFeatures: {
      layout: "Layout profissional",
      logo: "Logo da empresa",
      formats: "Múltiplos formatos"
    },

    // CTAs
    ctaTitle: "Pronto para expandir seu negócio online?",
    ctaSubtitle:
      "Crie sua loja agora mesmo e comece a vender seus produtos para todo o Brasil.",
    getStartedToday: "Comece hoje mesmo",
    startFreeTrial: "Iniciar Teste Grátis",
    upgradeToPremium: "Assinar Premium",
    footerTagline:
      "Plataforma de e-commerce para criar e gerenciar catálogos de produtos online.",
    footerProduct: "Produto",
    footerFaq: "FAQ",
    footerCompany: "Empresa",
    footerAbout: "Sobre nós",
    footerBlog: "Blog",
    footerCareers: "Carreiras",
    footerLegal: "Legal",
    footerTerms: "Termos de uso",
    footerPrivacy: "Privacidade",
    footerCookies: "Cookies",
    footerRights: "Todos os direitos reservados.",
  },
  auth: {
    title: "Doce Menu",
    subtitle: "Gerencie sua loja online",
    loginTitle: "Entrar na sua conta",
    registerTitle: "Criar conta",
    authError: "Ocorreu um erro na autenticação",
    loginSuccess: "Login realizado com sucesso!",
    registerSuccess: "Cadastro realizado com sucesso!",
    invalidCredentials: "Email ou senha inválidos",
    passwordMinLength: "A senha deve ter pelo menos 6 caracteres",
    emailRequired: "O email é obrigatório",
    passwordRequired: "A senha é obrigatória",
    nameRequired: "O nome é obrigatório",
    loginWithGoogle: "Entrar com Google",
    registerWithGoogle: "Cadastrar com Google",
    loginWithGoogleDesc: "Use sua conta Google para fazer login",
    registerWithGoogleDesc: "Crie uma conta com Google para continuar",
    username: "Nome de usuário",
    confirmPassword: "Confirmar senha",
    creatingAccount: "Criando conta...",
    loggingIn: "Entrando...",
  },
  dashboard: {
    title: "Dashboard",
    welcome: "Bem-vindo(a) à sua loja",
    visitsThisMonth: "Visitas este mês",
    ordersThisMonth: "Pedidos este mês",
    revenueThisMonth: "Faturamento este mês",
    viewDetails: "Ver detalhes",
    viewAllOrders: "Ver todos os pedidos",
    viewFinancialReport: "Ver relatório financeiro",
    recentOrders: "Pedidos Recentes",
    orderID: "ID",
    customer: "Cliente",
    orderDate: "Data",
    orderValue: "Valor",
    orderStatus: "Status",
    completed: "Entregue",
    pending: "Pendente",
    confirmed: "Confirmado",
    delivered: "Entregue",
    processing: "Em processamento",
    shipped: "Enviado",
    cancelled: "Cancelado",
    financialAnalysis: "Análise Financeira",
    periodFilter: {
      title: "Período",
      week: "Semana",
      month: "Mês",
      quarter: "Trimestre",
      year: "Ano",
      last7Days: "Últimos 7 dias",
      last30Days: "Últimos 30 dias",
      last90Days: "Últimos 90 dias",
      last365Days: "Últimos 365 dias",
      thisWeek: "Esta semana",
      thisMonth: "Este mês",
      thisQuarter: "Este trimestre",
      thisYear: "Este ano"
    },
    financialCards: {
      totalRevenue: "Receita Total",
      monthlyRevenue: "Receita Mensal",
      averageTicket: "Ticket Médio",
      paidOrders: "Pedidos Pagos",
      perPaidOrder: "Por pedido pago",
      lastMonths: "Últimos 6 meses",
      trend: "Tendência",
      growth: "Crescimento",
      decline: "Declínio"
    }
  },
  products: {
    title: "Produtos",
    addProduct: "Adicionar Produto",
    editProduct: "Editar Produto",
    deleteProduct: "Excluir Produto",
    productName: "Nome do produto",
    productDescription: "Descrição",
    productPrice: "Preço",
    productImage: "Imagem",
    productImages: "Imagens",
    imageUrl: "URL da imagem",
    imagesHelperText:
      "Você pode adicionar até 3 imagens para cada produto. A primeira imagem será exibida como principal.",
    productCategory: "Categoria",
    inStock: "Em estoque",
    productDetails: "Detalhes do produto",
    productDetailsDescription: "Preencha os detalhes do produto.",
    changeImage: "Alterar imagem",
    addMainImage: "Adicionar imagem principal",
    addExtraImage: "Adicionar imagem extra",
    hasVariations: "Possui variações",
    productAddedToast: "Produto adicionado",
    productAddedToRevision: "O produto foi adicionado à revisão do pedido",
    customProductAddedToRevision: "O produto personalizado foi adicionado à revisão do pedido",
    addToRevision: "Adicionar à Revisão",
    visibilityUpdated: "Visibilidade do produto atualizada com sucesso!",
    noSearchResults: "Nenhum produto encontrado para",
    searchProducts: "Buscar produtos...",
    noProductsFound: "Nenhum produto encontrado",
    noProducts: "Nenhum produto disponível",
    tryAnotherSearch: "Tente outra busca",
    addProductsFirst: "Adicione produtos primeiro",
    hasVariationsDescription:
      "O produto possui opções como tamanho, sabor, etc.",
    variations: "Variações",
    variation: "Variação",
    addVariation: "Adicionar variação",
    noVariations:
      'Nenhuma variação adicionada. Clique em "Adicionar variação" para começar.',
    variationName: "Nome da variação",
    variationNamePlaceholder: "Ex: Tamanho, Sabor, Cor",
    required: "Obrigatório",
    requiredDescription:
      "O cliente deve selecionar uma opção para esta variação.",
    multipleChoice: "Múltipla escolha",
    multipleChoiceDescription: "O cliente pode escolher mais de uma opção.",
    minSelections: "Seleções mínimas",
    maxSelections: "Seleções máximas",
    options: "Opções",
    addOption: "Adicionar opção",
    noOptions: "Adicione opções para esta variação.",
    optionNamePlaceholder: "Ex: Pequeno, Médio, Grande",
    outOfStock: "Esgotado",
    productAdded: "Produto adicionado com sucesso!",
    regularProductAddedToOrder: "O produto foi adicionado ao pedido",
    productUpdated: "Produto atualizado com sucesso!",
    updateSuccess: "Produto atualizado",
    updateSuccessDescription: "O produto foi atualizado com sucesso.",
    updateError: "Erro ao atualizar produto",
    updateErrorDescription:
      "Ocorreu um erro ao atualizar o produto. Tente novamente.",
    loadingError: "Erro ao carregar produto",
    tryAgain: "Tente novamente mais tarde.",
    imageUploadError: "Erro ao enviar imagem",
    imageUploadErrorDescription:
      "Ocorreu um erro ao enviar a imagem. Tente novamente.",
    productDeleted: "Produto excluído com sucesso!",
    confirmDelete: "Tem certeza que deseja excluir este produto?",
    uploadImage: "Carregar imagem",
    selectCategory: "Selecione uma categoria",
    available: "Disponível",
    unavailable: "Indisponível",
    noCategory: "Sem categoria",
    additionalPrice: "Preço adicional",
    optionPrice: "Preço adicional",
    hasVariationsShort: "Variações",
    uncategorized: "Sem categoria",
    deleting: "Excluindo...",
    newProduct: "Novo produto",
    quantity: "Quantidade",
    total: "Total",
    product: "Produto",
    unitPrice: "Preço Unitário",
    price: "Preço",
    originalPrice: "Preço original",
    priceAndQuantity: "Preço e Quantidade",
    observation: "Observação",
    addObservation: "Adicione uma observação...",
    other: "Outros",
    customOptionsDescription: "Adicione opções personalizadas como texto livre, valor e quantidade.",
    customOptionName: "Descrição",
    customOptionNamePlaceholder: "Ex: Decoração especial",
    addCustomOption: "Adicionar",
    noCustomOptions: "Nenhuma opção personalizada adicionada",
    customOptionNameRequired: "O nome da opção é obrigatório",
    missingRequired: "Opções obrigatórias",
    selectAllRequired: "Por favor, selecione todas as opções obrigatórias",
    notFound: "Produto não encontrado",
    itemUpdated: "Item atualizado",
    itemUpdatedDesc: "O item foi atualizado com sucesso",
    itemRemoved: "Item removido",
    itemRemovedFromRevision: "O item foi removido desta revisão",
    errorRemovingItem: "Erro ao remover item",
    confirmRemoveItem: "Tem certeza que deseja remover este item da revisão?",
  },
  categories: {
    title: "Categorias",
    addCategory: "Adicionar Categoria",
    editCategory: "Editar Categoria",
    deleteCategory: "Excluir Categoria",
    categoryName: "Nome da categoria",
    categoryDescription: "Descrição",

    confirmDelete: "Tem certeza que deseja excluir esta categoria?",
    noCategories: "Nenhuma categoria encontrada",
    newCategory: "Nova Categoria",
    categoryFormDescription: "Preencha os detalhes da categoria",
    noDescription: "Sem descrição",
    confirmDeleteCategoryDesc: "Esta ação não pode ser desfeita",
    uploadLogo: "Carregar logo",
    categoryLogo: "Logo da categoria",
    logoHelperText: "Carregar uma imagem para esta categoria (opcional)",
    categoryDetails: "Detalhes da Categoria",
  },
  orders: {
    title: "Pedidos",
    status: "Status",
    pending: "Pendente",
    createdAt: "Data de criação",
    orderDetails: "Detalhes do pedido",
    orderDetailsDescription: "Selecione o cliente e adicione produtos ao pedido",
    orderItems: "Produtos",
    orderTotal: "Total",
    orderDate: "Data",
    orderStatus: "Status",
    customerInfo: "Informações do cliente",
    paymentMethod: "Método de pagamento",
    paymentStatus: "Status Pagamento",
    notes: "Observações",
    updateStatus: "Atualizar status",
    statusUpdated: "Status atualizado com sucesso!",
    noOrders: "Nenhum pedido encontrado",
    noOrdersMatchingFilters: "Nenhum pedido corresponde aos filtros selecionados",
    itemsCount: "{{count}} itens",
    viewDetails: "Ver detalhes",
    failedToLoadOrder: "Erro ao carregar detalhes do pedido",
    quantity: "Quantidade",
    unitPrice: "Preço unitário",
    subtotal: "Subtotal",
    orderSummary: "Resumo do Pedido",
    backToOrders: "Voltar para pedidos",
    selectProducts: "Selecionar Produtos",
    addProduct: "Adicionar Produto",
    addProductToRevision: "Adicionar Produto à Revisão",
    selectCustomerDesc: "Selecione um cliente para esta revisão de pedido. Isso alterará o cliente associado à revisão.",
    currentCustomer: "Cliente Atual",
    selectCustomer: "Selecionar",
    customerChanged: "Cliente Alterado",
    customerChangedSuccess: "O cliente foi alterado com sucesso para esta revisão.",
    observation: "Observação",
    noObservation: "Sem observação",
    revised: "Revisado",
    lastRevision: "Última revisão",
    revisionNumber: "Revisão",
    ordersList: "Lista de Pedidos",
    newOrder: "Novo Pedido",
    selectProductsPage: "Selecionar Produtos",
    continueToOrder: "Continuar para o pedido",
    deliveryAddress: "Endereço de Entrega",
    street: "Rua",
    streetPlaceholder: "Rua, Avenida, etc.",
    number: "Número",
    numberPlaceholder: "Número",
    complement: "Complemento",
    complementPlaceholder: "Apto, Bloco, etc.",
    neighborhood: "Bairro",
    neighborhoodPlaceholder: "Bairro",
    cityState: "Cidade/Estado",
    cityStatePlaceholder: "Cidade/Estado",
    reference: "Ponto de Referência",
    referencePlaceholder: "Próximo a...",
    deliveryAddressRequired: "Endereço de entrega obrigatório",
    pleaseCompleteDeliveryAddress: "Por favor, preencha os campos obrigatórios do endereço de entrega",
    requiredFieldsNote: "Campos com * são obrigatórios",
    delivery: "Entrega",
    pickup: "Retirada",
    deliveryFee: "Taxa de Entrega",
    editDeliveryFee: "Editar Taxa de Entrega",
    saveDeliveryFee: "Salvar Taxa de Entrega",
    cancelEdit: "Cancelar Edição",
    deliveryFeeUpdated: "Taxa de entrega atualizada com sucesso",
    errorUpdatingDeliveryFee: "Erro ao atualizar taxa de entrega",
    editDiscount: "Editar Desconto",
    discountUpdated: "Desconto atualizado com sucesso",
    errorUpdatingDiscount: "Erro ao atualizar desconto",
    changeCustomer: "Trocar Cliente",
    selectCustomerButton: "Selecionar Cliente",
    searchCustomers: "Buscar Clientes",
    customerUpdated: "Cliente atualizado com sucesso",
    errorUpdatingCustomer: "Erro ao atualizar cliente",
    noCustomersFound: "Nenhum cliente encontrado",
    tryAnotherSearch: "Tente outra busca",
    searchToFindCustomers: "Digite para buscar clientes",
    refreshingData: "Buscando as informações mais recentes do pedido...",
    dataRefreshed: "Dados do pedido atualizados com sucesso.",
    refreshError: "Ocorreu um erro ao atualizar os dados. Tente novamente.",
    searchById: "Buscar por ID",
    searchByCustomer: "Buscar por cliente",
    filterByStatus: "Filtrar por status",
    filterByDate: "Filtrar por data",
    dateFilters: "Filtros de data",
    dateFilterType: "Tipo de filtro de data",
    selectDateFilterType: "Selecione o tipo de filtro",
    noDateFilter: "Sem filtro de data",
    monthlyFilter: "Filtro mensal",
    specificDateFilter: "Data específica",
    selectMonth: "Selecione o mês",
    currentMonth: "Mês atual",
    previousMonth: "Mês anterior",
    twoMonthsAgo: "Dois meses atrás",
    threeMonthsAgo: "Três meses atrás",
    selectDate: "Selecione a data",
    startDate: "Data de início",
    endDate: "Data de fim",
    pickStartDate: "Escolha a data de início",
    pickEndDate: "Escolha a data de fim",
    dateRangeSummary: "Período: {{start}} até {{end}}",
    from: "A partir de",
    until: "Até",
    clearFilters: "Limpar filtros",
    allStatuses: "Todos os status",
    selectCustomerDescription: "Selecione um cliente existente ou crie um novo cliente para o pedido",
    noCustomerSelected: "Nenhum cliente selecionado",
    selectCustomerToCreate: "Selecione um cliente para criar o pedido",
    addProductsDescription: "Adicione produtos ao pedido",
    noProductsAdded: "Nenhum produto adicionado",
    addProductsToOrder: "Adicione produtos para criar o pedido",
    addProducts: "Adicionar Produtos",
    addMoreProducts: "Adicionar Mais",
    checkoutDescription: "Revise e finalize o pedido",
    deliveryInfo: "Informações de Entrega",
    receivingMethod: "Método de Entrega",
    selectReceivingMethod: "Selecione o método",
    receivingDate: "Data de Entrega",
    receivingTime: "Horário de Recebimento",
    paymentInfo: "Informações de Pagamento",
    selectPaymentMethod: "Selecione o método",
    notesPlaceholder: "Observações sobre o pedido",
    createOrder: "Criar Pedido",
    orderCreated: "Pedido criado",
    orderCreatedSuccessfully: "O pedido foi criado com sucesso",
    selectCustomerFirst: "Selecione um cliente primeiro",
    pleaseSelectCustomerBeforeProducts: "Por favor, selecione um cliente antes de adicionar produtos",
    pleaseSelectCustomerBeforeCreating: "Por favor, selecione um cliente antes de criar o pedido",
    addProductsFirst: "Adicione produtos primeiro",
    pleaseAddProductsBeforeCreating: "Por favor, adicione produtos antes de criar o pedido",
    selectPaymentMethodFirst: "Selecione um método de pagamento",
    pleaseSelectPaymentMethodBeforeCreating: "Por favor, selecione um método de pagamento antes de criar o pedido",
    selectReceivingMethodFirst: "Selecione um método de recebimento",
    pleaseSelectReceivingMethodBeforeCreating: "Por favor, selecione um método de recebimento antes de criar o pedido",
    checkout: "Finalizar",
    deliveryAndPayment: "Entrega e Pagamento",
    deliveryAndPaymentDescription: "Configure as opções de entrega e pagamento",
    total: "Total",
    createCustomProduct: "Criar Produto Personalizado",
    editCustomProduct: "Editar Produto Personalizado",
    customProductAddedToOrder: "O produto personalizado foi adicionado ao pedido",
    customProduct: "Produto Personalizado",
    productNameRequired: "O nome do produto é obrigatório",
    productNamePlaceholder: "Ex: Bolo personalizado",
    productDescriptionPlaceholder: "Ex: Bolo personalizado com decoração especial",
    customProductDescription: "Crie um produto personalizado que existirá apenas neste pedido",
    customProductFormTitle: "Formulário do produto",
    customProductOptionsTitle: "Opções personalizadas",
    preview: "Visualizar Pedido",
    orderPreview: "Preview do Pedido",
    orderNotFound: "Pedido não encontrado",
    generatePdf: "Gerar PDF",
    downloadPdf: "Baixar PDF",
    pdfGenerated: "PDF gerado com sucesso",
    pdfGeneratedDesc: "O PDF do pedido foi gerado e está sendo baixado.",
    pdfGenerationError: "Ocorreu um erro ao gerar o PDF. Tente novamente.",
    orderInfo: "Informações do Pedido",
    printOrder: "Imprimir Pedido",
    revision: "Revisão",
    scanQrCode: "Escaneie o QR Code",
    toAccessOrder: "para acessar o pedido",
    generatedAt: "Gerado em",
    thankYou: "Obrigado pela preferência!",
    noCustomerInfo: "Sem informações do cliente",
    notSpecified: "Não especificado",
    receivingTimeLabel: "Horário",
    observationLabel: "Obs",
    items: "Produtos",
    selectedOptions: "Opções selecionadas",
    dateCreated: "Data de criação",
    orderNumber: "Número do pedido",
  },
  customers: {
    title: "Clientes",
    customerDetails: "Detalhes do cliente",
    newCustomer: "Novo Cliente",
    newCustomerDescription: "Preencha os dados para criar um novo cliente",
    createSuccess: "Cliente criado com sucesso",
    createSuccessMessage: "O cliente foi criado com sucesso",
    createError: "Erro ao criar cliente",
    createErrorMessage: "Ocorreu um erro ao criar o cliente. Tente novamente.",
    customerExists: "Cliente já existe",
    customerExistsMessage: "Um cliente com este email ou telefone já existe",
    name: "Nome",
    email: "Email",
    phone: "Telefone",
    orders: "Pedidos",
    ordersCount: "{{count}} pedidos",
    totalSpent: "Gasto total",
    lastOrder: "Último pedido",
    noCustomers: "Nenhum cliente encontrado",
    viewOrders: "Ver pedidos",
    editCustomer: "Editar Cliente",
    editCustomerDescription: "Atualize as informações do cliente",
    namePlaceholder: "Nome completo do cliente",
    emailPlaceholder: "<EMAIL>",
    phonePlaceholder: "(00) 00000-0000",
    updateSuccess: "Cliente atualizado",
    updateSuccessMessage: "Os dados do cliente foram atualizados com sucesso.",
    updateError: "Erro ao atualizar cliente",
    updateErrorMessage: "Ocorreu um erro ao atualizar os dados do cliente. Tente novamente.",
    details: "Detalhes",
    createdAt: "Data de cadastro",
    customerSince: "Cliente desde {{date}}",
    countryCode: "Código do país",
    contactViaWhatsApp: "Contato via WhatsApp",
    contact: "Contato",
    noMatchingCustomers: "Nenhum cliente encontrado para \"{{query}}\"",
    noOrders: "Este cliente ainda não fez nenhum pedido",
    saving: "Salvando...",
    personalInfo: "Dados Pessoais",
    address: "Endereço",
    complement: "Complemento",
    city: "Bairro",
    changeCustomer: "Trocar Cliente",
  },
  settings: {
    title: "Configurações da Loja",
    subtitle: "Personalize sua loja e configure as opções de funcionamento",
    storeInfo: "Informações da Loja",
    storeInfoDesc: "Nome, descrição, logo e URL personalizada",
    uploadLogo: "Carregar logo",
    visual: "Personalização Visual",
    visualDesc: "Cores, fontes e estilo da loja",
    layoutOptions: "Opções de Layout",
    layoutOptionsDesc: "Escolha como seus produtos serão exibidos",
    layoutType1: "Categorias horizontais + Grid de produtos",
    layoutType1Desc:
      "Navegação por categorias deslizantes, produtos em formato de grid com 2 colunas",
    layoutType2: "Categorias horizontais + Lista de produtos",
    layoutType2Desc:
      "Navegação por categorias deslizantes, produtos em formato de listtile",
    layoutType3: "Categorias em seções",
    layoutType3Desc:
      "Página única com seções por categoria, produtos em carrossel horizontal",
    payments: "Pagamentos",
    paymentsDesc: "Métodos de pagamento aceitos",
    languages: "Idiomas",
    languagesDesc: "Configurações de idioma da loja",
    basicInfo: "Informações Básicas",
    storeName: "Nome da loja",
    storeDescription: "Descrição da loja",
    storeLogo: "Logo da loja",
    storeUrl: "URL da loja",
    storeUrlPrefix: "app.docemenu.com.br/",
    colors: "Cores",
    primaryColor: "Cor primária",
    secondaryColor: "Cor secundária",
    accentColor: "Cor de destaque",
    previewTheme: "Visualizar tema",
    paymentMethods: "Métodos de pagamento",
    acceptCash: "Aceitar dinheiro",
    acceptCreditCard: "Aceitar cartão de crédito",
    acceptDebitCard: "Aceitar cartão de débito",
    acceptPix: "Aceitar Pix",
    acceptBankTransfer: "Aceitar transferência bancária",
    standardPaymentMethods: "Métodos de pagamento padrão",
    customPaymentMethods: "Métodos de pagamento personalizados",
    cashPayment: "Dinheiro",
    creditCardPayment: "Cartão de crédito",
    debitCardPayment: "Cartão de débito",
    pixPayment: "PIX",
    bankTransferPayment: "Transferência bancária",
    customPaymentMethodPlaceholder: "Ex: Boleto, Cheque, Fiado",
    paymentMethodExists: "Método de pagamento já existe",
    paymentMethodExistsDesc: "Este método de pagamento já foi adicionado",
    paymentMethodsDesc: "Configure os métodos de pagamento aceitos pela sua loja",
    add: "Adicionar",
    selectLanguage: "Selecionar idioma",
    portuguese: "Português",
    english: "Inglês",
    saveChanges: "Salvar alterações",
    changesSuccessfullySaved: "Alterações salvas com sucesso!",
    previewStore: "Visualizar loja",
    urlSlugInUse: "Esta URL já está em uso",
    logoUpload: "Carregar logo",
    maxSize: "Tamanho máximo: 2MB",
    storeHeaderImage: "Imagem de cabeçalho da loja",
    uploadHeaderImage: "Carregar imagem de cabeçalho",
    changeHeaderImage: "Alterar imagem de cabeçalho",
    headerImageHelp:
      "Carregue uma imagem de banner para sua loja (tamanho recomendado: 1200x300 pixels). Tamanho máximo 5MB.",
    headerImageUploaded: "Imagem de cabeçalho carregada",
    headerImageUploadSuccess:
      "A imagem de cabeçalho da sua loja foi atualizada com sucesso",
  },
  storefront: {
    addToCart: "Adicionar",
    outOfStock: "Esgotado",
    cart: "Carrinho",
    yourCart: "Seu Carrinho",
    viewCart: "Ver carrinho",
    viewAll: "Ver Todos",
    continueShopping: "Continuar Comprando",
    backToCart: "Voltar ao Carrinho",
    emptyCart: "Carrinho Vazio",
    checkout: "Finalizar Compra",
    subtotal: "Subtotal",
    total: "Total",
    shipping: "Frete",
    shippingCalc: "Frete calculado no checkout",
    removeItem: "Remover",
    quantity: "Quantidade",
    priceSummary: "Resumo do preço",
    basePrice: "Preço",
    totalPrice: "Preço total",
    itemTotal: "Total do item",
    // Cupons
    haveCoupon: "Tem um cupom de desconto?",
    enterCouponCode: "Digite o código do cupom",
    apply: "Aplicar",
    couponApplied: "Cupom aplicado",
    couponRemoved: "Cupom removido",
    discountApplied: "Desconto aplicado com sucesso",
    discountRemoved: "Desconto removido",
    invalidCoupon: "Cupom inválido",
    couponNotValid: "Este cupom não é válido",
    errorApplyingCoupon: "Erro ao aplicar o cupom",
    discount: "Desconto",
    discountFixed: "Desconto (Fixo):",
    discountPercentage: "Desconto ({{value}}%):",
    couponCode: "Código do cupom",
    couponDiscount: "Desconto do cupom",
    remove: "Remover",
    fixedDiscount: "Desconto fixo de {{value}}",
    percentDiscount: "Desconto de {{value}}%",
    cartCleared: "Carrinho limpo",
    allItemsRemoved: "Todos os itens foram removidos do carrinho",
    delivery: "Entrega",
    pickup: "Retirada",
    variations: "Variações",
    observation: "Observação",
    observationPlaceholder:
      "Alguma observação para este produto? Ex: Sem cebola, mais picante, etc.",
    selectRequiredOptions: "Selecione as opções obrigatórias",
    selectOne: "Selecione uma opção",
    selectMultiple: "Selecione múltiplas opções",
    selectUpTo: "Selecione até {{count}} opções",
    selectAtLeast: "Selecione pelo menos {{count}} opções",
    requiredVariation: "Opção obrigatória",
    optionalVariation: "Opção opcional",
    maxSelectionsReached: "Você atingiu o limite de {{max}} seleções",
    minSelectionsRequired: "Você precisa selecionar pelo menos {{min}} opções",
    productAddedToCart: "Produto adicionado ao carrinho",
    addedToCart: "Adicionado ao carrinho",
    hasOptions: "Com opções",
    completeOrder: "Complete Order",
    customerInfo: "Informações do cliente",
    customerInfoSummary: "Dados do Cliente",
    customerContact: "Contato",
    name: "Nome completo",
    email: "Email",
    phone: "Telefone",
    whatsapp: "WhatsApp",
    whatsappPlaceholder: "(00) 00000-0000",
    countryCode: "Cód. País",
    selectCountryCode: "Selecione o código do país",
    paymentMethod: "Método de pagamento",
    cash: "Dinheiro",
    creditCard: "Cartão de crédito",
    debitCard: "Cartão de débito",
    pix: "Pix",
    bankTransfer: "Transferência bancária",
    notes: "Observações",
    placeOrder: "Faça sua encomenda",
    orderSuccess: "Pedido enviado com sucesso!",
    orderError: "Erro ao enviar pedido",
    returnToStore: "Voltar à loja",
    orderNumber: "Pedido",
    allProducts: "Todos os Produtos",
    categories: "Categorias",
    moreInfo: "Mais informações",
    searchProducts: "Buscar produtos",
    poweredBy: "Desenvolvido por Doce Menu",
    receivingMethod: "Como deseja receber seu pedido?",
    receivingMethodTitle: "Método de Recebimento",
    productsLabel: "Produtos",
    orderSummary: "Resumo do Pedido",
    time: "Horário",
    deliveryAddressTitle: "Entrega no endereço",
    pickupLocation: "Retirada no local",
    deliveryFee: "Taxa de entrega",
    deliveryDate: "Data para entrega",
    pickupDate: "Data para retirada",
    deliveryAddress: "Endereço de entrega",
    street: "Rua",
    number: "Número",
    neighborhood: "Bairro",
    cityState: "Cidade e Estado",
    reference: "Ponto de referência (opcional)",
    minAdvanceDaysHint: "É necessário solicitar com pelo menos {{count}} dia(s) de antecedência.",
    selectAvailableDate: "Selecione um dia disponível no calendário.",
    suggestedDeliveryTime: "Horário de entrega",
    suggestedPickupTime: "Horário de retirada",
    selectAvailableTime: "Selecione um horário disponível",
    pickupDetails: "Detalhes da Retirada",
    deliveryDetails: "Detalhes da Entrega",
    date: "Data",
    // Persistência de dados
    dataSaved: "Dados salvos",
    dataLoaded: "Dados carregados",
    customerDataSaved: "Dados do cliente salvos",
    addressDataSaved: "Endereço salvo",
  },
  storeInfo: {
    contact: "Contato",
    address: "Endereço",
    storeLocation: "Localização da Loja",
    openLocation: "Ver no mapa",
    getDirections: "Como chegar",
  },
  store: {
    categories: "Categorias",
    loadingCategories: "Carregando categorias...",
    noImage: "Sem imagem",
    noProductsFound: "Nenhum produto encontrado para",
    noProductsInCategory: "Nenhum produto disponível nesta categoria.",
    moreInfo: "Mais informações",
    productNotFound: "Produto não encontrado",
  },
  payments: {
    paymentControl: "Controle de Recebimentos",
    registerPayment: "Registrar Pagamento",
    paymentHistory: "Histórico de Recebimentos",
    orderTotal: "Total do Pedido",
    totalReceived: "Total Recebido",
    pendingAmount: "Valor Pendente",
    paymentProgress: "Progresso do Pagamento",
    fullyPaid: "Totalmente pago",
    amount: "Valor",
    date: "Data",
    method: "Método",
    observation: "Observação",
    observationPlaceholder: "Informações adicionais sobre o recebimento...",
    selectMethod: "Selecione o método",
    fullAmount: "Valor Total",
    halfAmount: "Metade",
    refund: "Estorno",
    noPayments: "Nenhum recebimento registrado",
    noPaymentsDescription: "Clique em \"Registrar Pagamento\" para adicionar o primeiro recebimento",
    paymentsCount: "{{count}} recebimento(s)",
    totalPayments: "{{count}} recebimento(s)",
    success: "Sucesso",
    error: "Erro",
    paymentCreated: "Recebimento registrado com sucesso",
    paymentError: "Erro ao registrar recebimento",
    paymentDeleted: "Recebimento excluído com sucesso",
    deleteError: "Erro ao excluir recebimento",
    deletePayment: "Excluir Recebimento",
    confirmDelete: "Tem certeza que deseja excluir este recebimento?",
    confirmDeleteMessage: "Tem certeza que deseja excluir este recebimento? Esta ação não pode ser desfeita.",
    warning: "Atenção",
    deleteWarning: "O status de pagamento do pedido será recalculado automaticamente após a exclusão.",
    valueExceedsPending: "Valor excede o pendente",
    valueExceedsPendingMessage: "O valor informado é maior que o valor pendente do pedido. Isso resultará em pagamento em excesso.",
    excess: "Excesso",
    statusRecalculated: "Status de pagamento recalculado",
    recalculateError: "Erro ao recalcular status",
    disputeWarning: "Pagamento em disputa",
    disputeDescription: "Verifique os recebimentos marcados como disputa",
    refundWarning: "Pagamento estornado",
    refundDescription: "O valor foi estornado ao cliente",
    status: {
      pending: "Pendente",
      partiallyReceived: "Parcialmente Recebido",
      received: "Recebido",
      disputed: "Em Disputa",
      refunded: "Estornado",
      cancelled: "Cancelado"
    }
  },
  subscription: {
    title: "Planos de Assinatura",
    free: "Gratuito",
    premium: "Premium",
    month: "mês",
    popular: "Mais Popular",
    current_plan: "Plano Atual",
    current: "Atual",
    upgrade_required: "Upgrade Necessário",
    upgrade_description: "Esta funcionalidade requer o plano Premium",
    upgrade: "Fazer Upgrade",
    upgrade_now: "Fazer Upgrade Agora",
    select_free: "Selecionar Gratuito",
    processing: "Processando...",
    trial_days: "{{days}} dias grátis",
    trial_offer: "7 dias grátis para testar",
    trial_description: "Teste todas as funcionalidades premium por 7 dias sem compromisso",
    start_trial: "Iniciar Teste Grátis",
    monthly: "Mensal",
    yearly: "Anual",
    year: "ano",
    save_percentage: "Economize {{percentage}}%",
    billed_annually: "Cobrado anualmente",
    no_trial: "Sem período de teste",
    premium_features: "Recursos Premium",
    feature_blocked: {
      allowPdfGeneration: {
        title: "Geração de PDF Bloqueada",
        description: "A geração de PDFs está disponível apenas no plano Premium"
      },
      allowAnalytics: {
        title: "Analytics Bloqueado",
        description: "Relatórios e analytics estão disponíveis apenas no plano Premium"
      },
      allowWhatsappIntegration: {
        title: "WhatsApp Bloqueado",
        description: "Integração com WhatsApp está disponível apenas no plano Premium"
      },
      allowCoupons: {
        title: "Cupons Bloqueados",
        description: "Sistema de cupons está disponível apenas no plano Premium"
      },
      allowCustomization: {
        title: "Personalização Bloqueada",
        description: "Personalização avançada está disponível apenas no plano Premium"
      }
    },
    limit_exceeded: {
      maxProducts: {
        title: "Limite de Produtos Excedido",
        description: "Você atingiu o limite de {{limit}} produtos do plano gratuito"
      },
      maxOrdersPerMonth: {
        title: "Limite de Pedidos Excedido",
        description: "Você atingiu o limite de {{limit}} pedidos por mês do plano gratuito"
      }
    },
    usage: {
      products: "Produtos",
      orders: "Pedidos",
      unlimited: "Ilimitado"
    }
  },
  globalAdmin: {
    title: "Dashboard Global",
    subtitle: "Painel administrativo da plataforma Doce Menu",
    analytics: {
      title: "Analytics Globais",
      totalStores: "Total de Lojas",
      activeStores: "Lojas Ativas",
      premiumStores: "Lojas Premium",
      totalRevenue: "Receita Total",
      totalOrders: "Pedidos Totais",
      avgOrderValue: "Ticket Médio",
      revenueGrowth: "Crescimento da Receita",
      orderGrowth: "Crescimento de Pedidos",
      last30Days: "Últimos 30 dias"
    },
    stores: {
      title: "Gerenciamento de Lojas",
      subtitle: "Visualize e gerencie todas as lojas da plataforma",
      searchPlaceholder: "Buscar por nome, slug ou email...",
      filters: {
        status: "Status",
        planType: "Plano",
        all: "Todos",
        active: "Ativas",
        inactive: "Inativas",
        free: "Gratuito",
        premium: "Premium"
      },
      sort: {
        name: "Nome",
        createdAt: "Data de Criação",
        lastActivity: "Última Atividade"
      },
      card: {
        createdAt: "Criada em",
        lastActivity: "Última atividade",
        plan: "Plano",
        status: "Status",
        viewDetails: "Ver Detalhes",
        toggleStatus: "Alterar Status"
      },
      details: {
        title: "Detalhes da Loja",
        owner: "Proprietário",
        metrics: "Métricas",
        totalProducts: "Total de Produtos",
        totalOrders: "Total de Pedidos",
        totalCustomers: "Total de Clientes",
        recentOrders: "Pedidos Recentes",
        recentRevenue: "Receita Recente",
        avgOrderValue: "Ticket Médio"
      },
      actions: {
        activate: "Ativar Loja",
        deactivate: "Desativar Loja",
        confirmActivate: "Tem certeza que deseja ativar esta loja?",
        confirmDeactivate: "Tem certeza que deseja desativar esta loja?",
        activated: "Loja ativada com sucesso",
        deactivated: "Loja desativada com sucesso"
      }
    },
    subscriptions: {
      title: "Gerenciamento de Assinaturas",
      subtitle: "Controle assinaturas e pagamentos",
      distribution: "Distribuição de Planos",
      free: "Gratuito",
      premium: "Premium",
      canceled: "Canceladas",
      pastDue: "Em Atraso"
    },
    users: {
      title: "Gerenciamento de Usuários",
      promoteAdmin: "Promover a Super Admin",
      removeAdmin: "Remover Super Admin",
      confirmPromote: "Tem certeza que deseja promover este usuário a super-administrador?",
      confirmRemove: "Tem certeza que deseja remover as permissões de super-administrador deste usuário?",
      promoted: "Usuário promovido a super-administrador",
      removed: "Permissões de super-administrador removidas",
      cannotRemoveSelf: "Você não pode remover suas próprias permissões"
    },
    topStores: {
      title: "Top 10 Lojas",
      byOrders: "Por Número de Pedidos",
      byRevenue: "Por Receita",
      orders: "pedidos",
      revenue: "receita"
    }
  },
};

// English translations
const enTranslations = {
  admin: {
    categories: "Categories",
    newCategory: "New Category",
    noCategories: "No categories found",
    editCategory: "Edit Category",
    noDescription: "No description",
    categoryName: "Category name",
    categoryDescription: "Category description",
    categoryLogo: "Category logo",
    categoryVisible: "Visible in store",
    logoDescription:
      "Upload an image for this category (recommended size: 800x600px)",
    uploadLogo: "Upload logo",
    reorderCategories: "Reorder Categories",
    changeImage: "Change image",
    visibilityUpdated: "Category visibility updated successfully!",
    visible: "Visible",
    hidden: "Hidden",
    categoryAdded: "Category added successfully!",
    categoryUpdated: "Category updated successfully!",
    categoryDeleted: "Category deleted successfully!",
    categoryCreated: "Category created",
    categoryCreatedDesc: "The category was successfully created.",
    categoryUpdatedDesc: "The category was successfully updated.",
    categoryDeletedDesc: "The category was successfully deleted.",
  },
  coupons: {
    title: "Coupons",
    addCoupon: "Add Coupon",
    editCoupon: "Edit Coupon",
    deleteCoupon: "Delete Coupon",
    couponCode: "Coupon code",
    couponType: "Discount type",
    couponValue: "Discount value",
    minPurchase: "Minimum purchase",
    expirationDate: "Expiration date",
    singleUse: "Single use",
    active: "Active",
    inactive: "Inactive",
    status: "Status",
    actions: "Actions",
    fixedValue: "Fixed value",
    percentage: "Percentage",
    couponAdded: "Coupon added successfully!",
    couponUpdated: "Coupon updated successfully!",
    couponDeleted: "Coupon deleted successfully!",
    couponActivated: "Coupon activated successfully!",
    couponDeactivated: "Coupon deactivated successfully!",
    confirmDelete: "Are you sure you want to delete this coupon?",
    confirmDeleteDesc: "This action cannot be undone",
    noCoupons: "No coupons found",
    confirmRemove: "Are you sure you want to remove the coupon from this revision?",
    couponRemoved: "Coupon removed",
    couponRemovedFromRevision: "The coupon has been removed from this revision",
    errorRemovingCoupon: "Error removing coupon",
    codeExists: "A coupon with this code already exists",
    invalidCode: "Invalid coupon code. Use only letters, numbers, and dashes.",
    invalidValue: "Value must be greater than zero",
    invalidMinPurchase: "Minimum purchase must be greater than or equal to zero",
    invalidExpirationDate: "Expiration date must be in the future",
    formTitle: "Coupon details",
    formDescription: "Fill in the discount coupon details",
    discountPercentage: "{{value}}% discount",
  },
  common: {
    login: "Login",
    register: "Register",
    email: "Email",
    password: "Password",
    forgotPassword: "Forgot password?",
    noAccount: "Don't have an account?",
    hasAccount: "Already have an account?",
    fullName: "Full name",
    submit: "Submit",
    save: "Save",
    saving: "Saving...",
    update: "Update",
    cancel: "Cancel",
    delete: "Delete",
    deleting: "Deleting...",
    edit: "Edit",
    change: "Change",
    add: "Add",
    search: "Search",
    clearSearch: "Clear search",
    clear: "Clear",
    apply: "Apply",
    error: "Error",
    success: "Success",
    loading: "Loading...",
    creating: "Creating...",
    required: "This field is required",
    invalidEmail: "Invalid email",
    minLength: "Enter at least {{count}} characters",
    logout: "Logout",
    view: "View",
    viewDetails: "View details",
    back: "Back",
    goBack: "Go back",
    next: "Next",
    finish: "Finish",
    refresh: "Refresh",
    refreshing: "Refreshing data",
    confirmDelete: "Are you sure you want to delete?",
    yes: "Yes",
    no: "No",
    actions: "Actions",
    status: "Status",
    date: "Date",
    welcome: "Welcome",
    home: "Home",
    settings: "Settings",
    productsNav: "Products",
    products: "Products",
    categories: "Categories",
    orders: "Orders",
    customers: "Customers",
    dashboard: "Dashboard",
    viewStore: "View store",
    pageNotFound: "Page not found",
    pageNotFoundDescription:
      "The page you are looking for does not exist or has been moved.",
  },
  landing: {
    features: "Features",
    howItWorks: "How It Works",
    pricing: "Pricing",
    getStarted: "Get Started",
    learnMore: "Learn More",
    heroTitle: "Create your online store in",
    heroTitleHighlight: "minutes",
    heroSubtitle:
      "Complete platform to create online product catalogs and manage your orders with ease.",
    demoStore: "Demo Store",
    productsDemo: "Products",
    orders: "Orders",
    recentVisits: "Recent Visits",
    thisMonth: "This month",

    // Demo Store Card - Real Dashboard
    demoStoreTitle: "Sweet Charm",
    demoStoreUrl: "app.docemenu.com.br/sweetcharm",
    demoDashboardTitle: "Dashboard",

    // Main dashboard metrics
    demoPendingOrders: "Pending Orders",
    demoConfirmedOrders: "Confirmed Orders",
    demoMonthlyRevenue: "Monthly Revenue",
    demoTotalRevenue: "Total Revenue",
    demoAverageTicket: "Average Ticket",
    demoVisitsMonth: "Monthly Visits",
    demoConversionRate: "Conversion Rate",

    // Dashboard sections
    demoFinancialSummary: "Financial Summary",
    demoTopProducts: "Top Products",
    demoPendingOrdersList: "Orders in Progress",
    demoSiteActivity: "Site Activity",

    // Realistic bakery data
    demoBrigadeiro: "Gourmet Brigadeiro",
    demoBrownie: "Chocolate Brownie",
    demoCupcake: "Red Velvet Cupcake",
    demoTorta: "Strawberry Cake",
    demoBemCasado: "Bem Casado",
    demoTrufas: "Assorted Truffles",

    // Order statuses
    demoOrderConfirmed: "Confirmed",
    demoOrderPending: "Pending",
    demoOrderDelivered: "Delivered",
    demoOrderLate: "Late",

    // Values and metrics
    demoThisMonth: "This month",
    demoToday: "Today",
    demoGrowth: "Growth",
    featuresTitle: "Everything you need to sell online",

    delivery: "Delivery",
    pickup: "Pickup",
    receivingMethodTitle: "Receiving Method",
    deliveryAddress: "Delivery to address",
    pickupLocation: "Pickup at location",
    deliveryToAddress: "Delivery to address",
    pickupAtStore: "Pickup at store",
    receivingMethod: "How would you like to receive your order?",
    deliveryAddressTitle: "Delivery to address",
    street: "Street",
    customerInfoSummary: "Customer Information",
    customerContact: "Contact",
    number: "Number",
    productsLabel: "Products",
    neighborhood: "Neighborhood",
    cityState: "City and State",
    reference: "Reference point (optional)",
    deliveryFee: "Delivery fee",
    deliveryDate: "Delivery date",
    pickupDate: "Pickup date",
    suggestedDeliveryTime: "Delivery time",
    suggestedPickupTime: "Pickup time",
    selectAvailableTime: "Select an available time",
    pleaseSelectReceivingMethod: "Please select how you would like to receive your order (delivery or pickup).",
    pleaseSelectValidDate: "Please select a valid date for receiving your order.",
    pleaseCompleteAddress: "Please complete all address fields for delivery.",
    pleaseSelectPaymentMethod: "Please select a payment method.",
    pleaseCompleteCustomerInfo: "Please fill in all customer information.",
    invalidEmailFormat: "Please provide a valid email address.",
    invalidWhatsAppNumber: "WhatsApp number must have at least 8 digits.",
    minAdvanceDaysRequired: "You need to request at least {{days}} day(s) in advance.",
    selectAvailableDate: "Select an available date on the calendar.",
    orderSummary: "Order Summary",
    productsInOrder: "Products",
    time: "Time",

    // Features section
    featuresSubtitle:
      "Essential features to manage your product catalog and sell more.",
    feature1Title: "Product Catalog",
    feature1Desc:
      "Create a complete catalog with categories, images, and detailed descriptions.",
    feature2Title: "Order Management",
    feature2Desc: "Track all orders and update their status easily.",
    feature3Title: "Dashboard",
    feature3Desc: "View statistics and important information about your store.",
    feature4Title: "Customer Database",
    feature4Desc:
      "Keep a record of your customers and their purchasing preferences.",
    feature5Title: "Multilingual",
    feature5Desc:
      "Available in Portuguese and English to reach a wider audience.",
    feature6Title: "Fast & Easy",
    feature6Desc: "Set up your store in minutes and start selling immediately.",
    howItWorksTitle: "How It Works",
    howItWorksSubtitle:
      "Three simple steps to start selling your products online.",
    step1Title: "Create your store",
    step1Desc:
      "Register and set up your store with a name, logo, and custom settings.",
    step2Title: "Add your products",
    step2Desc:
      "Register your products with photos, descriptions, and prices to build your catalog.",
    step3Title: "Start selling",
    step3Desc: "Share your store link and start receiving orders immediately.",
    pricingTitle: "Plans & Pricing",
    pricingSubtitle:
      "Choose the ideal plan for your business. Start free and upgrade when you need more.",

    // Updated plans
    freePlan: "Free Plan",
    freePlanDesc: "Ideal for those just starting out.",
    premiumPlan: "Premium Plan",
    premiumPlanDesc: "For businesses that want to grow.",
    popular: "MOST POPULAR",

    // Prices
    freePrice: "Free",
    premiumPriceMonthly: "R$ 29.90",
    premiumPriceYearly: "R$ 299.00",
    perMonth: "/month",
    perYear: "/year",
    monthlyEquivalent: "R$ 24.92/month",
    savePercentage: "Save 16.6%",

    // Price toggle
    monthly: "Monthly",
    yearly: "Yearly",
    billingToggle: "Billing",

    // Trial
    trialOffer: "7 days free",
    trialDescription: "Test all premium features for 7 days",
    // Features by plan
    freeFeatures: {
      products: "Up to 10 products",
      orders: "Maximum 5 orders/month",
      store: "Basic online store",
      support: "Email support"
    },
    premiumFeatures: {
      products: "Up to 50 products",
      orders: "Unlimited orders",
      pdf: "PDF order generation",
      analytics: "Complete dashboard and reports",
      whatsapp: "WhatsApp integration",
      coupons: "Discount coupon system",
      customization: "Advanced visual customization",
      support: "Priority support"
    },

    // Feature comparison
    featuresComparison: "Feature Comparison",
    featuresComparisonSubtitle: "Compare all features available in each plan",
    feature: "Feature",
    included: "Included",
    notIncluded: "Not included",

    // Final CTA
    ctaFinalText: "No credit card required • Cancel anytime",

    // Specific features
    productManagement: "Product Management",
    orderManagement: "Order Management",
    customerManagement: "Customer Database",
    pdfGeneration: "PDF Generation",
    dashboardAnalytics: "Dashboard & Analytics",
    whatsappIntegration: "WhatsApp Integration",
    couponSystem: "Coupon System",
    visualCustomization: "Visual Customization",
    prioritySupport: "Priority Support",

    // System Features Section
    systemFeaturesTitle: "Complete System for Bakeries",
    systemFeaturesSubtitle: "All the tools you need to manage and grow your sweet business",

    completeCatalog: "Complete Catalog",
    completeCatalogDesc: "Organize your products in categories, add variations (sizes, flavors), manage inventory and prices with ease.",
    completeCatalogFeatures: {
      variations: "Products with variations",
      images: "Multiple images",
      inventory: "Inventory control"
    },

    smartOrders: "Smart Orders",
    smartOrdersDesc: "Complete order system with revisions, payment control, delivery and pickup scheduling.",
    smartOrdersFeatures: {
      revisions: "Order revisions",
      payments: "Payment control",
      scheduling: "Flexible scheduling"
    },

    advancedAnalytics: "Advanced Analytics",
    advancedAnalyticsDesc: "Track your business performance with detailed reports and insights about sales and customers.",
    advancedAnalyticsFeatures: {
      financial: "Financial reports",
      products: "Product analysis",
      customers: "Customer metrics"
    },

    customerManagementFull: "Customer Management",
    customerManagementFullDesc: "Keep a complete record of your customers with order history and preferences.",
    customerManagementFullFeatures: {
      history: "Complete history",
      contact: "Contact data",
      whatsapp: "WhatsApp integration"
    },

    couponsPromotions: "Coupons & Promotions",
    couponsPromotionsDesc: "Create promotional campaigns with custom discount coupons to increase your sales.",
    couponsPromotionsFeatures: {
      discount: "Fixed or % discount",
      minimum: "Minimum value",
      expiry: "Expiration date"
    },

    professionalPrinting: "Professional Printing",
    professionalPrintingDesc: "Generate professional PDFs of orders for printing in A4 or thermal (80mm) format.",
    professionalPrintingFeatures: {
      layout: "Professional layout",
      logo: "Company logo",
      formats: "Multiple formats"
    },

    // CTAs
    ctaTitle: "Ready to expand your online business?",
    ctaSubtitle:
      "Create your store right now and start selling your products all over Brazil.",
    getStartedToday: "Get started today",
    startFreeTrial: "Start Free Trial",
    upgradeToPremium: "Upgrade to Premium",
    footerTagline:
      "E-commerce platform to create and manage online product catalogs.",
    footerProduct: "Product",
    footerFaq: "FAQ",
    footerCompany: "Company",
    footerAbout: "About us",
    footerBlog: "Blog",
    footerCareers: "Careers",
    footerLegal: "Legal",
    footerTerms: "Terms of use",
    footerPrivacy: "Privacy",
    footerCookies: "Cookies",
    footerRights: "All rights reserved.",
  },
  auth: {
    title: "Doce Menu",
    subtitle: "Manage your online store",
    loginTitle: "Log in to your account",
    registerTitle: "Create an account",
    authError: "An authentication error occurred",
    loginSuccess: "Login successful!",
    registerSuccess: "Registration successful!",
    invalidCredentials: "Invalid email or password",
    passwordMinLength: "Password must be at least 6 characters",
    emailRequired: "Email is required",
    passwordRequired: "Password is required",
    nameRequired: "Name is required",
    loginWithGoogle: "Login with Google",
    registerWithGoogle: "Register with Google",
    loginWithGoogleDesc: "Use your Google account to log in",
    registerWithGoogleDesc: "Create an account with Google to continue",
    username: "Username",
    confirmPassword: "Confirm password",
    creatingAccount: "Creating account...",
    loggingIn: "Logging in...",
  },
  dashboard: {
    title: "Dashboard",
    welcome: "Welcome to your store",
    visitsThisMonth: "Visits this month",
    ordersThisMonth: "Orders this month",
    revenueThisMonth: "Revenue this month",
    viewDetails: "View details",
    viewAllOrders: "View all orders",
    viewFinancialReport: "View financial report",
    recentOrders: "Recent Orders",
    orderID: "ID",
    customer: "Customer",
    orderDate: "Date",
    orderValue: "Value",
    orderStatus: "Status",
    completed: "Completed",
    pending: "Pending",
    confirmed: "Confirmed",
    delivered: "Delivered",
    processing: "Processing",
    shipped: "Shipped",
    cancelled: "Cancelled",
  },
  products: {
    title: "Products",
    addProduct: "Add Product",
    editProduct: "Edit Product",
    deleteProduct: "Delete Product",
    productName: "Product name",
    productDescription: "Description",
    productPrice: "Price",
    productImage: "Image",
    productImages: "Images",
    imageUrl: "Image URL",
    imagesHelperText:
      "You can add up to 3 images for each product. The first image will be displayed as the main one.",
    productCategory: "Category",
    inStock: "In stock",
    productDetails: "Product details",
    productDetailsDescription: "Fill in the product details.",
    changeImage: "Change image",
    addMainImage: "Add main image",
    addExtraImage: "Add extra image",
    hasVariations: "Has variations",
    productAdded: "Product added",
    productAddedToOrder: "The product has been added to the order",
    productAddedToRevision: "The product has been added to the order revision",
    customProductAddedToRevision: "The custom product has been added to the order revision",
    addToOrder: "Add to Order",
    addToRevision: "Add to Revision",
    visibilityUpdated: "Product visibility updated successfully!",
    noSearchResults: "No products found for",
    searchProducts: "Search products...",
    noProductsFound: "No products found",
    noProducts: "No products available",
    tryAnotherSearch: "Try another search",
    addProductsFirst: "Add products first",
    hasVariationsDescription: "Product has options like size, flavor, etc.",
    variations: "Variations",
    variation: "Variation",
    addVariation: "Add variation",
    noVariations: 'No variations added. Click "Add variation" to begin.',
    variationName: "Variation name",
    variationNamePlaceholder: "Ex: Size, Flavor, Color",
    required: "Required",
    requiredDescription: "Customer must select an option for this variation.",
    multipleChoice: "Multiple choice",
    multipleChoiceDescription: "Customer can choose more than one option.",
    minSelections: "Minimum selections",
    maxSelections: "Maximum selections",
    options: "Options",
    addOption: "Add option",
    noOptions: "Add options for this variation.",
    optionNamePlaceholder: "Ex: Small, Medium, Large",
    outOfStock: "Out of stock",
    productAddedToast: "Product added",
    regularProductAddedToOrder: "The product has been added to the order",
    productUpdated: "Product updated successfully!",
    updateSuccess: "Product updated",
    updateSuccessDescription: "The product was successfully updated.",
    updateError: "Error updating product",
    updateErrorDescription:
      "An error occurred while updating the product. Please try again.",
    loadingError: "Error loading product",
    tryAgain: "Please try again later.",
    imageUploadError: "Error uploading image",
    imageUploadErrorDescription:
      "An error occurred while uploading the image. Please try again.",
    productDeleted: "Product deleted successfully!",
    confirmDelete: "Are you sure you want to delete this product?",
    uploadImage: "Upload image",
    selectCategory: "Select a category",
    available: "Available",
    unavailable: "Unavailable",
    noCategory: "No category",
    additionalPrice: "Additional price",
    optionPrice: "Additional price",
    hasVariationsShort: "Variations",
    uncategorized: "Uncategorized",
    deleting: "Deleting...",
    newProduct: "New product",
    quantity: "Quantity",
    total: "Total",
    product: "Product",
    unitPrice: "Unit Price",
    price: "Price",
    originalPrice: "Original price",
    priceAndQuantity: "Price and Quantity",
    observation: "Observation",
    addObservation: "Add an observation...",
    other: "Other",
    customOptionsDescription: "Add custom options as free text, price, and quantity.",
    customOptionName: "Description",
    customOptionNamePlaceholder: "Ex: Special decoration",
    addCustomOption: "Add",
    noCustomOptions: "No custom options added",
    customOptionNameRequired: "Option name is required",
    missingRequired: "Required options",
    selectAllRequired: "Please select all required options",
    notFound: "Product not found",
    itemUpdated: "Item updated",
    itemUpdatedDesc: "The item was successfully updated",
    itemRemoved: "Item removed",
    itemRemovedFromRevision: "The item has been removed from this revision",
    errorRemovingItem: "Error removing item",
    confirmRemoveItem: "Are you sure you want to remove this item from the revision?",
  },
  categories: {
    title: "Categories",
    addCategory: "Add Category",
    editCategory: "Edit Category",
    deleteCategory: "Delete Category",
    categoryName: "Category name",
    categoryDescription: "Description",
    confirmDelete: "Are you sure you want to delete this category?",
    uploadLogo: "Upload logo",
    changeImage: "Change image",
    logoDescription:
      "Upload an image for this category (recommended size: 800x600px)",
    categoryLogo: "Category logo",
    categoryDetails: "Category details",
    noCategories: "No categories found",
    newCategory: "New Category",
    categoryFormDescription: "Fill in the category details",
    noDescription: "No description available",
    confirmDeleteCategoryDesc: "This action cannot be undone",
    logoHelperText: "Upload an image for this category (optional)",
    positionNumber: "Position: {position}",
    reorderCategories: "Reorder Categories",
    categoriesReordered: "Categories have been reordered successfully",
  },
  orders: {
    title: "Orders",
    status: "Status",
    pending: "Pending",
    createdAt: "Creation date",
    orderDetails: "Order details",
    orderDetailsDescription: "Select a customer and add products to the order",
    orderItems: "Order items",
    orderTotal: "Total",
    orderDate: "Date",
    orderStatus: "Status",
    customerInfo: "Customer information",
    paymentMethod: "Payment method",
    paymentStatus: "Payment Status",
    notes: "Notes",
    updateStatus: "Update status",
    statusUpdated: "Status updated successfully!",
    noOrders: "No orders found",
    noOrdersMatchingFilters: "No orders match the selected filters",
    itemsCount: "{{count}} items",
    viewDetails: "View details",
    failedToLoadOrder: "Failed to load order details",
    quantity: "Quantity",
    unitPrice: "Unit price",
    subtotal: "Subtotal",
    orderSummary: "Order Summary",
    backToOrders: "Back to orders",
    selectProducts: "Select Products",
    addProduct: "Add Product",
    addProductToRevision: "Add Product to Revision",
    selectCustomerDesc: "Select a customer for this order revision. This will change the customer associated with the revision.",
    currentCustomer: "Current Customer",
    selectCustomer: "Select",
    customerChanged: "Customer Changed",
    customerChangedSuccess: "The customer has been successfully changed for this revision.",
    observation: "Observation",
    noObservation: "No observation",
    revised: "Revised",
    lastRevision: "Last revision",
    revisionNumber: "Revision",
    ordersList: "Orders List",
    newOrder: "New Order",
    selectProductsPage: "Select Products",
    continueToOrder: "Continue to order",
    deliveryAddress: "Delivery Address",
    street: "Street",
    streetPlaceholder: "Street, Avenue, etc.",
    number: "Number",
    numberPlaceholder: "Number",
    complement: "Complement",
    complementPlaceholder: "Apt, Block, etc.",
    neighborhood: "Neighborhood",
    neighborhoodPlaceholder: "Neighborhood",
    cityState: "City/State",
    cityStatePlaceholder: "City/State",
    reference: "Reference Point",
    referencePlaceholder: "Near to...",
    deliveryAddressRequired: "Delivery address required",
    pleaseCompleteDeliveryAddress: "Please fill in the required delivery address fields",
    requiredFieldsNote: "Fields with * are required",
    delivery: "Delivery",
    pickup: "Pickup",
    deliveryFee: "Delivery Fee",
    editDeliveryFee: "Edit Delivery Fee",
    saveDeliveryFee: "Save Delivery Fee",
    cancelEdit: "Cancel Edit",
    deliveryFeeUpdated: "Delivery fee updated successfully",
    errorUpdatingDeliveryFee: "Error updating delivery fee",
    editDiscount: "Edit Discount",
    discountUpdated: "Discount updated successfully",
    errorUpdatingDiscount: "Error updating discount",
    changeCustomer: "Change Customer",
    selectCustomerButton: "Select Customer",
    searchCustomers: "Search Customers",
    customerUpdated: "Customer updated successfully",
    errorUpdatingCustomer: "Error updating customer",
    noCustomersFound: "No customers found",
    tryAnotherSearch: "Try another search",
    searchToFindCustomers: "Type to search for customers",
    refreshingData: "Fetching the latest order information...",
    dataRefreshed: "Order data updated successfully.",
    refreshError: "An error occurred while updating the data. Please try again.",
    searchById: "Search by ID",
    searchByCustomer: "Search by customer",
    filterByStatus: "Filter by status",
    filterByDate: "Filter by date",
    dateFilters: "Date filters",
    dateFilterType: "Date filter type",
    selectDateFilterType: "Select filter type",
    noDateFilter: "No date filter",
    monthlyFilter: "Monthly filter",
    specificDateFilter: "Specific date",
    selectMonth: "Select month",
    currentMonth: "Current month",
    previousMonth: "Previous month",
    twoMonthsAgo: "Two months ago",
    threeMonthsAgo: "Three months ago",
    selectDate: "Select date",
    startDate: "Start date",
    endDate: "End date",
    pickStartDate: "Choose start date",
    pickEndDate: "Choose end date",
    dateRangeSummary: "Period: {{start}} to {{end}}",
    from: "From",
    until: "Until",
    clearFilters: "Clear filters",
    allStatuses: "All statuses",
    selectCustomerDescription: "Select an existing customer or create a new customer for the order",
    noCustomerSelected: "No customer selected",
    selectCustomerToCreate: "Select a customer to create the order",
    addProductsDescription: "Add products to the order",
    noProductsAdded: "No products added",
    addProductsToOrder: "Add products to create the order",
    addProducts: "Add Products",
    addMoreProducts: "Add More",
    checkoutDescription: "Review and finalize the order",
    deliveryInfo: "Delivery Information",
    receivingMethod: "Receiving Method",
    selectReceivingMethod: "Select method",
    receivingDate: "Receiving Date",
    receivingTime: "Receiving Time",
    paymentInfo: "Payment Information",
    selectPaymentMethod: "Select method",
    notesPlaceholder: "Notes about the order",
    placeOrder: "Place your order",
    createOrder: "Create Order",
    orderCreated: "Order created",
    orderCreatedSuccessfully: "The order has been created successfully",
    selectCustomerFirst: "Select a customer first",
    pleaseSelectCustomerBeforeProducts: "Please select a customer before adding products",
    pleaseSelectCustomerBeforeCreating: "Please select a customer before creating the order",
    addProductsFirst: "Add products first",
    pleaseAddProductsBeforeCreating: "Please add products before creating the order",
    selectPaymentMethodFirst: "Select a payment method",
    pleaseSelectPaymentMethodBeforeCreating: "Please select a payment method before creating the order",
    customers: {
      customerInfo: "Informações do Cliente"
    },
    selectReceivingMethodFirst: "Select a receiving method",
    pleaseSelectReceivingMethodBeforeCreating: "Please select a receiving method before creating the order",
    checkout: "Checkout",
    deliveryAndPayment: "Delivery and Payment",
    deliveryAndPaymentDescription: "Configure delivery and payment options",
    total: "Total",
    createCustomProduct: "Create Custom Product",
    editCustomProduct: "Edit Custom Product",
    customProductAddedToOrder: "The custom product has been added to the order",
    customProduct: "Custom Product",
    productNameRequired: "Product name is required",
    productNamePlaceholder: "Ex: Custom cake",
    productDescriptionPlaceholder: "Ex: Custom cake with special decoration",
    customProductDescription: "Create a custom product that will only exist in this order",
    customProductFormTitle: "Product form",
    customProductOptionsTitle: "Custom options",
    preview: "Preview Order",
    orderPreview: "Order Preview",
    orderNotFound: "Order not found",
    generatePdf: "Generate PDF",
    downloadPdf: "Download PDF",
    pdfGenerated: "PDF generated successfully",
    pdfGeneratedDesc: "The order PDF has been generated and is being downloaded.",
    pdfGenerationError: "An error occurred while generating the PDF. Please try again.",
    orderInfo: "Order Information",
    printOrder: "Print Order",
    revision: "Revision",
    scanQrCode: "Scan QR Code",
    toAccessOrder: "to access the order",
    generatedAt: "Generated on",
    thankYou: "Thank you for your preference!",
    noCustomerInfo: "No customer information",
    notSpecified: "Not specified",
    receivingTimeLabel: "Time",
    observationLabel: "Note",
    selectedOptions: "Selected options",
    dateCreated: "Creation date",
    orderNumber: "Order number",
  },
  customers: {
    title: "Customers",
    customerDetails: "Customer details",
    newCustomer: "New Customer",
    newCustomerDescription: "Fill in the details to create a new customer",
    createSuccess: "Customer created successfully",
    createSuccessMessage: "The customer has been created successfully",
    createError: "Error creating customer",
    createErrorMessage: "An error occurred while creating the customer. Please try again.",
    customerExists: "Customer already exists",
    customerExistsMessage: "A customer with this email or phone number already exists",
    name: "Name",
    email: "Email",
    phone: "Phone",
    orders: "Orders",
    ordersCount: "{{count}} orders",
    totalSpent: "Total spent",
    lastOrder: "Last order",
    noCustomers: "No customers found",
    viewOrders: "View orders",
    editCustomer: "Edit Customer",
    editCustomerDescription: "Update customer information",
    namePlaceholder: "Full customer name",
    emailPlaceholder: "<EMAIL>",
    phonePlaceholder: "(*************",
    updateSuccess: "Customer updated",
    updateSuccessMessage: "Customer information has been updated successfully.",
    updateError: "Error updating customer",
    updateErrorMessage: "An error occurred while updating customer information. Please try again.",
    details: "Details",
    createdAt: "Registration date",
    customerSince: "Customer since {{date}}",
    countryCode: "Country code",
    contactViaWhatsApp: "Contact via WhatsApp",
    contact: "Contact",
    noMatchingCustomers: "No customers matching \"{{query}}\"",
    noOrders: "This customer hasn't placed any orders yet",
    personalInfo: "Personal Information",
    address: "Address",
    complement: "Complement",
    city: "Neighborhood",
    saving: "Saving...",
    changeCustomer: "Change Customer",
  },
  settings: {
    title: "Store Settings",
    subtitle: "Customize your store and configure operation options",
    storeInfo: "Store Information",
    storeInfoDesc: "Name, description, logo and custom URL",
    uploadLogo: "Upload logo",
    visual: "Visual Customization",
    visualDesc: "Colors, fonts and store style",
    layoutOptions: "Layout Options",
    layoutOptionsDesc: "Choose how your products will be displayed",
    layoutType1: "Horizontal categories + Product grid",
    layoutType1Desc:
      "Navigation with sliding categories, products in a 2-column grid",
    layoutType2: "Horizontal Categories + Product List",
    layoutType2Desc: "Sliding category navigation with listtile product format",
    layoutType3: "Category sections",
    layoutType3Desc:
      "Single page with sections by category, products in horizontal carousel",
    payments: "Payments",
    paymentsDesc: "Accepted payment methods",
    languages: "Languages",
    languagesDesc: "Store language settings",
    basicInfo: "Basic Information",
    storeName: "Store name",
    storeDescription: "Store description",
    storeLogo: "Store logo",
    storeUrl: "Store URL",
    storeUrlPrefix: "app.docemenu.com.br/",
    colors: "Colors",
    primaryColor: "Primary color",
    secondaryColor: "Secondary color",
    accentColor: "Accent color",
    previewTheme: "Preview theme",
    paymentMethods: "Payment methods",
    acceptCash: "Accept cash",
    acceptCreditCard: "Accept credit card",
    acceptDebitCard: "Accept debit card",
    acceptPix: "Accept Pix",
    acceptBankTransfer: "Accept bank transfer",
    standardPaymentMethods: "Standard payment methods",
    customPaymentMethods: "Custom payment methods",
    cashPayment: "Cash",
    creditCardPayment: "Credit card",
    debitCardPayment: "Debit card",
    pixPayment: "PIX",
    bankTransferPayment: "Bank transfer",
    customPaymentMethodPlaceholder: "Ex: Check, Money order, Credit",
    paymentMethodExists: "Payment method already exists",
    paymentMethodExistsDesc: "This payment method has already been added",
    paymentMethodsDesc: "Configure the payment methods accepted by your store",
    add: "Add",
    selectLanguage: "Select language",
    portuguese: "Portuguese",
    english: "English",
    saveChanges: "Save changes",
    changesSuccessfullySaved: "Changes saved successfully!",
    previewStore: "Preview store",
    urlSlugInUse: "This URL is already in use",
    logoUpload: "Upload logo",
    maxSize: "Maximum size: 2MB",
    storeHeaderImage: "Store Header Image",
    uploadHeaderImage: "Upload header image",
    changeHeaderImage: "Change header image",
    headerImageHelp:
      "Upload a banner image for your store (recommended size: 1200x300 pixels). Max size 5MB.",
    headerImageUploaded: "Header image uploaded",
    headerImageUploadSuccess:
      "Your store header image has been updated successfully",
  },
  storefront: {
    addToCart: "Add",
    outOfStock: "Out of Stock",
    yourCart: "Your Cart",
    cart: "Cart",
    viewCart: "View Cart",
    viewAll: "View All",
    backToCart: "Back to Cart",
    continueShopping: "Continue Shopping",
    emptyCart: "Empty Cart",
    checkout: "Checkout",
    subtotal: "Subtotal",
    total: "Total",
    shipping: "Shipping",
    shippingCalc: "Shipping calculated at checkout",
    removeItem: "Remove",
    quantity: "Quantity",
    priceSummary: "Price Summary",
    basePrice: "Price",
    totalPrice: "Total Price",
    itemTotal: "Item Total",
    // Cupons
    haveCoupon: "Have a discount coupon?",
    enterCouponCode: "Enter coupon code",
    apply: "Apply",
    couponApplied: "Coupon applied",
    couponRemoved: "Coupon removed",
    discountApplied: "Discount applied successfully",
    discountRemoved: "Discount removed",
    invalidCoupon: "Invalid coupon",
    couponNotValid: "This coupon is not valid",
    errorApplyingCoupon: "Error applying coupon",
    discount: "Discount",
    discountFixed: "Discount (Fixed):",
    discountPercentage: "{{value}}% discount",
    couponCode: "Coupon code",
    couponDiscount: "Coupon discount",
    remove: "Remove",
    fixedDiscount: "Fixed discount of {{value}}",
    percentDiscount: "{{value}}% discount",
    cartCleared: "Cart cleared",
    allItemsRemoved: "All items have been removed from the cart",
    variations: "Variations",
    observation: "Observation",
    observationPlaceholder:
      "Any observation for this product? Ex: No onions, extra spicy, etc.",
    selectRequiredOptions: "Select required options",
    selectOne: "Select one option",
    selectMultiple: "Select multiple options",
    selectUpTo: "Select up to {{count}} options",
    selectAtLeast: "Select at least {{count}} options",
    requiredVariation: "Required option",
    optionalVariation: "Optional option",
    maxSelectionsReached: "You've reached the limit of {{max}} selections",
    minSelectionsRequired: "You need to select at least {{min}} options",
    productAddedToCart: "Product added to cart",
    addedToCart: "Added to cart",
    hasOptions: "With options",
    completeOrder: "Complete Order",
    customerInfo: "Customer Information",
    name: "Full name",
    email: "Email",
    phone: "Phone",
    whatsapp: "WhatsApp",
    whatsappPlaceholder: "(*************",
    countryCode: "Country Code",
    selectCountryCode: "Select country code",
    paymentMethod: "Payment Method",
    cash: "Cash",
    creditCard: "Credit Card",
    debitCard: "Debit Card",
    pix: "Pix",
    bankTransfer: "Bank Transfer",
    notes: "Notes",
    placeOrder: "Place your order",
    orderSuccess: "Order placed successfully!",
    orderError: "Error placing order",
    returnToStore: "Return to store",
    orderNumber: "Order number",
    allProducts: "All Products",
    categories: "Categories",
    moreInfo: "More information",
    searchProducts: "Search products",
    poweredBy: "Powered by Doce Menu",
    pickupDetails: "Pickup Details",
    deliveryDetails: "Delivery Details",
    date: "Date",
  },
  store: {
    categories: "Categories",
    loadingCategories: "Loading categories...",
    noImage: "No image",
    noProductsFound: "No products found matching",
    noProductsInCategory: "No products available in this category.",
    moreInfo: "More information",
    productNotFound: "Product not found",
  },
  payments: {
    paymentControl: "Payment Control",
    registerPayment: "Register Payment",
    paymentHistory: "Payment History",
    orderTotal: "Order Total",
    totalReceived: "Total Received",
    pendingAmount: "Pending Amount",
    paymentProgress: "Payment Progress",
    fullyPaid: "Fully paid",
    amount: "Amount",
    date: "Date",
    method: "Method",
    observation: "Observation",
    observationPlaceholder: "Additional information about the payment...",
    selectMethod: "Select method",
    fullAmount: "Full Amount",
    halfAmount: "Half",
    refund: "Refund",
    noPayments: "No payments registered",
    noPaymentsDescription: "Click \"Register Payment\" to add the first payment",
    paymentsCount: "{{count}} payment(s)",
    totalPayments: "{{count}} payment(s)",
    success: "Success",
    error: "Error",
    paymentCreated: "Payment registered successfully",
    paymentError: "Error registering payment",
    paymentDeleted: "Payment deleted successfully",
    deleteError: "Error deleting payment",
    deletePayment: "Delete Payment",
    confirmDelete: "Are you sure you want to delete this payment?",
    confirmDeleteMessage: "Are you sure you want to delete this payment? This action cannot be undone.",
    warning: "Warning",
    deleteWarning: "The order payment status will be automatically recalculated after deletion.",
    valueExceedsPending: "Value exceeds pending amount",
    valueExceedsPendingMessage: "The entered amount is greater than the pending amount for this order. This will result in overpayment.",
    excess: "Excess",
    statusRecalculated: "Payment status recalculated",
    recalculateError: "Error recalculating status",
    disputeWarning: "Payment in dispute",
    disputeDescription: "Check payments marked as disputed",
    refundWarning: "Payment refunded",
    refundDescription: "The amount was refunded to the customer",
    status: {
      pending: "Pending",
      partiallyReceived: "Partially Received",
      received: "Received",
      disputed: "Disputed",
      refunded: "Refunded",
      cancelled: "Cancelled"
    }
  },
  subscription: {
    title: "Subscription Plans",
    free: "Free",
    premium: "Premium",
    month: "month",
    popular: "Most Popular",
    current_plan: "Current Plan",
    current: "Current",
    upgrade_required: "Upgrade Required",
    upgrade_description: "This feature requires the Premium plan",
    upgrade: "Upgrade",
    upgrade_now: "Upgrade Now",
    select_free: "Select Free",
    processing: "Processing...",
    trial_days: "{{days}} days free",
    trial_offer: "7 days free trial",
    trial_description: "Test all premium features for 7 days with no commitment",
    start_trial: "Start Free Trial",
    monthly: "Monthly",
    yearly: "Yearly",
    year: "year",
    save_percentage: "Save {{percentage}}%",
    billed_annually: "Billed annually",
    no_trial: "No trial period",
    premium_features: "Premium Features",
    feature_blocked: {
      allowPdfGeneration: {
        title: "PDF Generation Blocked",
        description: "PDF generation is only available in the Premium plan"
      },
      allowAnalytics: {
        title: "Analytics Blocked",
        description: "Reports and analytics are only available in the Premium plan"
      },
      allowWhatsappIntegration: {
        title: "WhatsApp Blocked",
        description: "WhatsApp integration is only available in the Premium plan"
      },
      allowCoupons: {
        title: "Coupons Blocked",
        description: "Coupon system is only available in the Premium plan"
      },
      allowCustomization: {
        title: "Customization Blocked",
        description: "Advanced customization is only available in the Premium plan"
      }
    },
    limit_exceeded: {
      maxProducts: {
        title: "Product Limit Exceeded",
        description: "You have reached the limit of {{limit}} products in the free plan"
      },
      maxOrdersPerMonth: {
        title: "Order Limit Exceeded",
        description: "You have reached the limit of {{limit}} orders per month in the free plan"
      }
    },
    usage: {
      products: "Products",
      orders: "Orders",
      unlimited: "Unlimited"
    }
  },
  globalAdmin: {
    title: "Global Dashboard",
    subtitle: "Doce Menu platform administrative panel",
    analytics: {
      title: "Global Analytics",
      totalStores: "Total Stores",
      activeStores: "Active Stores",
      premiumStores: "Premium Stores",
      totalRevenue: "Total Revenue",
      totalOrders: "Total Orders",
      avgOrderValue: "Average Order Value",
      revenueGrowth: "Revenue Growth",
      orderGrowth: "Order Growth",
      last30Days: "Last 30 days"
    },
    stores: {
      title: "Store Management",
      subtitle: "View and manage all platform stores",
      searchPlaceholder: "Search by name, slug or email...",
      filters: {
        status: "Status",
        planType: "Plan",
        all: "All",
        active: "Active",
        inactive: "Inactive",
        free: "Free",
        premium: "Premium"
      },
      sort: {
        name: "Name",
        createdAt: "Creation Date",
        lastActivity: "Last Activity"
      },
      card: {
        createdAt: "Created on",
        lastActivity: "Last activity",
        plan: "Plan",
        status: "Status",
        viewDetails: "View Details",
        toggleStatus: "Toggle Status"
      },
      details: {
        title: "Store Details",
        owner: "Owner",
        metrics: "Metrics",
        totalProducts: "Total Products",
        totalOrders: "Total Orders",
        totalCustomers: "Total Customers",
        recentOrders: "Recent Orders",
        recentRevenue: "Recent Revenue",
        avgOrderValue: "Average Order Value"
      },
      actions: {
        activate: "Activate Store",
        deactivate: "Deactivate Store",
        confirmActivate: "Are you sure you want to activate this store?",
        confirmDeactivate: "Are you sure you want to deactivate this store?",
        activated: "Store activated successfully",
        deactivated: "Store deactivated successfully"
      }
    },
    subscriptions: {
      title: "Subscription Management",
      subtitle: "Control subscriptions and payments",
      distribution: "Plan Distribution",
      free: "Free",
      premium: "Premium",
      canceled: "Canceled",
      pastDue: "Past Due"
    },
    users: {
      title: "User Management",
      promoteAdmin: "Promote to Super Admin",
      removeAdmin: "Remove Super Admin",
      confirmPromote: "Are you sure you want to promote this user to super administrator?",
      confirmRemove: "Are you sure you want to remove super administrator permissions from this user?",
      promoted: "User promoted to super administrator",
      removed: "Super administrator permissions removed",
      cannotRemoveSelf: "You cannot remove your own permissions"
    },
    topStores: {
      title: "Top 10 Stores",
      byOrders: "By Number of Orders",
      byRevenue: "By Revenue",
      orders: "orders",
      revenue: "revenue"
    }
  },
};

// Initialize i18n
i18n.use(initReactI18next).init({
  resources: {
    pt: {
      translation: ptBrTranslations,
    },
    en: {
      translation: enTranslations,
    },
  },
  lng: "pt", // Default language
  fallbackLng: "pt",
  interpolation: {
    escapeValue: false, // React already escapes values
  },
});

export default i18n;