-- Script para corrigir inconsistências nas tabelas de variação de produtos

-- 1. Corrigir product_variations: adicionar coluna multiple_choice que está faltando
ALTER TABLE product_variations 
ADD COLUMN IF NOT EXISTS multiple_choice BOOLEAN DEFAULT FALSE;

-- 2. Ajustar a estrutura de variation_options para melhor compatibilidade com o código
-- Verificar a estrutura atual
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'variation_options';

-- 3. Solução alternativa: criar função de compatibilidade
-- Esta função permite que o código use tanto 'price' quanto 'additional_price'
CREATE OR REPLACE FUNCTION insert_variation_option(
  p_variation_id INTEGER,
  p_name TEXT,
  p_price REAL
) RETURNS INTEGER AS $$
DECLARE
  option_id INTEGER;
BEGIN
  -- Inserir com o nome de coluna correto
  INSERT INTO variation_options (variation_id, name, additional_price)
  VALUES (p_variation_id, p_name, p_price)
  RETURNING id INTO option_id;
  
  RETURN option_id;
END;
$$ LANGUAGE plpgsql;

-- 4. Exemplo de uso da função
-- SELECT insert_variation_option(1, 'Opção via função SQL', 5.0);

-- 5. Criar uma função para atualizar option
CREATE OR REPLACE FUNCTION update_variation_option(
  p_id INTEGER,
  p_name TEXT DEFAULT NULL,
  p_price REAL DEFAULT NULL
) RETURNS BOOLEAN AS $$
BEGIN
  -- Atualizar com os campos fornecidos
  UPDATE variation_options
  SET 
    name = COALESCE(p_name, name),
    additional_price = COALESCE(p_price, additional_price)
  WHERE id = p_id;
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- Exemplo de uso da função de atualização
-- SELECT update_variation_option(1, 'Novo nome', 10.0);
-- Para atualizar apenas o preço:
-- SELECT update_variation_option(1, NULL, 15.0);