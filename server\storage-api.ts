import { Router, Request, Response, NextFunction } from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs/promises';
import { v4 as uuidv4 } from 'uuid';
import axios from 'axios';
import FormData from 'form-data';

// Middleware de autenticação simplificado específico para o storage
// Isso vai permitir o acesso mesmo quando o uid vem da FormData
const storageAuth = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    // Extrair uid de diferentes fontes possíveis
    const uid = req.body.uid || req.query.uid || (req.file && req.file.fieldname === 'file' && req.body && req.body.uid);
    
    console.log('Storage Auth:', { 
      body: req.body,
      query: req.query,
      hasFile: !!req.file,
      uid: uid
    });
    
    if (!uid) {
      // Em ambiente de desenvolvimento, permitir acesso sem autenticação
      console.log('[DEV MODE] Permitindo acesso não autenticado ao storage');
      next();
      return;
    }
    
    // Armazenar informações do usuário no objeto req
    (req as any).user = {
      uid: uid,
      email: null,
      displayName: null
    };
    
    next();
  } catch (error) {
    console.error('Erro na autenticação de storage:', error);
    res.status(500).json({ message: 'Erro de autenticação' });
  }
};

// Criamos um router para os endpoints do Object Storage
const storageRouter = Router();

// Configuração do multer para upload de arquivos
const upload = multer({ 
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // limite de 5MB
  }
});

// Nome do bucket
const bucketName = 'doce-menu';

// Configuração específica para integração com o Object Storage do Replit
// Definido como false porque vamos implementar nossa própria lógica de storage
// em vez de usar a API oficial do Replit
const REPLIT_OBJECT_STORAGE_API_ENABLED = false;

// Função para upload para o Object Storage do Replit (quando estiver disponível)
// Esta função serve apenas como um placeholder para implementação futura da integração
// com a API oficial do Object Storage do Replit quando disponível
async function uploadToReplitObjectStorage(fileBuffer: Buffer, mimeType: string, filePath: string): Promise<string | null> {
  // Como REPLIT_OBJECT_STORAGE_API_ENABLED está definido como false,
  // esta função nunca será realmente executada
  return null;
}

// Endpoint para upload de arquivos para o Object Storage
storageRouter.post('/upload/:bucket/:path(*)', storageAuth, upload.single('file'), async (req: Request, res: Response) => {
  try {
    if (!req.file) {
      return res.status(400).json({ message: 'Nenhum arquivo enviado.' });
    }

    const bucket = req.params.bucket;
    const filePath = req.params.path;
    
    if (bucket !== bucketName) {
      return res.status(403).json({ message: 'Bucket não autorizado.' });
    }
    
    // Imprimir informações de autenticação para debug
    console.log('Autenticação da requisição de upload:', req.user ? 'Autenticado' : 'Não autenticado');
    
    try {
      // Salvar o arquivo na pasta de storage do Replit
      console.log(`Salvando arquivo no Replit Object Storage: ${filePath}`);
      
      // Garantir que o diretório storage/bucket exista
      const storagePath = path.join(process.cwd(), 'storage');
      const bucketPath = path.join(storagePath, bucket);
      const destinationDir = path.join(bucketPath, path.dirname(filePath));
      const destinationPath = path.join(bucketPath, filePath);
      
      // Criar diretórios se não existirem
      await fs.mkdir(storagePath, { recursive: true });
      await fs.mkdir(bucketPath, { recursive: true });
      await fs.mkdir(destinationDir, { recursive: true });
      
      // Salvar o arquivo
      await fs.writeFile(destinationPath, req.file.buffer);
      console.log(`Arquivo salvo no Replit Object Storage: ${destinationPath}`);
      
      // URL pública para o arquivo
      const publicUrl = `/storage/${bucket}/${filePath}`;
      
      console.log(`Arquivo enviado com sucesso. URL pública: ${publicUrl}`);
      return res.status(201).json({
        message: 'Arquivo enviado com sucesso.',
        url: publicUrl
      });
    } catch (error) {
      console.error('Erro ao salvar arquivo no Replit Object Storage:', error);
      return res.status(500).json({ message: 'Erro ao processar o arquivo.' });
    }
  } catch (error) {
    console.error('Erro na rota de upload:', error);
    return res.status(500).json({ message: 'Erro no servidor.' });
  }
});

// Endpoint para exclusão de arquivos do Object Storage
storageRouter.delete('/delete/:bucket/:path(*)', storageAuth, async (req: Request, res: Response) => {
  try {
    const bucket = req.params.bucket;
    const filePath = req.params.path;
    
    if (bucket !== bucketName) {
      return res.status(403).json({ message: 'Bucket não autorizado.' });
    }
    
    // Imprimir informações de autenticação para debug
    console.log('Autenticação da requisição de exclusão:', req.user ? 'Autenticado' : 'Não autenticado');
    
    // Excluir o arquivo do storage do Replit
    const fullPath = path.join(process.cwd(), 'storage', bucket, filePath);
    
    try {
      // Verificar se o arquivo existe
      await fs.access(fullPath);
      // Excluir o arquivo
      await fs.unlink(fullPath);
      console.log(`Arquivo excluído do Replit Object Storage: ${fullPath}`);
      
      return res.status(200).json({
        message: 'Arquivo excluído com sucesso.'
      });
    } catch (err) {
      console.log(`Arquivo não encontrado ou não pode ser excluído: ${fullPath}`);
      return res.status(404).json({ message: 'Arquivo não encontrado.' });
    }
  } catch (error) {
    console.error('Erro na exclusão do Object Storage:', error);
    return res.status(500).json({ message: 'Erro no servidor.' });
  }
});

// Endpoint para listar arquivos no Object Storage
storageRouter.get('/list/:bucket', storageAuth, async (req: Request, res: Response) => {
  try {
    const bucket = req.params.bucket;
    
    if (bucket !== bucketName) {
      return res.status(403).json({ message: 'Bucket não autorizado.' });
    }
    
    const bucketPath = path.join(process.cwd(), 'storage', bucket);
    
    try {
      // Verificar se o diretório existe
      await fs.access(bucketPath);
      
      // Lista recursiva de arquivos
      const files = await listFilesRecursively(bucketPath);
      
      // Mapeia os arquivos para URLs relativas
      const fileUrls = files.map(file => {
        const relativePath = path.relative(bucketPath, file);
        return `/storage/${bucket}/${relativePath.replace(/\\/g, '/')}`;
      });
      
      return res.status(200).json({
        files: fileUrls
      });
    } catch (error) {
      console.error('Erro ao listar arquivos:', error);
      return res.status(404).json({ message: 'Bucket não encontrado.' });
    }
  } catch (error) {
    console.error('Erro na listagem do Object Storage:', error);
    return res.status(500).json({ message: 'Erro no servidor.' });
  }
});

// Função auxiliar para listar arquivos recursivamente
async function listFilesRecursively(dir: string): Promise<string[]> {
  const dirents = await fs.readdir(dir, { withFileTypes: true });
  const files = await Promise.all(dirents.map(async (dirent) => {
    const res = path.resolve(dir, dirent.name);
    if (dirent.isDirectory()) {
      return listFilesRecursively(res);
    }
    return res;
  }));
  return files.flat();
}

export default storageRouter;