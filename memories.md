
# General
- Voce é um especialista em desenvolvimento app web com: react, node js e supabase
- User is using Supabase for database instead of direct PostgreSQL.
- When changes are done
- use localhost:5000 to test
- always check translations for texts added / changed
- textos em portugues
- Não fazer commit automaticamente - sempre perguntar ao usuário antes de fazer commit de alterações.
- Usar o símbolo de moeda (currency) da configuração da loja em todos os campos e exibições de valores monetários.
- Sempre considerar a interface UX para iOS mobile ao implementar ou modificar componentes, garantindo boa experiência em dispositivos Apple.

# Customer Management
- When creating an order from the cart, check if the customer already exists by email first, then by phone number, and only create a new customer if they don't exist.
- Add WhatsApp integration buttons for customer phone numbers.
- Add sorting buttons by name and creation date on the customer list screen.
- Add pagination for large customer lists.

# UI Improvements
- Fix duplicated headers in orders and customers pages the same way as in products page.
- Replace tables with card-based layouts for mobile-friendly order lists, using vertical stacking with clear sections for order details.
- Use card-based navigation where clicking on a card opens details.
- User prefers non-modal screens for editing/viewing details.
- Keep translations updated for UI elements, including 'common.goBack'.
- Don't use modal for order details, use a new page instead
- Improve mobile layouts by reducing duplicate buttons - specifically, use only one button for editing order items.
- Don't use nested cards for item lists in order details; prefer a cleaner layout without the extra card wrapper.
- Use 'preço' instead of 'preço base' in UI text labels.
- Price editing and quantity fields should be placed on the same line to improve layout.
- When implementing form fields for price and quantity, place price field before quantity field.
- Move subtotal and delivery fee information from the checkout tab to the order details tab.
- Adicionar ícone para editar a taxa de frete na tela de revisão de pedidos.
- Não usar modal para edição de frete; preferir edição direta na tela com botões de salvar/cancelar.
- Revisar a tela de editar revisão de pedidos e atualizar as traduções da interface.
- On the order details screen, move the total amount to display below the list of selected options instead of below the unit price, and fix the total sum calculation.

# Product Management
- Add search functionality to the products screen that searches by product name.
- In variations, add a new option called 'outros' (others) where admins can add text, value, and quantity.
- After adding a new product, return to the orders.newOrder screen instead of the current behavior.

# Order Management
- Add search functionality to the orders screen with filters for order ID, customer, status, and date (monthly period or specific date).
- When implementing date filters with specific dates, allow users to select both start and end dates to define a date range.
- Add order revision functionality allowing admins to modify dates, times, payment methods, add/remove items, update total value, and update shipping costs.
- Add a field to edit the base value/price of the product in the order revision product details screen.
- User wants to work on the product editing screen for order revisions.
- Don't use modal screens for order revision functionality, use a new page instead.
- Order item tables should store all product data to prevent data loss if the original product is deleted.
- Remove revision details from the order management interface.
- In the order revision product editing screen, the 'outros' variations should not be multiplied by the product quantity.
- Add functionality to change the customer in order revisions with a button in customer information section, showing a filterable customer list, and allowing selection to update the revision's customer.
- Don't use modal screens for customer selection; prefer a different UI approach for selecting customers.
- Analisar e implementar uma estratégia eficiente para administradores criarem novos pedidos diretamente na interface.
- On the order list screen, display revision data for orders that have revisions.
- On the new order screen, display delivery address fields only when the receiving method is set to 'delivery'.
- Address fields in order creation and order revision screens should be consistent, with no extra fields like ZIP code in one screen that aren't in the other.
- On the new order screen, combine the customer and products tabs into a single tab.
- When creating a new order, use the admin/orders/product-details/ screen when an admin selects a product to add to the order.
- Reutilizar a página de detalhes do produto para adicionar produtos em branco em novos pedidos, permitindo edição do nome e descrição do produto.
- Produtos personalizados existem apenas no contexto do pedido e não são armazenados no banco de dados.
- Add subtotal per item on the new order screen.
- Add a button on the new order screen to edit products using the product details screen.
- Add translations for the orders.selectProducts screen.