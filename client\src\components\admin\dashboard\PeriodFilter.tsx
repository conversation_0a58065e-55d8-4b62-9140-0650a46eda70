import { useState } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card } from "@/components/ui/card";
import { Calendar, ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";

export type PeriodType = "week" | "month" | "quarter" | "year";
export type PeriodValue = "last7Days" | "last30Days" | "last90Days" | "last365Days" | "thisWeek" | "thisMonth" | "thisQuarter" | "thisYear";

interface PeriodFilterProps {
  selectedPeriod: PeriodValue;
  onPeriodChange: (period: PeriodValue) => void;
  className?: string;
}

const periodOptions: { value: PeriodValue; type: PeriodType }[] = [
  { value: "last7Days", type: "week" },
  { value: "thisWeek", type: "week" },
  { value: "last30Days", type: "month" },
  { value: "thisMonth", type: "month" },
  { value: "last90Days", type: "quarter" },
  { value: "thisQuarter", type: "quarter" },
  { value: "last365Days", type: "year" },
  { value: "thisYear", type: "year" },
];

export function PeriodFilter({ selectedPeriod, onPeriodChange, className }: PeriodFilterProps) {
  const { t } = useTranslation();
  const [selectedType, setSelectedType] = useState<PeriodType>("month");

  const filteredOptions = periodOptions.filter(option => option.type === selectedType);

  const handleTypeChange = (type: PeriodType) => {
    setSelectedType(type);
    // Automatically select the first option of the new type
    const firstOption = periodOptions.find(option => option.type === type);
    if (firstOption) {
      onPeriodChange(firstOption.value);
    }
  };

  const handlePeriodChange = (period: PeriodValue) => {
    onPeriodChange(period);
  };

  return (
    <Card className={cn("p-4 bg-white/80 backdrop-blur-sm border-gray-200/50 shadow-sm", className)}>
      <div className="flex flex-col sm:flex-row gap-3 items-start sm:items-center">
        <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
          <Calendar className="h-4 w-4 text-blue-600" />
          <span>{t('dashboard.periodFilter.title')}</span>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
          {/* Period Type Selector */}
          <div className="flex rounded-lg bg-gray-100 p-1 shadow-inner">
            {(['week', 'month', 'quarter', 'year'] as PeriodType[]).map((type) => (
              <Button
                key={type}
                variant="ghost"
                size="sm"
                onClick={() => handleTypeChange(type)}
                className={cn(
                  "px-3 py-1.5 text-xs font-medium rounded-md transition-all duration-200",
                  "hover:bg-white hover:shadow-sm",
                  selectedType === type
                    ? "bg-white text-blue-600 shadow-sm border border-blue-200/50"
                    : "text-gray-600 hover:text-gray-800"
                )}
              >
                {t(`dashboard.periodFilter.${type}`)}
              </Button>
            ))}
          </div>

          {/* Period Value Selector */}
          <Select value={selectedPeriod} onValueChange={handlePeriodChange}>
            <SelectTrigger className="w-full sm:w-48 bg-white border-gray-200 shadow-sm hover:border-blue-300 focus:border-blue-500 transition-colors">
              <SelectValue />
              <ChevronDown className="h-4 w-4 text-gray-500" />
            </SelectTrigger>
            <SelectContent className="bg-white border-gray-200 shadow-lg">
              {filteredOptions.map((option) => (
                <SelectItem 
                  key={option.value} 
                  value={option.value}
                  className="hover:bg-blue-50 focus:bg-blue-50 cursor-pointer"
                >
                  {t(`dashboard.periodFilter.${option.value}`)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
    </Card>
  );
}
