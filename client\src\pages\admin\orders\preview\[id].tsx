import { useEffect, useState, useRef } from 'react';
import { useLocation } from 'wouter';
import AdminLayout from '@/components/admin/AdminLayout';
import { useTranslation } from '@/hooks/useTranslation';
import { useStore } from '@/context/StoreContext';
import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { formatCurrency, formatPhoneWithCountryCode, formatDate } from '@/lib/utils';
import { generatePdfFromElement } from '@/lib/pdfUtils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import '@/styles/print-order.css';
import {
  ShoppingBag,
  ArrowLeft,
  Printer,
  User,
  FileText,
  MapPin,
  CreditCard,
  Phone,
  Instagram,
  RefreshCw,
  Mail
} from 'lucide-react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

// Função auxiliar para garantir que valores numéricos sejam válidos e arredondados para 2 casas decimais
const safeNumber = (value: any): number => {
  if (value === undefined || value === null || isNaN(value)) {
    return 0;
  }

  // Converter para número e arredondar para 2 casas decimais
  const numValue = Number(value);

  // Não arredondar valores inteiros para evitar problemas com IDs e quantidades
  if (Number.isInteger(numValue)) {
    return numValue;
  }

  // Arredondar valores monetários para 2 casas decimais
  return parseFloat(numValue.toFixed(2));
};

type OrderPreviewPageProps = {
  id: number;
};

export default function OrderPreviewPage({ id }: OrderPreviewPageProps) {
  const [, setLocation] = useLocation();
  const { t } = useTranslation();
  const { toast } = useToast();
  const { store } = useStore();
  const printRef = useRef<HTMLDivElement>(null);

  // State for active revision
  const [activeRevision, setActiveRevision] = useState<any>(null);

  // Fetch order data
  const { data: order, isLoading, error, refetch: refetchOrder } = useQuery({
    queryKey: [`/api/orders/${id}`],
    enabled: !!id && !!store,
  });

  // Fetch order revisions
  const { data: revisions, refetch: refetchRevisions } = useQuery({
    queryKey: [`/api/orders/${id}/revisions`],
    enabled: !!id && !!store,
  });

  // Função para atualizar os dados do pedido e revisões
  const refreshOrderData = async () => {
    toast({
      title: t('common.refreshing') || "Atualizando dados",
      description: t('orders.refreshingData') || "Buscando as informações mais recentes do pedido...",
      variant: "default"
    });

    try {
      await Promise.all([refetchOrder(), refetchRevisions()]);

      toast({
        title: t('common.success') || "Sucesso",
        description: t('orders.dataRefreshed') || "Dados do pedido atualizados com sucesso.",
        variant: "default"
      });
    } catch (error) {
      console.error('Erro ao atualizar dados:', error);
      toast({
        title: t('common.error') || "Erro",
        description: t('orders.refreshError') || "Ocorreu um erro ao atualizar os dados. Tente novamente.",
        variant: "destructive"
      });
    }
  };

  // Set active revision to the latest one if available
  useEffect(() => {
    if (revisions && revisions.length > 0) {
      // Find the active revision or use the latest one
      const active = revisions.find((rev: any) => rev.isActive) || revisions[0];
      setActiveRevision(active);
    }
  }, [revisions]);

  // Estilos para impressão - definido antes de qualquer condicional
  useEffect(() => {
    // Adicionar estilos para impressão
    const style = document.createElement('style');
    style.id = 'print-styles';
    style.innerHTML = `
      @media print {
        body * {
          visibility: hidden;
        }
        #printRef, #printRef * {
          visibility: visible;
        }
        #printRef {
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
        }
        .no-print {
          display: none !important;
        }
        .page-break {
          page-break-before: always;
        }
        table {
          page-break-inside: avoid;
        }
        .print-full-width {
          width: 100% !important;
        }
      }
    `;
    document.head.appendChild(style);

    // Limpar ao desmontar
    return () => {
      const existingStyle = document.getElementById('print-styles');
      if (existingStyle) {
        existingStyle.remove();
      }
    };
  }, []); // Removida a dependência para evitar problemas com renderização condicional

  // Handle back button
  const handleBack = () => {
    setLocation(`/admin/orders/${id}`);
  };

  // Function to generate PDF from the preview element
  const handleGeneratePdfFromElement = async () => {
    if (!printRef.current) return;

    try {
      const fileName = `pedido-${id}${activeRevision ? `-revisao-${activeRevision.revisionNumber}` : ''}.pdf`;
      await generatePdfFromElement(printRef.current, fileName);

      toast({
        title: t('orders.pdfGenerated') || "PDF gerado com sucesso",
        description: t('orders.pdfGeneratedDesc') || `O PDF do pedido #${id} foi gerado e está sendo baixado.`,
        variant: "default"
      });
    } catch (error) {
      console.error('Erro ao gerar PDF:', error);
      toast({
        title: t('common.error') || "Erro",
        description: t('orders.pdfGenerationError') || "Ocorreu um erro ao gerar o PDF. Tente novamente.",
        variant: "destructive"
      });
    }
  };



  if (isLoading) {
    return (
      <AdminLayout title={t('orders.orderPreview') || "Preview do Pedido"}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
        </div>
      </AdminLayout>
    );
  }

  if (error || !order) {
    return (
      <AdminLayout title={t('orders.orderPreview') || "Preview do Pedido"}>
        <div className="p-4 bg-error/10 text-error rounded-md">
          <h2 className="text-lg font-semibold">{t('common.error') || "Erro"}</h2>
          <p>{t('orders.orderNotFound') || "Pedido não encontrado"}</p>
          <Button variant="outline" className="mt-4" onClick={() => setLocation('/admin/orders')}>
            {t('common.goBack') || "Voltar"}
          </Button>
        </div>
      </AdminLayout>
    );
  }

  // Determine which data to use (revision or original order)
  const displayData = activeRevision || order;
  const currency = store?.currency || 'R$';

  // Função para formatar o método de pagamento
  const getFormattedPaymentMethod = (paymentMethodId: string): string => {
    // Para métodos personalizados
    if (paymentMethodId && paymentMethodId.startsWith('custom_')) {
      const customIndex = parseInt(paymentMethodId.replace('custom_', ''), 10);
      if (!isNaN(customIndex) &&
          store?.paymentMethods?.customMethods &&
          Array.isArray(store.paymentMethods.customMethods) &&
          customIndex < store.paymentMethods.customMethods.length) {
        return store.paymentMethods.customMethods[customIndex];
      }
    }

    // Para métodos padrão
    switch(paymentMethodId) {
      case 'cash':
        return t('storefront.cash') || 'Dinheiro';
      case 'creditCard':
        return t('storefront.creditCard') || 'Cartão de Crédito';
      case 'debitCard':
        return t('storefront.debitCard') || 'Cartão de Débito';
      case 'pix':
        return t('storefront.pix') || 'PIX';
      case 'bankTransfer':
        return t('storefront.bankTransfer') || 'Transferência Bancária';
      default:
        return paymentMethodId;
    }
  };

  // Obter cores da configuração da loja
  const storeColors = store?.colors || { primary: "#6082e6", secondary: "#577590", accent: "#F4A261" };

  // Criar variações de cores para diferentes elementos visuais
  const colorVariations = {
    // Versões com transparência para fundos e bordas
    primaryLight: `${storeColors.primary}15`,
    primaryMedium: `${storeColors.primary}30`,
    primaryBorder: `${storeColors.primary}40`,

    secondaryLight: `${storeColors.secondary}15`,
    secondaryMedium: `${storeColors.secondary}30`,

    accentLight: `${storeColors.accent}20`,
    accentMedium: `${storeColors.accent}40`,

    // Cores para texto e elementos de destaque
    textPrimary: storeColors.primary,
    textSecondary: storeColors.secondary,

    // Cores para fundos
    bgLight: '#FFFFFF',
    bgMedium: `${storeColors.accent}30`,
    bgDark: storeColors.primary
  };

  return (
    <AdminLayout title={t('orders.orderPreview') || "Preview do Pedido"}>
      <div className="mb-4 flex items-center justify-between">
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleBack}
            className="flex items-center"
            style={{
              borderColor: `${storeColors.primary}40`,
              color: storeColors.primary
            }}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t('common.goBack') || "Voltar"}
          </Button>

          <Button
            variant="outline"
            onClick={refreshOrderData}
            className="flex items-center"
            style={{
              borderColor: `${storeColors.primary}40`,
              color: storeColors.primary
            }}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            {t('common.refresh') || "Atualizar"}
          </Button>
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleGeneratePdfFromElement}
            className="flex items-center"
            style={{
              borderColor: `${storeColors.primary}40`,
              color: storeColors.primary
            }}
          >
            <Printer className="h-4 w-4 mr-2" />
            {t('orders.generatePdf') || "Gerar PDF"}
          </Button>
        </div>
      </div>

      {/* Container com scroll horizontal para manter dimensões A4 fixas */}
      <div className="print-preview-container">
        {/* Preview content that will be converted to PDF */}
        <div
          id="printRef"
          ref={printRef}
          className="print-order print-preview p-6 rounded-lg shadow-lg"
          style={{
            backgroundColor: colorVariations.bgLight,
            border: `1px solid ${colorVariations.accentMedium}`
          }}
        >
        {/* Header com logo e informações do pedido */}
        <div className="print-header">
          {/* Coluna Esquerda - Logo */}
          <div className="print-header-logo">
            {store?.logo ? (
              <img
                src={store.logo}
                alt={store?.name || 'Doce Menu'}
                className="logo"
                onLoad={() => console.log('Logo carregado com sucesso')}
                onError={(e) => {
                  console.error('Erro ao carregar logo:', e);
                  // Fallback para texto caso a imagem falhe
                  e.currentTarget.style.display = 'none';
                }}
              />
            ) : (
              <div className="logo-fallback">
                {store?.name?.charAt(0) || 'D'}
              </div>
            )}
          </div>

          {/* Coluna Central - Informações da Loja */}
          <div className="print-header-center">
            <h1 style={{ color: storeColors.primary }}>Pedido</h1>
            <h2>{store?.name || 'Doce Menu'}</h2>

            <div className="print-header-info">
              {store?.addressStreet && (
                <p>
                  {store.addressStreet}
                  {store.addressNumber && `, ${store.addressNumber}`}
                  {store.addressNeighborhood && ` - ${store.addressNeighborhood}`}
                  <br />
                  {store.addressCity && `${store.addressCity}`}
                  {store.addressState && ` - ${store.addressState}`}
                </p>
              )}
              {store?.contactEmail && <p>Email: {store.contactEmail}</p>}
              {store?.whatsapp && <p>WhatsApp: {store.whatsapp}</p>}
            </div>
          </div>

          {/* Coluna Direita - Informações do Pedido */}
          <div className="print-header-right">
            <div className="print-header-order-info">
              <p style={{ color: storeColors.primary, marginBottom: '10px' }}>
                {t('orders.orderNumber') || "Pedido"} #{order.id}
              </p>

              <div className="print-status-container">
                <div className="print-status-label">
                  <strong>{t('orders.status') || "Status"}:</strong>
                </div>
                <div className="print-status-value">
                  <span className={`print-status-badge print-status-${displayData.status?.toLowerCase() || 'pending'}`}>
                    {displayData.status === 'pending' ? (t('orders.pending') || "Pendente") : displayData.status}
                  </span>
                </div>
              </div>

              <div className="print-info-block">
                <strong>{t('orders.receivingMethod') || "Método de Entrega"}:</strong>
                <div className="print-info-value">
                  {displayData.receivingMethod === 'delivery' ? t('orders.delivery') || "Entrega" : t('orders.pickup') || "Retirada"}
                </div>
              </div>

              {displayData.receivingDate && (
                <div className="print-info-block">
                  <strong>{t('orders.receivingDate') || "Data de Entrega"}:</strong>
                  <div className="print-info-value">
                    {formatDate(displayData.receivingDate)}
                    {displayData.receivingTime && ` às ${displayData.receivingTime}`}
                  </div>
                </div>
              )}
            </div>
          </div>

        </div>

        {/* Seção de informações do cliente e endereço - layout de duas colunas */}
        <div className="print-section mb-6">
          <div className="print-section-title" style={{ color: storeColors.primary }}>
            <User className="h-5 w-5" />
            {t('orders.customerInfo') || "Informações do Cliente"}
          </div>

          {displayData.customer ? (
            <div className="print-customer-info-container">
              <div className="print-customer-details-grid">
                {/* Coluna 1: Informações pessoais do cliente */}
                <div className="print-customer-column">
                  <div className="print-column-title" style={{ color: storeColors.primary }}>
                    <User className="h-3 w-3 inline mr-1" />
                    {t('customers.personalInfo') || "Dados Pessoais"}
                  </div>

                  <div className="print-info-row">
                    <div className="print-info-label" style={{ color: storeColors.primary }}>
                      <User className="h-3 w-3 inline mr-1" />
                      {t('customers.name') || "Nome"}:
                    </div>
                    <div className="print-info-value">{displayData.customer.name}</div>
                  </div>

                  {displayData.customer.email && (
                    <div className="print-info-row">
                      <div className="print-info-label" style={{ color: storeColors.primary }}>
                        <Mail className="h-3 w-3 inline mr-1" />
                        {t('customers.email') || "Email"}:
                      </div>
                      <div className="print-info-value">{displayData.customer.email}</div>
                    </div>
                  )}

                  {displayData.customer.phone && (
                    <div className="print-info-row">
                      <div className="print-info-label" style={{ color: storeColors.primary }}>
                        <Phone className="h-3 w-3 inline mr-1" />
                        {t('customers.phone') || "Telefone"}:
                      </div>
                      <div className="print-info-value">
                        {formatPhoneWithCountryCode(displayData.customer.phone, store?.countryCode || '+55')}
                      </div>
                    </div>
                  )}
                </div>

                {/* Coluna 2: Endereço de entrega (apenas quando o método de recebimento for "delivery") */}
                {displayData.receivingMethod === 'delivery' && displayData.deliveryAddress ? (
                  <div className="print-customer-column">
                    <div className="print-column-title" style={{ color: storeColors.primary }}>
                      <MapPin className="h-3 w-3 inline mr-1" />
                      {t('orders.deliveryAddress') || "Endereço de Entrega"}
                    </div>

                    <div className="print-info-row">
                      <div className="print-info-label" style={{ color: storeColors.primary }}>
                        <MapPin className="h-3 w-3 inline mr-1" />
                        {t('customers.address') || "Endereço"}:
                      </div>
                      <div className="print-info-value">
                        <span className="font-medium">
                          {displayData.deliveryAddress.street}
                          {displayData.deliveryAddress.number && `, ${displayData.deliveryAddress.number}`}
                        </span>
                      </div>
                    </div>

                    {displayData.deliveryAddress.complement && (
                      <div className="print-info-row">
                        <div className="print-info-label" style={{ color: storeColors.primary }}>
                          <MapPin className="h-3 w-3 inline mr-1" />
                          {t('customers.complement') || "Complemento"}:
                        </div>
                        <div className="print-info-value">
                          {displayData.deliveryAddress.complement}
                        </div>
                      </div>
                    )}

                    <div className="print-info-row">
                      <div className="print-info-label" style={{ color: storeColors.primary }}>
                        <MapPin className="h-3 w-3 inline mr-1" />
                        {t('customers.city') || "Bairro"}:
                      </div>
                      <div className="print-info-value">
                        {displayData.deliveryAddress.neighborhood && `${displayData.deliveryAddress.neighborhood}, `}
                        {displayData.deliveryAddress.city}
                        {displayData.deliveryAddress.state && ` - ${displayData.deliveryAddress.state}`}
                      </div>
                    </div>
                  </div>
                ) : null}
              </div>
            </div>
          ) : (
            <p className="text-gray-500 italic text-xs">{t('orders.noCustomerInfo') || "Sem informações do cliente"}</p>
          )}
        </div>

        <div className="print-section-divider"></div>

        {/* Order items */}
        <div className="print-section mb-6">
          <div className="print-section-title" style={{ color: storeColors.primary }}>
            <ShoppingBag className="h-5 w-5" />
            {t('orders.items') || "Itens do Pedido"}
          </div>

          <table className="print-products-table">
            <thead>
              <tr>
                <th style={{ color: storeColors.primary, width: '50%' }}>
                  {t('products.product') || "Produto"}
                </th>
                <th className="print-text-center" style={{ color: storeColors.primary, width: '15%' }}>
                  {t('orders.quantity') || "Qtd"}
                </th>
                <th style={{ color: storeColors.primary, width: '17.5%', textAlign: 'right' }}>
                  {t('products.price') || "Preço"}
                </th>
                <th style={{ color: storeColors.primary, width: '17.5%', textAlign: 'right' }}>
                  {t('orders.subtotal') || "Subtotal"}
                </th>
              </tr>
            </thead>
            <tbody>
              {displayData.items && displayData.items.map((item: any) => {
                const quantity = safeNumber(item.quantity);
                const baseUnitPrice = safeNumber(item.price || item.unitPrice);
                const basePrice = baseUnitPrice * quantity;

                // Calcular preço das variações usando a mesma lógica da tela de detalhes
                let variationsTotal = 0;

                // Verificar se temos variações no formato selectedOptions
                if (item.selectedOptions && Array.isArray(item.selectedOptions) && item.selectedOptions.length > 0) {
                  variationsTotal = item.selectedOptions.reduce((total: number, variation: any) => {
                    const variationPrice = parseFloat(variation.price) || 0;
                    const variationQuantity = variation.quantity || 1;

                    // Verificar se é uma variação do tipo "outros"
                    const isCustomVariation = variation.variationName?.toLowerCase() === 'outros' ||
                                             variation.optionName?.toLowerCase() === 'outros';

                    // Para variações do tipo "outros", não multiplicar pela quantidade do produto
                    if (isCustomVariation) {
                      return total + (variationPrice * variationQuantity);
                    } else {
                      // Para variações normais, multiplicar pela quantidade do produto
                      return total + (variationPrice * variationQuantity * quantity);
                    }
                  }, 0);
                }
                // Verificar se temos variações no formato selectedVariations
                else if (item.selectedVariations && Array.isArray(item.selectedVariations) && item.selectedVariations.length > 0) {
                  variationsTotal = item.selectedVariations.reduce((total: number, variation: any) => {
                    const variationPrice = parseFloat(variation.price) || 0;
                    const variationQuantity = variation.quantity || 1;

                    // Verificar se é uma variação do tipo "outros"
                    const isCustomVariation = variation.variationName?.toLowerCase() === 'outros' ||
                                             variation.optionName?.toLowerCase() === 'outros';

                    // Para variações do tipo "outros", não multiplicar pela quantidade do produto
                    if (isCustomVariation) {
                      return total + (variationPrice * variationQuantity);
                    } else {
                      // Para variações normais, multiplicar pela quantidade do produto
                      return total + (variationPrice * variationQuantity * quantity);
                    }
                  }, 0);
                }

                // Subtotal do item (preço base + variações)
                const itemTotal = basePrice + variationsTotal;

                // Preço unitário para exibição na coluna de preço
                const unitPrice = baseUnitPrice + (quantity > 0 ? (variationsTotal / quantity) : 0);

                return (
                  <tr key={item.id}>
                    <td>
                      <div className="print-product-name">
                        {item.productName || (item.product && item.product.name) || "Produto"}
                      </div>

                      {/* Variações selecionadas com informações detalhadas */}
                      {((item.selectedVariations && Array.isArray(item.selectedVariations) && item.selectedVariations.length > 0) ||
                        (item.selectedOptions && Array.isArray(item.selectedOptions) && item.selectedOptions.length > 0)) && (
                        <div className="print-variations-container">
                          <div className="print-variations-title">
                            {t('orders.selectedOptions') || 'Opções selecionadas'}:
                          </div>
                          <div className="print-variations-list">
                            {(item.selectedVariations || item.selectedOptions || []).map((option: any, index: number) => (
                              <div key={`${item.id}-${index}`} className="print-product-variation">
                                <span className="print-variation-name">{option.variationName}:</span>
                                <span className="print-option-name">{option.optionName}</span>
                                {option.quantity && option.quantity > 1 && (
                                  <span className="print-option-quantity"> x{option.quantity}</span>
                                )}
                                {option.price > 0 && (
                                  <span className="print-option-price">
                                    {' '}(+{formatCurrency(safeNumber(option.price) * (option.quantity || 1), currency)}
                                    {!option.isCustom && quantity > 1 && (
                                      <span className="print-multiplication-info"> x{quantity}</span>
                                    )})
                                  </span>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Observações do produto */}
                      {item.observation && (
                        <div className="print-product-observation">
                          <span style={{ color: storeColors.secondary }}>{t('orders.observationLabel') || "Obs"}:</span> {item.observation}
                        </div>
                      )}
                    </td>
                    <td className="print-text-center">
                      <span className="font-medium">{quantity}</span>
                    </td>
                    <td className="print-text-right">
                      <div className="print-price-breakdown">
                        {/* Preço original do produto */}
                        <div className="print-base-price">
                          {formatCurrency(basePrice, currency)}
                        </div>

                        {/* Valores das variações */}
                        {((item.selectedVariations && item.selectedVariations.length > 0) ||
                          (item.selectedOptions && item.selectedOptions.length > 0)) && (
                          <div className="print-variations-prices">
                            {(item.selectedVariations || item.selectedOptions || []).map((option: any, index: number) => {
                              if (option.price > 0) {
                                // Para variações customizadas, não multiplicar pela quantidade do produto
                                // Para variações normais, multiplicar pela quantidade do produto
                                const isCustom = option.isCustom || option.variationName?.toLowerCase() === 'outros' ||
                                                 option.optionName?.toLowerCase() === 'outros';
                                const variationQuantity = option.quantity || 1;
                                const displayPrice = isCustom ?
                                  (option.price * variationQuantity) :
                                  (option.price * variationQuantity * quantity);

                                return (
                                  <div key={index} className="print-variation-price">
                                    +{formatCurrency(displayPrice, currency)}
                                  </div>
                                );
                              }
                              return null;
                            })}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="print-text-right font-medium">
                      {formatCurrency(itemTotal, currency)}
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>

        {/* Order totals */}
        <div className="print-totals">
          <div className="print-section-title" style={{ color: storeColors.primary }}>
            <FileText className="h-5 w-5" />
            {t('orders.orderTotal') || "Total do Pedido"}
          </div>

          <div className="print-total-row">
            <div className="print-total-label" style={{ color: storeColors.primary }}>
              {t('orders.subtotal') || "Subtotal"}:
            </div>
            <div>
              {(() => {
                // Calcular o subtotal a partir dos itens usando a mesma lógica da tela de detalhes
                let calculatedSubtotal = 0;
                if (displayData.items && Array.isArray(displayData.items)) {
                  // Função para calcular o preço total de um item (preço base + variações)
                  const calculateItemTotal = (item: any): number => {
                    // Verificar se o item existe
                    if (!item) return 0;

                    // Obter a quantidade do item
                    const quantity = item.quantity || 1;

                    // Determinar o preço base
                    const baseUnitPrice = item.price || item.unitPrice || 0;
                    const basePrice = baseUnitPrice * quantity;

                    // Adicionar preço das variações
                    let variationsTotal = 0;

                    // Verificar se temos variações no formato selectedOptions
                    if (item.selectedOptions && Array.isArray(item.selectedOptions) && item.selectedOptions.length > 0) {
                      // Para cada variação, calcular o preço total
                      variationsTotal = item.selectedOptions.reduce(
                        (total: number, variation: any) => {
                          const variationPrice = parseFloat(variation.price) || 0;
                          const variationQuantity = variation.quantity || 1;

                          // Verificar se é uma variação do tipo "outros"
                          const isCustomVariation = variation.variationName?.toLowerCase() === 'outros' ||
                                                   variation.optionName?.toLowerCase() === 'outros';

                          // Para variações do tipo "outros", não multiplicar pela quantidade do produto
                          if (isCustomVariation) {
                            return total + (variationPrice * variationQuantity);
                          } else {
                            // Para variações normais, multiplicar pela quantidade do produto
                            return total + (variationPrice * variationQuantity * quantity);
                          }
                        },
                        0
                      );
                    }
                    // Verificar se temos variações no formato selectedVariations
                    else if (item.selectedVariations && Array.isArray(item.selectedVariations) && item.selectedVariations.length > 0) {
                      // Para cada variação, calcular o preço total
                      variationsTotal = item.selectedVariations.reduce(
                        (total: number, variation: any) => {
                          const variationPrice = parseFloat(variation.price) || 0;
                          const variationQuantity = variation.quantity || 1;

                          // Verificar se é uma variação do tipo "outros"
                          const isCustomVariation = variation.variationName?.toLowerCase() === 'outros' ||
                                                   variation.optionName?.toLowerCase() === 'outros';

                          // Para variações do tipo "outros", não multiplicar pela quantidade do produto
                          if (isCustomVariation) {
                            return total + (variationPrice * variationQuantity);
                          } else {
                            // Para variações normais, multiplicar pela quantidade do produto
                            return total + (variationPrice * variationQuantity * quantity);
                          }
                        },
                        0
                      );
                    }

                    // Log para depuração
                    console.log('Cálculo do valor total do item na preview:', {
                      nome: item.name || item.productName,
                      precoBase: baseUnitPrice,
                      quantidade: quantity,
                      precoBaseTotal: basePrice,
                      totalVariacoes: variationsTotal,
                      total: basePrice + variationsTotal
                    });

                    return basePrice + variationsTotal;
                  };

                  // Calcular o subtotal somando o valor total de cada item
                  calculatedSubtotal = displayData.items.reduce((total, item) => {
                    const itemTotal = calculateItemTotal(item);
                    return total + itemTotal;
                  }, 0);

                  // Arredondar para 2 casas decimais com precisão matemática
                  calculatedSubtotal = parseFloat(calculatedSubtotal.toFixed(2));
                }

                // Obter o subtotal armazenado
                const storedSubtotal = safeNumber(displayData.subtotal);

                // Comparar os subtotais
                if (Math.abs(calculatedSubtotal - storedSubtotal) > 0.01) {
                  console.log('Diferença detectada no subtotal:', {
                    storedSubtotal,
                    calculatedSubtotal,
                    difference: calculatedSubtotal - storedSubtotal,
                    items: displayData.items
                  });
                }

                // Usar o subtotal calculado para garantir consistência com a tela de detalhes
                return formatCurrency(calculatedSubtotal, currency);
              })()}
            </div>
          </div>

          {(displayData.receivingMethod === 'delivery' || safeNumber(displayData.deliveryFee) > 0) && (
            <div className="print-total-row">
              <div className="print-total-label" style={{ color: storeColors.primary }}>
                {t('orders.deliveryFee') || "Taxa de Entrega"}:
              </div>
              <div>{formatCurrency(safeNumber(displayData.deliveryFee), currency)}</div>
            </div>
          )}

          {(() => {
            // Calcular o subtotal a partir dos itens usando a mesma lógica da tela de detalhes
            let calculatedSubtotal = 0;
            if (displayData.items && Array.isArray(displayData.items)) {
              // Função para calcular o preço total de um item (preço base + variações)
              const calculateItemTotal = (item: any): number => {
                // Verificar se o item existe
                if (!item) return 0;

                // Obter a quantidade do item
                const quantity = item.quantity || 1;

                // Determinar o preço base
                const baseUnitPrice = item.price || item.unitPrice || 0;
                const basePrice = baseUnitPrice * quantity;

                // Adicionar preço das variações
                let variationsTotal = 0;

                // Verificar se temos variações no formato selectedOptions
                if (item.selectedOptions && Array.isArray(item.selectedOptions) && item.selectedOptions.length > 0) {
                  // Para cada variação, calcular o preço total
                  variationsTotal = item.selectedOptions.reduce(
                    (total: number, variation: any) => {
                      const variationPrice = parseFloat(variation.price) || 0;
                      const variationQuantity = variation.quantity || 1;

                      // Verificar se é uma variação do tipo "outros"
                      const isCustomVariation = variation.variationName?.toLowerCase() === 'outros' ||
                                               variation.optionName?.toLowerCase() === 'outros';

                      // Para variações do tipo "outros", não multiplicar pela quantidade do produto
                      if (isCustomVariation) {
                        return total + (variationPrice * variationQuantity);
                      } else {
                        // Para variações normais, multiplicar pela quantidade do produto
                        return total + (variationPrice * variationQuantity * quantity);
                      }
                    },
                    0
                  );
                }
                // Verificar se temos variações no formato selectedVariations
                else if (item.selectedVariations && Array.isArray(item.selectedVariations) && item.selectedVariations.length > 0) {
                  // Para cada variação, calcular o preço total
                  variationsTotal = item.selectedVariations.reduce(
                    (total: number, variation: any) => {
                      const variationPrice = parseFloat(variation.price) || 0;
                      const variationQuantity = variation.quantity || 1;

                      // Verificar se é uma variação do tipo "outros"
                      const isCustomVariation = variation.variationName?.toLowerCase() === 'outros' ||
                                               variation.optionName?.toLowerCase() === 'outros';

                      // Para variações do tipo "outros", não multiplicar pela quantidade do produto
                      if (isCustomVariation) {
                        return total + (variationPrice * variationQuantity);
                      } else {
                        // Para variações normais, multiplicar pela quantidade do produto
                        return total + (variationPrice * variationQuantity * quantity);
                      }
                    },
                    0
                  );
                }

                return basePrice + variationsTotal;
              };

              // Calcular o subtotal somando o valor total de cada item
              calculatedSubtotal = displayData.items.reduce((total, item) => {
                const itemTotal = calculateItemTotal(item);
                return total + itemTotal;
              }, 0);

              // Arredondar para 2 casas decimais com precisão matemática
              calculatedSubtotal = parseFloat(calculatedSubtotal.toFixed(2));
            }

            // Calcular desconto usando a mesma lógica da tela de detalhes
            let discountAmount = 0;
            const storedSubtotal = safeNumber(displayData.subtotal);

            if (displayData.discount_type === 'fixed') {
              // Desconto em valor fixo
              discountAmount = safeNumber(displayData.discount);
            } else if (displayData.discount_type === 'percentage') {
              // Desconto em percentual - usar a porcentagem original se disponível
              const percentageValue = displayData.original_percentage || safeNumber(displayData.discount);

              // Usar o subtotal calculado em vez do armazenado
              discountAmount = (percentageValue / 100) * calculatedSubtotal;

              // Arredondar para 2 casas decimais com precisão matemática
              discountAmount = parseFloat(discountAmount.toFixed(2));

              // Log para depuração
              console.log('Cálculo de desconto percentual na preview:', {
                calculatedSubtotal,
                storedSubtotal,
                percentageValue,
                calculoExplicito: `${percentageValue}% de ${calculatedSubtotal} = ${discountAmount}`,
                discountAmount
              });
            } else {
              // Fallback para o valor direto do desconto
              discountAmount = safeNumber(displayData.discount);
            }

            // Garantir que o desconto não seja maior que o subtotal
            discountAmount = Math.min(discountAmount, calculatedSubtotal);

            // Arredondar para 2 casas decimais com precisão matemática
            discountAmount = parseFloat(discountAmount.toFixed(2));

            // Comparar com o valor armazenado para depuração
            const storedDiscount = safeNumber(displayData.discount);
            if (Math.abs(discountAmount - storedDiscount) > 0.01 && displayData.discount_type === 'percentage') {
              console.log('Diferença detectada no desconto:', {
                calculatedSubtotal,
                storedSubtotal,
                discountType: displayData.discount_type,
                storedDiscount,
                calculatedDiscount: discountAmount,
                percentageValue: displayData.original_percentage,
                difference: discountAmount - storedDiscount
              });
            }

            return discountAmount > 0 ? (
              <>
                <div className="print-total-row print-discount">
                  <div className="print-total-label">
                    {displayData.discount_type === 'percentage' && displayData.original_percentage
                      ? (t('storefront.discountPercentage', { value: displayData.original_percentage }) || `Desconto (${displayData.original_percentage}%):`)
                      : (t('storefront.discountFixed') || "Desconto (Fixo):")}
                  </div>
                  <div>
                    -{formatCurrency(discountAmount, currency)}
                  </div>
                </div>
                {displayData.couponCode && (
                  <div className="print-coupon-code">
                    {t('storefront.couponCode') || "Código do cupom"}: {displayData.couponCode}
                  </div>
                )}
              </>
            ) : null;
          })()}

          <div className="print-grand-total" style={{
            backgroundColor: storeColors.primary,
            color: 'white'
          }}>
            <div className="print-total-label">
              {t('orders.total') || "Total"}:
            </div>
            <div>
              {(() => {
                // Calcular o subtotal a partir dos itens usando a mesma lógica da tela de detalhes
                let calculatedSubtotal = 0;
                if (displayData.items && Array.isArray(displayData.items)) {
                  // Função para calcular o preço total de um item (preço base + variações)
                  const calculateItemTotal = (item: any): number => {
                    // Verificar se o item existe
                    if (!item) return 0;

                    // Obter a quantidade do item
                    const quantity = item.quantity || 1;

                    // Determinar o preço base
                    const baseUnitPrice = item.price || item.unitPrice || 0;
                    const basePrice = baseUnitPrice * quantity;

                    // Adicionar preço das variações
                    let variationsTotal = 0;

                    // Verificar se temos variações no formato selectedOptions
                    if (item.selectedOptions && Array.isArray(item.selectedOptions) && item.selectedOptions.length > 0) {
                      // Para cada variação, calcular o preço total
                      variationsTotal = item.selectedOptions.reduce(
                        (total: number, variation: any) => {
                          const variationPrice = parseFloat(variation.price) || 0;
                          const variationQuantity = variation.quantity || 1;

                          // Verificar se é uma variação do tipo "outros"
                          const isCustomVariation = variation.variationName?.toLowerCase() === 'outros' ||
                                                   variation.optionName?.toLowerCase() === 'outros';

                          // Para variações do tipo "outros", não multiplicar pela quantidade do produto
                          if (isCustomVariation) {
                            return total + (variationPrice * variationQuantity);
                          } else {
                            // Para variações normais, multiplicar pela quantidade do produto
                            return total + (variationPrice * variationQuantity * quantity);
                          }
                        },
                        0
                      );
                    }
                    // Verificar se temos variações no formato selectedVariations
                    else if (item.selectedVariations && Array.isArray(item.selectedVariations) && item.selectedVariations.length > 0) {
                      // Para cada variação, calcular o preço total
                      variationsTotal = item.selectedVariations.reduce(
                        (total: number, variation: any) => {
                          const variationPrice = parseFloat(variation.price) || 0;
                          const variationQuantity = variation.quantity || 1;

                          // Verificar se é uma variação do tipo "outros"
                          const isCustomVariation = variation.variationName?.toLowerCase() === 'outros' ||
                                                   variation.optionName?.toLowerCase() === 'outros';

                          // Para variações do tipo "outros", não multiplicar pela quantidade do produto
                          if (isCustomVariation) {
                            return total + (variationPrice * variationQuantity);
                          } else {
                            // Para variações normais, multiplicar pela quantidade do produto
                            return total + (variationPrice * variationQuantity * quantity);
                          }
                        },
                        0
                      );
                    }

                    return basePrice + variationsTotal;
                  };

                  // Calcular o subtotal somando o valor total de cada item
                  calculatedSubtotal = displayData.items.reduce((total, item) => {
                    const itemTotal = calculateItemTotal(item);
                    return total + itemTotal;
                  }, 0);

                  // Arredondar para 2 casas decimais com precisão matemática
                  calculatedSubtotal = parseFloat(calculatedSubtotal.toFixed(2));
                }

                let discountAmount = 0;

                // Calcular o valor do desconto com base no tipo
                if (displayData.discount_type === 'fixed') {
                  // Desconto em valor fixo
                  discountAmount = safeNumber(displayData.discount);
                } else if (displayData.discount_type === 'percentage') {
                  // Desconto em percentual - usar a porcentagem original se disponível
                  const percentageValue = displayData.original_percentage || safeNumber(displayData.discount);
                  discountAmount = (percentageValue / 100) * calculatedSubtotal;
                  // Arredondar para 2 casas decimais com precisão matemática
                  discountAmount = parseFloat(discountAmount.toFixed(2));
                } else {
                  // Fallback para o valor direto do desconto
                  discountAmount = safeNumber(displayData.discount);
                }

                // Garantir que o desconto não seja maior que o subtotal
                discountAmount = Math.min(discountAmount, calculatedSubtotal);

                // Calcular o total: subtotal - desconto + taxa de entrega
                const deliveryFee = safeNumber(displayData.deliveryFee);
                const calculatedTotal = Math.max(0, calculatedSubtotal - discountAmount + deliveryFee);

                // Arredondar para 2 casas decimais com precisão matemática
                const roundedTotal = parseFloat(calculatedTotal.toFixed(2));

                // Comparar com o valor armazenado para depuração
                const storedTotal = safeNumber(displayData.total);
                if (Math.abs(roundedTotal - storedTotal) > 0.01) {
                  console.log('Diferença detectada no total:', {
                    calculatedSubtotal,
                    storedSubtotal: safeNumber(displayData.subtotal),
                    discountType: displayData.discount_type,
                    discountValue: displayData.discount,
                    percentageValue: displayData.original_percentage,
                    discountAmount,
                    deliveryFee,
                    calculatedTotal,
                    roundedTotal,
                    storedTotal,
                    difference: roundedTotal - storedTotal
                  });
                }

                // Usar o valor recalculado para garantir consistência
                return formatCurrency(roundedTotal, currency);
              })()}
            </div>
          </div>
        </div>

        {/* Payment Method */}
        <div className="print-section mb-6">
          <div className="print-section-title" style={{ color: storeColors.primary }}>
            <CreditCard className="h-5 w-5" />
            {t('orders.paymentMethod') || "Método de Pagamento"}
          </div>
          <div className="flex items-center gap-2 mt-2">
            <CreditCard className="h-4 w-4" style={{ color: storeColors.secondary }} />
            <span>
              {displayData.paymentMethodName || (displayData.paymentMethod ? getFormattedPaymentMethod(displayData.paymentMethod) : t('orders.notSpecified') || "Não especificado")}
            </span>
          </div>
        </div>

        {/* Order notes if available */}
        {displayData.notes && (
          <div className="print-notes">
            <div className="print-section-title" style={{ color: storeColors.primary }}>
              <FileText className="h-5 w-5" />
              {t('orders.notes') || "Observações"}
            </div>
            <p>{displayData.notes}</p>
          </div>
        )}

        {/* Rodapé com agradecimento */}
          <div className="print-footer">
            <div className="print-generation-info">
              <div className="text-xs" style={{ color: storeColors.secondary }}>
                <p className="mb-1">{t('orders.createdAt') || "Data de criação"}: {formatDate(displayData.createdAt)}</p>
                <p>{t('orders.generatedAt')}: {formatDate(new Date())}
                {activeRevision && (
                  <span className="print-revision-info">
                    | <strong>{t('orders.revision') || "Revisão"}:</strong> #{activeRevision.revisionNumber}
                  </span>
                )}</p>
              </div>
            </div>

          <p className="print-thank-you" style={{ color: storeColors.primary }}>
            {t('orders.thankYou') || "Obrigado pela preferência!"}
          </p>

          <div className="print-store-info">
            <p className="print-store-name">
              {store?.name || 'Doce Menu'}
            </p>

            <div className="print-social-links">
              {store?.whatsapp && (
                <div className="print-social-item">
                  <Phone className="h-4 w-4" style={{ color: storeColors.primary }} />
                  <span>{store.countryCode || '+55'} {store.whatsapp}</span>
                </div>
              )}

              {store?.instagram && (
                <div className="print-social-item">
                  <Instagram className="h-4 w-4" style={{ color: storeColors.primary }} />
                  <span>@{store.instagram.replace('@', '')}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      </div> {/* Fechamento do print-preview-container */}
    </AdminLayout>
  );
}