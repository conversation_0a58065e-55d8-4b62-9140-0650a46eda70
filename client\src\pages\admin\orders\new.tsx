import { useState, useEffect } from 'react';
import { useLocation } from 'wouter';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useTranslation } from '@/hooks/useTranslation';
import { useStore } from '@/context/StoreContext';
import { useAdminOrder } from '@/context/AdminOrderContext';
import { useToast } from '@/hooks/use-toast';
import AdminLayout from '@/components/admin/AdminLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowLeft, Plus, User, Calendar, Clock, CreditCard, Truck, MapPin, ShoppingBag, Trash2, Pencil } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from '@/components/ui/breadcrumb';

export default function NewOrderPage() {
  const { t } = useTranslation();
  const [, navigate] = useLocation();
  const { store } = useStore();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState('order_details');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Obter o contexto do pedido em criação
  const {
    items,
    customer,
    receivingMethod,
    receivingDate,
    receivingTime,
    paymentMethod,
    deliveryAddress,
    notes,
    deliveryFee,
    subtotal,
    total,
    setCustomer,
    setReceivingMethod,
    setReceivingDate,
    setReceivingTime,
    setPaymentMethod,
    setDeliveryAddress,
    setNotes,
    setDeliveryFee,
    resetOrder,
    removeItem
  } = useAdminOrder();

  // Verificar se o usuário está autenticado e tem uma loja
  useEffect(() => {
    if (!store) {
      navigate('/admin/settings');
    }
  }, [store, navigate]);

  // Limpar o endereço de entrega quando o método for alterado para pickup
  useEffect(() => {
    if (receivingMethod === 'pickup') {
      setDeliveryAddress(null);
    }
  }, [receivingMethod, setDeliveryAddress]);

  // Função para navegar para a seleção de cliente
  const handleSelectCustomer = () => {
    // Salvar o estado atual no localStorage para recuperar depois
    localStorage.setItem('adminOrderCreation', 'true');
    navigate('/admin/orders/select-customer');
  };

  // Função para calcular o preço total de um item (preço base + variações)
  const calculateItemTotal = (item: any) => {
    // Preço base × quantidade
    let basePrice = item.price * item.quantity;

    // Adicionar preço das variações
    let variationsTotal = 0;
    if (item.selectedVariations && item.selectedVariations.length > 0) {
      variationsTotal = item.selectedVariations.reduce(
        (total: number, variation: any) => total + (parseFloat(variation.price) || 0),
        0
      ) * item.quantity;
    }

    const itemTotal = basePrice + variationsTotal;

    console.log('Cálculo do item:', {
      nome: item.name,
      precoBase: item.price,
      quantidade: item.quantity,
      precoBaseTotal: basePrice,
      variacoes: item.selectedVariations,
      totalVariacoes: variationsTotal,
      total: itemTotal
    });

    return itemTotal;
  };

  // Função para obter o nome do método de pagamento
  const getPaymentMethodName = (paymentMethodId: string): string => {
    // Para métodos personalizados
    if (paymentMethodId && paymentMethodId.startsWith('custom_')) {
      const customIndex = parseInt(paymentMethodId.replace('custom_', ''), 10);
      if (!isNaN(customIndex) &&
          store?.paymentMethods?.customMethods &&
          Array.isArray(store.paymentMethods.customMethods) &&
          customIndex < store.paymentMethods.customMethods.length) {
        return store.paymentMethods.customMethods[customIndex];
      }
    }

    // Para métodos padrão
    switch(paymentMethodId) {
      case 'cash':
        return t('storefront.cash') || 'Dinheiro';
      case 'creditCard':
        return t('storefront.creditCard') || 'Cartão de Crédito';
      case 'debitCard':
        return t('storefront.debitCard') || 'Cartão de Débito';
      case 'pix':
        return t('storefront.pix') || 'PIX';
      case 'bankTransfer':
        return t('storefront.bankTransfer') || 'Transferência Bancária';
      default:
        return paymentMethodId;
    }
  };

  // Função para navegar para a adição de produtos
  const handleAddProducts = () => {
    if (!customer) {
      toast({
        title: t('orders.selectCustomerFirst') || 'Selecione um cliente primeiro',
        description: t('orders.pleaseSelectCustomerBeforeProducts') || 'Por favor, selecione um cliente antes de adicionar produtos',
        variant: 'destructive',
      });
      return;
    }

    // Navegar para a página de seleção de produtos
    navigate('/admin/orders/select-products');
  };

  // Função para editar um item do pedido
  const handleEditItem = (item: any) => {
    // Verificar se é um produto personalizado (ID negativo)
    const isCustomProduct = typeof item.productId === 'number' && item.productId < 0;

    console.log('Editando item do pedido:', {
      nome: item.name,
      productId: item.productId,
      isCustomProduct,
      precoBase: item.price,
      quantidade: item.quantity
    });

    // Salvar o ID do produto no localStorage para recuperar na página de detalhes
    localStorage.setItem('adminOrderCreationProduct', 'true');
    localStorage.setItem('currentProductId', item.productId.toString());
    localStorage.setItem('editingOrderItem', 'true');
    localStorage.setItem('editingOrderItemId', item.id.toString());

    // Adicionar flag para indicar se é um produto personalizado
    localStorage.setItem('isCustomProduct', isCustomProduct ? 'true' : 'false');

    if (isCustomProduct) {
      // Se for um produto personalizado, navegar para a página de produto personalizado
      // com os dados do produto atual
      navigate('/admin/orders/custom-product');
    } else {
      // Se for um produto do banco, navegar para a página de detalhes do produto
      navigate(`/admin/orders/product-details/${item.productId}`);
    }
  };

  // Mutation para criar o pedido
  const createOrderMutation = useMutation({
    mutationFn: async (orderData: any) => {
      if (!store) {
        throw new Error('Loja não encontrada');
      }

      // Criar o pedido usando a API
      return apiRequest('POST', `/api/admin/orders`, orderData);
    },
    onSuccess: (data) => {
      // Limpar o pedido após a criação
      resetOrder();

      // Invalidar as consultas para atualizar a lista de pedidos
      queryClient.invalidateQueries({ queryKey: ['/api/orders'] });

      // Navegar para a lista de pedidos
      navigate('/admin/orders');

      toast({
        title: t('orders.orderCreated') || 'Pedido criado',
        description: t('orders.orderCreatedSuccessfully') || 'O pedido foi criado com sucesso',
        variant: 'default',
      });
    },
    onError: (error) => {
      console.error('Erro ao criar pedido:', error);
      toast({
        title: t('common.error') || 'Erro',
        description: error instanceof Error ? error.message : String(error),
        variant: 'destructive',
      });
    }
  });

  // Função para criar o pedido
  const handleCreateOrder = async () => {
    // Log para depurar o cálculo do subtotal
    console.log('Resumo do pedido:', {
      itens: items.map(item => ({
        nome: item.name,
        precoBase: item.price,
        quantidade: item.quantity,
        variacoes: item.selectedVariations,
        totalItem: calculateItemTotal(item)
      })),
      subtotal,
      taxaEntrega: deliveryFee,
      total
    });

    if (!customer) {
      toast({
        title: t('orders.selectCustomerFirst') || 'Selecione um cliente primeiro',
        description: t('orders.pleaseSelectCustomerBeforeCreating') || 'Por favor, selecione um cliente antes de criar o pedido',
        variant: 'destructive',
      });
      return;
    }

    if (items.length === 0) {
      toast({
        title: t('orders.addProductsFirst') || 'Adicione produtos primeiro',
        description: t('orders.pleaseAddProductsBeforeCreating') || 'Por favor, adicione produtos antes de criar o pedido',
        variant: 'destructive',
      });
      return;
    }

    if (!paymentMethod) {
      toast({
        title: t('orders.selectPaymentMethodFirst') || 'Selecione um método de pagamento',
        description: t('orders.pleaseSelectPaymentMethodBeforeCreating') || 'Por favor, selecione um método de pagamento antes de criar o pedido',
        variant: 'destructive',
      });
      return;
    }

    if (!receivingMethod) {
      toast({
        title: t('orders.selectReceivingMethodFirst') || 'Selecione um método de recebimento',
        description: t('orders.pleaseSelectReceivingMethodBeforeCreating') || 'Por favor, selecione um método de recebimento antes de criar o pedido',
        variant: 'destructive',
      });
      return;
    }

    // Validar endereço de entrega quando o método for delivery
    if (receivingMethod === 'delivery') {
      if (!deliveryAddress || !deliveryAddress.street || !deliveryAddress.number || !deliveryAddress.neighborhood || !deliveryAddress.cityState) {
        toast({
          title: t('orders.deliveryAddressRequired') || 'Endereço de entrega obrigatório',
          description: t('orders.pleaseCompleteDeliveryAddress') || 'Por favor, preencha os campos obrigatórios do endereço de entrega',
          variant: 'destructive',
        });
        return;
      }
    }

    // Preparar os dados do pedido
    const orderData = {
      customer: {
        id: customer.id,
        name: customer.name,
        email: customer.email,
        phone: customer.phone
      },
      items: items.map(item => {
        // Verificar se é um produto personalizado (ID negativo)
        const isCustomProduct = typeof item.productId === 'number' && item.productId < 0;

        console.log('Preparando item para o pedido:', {
          nome: item.name,
          productId: item.productId,
          isCustomProduct,
          precoBase: item.price,
          quantidade: item.quantity
        });

        // Se for um produto personalizado, usamos um ID especial para o backend
        // O backend deve tratar esse ID especial como um produto personalizado
        return {
          productId: isCustomProduct ? -1 : item.productId, // Usar -1 para produtos personalizados
          quantity: item.quantity,
          price: item.price,
          name: item.name, // Enviar o nome para produtos personalizados
          isCustomProduct: isCustomProduct, // Flag para indicar produto personalizado
          selectedVariations: item.selectedVariations || [],
          observation: item.observation || undefined
        };
      }),
      receivingMethod,
      receivingDate: receivingDate ? receivingDate.toISOString() : new Date().toISOString(),
      receivingTime,
      paymentMethod,
      paymentMethodName: getPaymentMethodName(paymentMethod), // Adicionar o nome do método de pagamento
      deliveryAddress,
      notes,
      subtotal,
      deliveryFee,
      total
    };

    // Criar o pedido
    setIsSubmitting(true);
    try {
      await createOrderMutation.mutateAsync(orderData);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <AdminLayout title={t('orders.newOrder') || 'Novo Pedido'}>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <Breadcrumb className="mb-2">
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/admin">{t('common.dashboard') || "Dashboard"}</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbLink href="/admin/orders">{t('common.orders') || "Pedidos"}</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbLink>{t('orders.newOrder') || "Novo Pedido"}</BreadcrumbLink>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
          <Button variant="outline" onClick={() => navigate('/admin/orders')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t('common.goBack') || "Voltar"}
          </Button>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-2 mb-6">
            <TabsTrigger value="order_details">{t('orders.orderDetails') || "Detalhes do Pedido"}</TabsTrigger>
            <TabsTrigger value="checkout">{t('orders.deliveryAndPayment') || "Entrega e Pagamento"}</TabsTrigger>
          </TabsList>

          <TabsContent value="order_details" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>{t('orders.orderDetails') || "Detalhes do Pedido"}</CardTitle>
                <CardDescription>
                  {t('orders.orderDetailsDescription') || "Selecione o cliente e adicione produtos ao pedido"}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Seção do cliente */}
                <div>
                  <h3 className="text-lg font-medium mb-4">{t('dashboard.customer') || "Cliente"}</h3>
                  {customer ? (
                    <div className="bg-muted p-4 rounded-lg mb-4">
                      <div className="flex justify-between items-center">
                        <div>
                          <h3 className="font-medium">{customer.name}</h3>
                          {customer.email && <p className="text-sm text-muted-foreground">{customer.email}</p>}
                          {customer.phone && <p className="text-sm text-muted-foreground">{customer.phone}</p>}
                        </div>
                        <Button variant="outline" size="sm" onClick={handleSelectCustomer}>
                          {t('common.change') || "Alterar"}
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center py-8 mb-4 border rounded-lg">
                      <User className="h-12 w-12 text-muted-foreground mb-4" />
                      <h3 className="text-lg font-medium mb-2">{t('orders.noCustomerSelected') || "Nenhum cliente selecionado"}</h3>
                      <p className="text-sm text-muted-foreground mb-4 text-center">
                        {t('orders.selectCustomerToCreate') || "Selecione um cliente para criar o pedido"}
                      </p>
                      <Button onClick={handleSelectCustomer}>
                        <User className="h-4 w-4 mr-2" />
                        {t('orders.selectCustomerButton') || "Selecionar Cliente"}
                      </Button>
                    </div>
                  )}
                </div>

                {/* Seção de produtos */}
                <div>
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-medium">{t('common.products') || "Produtos"}</h3>
                    <Button variant="outline" size="sm" onClick={handleAddProducts} disabled={!customer}>
                      <Plus className="h-4 w-4 mr-2" />
                      {t('orders.addProducts') || "Adicionar Produtos"}
                    </Button>
                  </div>

                  {items.length > 0 ? (
                    <div className="space-y-4">
                      {items.map((item) => (
                        <div key={item.id} className="flex flex-col sm:flex-row sm:items-center justify-between border-b pb-3">
                          <div className="flex items-center">
                            {item.image ? (
                              <div className="w-12 h-12 rounded-md overflow-hidden mr-3 flex-shrink-0">
                                <img
                                  src={item.image}
                                  alt={item.name}
                                  className="w-full h-full object-cover"
                                />
                              </div>
                            ) : (
                              <div className="w-12 h-12 rounded-md bg-muted flex items-center justify-center mr-3 flex-shrink-0">
                                <ShoppingBag className="h-6 w-6 text-muted-foreground" />
                              </div>
                            )}
                            <div>
                              <p className="font-medium">{item.name}</p>
                              <div className="flex items-center text-sm text-muted-foreground">
                                <span>{formatCurrency(item.price)}</span>
                                <span className="mx-1">×</span>
                                <span>{item.quantity}</span>
                              </div>
                              {item.selectedVariations && item.selectedVariations.length > 0 && (
                                <div className="mt-1 text-xs text-muted-foreground">
                                  {item.selectedVariations.map((variation, index) => (
                                    <div key={index} className="flex items-center">
                                      <span>{variation.variationName}: {variation.optionName}</span>
                                      {variation.price > 0 && (
                                        <span className="ml-1">
                                          (+{formatCurrency(variation.price)})
                                        </span>
                                      )}
                                    </div>
                                  ))}
                                </div>
                              )}
                            </div>
                          </div>

                          <div className="flex items-center justify-between mt-2 sm:mt-0">
                            <div className="flex flex-col items-end mr-4">
                              <span className="text-sm font-medium">{t('orders.subtotal') || "Subtotal"}:</span>
                              <span className="font-medium text-primary">{formatCurrency(calculateItemTotal(item))}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleEditItem(item)}
                                className="text-primary hover:text-primary"
                                title={t('common.edit') || "Editar"}
                              >
                                <Pencil className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => removeItem(item.id)}
                                className="text-destructive hover:text-destructive"
                                title={t('common.delete') || "Excluir"}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}
                      <div className="space-y-3 pt-3 border-t">
                        <div className="flex justify-between">
                          <span className="font-medium">{t('orders.subtotal') || "Subtotal"}</span>
                          <span className="font-medium">{formatCurrency(subtotal)}</span>
                        </div>

                        <div className="flex justify-between items-center">
                          <span className="font-medium">{t('orders.deliveryFee') || "Taxa de entrega"}</span>
                          <div className="flex items-center gap-2">
                            <Input
                              type="number"
                              min="0"
                              step="0.01"
                              value={deliveryFee}
                              onChange={(e) => setDeliveryFee(parseFloat(e.target.value) || 0)}
                              className="w-24 h-8 text-right"
                            />
                            <span className="font-medium">{formatCurrency(deliveryFee)}</span>
                          </div>
                        </div>

                        <div className="flex justify-between pt-2 border-t">
                          <span className="font-bold">{t('orders.total') || "Total"}</span>
                          <span className="font-bold">{formatCurrency(total)}</span>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center py-8 border rounded-lg">
                      <ShoppingBag className="h-12 w-12 text-muted-foreground mb-4" />
                      <h3 className="text-lg font-medium mb-2">{t('orders.noProductsAdded') || "Nenhum produto adicionado"}</h3>
                      <p className="text-sm text-muted-foreground mb-4 text-center">
                        {t('orders.addProductsToOrder') || "Adicione produtos para criar o pedido"}
                      </p>
                      <Button onClick={handleAddProducts} disabled={!customer}>
                        <Plus className="h-4 w-4 mr-2" />
                        {t('orders.addProducts') || "Adicionar Produtos"}
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={() => navigate('/admin/orders')}>
                  {t('common.cancel') || "Cancelar"}
                </Button>
                <Button onClick={() => setActiveTab('checkout')} disabled={!customer || items.length === 0}>
                  {t('common.next') || "Próximo"}
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="checkout" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>{t('orders.deliveryAndPayment') || "Entrega e Pagamento"}</CardTitle>
                <CardDescription>
                  {t('orders.deliveryAndPaymentDescription') || "Configure as opções de entrega e pagamento"}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">


                {/* Informações de entrega e pagamento */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-lg font-medium mb-2">{t('orders.deliveryInfo') || "Informações de Entrega"}</h3>
                    <div className="space-y-3">
                      <div>
                        <Label htmlFor="receivingMethod">{t('orders.receivingMethod') || "Método de Recebimento"}</Label>
                        <Select
                          value={receivingMethod}
                          onValueChange={setReceivingMethod}
                        >
                          <SelectTrigger id="receivingMethod">
                            <SelectValue placeholder={t('orders.selectReceivingMethod') || "Selecione o método"} />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="delivery">{t('orders.delivery') || "Entrega"}</SelectItem>
                            <SelectItem value="pickup">{t('orders.pickup') || "Retirada"}</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label htmlFor="receivingDate">{t('orders.receivingDate') || "Data de Recebimento"}</Label>
                        <Input
                          id="receivingDate"
                          type="date"
                          value={receivingDate ? new Date(receivingDate).toISOString().split('T')[0] : ''}
                          onChange={(e) => setReceivingDate(e.target.value ? new Date(e.target.value) : null)}
                        />
                      </div>

                      <div>
                        <Label htmlFor="receivingTime">{t('orders.receivingTime') || "Horário de Recebimento"}</Label>
                        <Input
                          id="receivingTime"
                          type="time"
                          value={receivingTime}
                          onChange={(e) => setReceivingTime(e.target.value)}
                        />
                      </div>

                      {/* Campos de endereço de entrega (apenas quando o método for delivery) */}
                      {receivingMethod === 'delivery' && (
                        <div className="space-y-3 mt-4 border-t pt-4">
                          <h4 className="font-medium">{t('orders.deliveryAddress') || "Endereço de Entrega"}</h4>
                          <p className="text-xs text-muted-foreground mb-3">
                            {t('orders.requiredFieldsNote') || "Campos com * são obrigatórios"}
                          </p>

                          <div>
                            <Label htmlFor="street" className="flex items-center">
                              {t('orders.street') || "Rua"} <span className="text-destructive ml-1">*</span>
                            </Label>
                            <Input
                              id="street"
                              value={deliveryAddress?.street || ''}
                              onChange={(e) => setDeliveryAddress({
                                ...deliveryAddress || {},
                                street: e.target.value
                              })}
                              placeholder={t('orders.streetPlaceholder') || "Rua, Avenida, etc."}
                            />
                          </div>

                          <div className="grid grid-cols-2 gap-3">
                            <div>
                              <Label htmlFor="number" className="flex items-center">
                                {t('orders.number') || "Número"} <span className="text-destructive ml-1">*</span>
                              </Label>
                              <Input
                                id="number"
                                value={deliveryAddress?.number || ''}
                                onChange={(e) => setDeliveryAddress({
                                  ...deliveryAddress || {},
                                  number: e.target.value
                                })}
                                placeholder={t('orders.numberPlaceholder') || "Número"}
                              />
                            </div>
                            <div>
                              <Label htmlFor="complement">{t('orders.complement') || "Complemento"}</Label>
                              <Input
                                id="complement"
                                value={deliveryAddress?.complement || ''}
                                onChange={(e) => setDeliveryAddress({
                                  ...deliveryAddress || {},
                                  complement: e.target.value
                                })}
                                placeholder={t('orders.complementPlaceholder') || "Apto, Bloco, etc."}
                              />
                            </div>
                          </div>

                          <div>
                            <Label htmlFor="neighborhood" className="flex items-center">
                              {t('orders.neighborhood') || "Bairro"} <span className="text-destructive ml-1">*</span>
                            </Label>
                            <Input
                              id="neighborhood"
                              value={deliveryAddress?.neighborhood || ''}
                              onChange={(e) => setDeliveryAddress({
                                ...deliveryAddress || {},
                                neighborhood: e.target.value
                              })}
                              placeholder={t('orders.neighborhoodPlaceholder') || "Bairro"}
                            />
                          </div>

                          <div>
                            <Label htmlFor="cityState" className="flex items-center">
                              {t('orders.cityState') || "Cidade/Estado"} <span className="text-destructive ml-1">*</span>
                            </Label>
                            <Input
                              id="cityState"
                              value={deliveryAddress?.cityState || ''}
                              onChange={(e) => setDeliveryAddress({
                                ...deliveryAddress || {},
                                cityState: e.target.value
                              })}
                              placeholder={t('orders.cityStatePlaceholder') || "Cidade/Estado"}
                            />
                          </div>

                          <div>
                            <Label htmlFor="reference">{t('orders.reference') || "Ponto de Referência"}</Label>
                            <Input
                              id="reference"
                              value={deliveryAddress?.reference || ''}
                              onChange={(e) => setDeliveryAddress({
                                ...deliveryAddress || {},
                                reference: e.target.value
                              })}
                              placeholder={t('orders.referencePlaceholder') || "Próximo a..."}
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium mb-2">{t('orders.paymentInfo') || "Informações de Pagamento"}</h3>
                    <div className="space-y-3">
                      <div>
                        <Label htmlFor="paymentMethod">{t('orders.paymentMethod') || "Método de Pagamento"}</Label>
                        <Select
                          value={paymentMethod}
                          onValueChange={setPaymentMethod}
                        >
                          <SelectTrigger id="paymentMethod">
                            <SelectValue placeholder={t('orders.selectPaymentMethod') || "Selecione o método"} />
                          </SelectTrigger>
                          <SelectContent>
                            {/* Métodos padrão */}
                            {store?.paymentMethods?.cash && (
                              <SelectItem value="cash">{t('storefront.cash') || "Dinheiro"}</SelectItem>
                            )}
                            {store?.paymentMethods?.creditCard && (
                              <SelectItem value="creditCard">{t('storefront.creditCard') || "Cartão de Crédito"}</SelectItem>
                            )}
                            {store?.paymentMethods?.debitCard && (
                              <SelectItem value="debitCard">{t('storefront.debitCard') || "Cartão de Débito"}</SelectItem>
                            )}
                            {store?.paymentMethods?.pix && (
                              <SelectItem value="pix">{t('storefront.pix') || "PIX"}</SelectItem>
                            )}
                            {store?.paymentMethods?.bankTransfer && (
                              <SelectItem value="bankTransfer">{t('storefront.bankTransfer') || "Transferência Bancária"}</SelectItem>
                            )}

                            {/* Métodos personalizados */}
                            {store?.paymentMethods?.customMethods?.map((method, index) => (
                              <SelectItem key={index} value={`custom_${index}`}>
                                {method}
                              </SelectItem>
                            ))}

                            {/* Fallback caso não haja métodos configurados */}
                            {(!store?.paymentMethods ||
                              (!store.paymentMethods.cash &&
                               !store.paymentMethods.creditCard &&
                               !store.paymentMethods.debitCard &&
                               !store.paymentMethods.pix &&
                               !store.paymentMethods.bankTransfer &&
                               (!store.paymentMethods.customMethods || store.paymentMethods.customMethods.length === 0)
                              )) && (
                              <>
                                <SelectItem value="cash">{t('storefront.cash') || "Dinheiro"}</SelectItem>
                                <SelectItem value="creditCard">{t('storefront.creditCard') || "Cartão de Crédito"}</SelectItem>
                                <SelectItem value="debitCard">{t('storefront.debitCard') || "Cartão de Débito"}</SelectItem>
                                <SelectItem value="pix">{t('storefront.pix') || "PIX"}</SelectItem>
                              </>
                            )}
                          </SelectContent>
                        </Select>
                      </div>



                      <div>
                        <Label htmlFor="notes">{t('orders.notes') || "Observações"}</Label>
                        <Textarea
                          id="notes"
                          value={notes}
                          onChange={(e) => setNotes(e.target.value)}
                          placeholder={t('orders.notesPlaceholder') || "Observações sobre o pedido"}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" onClick={() => setActiveTab('order_details')}>
                  {t('common.back') || "Voltar"}
                </Button>
                <Button
                  onClick={handleCreateOrder}
                  disabled={isSubmitting || !customer || items.length === 0 || !paymentMethod || !receivingMethod}
                >
                  {isSubmitting ? (t('common.creating') || "Criando...") : (t('orders.createOrder') || "Criar Pedido")}
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
}
