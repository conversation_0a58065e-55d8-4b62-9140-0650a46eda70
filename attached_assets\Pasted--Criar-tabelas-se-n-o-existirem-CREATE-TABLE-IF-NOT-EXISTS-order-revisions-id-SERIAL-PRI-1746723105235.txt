-- <PERSON><PERSON><PERSON> tabelas se não existirem
CREATE TABLE IF NOT EXISTS "order_revisions" (
  "id" SERIAL PRIMARY KEY,
  "order_id" INTEGER NOT NULL REFERENCES "orders"("id") ON DELETE CASCADE,
  "revision_number" INTEGER NOT NULL,
  "status" VARCHAR(50) NOT NULL DEFAULT 'pending',
  "receiving_method" VARCHAR(50) NOT NULL,
  "receiving_date" TIMESTAMP NOT NULL,
  "receiving_time" VARCHAR(50),
  "delivery_address" JSONB,
  "payment_method" VARCHAR(100) NOT NULL,
  "subtotal" REAL NOT NULL,
  "delivery_fee" REAL DEFAULT 0,
  "total" REAL NOT NULL,
  "notes" TEXT,
  "created_at" TIMESTAMP NOT NULL DEFAULT NOW(),
  "created_by" INTEGER, 
  "is_current" BOOLEAN NOT NULL DEFAULT FALSE,
  UNIQUE(order_id, revision_number)
);

CREATE TABLE IF NOT EXISTS "order_revision_items" (
  "id" SERIAL PRIMARY KEY,
  "revision_id" INTEGER NOT NULL REFERENCES "order_revisions"("id") ON DELETE CASCADE,
  "product_id" INTEGER REFERENCES "products"("id"),
  "product_name" VARCHAR(255) NOT NULL,
  "product_description" TEXT,
  "product_image" TEXT,
  "quantity" INTEGER NOT NULL,
  "unit_price" REAL NOT NULL,
  "subtotal" REAL NOT NULL,
  "selected_variations" JSONB DEFAULT '[]',
  "observation" TEXT
);

-- Criar índices se não existirem
CREATE INDEX IF NOT EXISTS idx_order_revisions_order_id ON order_revisions(order_id);
CREATE INDEX IF NOT EXISTS idx_order_revisions_is_current ON order_revisions(is_current);
CREATE INDEX IF NOT EXISTS idx_order_revision_items_revision_id ON order_revision_items(revision_id);

-- Criar trigger para garantir apenas uma revisão atual por pedido
CREATE OR REPLACE FUNCTION ensure_single_current_revision()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.is_current = TRUE THEN
    -- Set all other revisions for this order to not current
    UPDATE order_revisions
    SET is_current = FALSE
    WHERE order_id = NEW.order_id AND id != NEW.id;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Criar o trigger se não existir
DROP TRIGGER IF EXISTS enforce_single_current_revision ON order_revisions;
CREATE TRIGGER enforce_single_current_revision
BEFORE INSERT OR UPDATE ON order_revisions
FOR EACH ROW
EXECUTE FUNCTION ensure_single_current_revision();

-- Criar ou substituir a função para criar uma nova revisão
CREATE OR REPLACE FUNCTION create_order_revision(order_id_param INTEGER)
RETURNS INTEGER AS $$
DECLARE
  new_revision_id INTEGER;
  new_revision_number INTEGER;
  order_record RECORD;
BEGIN
  -- Get the next revision number for this order
  SELECT COALESCE(MAX(revision_number), 0) + 1 INTO new_revision_number
  FROM order_revisions
  WHERE order_id = order_id_param;
  
  -- Get the order data
  SELECT * INTO order_record
  FROM orders
  WHERE id = order_id_param;
  
  -- Create the new revision
  INSERT INTO order_revisions (
    order_id,
    revision_number,
    status,
    receiving_method,
    receiving_date,
    receiving_time,
    delivery_address,
    payment_method,
    subtotal,
    delivery_fee,
    total,
    notes,
    is_current
  ) VALUES (
    order_id_param,
    new_revision_number,
    order_record.status,
    order_record.receiving_method,
    order_record.receiving_date,
    order_record.receiving_time,
    order_record.delivery_address,
    order_record.payment_method,
    order_record.subtotal,
    order_record.delivery_fee,
    order_record.total,
    order_record.notes,
    FALSE -- New revision is not current by default
  ) RETURNING id INTO new_revision_id;
  
  -- Copy order items to the new revision with complete product information
  INSERT INTO order_revision_items (
    revision_id,
    product_id,
    product_name,
    product_description,
    product_image,
    quantity,
    unit_price,
    subtotal,
    selected_variations,
    observation
  )
  SELECT
    new_revision_id,
    oi.product_id,
    p.name,
    p.description,
    (CASE WHEN p.images IS NOT NULL AND jsonb_array_length(p.images) > 0 
          THEN jsonb_extract_path_text(p.images, '0')
          ELSE NULL
     END),
    oi.quantity,
    oi.price,
    (oi.quantity * oi.price),
    oi.selected_options,
    oi.observation
  FROM order_items oi
  LEFT JOIN products p ON oi.product_id = p.id
  WHERE oi.order_id = order_id_param;
  
  RETURN new_revision_id;
END;
$$ LANGUAGE plpgsql;