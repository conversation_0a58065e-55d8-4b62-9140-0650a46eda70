import { Request, Response } from 'express';
import { supabase } from '../db';
import { storage } from '../storage';
import { calculateDiscount, calculateSubtotalFromItems, calculateTotal } from '../utils/discount-helpers';
import { recalculateAndUpdateRevision } from '../utils/revision-calculations';

// Atualizar o desconto de uma revisão de pedido
export async function updateRevisionDiscount(req: any, res: Response) {
  try {
    const revisionId = parseInt(req.params.revisionId);

    // Validar ID da revisão
    if (isNaN(revisionId) || revisionId <= 0) {
      return res.status(400).json({ message: "ID de revisão inválido" });
    }

    console.log('Atualizando desconto para revisão ID:', revisionId);
    console.log('Request body:', req.body);

    const { discount, discountType } = req.body;
    console.log('Dados extraídos:', {
      discount,
      discountType,
      tipoDiscount: typeof discount,
      tipoDiscountType: typeof discountType
    });

    // Validações de entrada mais robustas
    if (discount === undefined || discount === null) {
      return res.status(400).json({ message: "Valor do desconto é obrigatório" });
    }

    if (!discountType || !['fixed', 'percentage'].includes(discountType)) {
      return res.status(400).json({ message: "Tipo de desconto inválido. Use 'fixed' ou 'percentage'" });
    }

    // Verificar se o usuário tem acesso à loja
    const firebaseUid = req.user.uid;
    const store = await storage.getStoreByFirebaseUid(firebaseUid);
    if (!store) {
      return res.status(404).json({ message: "Loja não encontrada" });
    }

    // Buscar a revisão
    const revision = await storage.getOrderRevision(revisionId);
    if (!revision) {
      return res.status(404).json({ message: "Revisão não encontrada" });
    }

    // Buscar o pedido para verificar se pertence à loja
    const order = await storage.getOrder(revision.orderId);
    if (!order || order.storeId !== store.id) {
      return res.status(403).json({ message: "Acesso negado a esta revisão" });
    }

    // Buscar os itens da revisão para calcular o subtotal correto
    const items = await storage.getOrderRevisionItems(revisionId);

    // Verificar se há itens na revisão
    if (!items || items.length === 0) {
      return res.status(400).json({ message: "A revisão não possui itens para calcular o desconto" });
    }

    // Calcular o subtotal baseado nos itens atuais, não no valor armazenado
    const calculatedSubtotal = calculateSubtotalFromItems(items);
    console.log('Subtotal calculado com base nos itens:', calculatedSubtotal);

    // Converter o valor do desconto para número com validação mais robusta
    let numericDiscount: number;

    if (typeof discount === 'string') {
      // Se for string, tentar converter tratando vírgula como separador decimal
      numericDiscount = parseFloat(discount.replace(',', '.'));
    } else if (typeof discount === 'number') {
      numericDiscount = discount;
    } else {
      return res.status(400).json({ message: "Valor de desconto deve ser um número" });
    }

    if (isNaN(numericDiscount) || numericDiscount < 0) {
      return res.status(400).json({ message: "Valor de desconto inválido. Deve ser um número não-negativo" });
    }

    console.log('Valor do desconto convertido:', { original: discount, convertido: numericDiscount });

    console.log('Valores recebidos para cálculo de desconto:', {
      subtotal: calculatedSubtotal,
      discountValue: numericDiscount,
      discountType,
      isPercentage: discountType === 'percentage'
    });

    // Usar as funções utilitárias para calcular o desconto e o total
    const { finalDiscount, originalPercentage } = calculateDiscount(
      calculatedSubtotal,
      numericDiscount,
      discountType as 'fixed' | 'percentage'
    );

    console.log('Discount calculation details:', {
      subtotal: calculatedSubtotal,
      discountType,
      inputDiscount: numericDiscount,
      calculatedDiscount: finalDiscount,
      originalPercentage
    });

    // Calcular o novo total usando o subtotal calculado
    const deliveryFee = revision.deliveryFee || 0;
    const total = calculateTotal(calculatedSubtotal, finalDiscount, deliveryFee);

    // Registrar valores para debug
    console.log('Valores calculados:', {
      calculatedSubtotal,
      subtotalOriginal: revision.subtotal, // o valor anterior armazenado
      discountType,
      discountValue: numericDiscount,
      finalDiscount,
      originalPercentage,
      deliveryFee,
      total
    });

    // Validar se a matemática está correta
    const expectedTotal = Math.max(0, calculatedSubtotal - finalDiscount + deliveryFee);
    if (Math.abs(expectedTotal - total) > 0.01) {
      console.error('ERRO: Discrepância no cálculo do total!', {
        expectedTotal,
        calculatedTotal: total
      });
    }

    // Log adicional para depuração do cálculo de descontos
    console.log('Detalhes do cálculo de desconto:', {
      calculatedSubtotal,
      discountType,
      originalPercentage,
      finalDiscount: finalDiscount.toFixed(2),
      calculoExplicito: discountType === 'percentage'
        ? `${numericDiscount}% de ${calculatedSubtotal} = ${((numericDiscount / 100) * calculatedSubtotal).toFixed(2)}`
        : `Valor fixo de ${numericDiscount}`
    });

    // Atualizar a revisão com o novo subtotal, desconto e total usando exclusivamente o Supabase
    try {
      console.log('Atualizando dados da revisão usando Supabase...');

      // Preparar os dados para atualização com campos consistentes
      const updateData = {
        subtotal: calculatedSubtotal, // Agora atualizamos o subtotal também!
        discount: finalDiscount,
        discount_type: discountType,
        original_percentage: originalPercentage, // Armazenar percentual original se aplicável
        total: total
      };

      // Adicionar campos alternativos para compatibilidade
      if (discountType === 'percentage' && originalPercentage > 0) {
        updateData.original_percentage = originalPercentage;
      }

      console.log('Dados a serem atualizados no Supabase:', updateData);

      const { data: result, error } = await supabase
        .from('order_revisions')
        .update(updateData)
        .eq('id', revisionId)
        .select('*')
        .single();

      if (error) {
        console.error('Erro ao atualizar revisão usando Supabase:', error);
        console.error('Detalhes do erro:', {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        });
        throw error;
      }

      console.log('Revisão atualizada com sucesso usando Supabase:', result);

      // Aguardar um momento para garantir que a transação anterior foi commitada
      await new Promise(resolve => setTimeout(resolve, 100));

      // ✅ APLICAR MÉTODO UNIFICADO APÓS ATUALIZAÇÃO DO DESCONTO
      console.log('🔄 Aplicando método unificado após atualização do desconto...');
      
      // Importar a função unificada
      const { updateRevisionTotalsUnified } = await import('../routes');
      
      const revisionAfterDiscountUpdate = await storage.getOrderRevision(revisionId);
      const unifiedSuccess = await updateRevisionTotalsUnified(revisionId, items, revisionAfterDiscountUpdate);

      if (!unifiedSuccess) {
        console.error('❌ Falha no método unificado após atualização do desconto');
      } else {
        console.log('✅ Método unificado aplicado com sucesso após atualização do desconto');
      }

      // Preparar a resposta com informações adicionais para depuração
      const response = {
        ...result,
        items,
        _debug: {
          calculatedSubtotal,
          discountType,
          originalDiscount: numericDiscount,
          finalDiscount,
          originalPercentage,
          total,
          recalculationPerformed: unifiedSuccess
        }
      };

      console.log('Resposta a ser enviada ao cliente:', response);

      // Retornar a revisão atualizada com os itens
      return res.status(200).json(response);
    } catch (error) {
      console.error('Erro ao atualizar revisão:', error);

      // Log detalhado do erro para depuração
      if (error instanceof Error) {
        console.error('Stack trace:', error.stack);
      }

      return res.status(500).json({
        message: "Falha ao atualizar revisão",
        error: error instanceof Error ? error.message : String(error),
        revisionId: revisionId,
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    console.error('Erro ao processar requisição de desconto:', error);
    return res.status(500).json({
      message: "Erro interno ao processar desconto",
      error: error instanceof Error ? error.message : String(error)
    });
  }
}
