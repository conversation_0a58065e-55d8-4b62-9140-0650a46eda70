import { useQuery } from "@tanstack/react-query";

interface DashboardData {
  summary: {
    pendingOrders: number;
    confirmedOrders: number;
    monthlyRevenue: number;
    nextDelivery: {
      id: number;
      customerName: string;
      deliveryTime: string;
    } | null;
  };
  pendingOrders: Array<{
    id: number;
    customerName: string;
    customerPhone: string;
    products: string;
    total: number;
    status: string;
    deliveryTime: string;
    isLate: boolean;
  }>;
  siteActivity: {
    monthlyVisits: number;
    todayVisits: number;
    conversionRate: number;
    returningCustomers: number;
  };
  topProducts: Array<{
    name: string;
    totalSold: number;
    revenue: number;
  }>;
  recentCustomers: Array<{
    id: number;
    name: string;
    phone: string;
    lastOrderDate: string;
    totalOrders: number;
  }>;

  financialMetrics: {
    summary: {
      totalRevenue: number;
      currentMonthRevenue: number;
      monthlyGrowth: number;
      avgOrderValue: number;
      totalOrders: number;
    };
    revenueChart: Array<{
      month: string;
      revenue: number;
      orders: number;
    }>;
    topProductsByRevenue: Array<{
      name: string;
      revenue: number;
      quantity: number;
      avgPrice: number;
    }>;
    monthlyComparison: {
      current: number;
      previous: number;
      growth: number;
    };
  };
}

export function useDashboardData() {
  return useQuery<DashboardData>({
    queryKey: ['/api/dashboard'],
    refetchInterval: 60000, // 1 minuto
    staleTime: 30000, // 30 segundos
  });
}