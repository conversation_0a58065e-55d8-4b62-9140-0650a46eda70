/**
 * Este arquivo contém adaptadores para trabalhar com variation_options
 * corrigindo a incompatibilidade entre o schema TypeScript (price) e 
 * o banco de dados PostgreSQL (additional_price)
 */

import { db } from './db';
import { VariationOption, InsertVariationOption, variationOptions } from '@shared/schema';
import { sql, eq } from 'drizzle-orm';

/**
 * Cria uma nova opção de variação utilizando a função SQL compatível
 */
export async function createVariationOption(insertOption: InsertVariationOption): Promise<VariationOption> {
  try {
    // Log de debug
    console.log('Chamando createVariationOption com dados:', JSON.stringify(insertOption));
    
    // Executar SQL personalizado para inserir com additional_price
    const result = await db.execute(sql`
      INSERT INTO variation_options (
        variation_id, 
        name, 
        additional_price
      ) 
      VALUES (
        ${insertOption.variationId}, 
        ${insertOption.name}, 
        ${insertOption.price || 0}
      )
      RETURNING id
    `);
    
    if (!result || !result.rows || result.rows.length === 0) {
      throw new Error('Nenhum dado retornado ao criar opção de variação');
    }
    
    const optionId = result.rows[0].id;
    console.log('Opção de variação criada com ID:', optionId);
    
    // Buscar a opção completa
    const option = await db.execute(sql`
      SELECT 
        id, 
        variation_id, 
        name, 
        additional_price, 
        created_at
      FROM variation_options
      WHERE id = ${optionId}
    `);
      
    if (!option || !option.rows || option.rows.length === 0) {
      throw new Error('Falha ao buscar opção recém-criada');
    }
    
    const row = option.rows[0];
    
    // Mapear para o formato esperado pelo código
    return {
      id: row.id,
      variationId: row.variation_id,
      name: row.name,
      price: row.additional_price,
      createdAt: new Date(row.created_at)
    };
  } catch (error) {
    console.error('Erro inesperado em createVariationOption:', error);
    throw error;
  }
}

/**
 * Atualiza uma opção de variação utilizando a função SQL compatível
 */
export async function updateVariationOption(
  id: number,
  optionUpdate: Partial<VariationOption>
): Promise<VariationOption | undefined> {
  try {
    // Log de debug
    console.log('Chamando updateVariationOption com ID:', id, 'e dados:', JSON.stringify(optionUpdate));
    
    // Executar SQL personalizado para atualizar com additional_price
    const result = await db.execute(sql`
      UPDATE variation_options 
      SET 
        name = ${optionUpdate.name}, 
        additional_price = ${optionUpdate.price || 0}
      WHERE id = ${id}
      RETURNING id
    `);
    
    if (!result || !result.rows || result.rows.length === 0) {
      console.error('Opção de variação não encontrada para atualização');
      return undefined;
    }
    
    // Buscar a opção atualizada
    const option = await db.execute(sql`
      SELECT 
        id, 
        variation_id, 
        name, 
        additional_price, 
        created_at
      FROM variation_options
      WHERE id = ${id}
    `);
      
    if (!option || !option.rows || option.rows.length === 0) {
      console.error('Erro ao buscar opção de variação após atualização');
      return undefined;
    }
    
    const row = option.rows[0];
    
    // Mapear para o formato esperado pelo código
    return {
      id: row.id,
      variationId: row.variation_id,
      name: row.name,
      price: row.additional_price,
      createdAt: new Date(row.created_at)
    };
  } catch (error) {
    console.error('Erro inesperado em updateVariationOption:', error);
    return undefined;
  }
}