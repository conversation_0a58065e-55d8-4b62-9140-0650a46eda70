import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Check, Crown, Zap, Star, ToggleLeft, ToggleRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { useSubscription } from '@/context/SubscriptionContext';
import { PLAN_CONFIGS, PLAN_PRICING_OPTIONS, type PlanType } from '@shared/schema';
import { cn } from '@/lib/utils';

interface PlanSelectorProps {
  onPlanSelect?: (planType: PlanType) => void;
  currentPlan?: PlanType;
  showCurrentPlan?: boolean;
}

export function PlanSelector({ onPlanSelect, currentPlan = 'free', showCurrentPlan = true }: PlanSelectorProps) {
  const { t } = useTranslation();
  const { createCheckoutSession } = useSubscription();
  const [isLoading, setIsLoading] = useState<PlanType | null>(null);
  const [isYearly, setIsYearly] = useState(false);

  const handleSelectPlan = async (planType: PlanType) => {
    if (planType === 'free') {
      onPlanSelect?.(planType);
      return;
    }

    if (planType === 'premium') {
      setIsLoading('premium');
      try {
        const interval = isYearly ? 'year' : 'month';
        const checkoutUrl = await createCheckoutSession(interval);
        if (checkoutUrl) {
          window.location.href = checkoutUrl;
        }
      } catch (error) {
        console.error('Erro ao criar checkout:', error);
      } finally {
        setIsLoading(null);
      }
    }

    onPlanSelect?.(planType);
  };

  const formatPrice = (price: number, currency: string) => {
    if (price === 0) return t('subscription.free');
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(price);
  };

  const getPremiumPrice = () => {
    if (isYearly) {
      return {
        price: PLAN_PRICING_OPTIONS.premium.yearly.price,
        interval: 'year',
        monthlyEquivalent: PLAN_PRICING_OPTIONS.premium.yearly.monthlyEquivalent,
        savings: PLAN_PRICING_OPTIONS.premium.yearly.savings,
      };
    }
    return {
      price: PLAN_PRICING_OPTIONS.premium.monthly.price,
      interval: 'month',
      monthlyEquivalent: null,
      savings: 0,
    };
  };

  return (
    <div className="space-y-6 max-w-4xl mx-auto">
      {/* Toggle Mensal/Anual */}
      <div className="flex items-center justify-center space-x-4 p-4 bg-gray-50 rounded-lg">
        <span className={cn(
          "text-sm font-medium transition-colors",
          !isYearly ? "text-gray-900" : "text-gray-500"
        )}>
          Mensal
        </span>
        <Switch
          checked={isYearly}
          onCheckedChange={setIsYearly}
          className="data-[state=checked]:bg-green-600"
        />
        <span className={cn(
          "text-sm font-medium transition-colors",
          isYearly ? "text-gray-900" : "text-gray-500"
        )}>
          Anual
        </span>
        {isYearly && (
          <Badge className="bg-green-100 text-green-800 text-xs">
            Economize 17%
          </Badge>
        )}
      </div>

      <div className="grid gap-6 md:grid-cols-2">
      {Object.values(PLAN_CONFIGS).map((plan) => {
        const isCurrentPlan = currentPlan === plan.id;
        const isPopular = plan.popular;
        const isPremium = plan.id === 'premium';

        return (
          <Card 
            key={plan.id} 
            className={cn(
              "relative transition-all duration-200 hover:shadow-lg",
              isCurrentPlan && showCurrentPlan && "ring-2 ring-primary",
              isPopular && "border-primary shadow-md scale-105"
            )}
          >
            {isPopular && (
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <Badge className="bg-primary text-primary-foreground px-3 py-1 rounded-full">
                  <Star className="w-3 h-3 mr-1" />
                  {t('subscription.popular')}
                </Badge>
              </div>
            )}

            <CardHeader className="text-center pb-4">
              <div className="flex justify-center mb-2">
                {isPremium ? (
                  <Crown className="w-8 h-8 text-yellow-500" />
                ) : (
                  <Zap className="w-8 h-8 text-blue-500" />
                )}
              </div>
              
              <CardTitle className="text-xl font-bold">
                {plan.name}
              </CardTitle>
              
              <div className="mt-2">
                {isPremium ? (
                  <>
                    <div className="space-y-1">
                      <span className="text-3xl font-bold">
                        {formatPrice(getPremiumPrice().price, plan.currency)}
                      </span>
                      <span className="text-muted-foreground ml-1">
                        /{isYearly ? 'ano' : t('subscription.month')}
                      </span>
                      {isYearly && (
                        <div className="text-sm text-green-600">
                          {formatPrice(getPremiumPrice().monthlyEquivalent!, plan.currency)}/mês
                        </div>
                      )}
                    </div>
                    {isYearly && (
                      <div className="text-xs text-green-600 font-medium mt-1">
                        Economize {formatPrice(getPremiumPrice().savings, plan.currency)} por ano
                      </div>
                    )}
                  </>
                ) : (
                  <span className="text-3xl font-bold">
                    {formatPrice(plan.price, plan.currency)}
                  </span>
                )}
              </div>

              {isPremium && !isYearly && (
                <CardDescription className="text-sm text-green-600 font-medium">
                  {t('subscription.trial_days', { days: 7 })}
                </CardDescription>
              )}

              {isPremium && isYearly && (
                <CardDescription className="text-sm text-blue-600 font-medium">
                  Sem período de teste • Cobrança anual
                </CardDescription>
              )}
            </CardHeader>

            <CardContent className="space-y-3">
              {plan.features.map((feature, index) => (
                <div key={index} className="flex items-start gap-2">
                  <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span className="text-sm text-muted-foreground">{feature}</span>
                </div>
              ))}
            </CardContent>

            <CardFooter className="pt-4">
              {isCurrentPlan && showCurrentPlan ? (
                <Button 
                  variant="outline" 
                  className="w-full" 
                  disabled
                >
                  {t('subscription.current_plan')}
                </Button>
              ) : (
                <Button
                  onClick={() => handleSelectPlan(plan.id)}
                  disabled={isLoading === plan.id}
                  className={cn(
                    "w-full transition-all duration-200",
                    isPremium && "bg-primary hover:bg-primary/90"
                  )}
                  variant={isPremium ? "default" : "outline"}
                >
                  {isLoading === plan.id ? (
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                      {t('subscription.processing')}
                    </div>
                  ) : (
                    <>
                      {plan.price === 0 
                        ? t('subscription.select_free') 
                        : t('subscription.upgrade_now')
                      }
                    </>
                  )}
                </Button>
              )}
            </CardFooter>
          </Card>
        );
      })}
      </div>
    </div>
  );
}

// Componente compacto para mostrar plano atual
export function CurrentPlanBadge() {
  const { t } = useTranslation();
  const { planConfig, subscription } = useSubscription();

  if (!planConfig) return null;

  const isPremium = planConfig.id === 'premium';

  return (
    <Badge 
      variant={isPremium ? "default" : "secondary"}
      className={cn(
        "flex items-center gap-1",
        isPremium && "bg-gradient-to-r from-yellow-500 to-orange-500 text-white"
      )}
    >
      {isPremium ? (
        <Crown className="w-3 h-3" />
      ) : (
        <Zap className="w-3 h-3" />
      )}
      {planConfig.name}
    </Badge>
  );
}

// Componente para mostrar progresso de uso
export function UsageProgress({ feature, label }: { 
  feature: 'maxProducts' | 'maxOrdersPerMonth';
  label: string;
}) {
  const { usageInfo } = useSubscription();

  if (!usageInfo) return null;

  const usage = usageInfo.usage[feature];
  const percentage = usage.limit === -1 ? 0 : Math.min(100, (usage.current / usage.limit) * 100);
  const isNearLimit = percentage > 80;
  const isAtLimit = usage.isLimitExceeded;

  return (
    <div className="space-y-2">
      <div className="flex justify-between text-sm">
        <span className="text-muted-foreground">{label}</span>
        <span className={cn(
          "font-medium",
          isAtLimit && "text-red-500",
          isNearLimit && !isAtLimit && "text-yellow-500"
        )}>
          {usage.current}
          {usage.limit !== -1 && ` / ${usage.limit}`}
          {usage.limit === -1 && " (ilimitado)"}
        </span>
      </div>
      
      {usage.limit !== -1 && (
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className={cn(
              "h-2 rounded-full transition-all duration-300",
              isAtLimit && "bg-red-500",
              isNearLimit && !isAtLimit && "bg-yellow-500",
              !isNearLimit && "bg-green-500"
            )}
            style={{ width: `${Math.min(100, percentage)}%` }}
          />
        </div>
      )}
    </div>
  );
}
