import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Calendar, Filter, X } from "lucide-react";

interface OrderFiltersProps {
  onFiltersChange: (filters: OrderFilters) => void;
  totalCount: number;
  filteredCount: number;
}

export interface OrderFilters {
  status: string;
  paymentStatus: string;
  deliveryDate: string;
  isLate: boolean | null;
  customerSearch: string;
}

export function OrderFilters({ onFiltersChange, totalCount, filteredCount }: OrderFiltersProps) {
  const [filters, setFilters] = useState<OrderFilters>({
    status: '',
    paymentStatus: '',
    deliveryDate: '',
    isLate: null,
    customerSearch: ''
  });

  const [showFilters, setShowFilters] = useState(false);

  const updateFilters = (newFilters: Partial<OrderFilters>) => {
    const updated = { ...filters, ...newFilters };
    setFilters(updated);
    onFiltersChange(updated);
  };

  const clearFilters = () => {
    const clearedFilters: OrderFilters = {
      status: '',
      paymentStatus: '',
      deliveryDate: '',
      isLate: null,
      customerSearch: ''
    };
    setFilters(clearedFilters);
    onFiltersChange(clearedFilters);
  };

  const hasActiveFilters = Object.values(filters).some(value => 
    value !== '' && value !== null
  );

  return (
    <div className="space-y-4">
      {/* Header dos filtros */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant={showFilters ? "default" : "outline"}
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter className="w-4 h-4 mr-2" />
            Filtros
          </Button>
          {hasActiveFilters && (
            <Button variant="ghost" size="sm" onClick={clearFilters}>
              <X className="w-4 h-4 mr-2" />
              Limpar
            </Button>
          )}
        </div>
        <div className="text-sm text-gray-600">
          {filteredCount !== totalCount ? (
            <>Mostrando {filteredCount} de {totalCount} pedidos</>
          ) : (
            <>{totalCount} pedidos</>
          )}
        </div>
      </div>

      {/* Painel de filtros */}
      {showFilters && (
        <Card>
          <CardContent className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              {/* Busca por cliente */}
              <div className="space-y-2">
                <Label htmlFor="customer-search">Cliente</Label>
                <Input
                  id="customer-search"
                  placeholder="Nome ou telefone"
                  value={filters.customerSearch}
                  onChange={(e) => updateFilters({ customerSearch: e.target.value })}
                />
              </div>

              {/* Status do pedido */}
              <div className="space-y-2">
                <Label>Status do Pedido</Label>
                <Select
                  value={filters.status}
                  onValueChange={(value) => updateFilters({ status: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Todos" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Todos</SelectItem>
                    <SelectItem value="pending">Pendente</SelectItem>
                    <SelectItem value="preparing">Preparando</SelectItem>
                    <SelectItem value="ready">Pronto</SelectItem>
                    <SelectItem value="delivered">Entregue</SelectItem>
                    <SelectItem value="cancelled">Cancelado</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Status do pagamento */}
              <div className="space-y-2">
                <Label>Status do Pagamento</Label>
                <Select
                  value={filters.paymentStatus}
                  onValueChange={(value) => updateFilters({ paymentStatus: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Todos" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Todos</SelectItem>
                    <SelectItem value="pendente">Pendente</SelectItem>
                    <SelectItem value="recebido">Recebido</SelectItem>
                    <SelectItem value="parcial">Parcial</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Situação da entrega */}
              <div className="space-y-2">
                <Label>Situação da Entrega</Label>
                <Select
                  value={filters.isLate === null ? '' : filters.isLate.toString()}
                  onValueChange={(value) => 
                    updateFilters({ 
                      isLate: value === '' ? null : value === 'true' 
                    })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Todas" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Todas</SelectItem>
                    <SelectItem value="true">Atrasadas</SelectItem>
                    <SelectItem value="false">No Prazo</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Data de entrega */}
              <div className="space-y-2">
                <Label htmlFor="delivery-date">Data de Entrega</Label>
                <div className="relative">
                  <Input
                    id="delivery-date"
                    type="date"
                    value={filters.deliveryDate}
                    onChange={(e) => updateFilters({ deliveryDate: e.target.value })}
                  />
                  <Calendar className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}