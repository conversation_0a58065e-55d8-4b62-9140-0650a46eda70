import { useState } from 'react';
import { <PERSON> } from 'wouter';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Form, 
  FormControl, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from '@/components/ui/form';
import { Separator } from '@/components/ui/separator';
import { useAuth } from '@/context/FirebaseAuthContext';
import { useTranslation } from '@/hooks/useTranslation';
import { FcGoogle } from 'react-icons/fc';
import { Mail, Eye, EyeOff } from 'lucide-react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';

interface AuthFormProps {
  mode: 'login' | 'register';
}

// Formulários de validação
const loginSchema = z.object({
  email: z.string().email('Email inválido'),
  password: z.string().min(6, 'A senha deve ter pelo menos 6 caracteres'),
});

const registerSchema = z.object({
  username: z.string().min(3, 'O nome de usuário deve ter pelo menos 3 caracteres'),
  email: z.string().email('Email inválido'),
  password: z.string().min(6, 'A senha deve ter pelo menos 6 caracteres'),
  confirmPassword: z.string().min(6, 'A senha deve ter pelo menos 6 caracteres'),
}).refine((data) => data.password === data.confirmPassword, {
  message: 'As senhas não correspondem',
  path: ['confirmPassword'],
});

type LoginFormValues = z.infer<typeof loginSchema>;
type RegisterFormValues = z.infer<typeof registerSchema>;

export function AuthForm({ mode }: AuthFormProps) {
  const { t } = useTranslation();
  const { loading, signInWithGoogle, signInWithEmail, signUpWithEmail } = useAuth();
  const [isProcessing, setIsProcessing] = useState(false);
  const [authMethod, setAuthMethod] = useState<'google' | 'email'>('google');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Formulário de login
  const loginForm = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  // Formulário de registro
  const registerForm = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
    },
  });

  // Autenticação com Google
  const handleGoogleAuth = async () => {
    try {
      setIsProcessing(true);
      await signInWithGoogle();
    } catch (error) {
      console.error("Erro ao autenticar com Google:", error);
    } finally {
      setIsProcessing(false);
    }
  };

  // Autenticação com Email e Senha - Login
  const onLoginSubmit = async (data: LoginFormValues) => {
    try {
      setIsProcessing(true);
      await signInWithEmail(data.email, data.password);
    } catch (error) {
      console.error("Erro ao fazer login com email:", error);
    } finally {
      setIsProcessing(false);
    }
  };

  // Autenticação com Email e Senha - Registro
  const onRegisterSubmit = async (data: RegisterFormValues) => {
    try {
      setIsProcessing(true);
      await signUpWithEmail(data.email, data.password, data.username);
    } catch (error) {
      console.error("Erro ao registrar com email:", error);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="text-3xl font-bold text-primary">
          {t('auth.title')}
        </CardTitle>
        <CardDescription>
          {t('auth.subtitle')}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Tabs defaultValue="google" onValueChange={(value) => setAuthMethod(value as 'google' | 'email')}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="google">
              <FcGoogle className="mr-2 h-4 w-4" />
              Google
            </TabsTrigger>
            <TabsTrigger value="email">
              <Mail className="mr-2 h-4 w-4" />
              Email
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="google" className="pt-4">
            <div className="text-center">
              <p className="mb-4">
                {mode === 'login' 
                  ? t('auth.loginWithGoogleDesc') || "Use sua conta Google para fazer login" 
                  : t('auth.registerWithGoogleDesc') || "Crie uma conta com Google para continuar"}
              </p>
              
              <Button 
                className="w-full flex items-center justify-center gap-2" 
                variant="outline"
                onClick={handleGoogleAuth} 
                disabled={loading || isProcessing}
              >
                <FcGoogle className="h-5 w-5" />
                {loading || isProcessing 
                  ? t('common.loading')
                  : mode === 'login' 
                    ? t('auth.loginWithGoogle')
                    : t('auth.registerWithGoogle')
                }
              </Button>
            </div>
          </TabsContent>
          
          <TabsContent value="email" className="pt-4">
            {mode === 'login' ? (
              <Form {...loginForm}>
                <form onSubmit={loginForm.handleSubmit(onLoginSubmit)} className="space-y-4">
                  <FormField
                    control={loginForm.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input 
                            type="email" 
                            placeholder="<EMAIL>" 
                            {...field} 
                            autoComplete="email"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={loginForm.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Senha</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input 
                              type={showPassword ? "text" : "password"} 
                              placeholder="********" 
                              {...field} 
                              autoComplete="current-password"
                            />
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              className="absolute right-0 top-0 h-full px-3"
                              onClick={() => setShowPassword(!showPassword)}
                            >
                              {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                            </Button>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <Button 
                    type="submit" 
                    className="w-full" 
                    disabled={loading || isProcessing}
                  >
                    {loading || isProcessing ? t('auth.loggingIn') : t('common.login')}
                  </Button>
                </form>
              </Form>
            ) : (
              <Form {...registerForm}>
                <form onSubmit={registerForm.handleSubmit(onRegisterSubmit)} className="space-y-4">
                  <FormField
                    control={registerForm.control}
                    name="username"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('auth.username')}</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="seuNome" 
                            {...field} 
                            autoComplete="username"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={registerForm.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input 
                            type="email" 
                            placeholder="<EMAIL>" 
                            {...field} 
                            autoComplete="email"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={registerForm.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Senha</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input 
                              type={showPassword ? "text" : "password"} 
                              placeholder="********" 
                              {...field} 
                              autoComplete="new-password"
                            />
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              className="absolute right-0 top-0 h-full px-3"
                              onClick={() => setShowPassword(!showPassword)}
                            >
                              {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                            </Button>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={registerForm.control}
                    name="confirmPassword"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Confirmar senha</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input 
                              type={showConfirmPassword ? "text" : "password"} 
                              placeholder="********" 
                              {...field} 
                              autoComplete="new-password"
                            />
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              className="absolute right-0 top-0 h-full px-3"
                              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            >
                              {showConfirmPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                            </Button>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <Button 
                    type="submit" 
                    className="w-full" 
                    disabled={loading || isProcessing}
                  >
                    {loading || isProcessing ? t('auth.creatingAccount') : t('common.register')}
                  </Button>
                </form>
              </Form>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="text-center">
        <p className="w-full text-sm">
          {mode === 'login' ? t('common.noAccount') : t('common.hasAccount')}{' '}
          <Link
            href={mode === 'login' ? '/register' : '/login'}
            className="text-primary hover:underline font-medium"
          >
            {mode === 'login' ? t('common.register') : t('common.login')}
          </Link>
        </p>
      </CardFooter>
    </Card>
  );
}

export default AuthForm;
