import { useState } from 'react';
import { <PERSON> } from 'wouter';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { useAuth } from '@/context/FirebaseAuthContext';
import { useTranslation } from '@/hooks/useTranslation';
import { useStore } from '@/context/StoreContext';
import { FcGoogle } from 'react-icons/fc';
import { Eye, EyeOff, ArrowRight } from 'lucide-react';
import { cn } from '@/lib/utils';

interface AuthFormProps {
  mode: 'login' | 'register';
}

// Create validation schemas with internationalization
const createLoginSchema = (t: (key: string) => string) => z.object({
  email: z.string().email(t('auth.invalidEmail')),
  password: z.string().min(6, t('auth.passwordTooShort')),
});

const createRegisterSchema = (t: (key: string) => string) => z.object({
  username: z.string().min(3, t('auth.usernameTooShort')),
  email: z.string().email(t('auth.invalidEmail')),
  password: z.string().min(6, t('auth.passwordTooShort')),
  confirmPassword: z.string().min(6, t('auth.passwordTooShort')),
}).refine((data) => data.password === data.confirmPassword, {
  message: t('auth.passwordsDontMatch'),
  path: ['confirmPassword'],
});

type LoginFormValues = z.infer<ReturnType<typeof createLoginSchema>>;
type RegisterFormValues = z.infer<ReturnType<typeof createRegisterSchema>>;

export function AuthForm({ mode }: AuthFormProps) {
  const { t } = useTranslation();
  const { loading, signInWithGoogle, signInWithEmail, signUpWithEmail } = useAuth();
  const { store } = useStore();
  const [isProcessing, setIsProcessing] = useState(false);
  const [authMethod, setAuthMethod] = useState<'google' | 'email'>('google');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Create schemas with current translation function
  const loginSchema = createLoginSchema(t);
  const registerSchema = createRegisterSchema(t);

  // Formulário de login
  const loginForm = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  // Formulário de registro
  const registerForm = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
    },
  });

  // Autenticação com Google
  const handleGoogleAuth = async () => {
    try {
      setIsProcessing(true);
      await signInWithGoogle();
    } catch (error) {
      console.error("Erro ao autenticar com Google:", error);
    } finally {
      setIsProcessing(false);
    }
  };

  // Autenticação com Email e Senha - Login
  const onLoginSubmit = async (data: LoginFormValues) => {
    try {
      setIsProcessing(true);
      await signInWithEmail(data.email, data.password);
    } catch (error) {
      console.error("Erro ao fazer login com email:", error);
    } finally {
      setIsProcessing(false);
    }
  };

  // Autenticação com Email e Senha - Registro
  const onRegisterSubmit = async (data: RegisterFormValues) => {
    try {
      setIsProcessing(true);
      await signUpWithEmail(data.email, data.password, data.username);
    } catch (error) {
      console.error("Erro ao registrar com email:", error);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="w-full max-w-sm mx-auto ios-auth-container auth-form-animate">
      {/* Header Section */}
      <div className="text-center mb-12">
        {/* Store Logo */}
        {store?.logo && (
          <div className="mb-6">
            <img
              src={store.logo}
              alt={store.name || t('auth.title')}
              className="h-16 w-16 mx-auto rounded-2xl shadow-sm"
            />
          </div>
        )}

        {/* Title */}
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          {mode === 'login' ? t('auth.welcomeBack') : t('auth.createAccount')}
        </h1>

        {/* Subtitle */}
        <p className="text-gray-600 text-base">
          {mode === 'login' ? t('auth.loginTitle') : t('auth.registerTitle')}
        </p>
      </div>

      {/* Auth Methods */}
      <div className="space-y-4">
        {/* Google Sign In Button */}
        <Button
          variant="outline"
          size="lg"
          className={cn(
            "w-full h-12 text-base font-medium",
            "border-gray-200 hover:border-gray-300",
            "bg-white hover:bg-gray-50",
            "ios-button ios-transition",
            "shadow-sm hover:shadow-md",
            "rounded-xl"
          )}
          onClick={handleGoogleAuth}
          disabled={loading || isProcessing}
        >
          <FcGoogle className="h-5 w-5 mr-3" />
          {loading || isProcessing
            ? t('common.loading')
            : t('auth.continueWithGoogle')
          }
        </Button>

        {/* Divider */}
        <div className="relative my-6">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-200" />
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-4 bg-white text-gray-500">
              {t('auth.orContinueWith')}
            </span>
          </div>
        </div>
        {/* Email Form */}
        {mode === 'login' ? (
          <Form {...loginForm}>
            <form onSubmit={loginForm.handleSubmit(onLoginSubmit)} className="space-y-5">
              <FormField
                control={loginForm.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium text-gray-700">
                      {t('auth.email')}
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder={t('auth.emailPlaceholder')}
                        className={cn(
                          "h-12 text-base rounded-xl border-gray-200",
                          "focus:border-blue-500 focus:ring-blue-500",
                          "ios-input ios-transition",
                          "bg-gray-50 focus:bg-white"
                        )}
                        {...field}
                        autoComplete="email"
                      />
                    </FormControl>
                    <FormMessage className="text-sm text-red-500" />
                  </FormItem>
                )}
              />

              <FormField
                control={loginForm.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium text-gray-700">
                      {t('auth.password')}
                    </FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type={showPassword ? "text" : "password"}
                          placeholder={t('auth.passwordPlaceholder')}
                          className={cn(
                            "h-12 text-base rounded-xl border-gray-200 pr-12",
                            "focus:border-blue-500 focus:ring-blue-500",
                            "transition-all duration-200 ease-in-out",
                            "bg-gray-50 focus:bg-white"
                          )}
                          {...field}
                          autoComplete="current-password"
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          className="absolute right-0 top-0 h-12 w-12 rounded-xl"
                          onClick={() => setShowPassword(!showPassword)}
                          aria-label={showPassword ? t('auth.hidePassword') : t('auth.showPassword')}
                        >
                          {showPassword ? <EyeOff size={18} className="text-gray-400" /> : <Eye size={18} className="text-gray-400" />}
                        </Button>
                      </div>
                    </FormControl>
                    <FormMessage className="text-sm text-red-500" />
                  </FormItem>
                )}
              />

              <Button
                type="submit"
                size="lg"
                className={cn(
                  "w-full h-12 text-base font-semibold rounded-xl",
                  "bg-blue-600 hover:bg-blue-700 text-white",
                  "transition-all duration-200 ease-in-out",
                  "shadow-sm hover:shadow-md",
                  "disabled:opacity-50 disabled:cursor-not-allowed"
                )}
                disabled={loading || isProcessing}
                style={{
                  backgroundColor: store?.colors?.primary || '#3B82F6',
                }}
              >
                {loading || isProcessing ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    {t('auth.loggingIn')}
                  </div>
                ) : (
                  <div className="flex items-center">
                    {t('common.login')}
                    <ArrowRight size={18} className="ml-2" />
                  </div>
                )}
              </Button>
            </form>
          </Form>
        ) : (
          <Form {...registerForm}>
            <form onSubmit={registerForm.handleSubmit(onRegisterSubmit)} className="space-y-5">
              <FormField
                control={registerForm.control}
                name="username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium text-gray-700">
                      {t('auth.username')}
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder={t('auth.usernamePlaceholder')}
                        className={cn(
                          "h-12 text-base rounded-xl border-gray-200",
                          "focus:border-blue-500 focus:ring-blue-500",
                          "transition-all duration-200 ease-in-out",
                          "bg-gray-50 focus:bg-white"
                        )}
                        {...field}
                        autoComplete="username"
                      />
                    </FormControl>
                    <FormMessage className="text-sm text-red-500" />
                  </FormItem>
                )}
              />

              <FormField
                control={registerForm.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium text-gray-700">
                      {t('auth.email')}
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder={t('auth.emailPlaceholder')}
                        className={cn(
                          "h-12 text-base rounded-xl border-gray-200",
                          "focus:border-blue-500 focus:ring-blue-500",
                          "transition-all duration-200 ease-in-out",
                          "bg-gray-50 focus:bg-white"
                        )}
                        {...field}
                        autoComplete="email"
                      />
                    </FormControl>
                    <FormMessage className="text-sm text-red-500" />
                  </FormItem>
                )}
              />

              <FormField
                control={registerForm.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium text-gray-700">
                      {t('auth.password')}
                    </FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type={showPassword ? "text" : "password"}
                          placeholder={t('auth.passwordPlaceholder')}
                          className={cn(
                            "h-12 text-base rounded-xl border-gray-200 pr-12",
                            "focus:border-blue-500 focus:ring-blue-500",
                            "transition-all duration-200 ease-in-out",
                            "bg-gray-50 focus:bg-white"
                          )}
                          {...field}
                          autoComplete="new-password"
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          className="absolute right-0 top-0 h-12 w-12 rounded-xl"
                          onClick={() => setShowPassword(!showPassword)}
                          aria-label={showPassword ? t('auth.hidePassword') : t('auth.showPassword')}
                        >
                          {showPassword ? <EyeOff size={18} className="text-gray-400" /> : <Eye size={18} className="text-gray-400" />}
                        </Button>
                      </div>
                    </FormControl>
                    <FormMessage className="text-sm text-red-500" />
                  </FormItem>
                )}
              />

              <FormField
                control={registerForm.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium text-gray-700">
                      {t('auth.confirmPassword')}
                    </FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type={showConfirmPassword ? "text" : "password"}
                          placeholder={t('auth.confirmPasswordPlaceholder')}
                          className={cn(
                            "h-12 text-base rounded-xl border-gray-200 pr-12",
                            "focus:border-blue-500 focus:ring-blue-500",
                            "transition-all duration-200 ease-in-out",
                            "bg-gray-50 focus:bg-white"
                          )}
                          {...field}
                          autoComplete="new-password"
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          className="absolute right-0 top-0 h-12 w-12 rounded-xl"
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                          aria-label={showConfirmPassword ? t('auth.hidePassword') : t('auth.showPassword')}
                        >
                          {showConfirmPassword ? <EyeOff size={18} className="text-gray-400" /> : <Eye size={18} className="text-gray-400" />}
                        </Button>
                      </div>
                    </FormControl>
                    <FormMessage className="text-sm text-red-500" />
                  </FormItem>
                )}
              />

              <Button
                type="submit"
                size="lg"
                className={cn(
                  "w-full h-12 text-base font-semibold rounded-xl",
                  "bg-blue-600 hover:bg-blue-700 text-white",
                  "transition-all duration-200 ease-in-out",
                  "shadow-sm hover:shadow-md",
                  "disabled:opacity-50 disabled:cursor-not-allowed"
                )}
                disabled={loading || isProcessing}
                style={{
                  backgroundColor: store?.colors?.primary || '#3B82F6',
                }}
              >
                {loading || isProcessing ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    {t('auth.creatingAccount')}
                  </div>
                ) : (
                  <div className="flex items-center">
                    {t('auth.createAccount')}
                    <ArrowRight size={18} className="ml-2" />
                  </div>
                )}
              </Button>
            </form>
          </Form>
        )}
      </div>

      {/* Footer Link */}
      <div className="text-center mt-8">
        <p className="text-gray-600 text-sm">
          {mode === 'login' ? 'Não tem uma conta?' : 'Já tem uma conta?'}{' '}
          <Link
            href={mode === 'login' ? '/register' : '/login'}
            className="font-medium text-blue-600 hover:text-blue-700 transition-colors duration-200"
            style={{
              color: store?.colors?.primary || '#3B82F6',
            }}
          >
            {mode === 'login' ? t('auth.createAccount') : 'Fazer login'}
          </Link>
        </p>
      </div>
    </div>
  );
}

export default AuthForm;
