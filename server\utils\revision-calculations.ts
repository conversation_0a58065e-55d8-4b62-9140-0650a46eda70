import { storage } from '../storage';

/**
 * Interface para os dados de uma revisão
 */
export interface RevisionData {
  id: number;
  subtotal?: number;
  discount?: number;
  discountType?: 'fixed' | 'percentage';
  originalPercentage?: number;
  deliveryFee?: number;
  total?: number;
}

/**
 * Interface para os dados de um item de revisão
 */
export interface RevisionItemData {
  id: number;
  unitPrice: number;
  quantity: number;
  subtotal?: number;
}

/**
 * Resultado dos cálculos de uma revisão
 */
export interface CalculationResult {
  subtotal: number;
  discount: number;
  deliveryFee: number;
  total: number;
  needsUpdate: boolean;
  differences: {
    subtotal: number;
    discount: number;
    total: number;
  };
}

/**
 * Calcula o subtotal baseado nos itens de uma revisão
 * IMPORTANTE: Usa o subtotal armazenado dos itens, que pode incluir variações e customizações
 */
export function calculateSubtotalFromItems(items: RevisionItemData[]): number {
  console.log(`🧮 Calculando subtotal de ${items.length} itens:`, items.map(item => ({
    id: item.id,
    unitPrice: item.unitPrice,
    quantity: item.quantity,
    subtotalCalculado: (item.unitPrice || 0) * (item.quantity || 0),
    subtotalArmazenado: item.subtotal
  })));

  // Usar o subtotal armazenado dos itens (que pode incluir variações)
  const totalFromStoredSubtotal = items.reduce((sum, item) => {
    console.log(`  Item ${item.id}: subtotal armazenado = ${item.subtotal || 0}`);
    return sum + (item.subtotal || 0);
  }, 0);

  // Calcular também baseado em unitPrice × quantity para comparação
  const totalFromCalculation = items.reduce((sum, item) => {
    const itemSubtotal = (item.unitPrice || 0) * (item.quantity || 0);
    return sum + itemSubtotal;
  }, 0);

  const variationsValue = totalFromStoredSubtotal - totalFromCalculation;

  console.log(`🧮 Análise do subtotal:`, {
    subtotalSemVariacoes: totalFromCalculation,
    subtotalComVariacoes: totalFromStoredSubtotal,
    valorDasVariacoes: variationsValue,
    percentualVariacoes: totalFromCalculation > 0 ? ((variationsValue / totalFromCalculation) * 100).toFixed(2) + '%' : '0%',
    usingCorrectSubtotal: true
  });

  // Retornar o subtotal armazenado (que é o correto e inclui variações)
  return totalFromStoredSubtotal;
}

/**
 * Calcula o desconto baseado no tipo e valores da revisão
 */
export function calculateDiscount(
  subtotal: number,
  discountType: 'fixed' | 'percentage',
  discountValue: number,
  originalPercentage?: number
): number {
  if (discountType === 'percentage' && originalPercentage !== undefined && originalPercentage > 0) {
    // Para desconto percentual, usar o percentual original
    return (subtotal * originalPercentage) / 100;
  } else {
    // Para desconto fixo, usar o valor direto
    return discountValue || 0;
  }
}

/**
 * Calcula todos os valores de uma revisão de forma consistente
 */
export async function calculateRevisionValues(revisionId: number): Promise<CalculationResult | null> {
  try {
    // Buscar dados da revisão
    const revision = await storage.getOrderRevision(revisionId);
    if (!revision) {
      console.error(`Revisão ID ${revisionId} não encontrada`);
      return null;
    }

    // Buscar itens da revisão
    const items = await storage.getOrderRevisionItems(revisionId);
    console.log(`📦 Itens obtidos do banco para revisão ${revisionId}:`, items);

    if (!items || items.length === 0) {
      console.warn(`Revisão ID ${revisionId} não possui itens`);
      return null;
    }

    // Verificar estrutura dos dados dos itens
    console.log(`🔍 Estrutura dos itens:`, items.map(item => ({
      id: item.id,
      unitPrice: item.unitPrice,
      quantity: item.quantity,
      subtotal: item.subtotal,
      hasUnitPrice: 'unitPrice' in item,
      hasPrice: 'price' in item,
      allKeys: Object.keys(item)
    })));

    // Calcular subtotal baseado nos itens
    const calculatedSubtotal = calculateSubtotalFromItems(items);

    // Calcular desconto
    const discountType = revision.discountType || 'fixed';
    const discountValue = revision.discount || 0;
    const originalPercentage = revision.originalPercentage || 0;

    let calculatedDiscount = calculateDiscount(
      calculatedSubtotal,
      discountType,
      discountValue,
      originalPercentage
    );

    // Garantir que o desconto não seja maior que o subtotal
    calculatedDiscount = Math.min(calculatedDiscount, calculatedSubtotal);

    // Calcular total
    const deliveryFee = revision.deliveryFee || 0;
    const calculatedTotal = Math.max(0, calculatedSubtotal - calculatedDiscount + deliveryFee);

    // Verificar se há discrepâncias
    const subtotalDiff = Math.abs((revision.subtotal || 0) - calculatedSubtotal);
    const discountDiff = Math.abs((revision.discount || 0) - calculatedDiscount);
    const totalDiff = Math.abs((revision.total || 0) - calculatedTotal);

    const needsUpdate = subtotalDiff > 0.01 || discountDiff > 0.01 || totalDiff > 0.01;

    console.log(`🔍 Análise de discrepâncias para revisão ${revisionId}:`, {
      valoresNoBanco: {
        subtotal: revision.subtotal || 0,
        discount: revision.discount || 0,
        total: revision.total || 0
      },
      valoresCalculados: {
        subtotal: calculatedSubtotal,
        discount: calculatedDiscount,
        total: calculatedTotal
      },
      diferencas: {
        subtotal: subtotalDiff,
        discount: discountDiff,
        total: totalDiff
      },
      needsUpdate,
      threshold: 0.01
    });

    return {
      subtotal: calculatedSubtotal,
      discount: calculatedDiscount,
      deliveryFee,
      total: calculatedTotal,
      needsUpdate,
      differences: {
        subtotal: subtotalDiff,
        discount: discountDiff,
        total: totalDiff
      }
    };
  } catch (error) {
    console.error(`Erro ao calcular valores para revisão ID ${revisionId}:`, error);
    return null;
  }
}

/**
 * Atualiza os valores calculados de uma revisão no banco de dados
 */
export async function updateRevisionCalculatedValues(
  revisionId: number,
  calculatedValues: CalculationResult
): Promise<boolean> {
  try {
    const updateData = {
      subtotal: calculatedValues.subtotal,
      discount: calculatedValues.discount,
      total: calculatedValues.total
    };

    console.log(`💾 Atualizando valores calculados para revisão ID ${revisionId}:`, updateData);

    // Buscar valores antes da atualização para comparação
    const revisionBefore = await storage.getOrderRevision(revisionId);
    console.log(`📋 Valores antes da atualização:`, {
      subtotal: revisionBefore?.subtotal || 0,
      discount: revisionBefore?.discount || 0,
      total: revisionBefore?.total || 0
    });

    // Usar o método de storage para atualizar
    const updatedRevision = await storage.updateOrderRevision(revisionId, updateData);

    if (updatedRevision) {
      console.log(`✅ Revisão ID ${revisionId} atualizada com sucesso`);

      // Verificar se os valores foram realmente salvos
      const revisionAfter = await storage.getOrderRevision(revisionId);
      console.log(`📋 Valores após a atualização:`, {
        subtotal: revisionAfter?.subtotal || 0,
        discount: revisionAfter?.discount || 0,
        total: revisionAfter?.total || 0
      });

      // Verificar se houve mudança real
      const actuallyUpdated = (
        Math.abs((revisionAfter?.subtotal || 0) - updateData.subtotal) < 0.01 &&
        Math.abs((revisionAfter?.discount || 0) - updateData.discount) < 0.01 &&
        Math.abs((revisionAfter?.total || 0) - updateData.total) < 0.01
      );

      if (actuallyUpdated) {
        console.log(`✅ Valores confirmados no banco de dados`);
      } else {
        console.error(`❌ Valores não foram persistidos corretamente no banco`);
        return false;
      }

      return true;
    } else {
      console.error(`❌ Falha ao atualizar revisão ID ${revisionId}`);
      return false;
    }
  } catch (error) {
    console.error(`Erro ao atualizar valores da revisão ID ${revisionId}:`, error);
    return false;
  }
}

/**
 * Recalcula e atualiza todos os valores de uma revisão
 */
export async function recalculateAndUpdateRevision(revisionId: number): Promise<boolean> {
  try {
    console.log(`🔄 Recalculando valores para revisão ID: ${revisionId}`);

    const calculatedValues = await calculateRevisionValues(revisionId);
    if (!calculatedValues) {
      return false;
    }

    console.log(`📊 Valores calculados para revisão ID ${revisionId}:`, {
      subtotal: calculatedValues.subtotal,
      discount: calculatedValues.discount,
      total: calculatedValues.total,
      needsUpdate: calculatedValues.needsUpdate,
      differences: calculatedValues.differences
    });

    if (calculatedValues.needsUpdate) {
      console.log(`🔧 Aplicando correções para revisão ID ${revisionId}`);
      return await updateRevisionCalculatedValues(revisionId, calculatedValues);
    } else {
      console.log(`✅ Revisão ID ${revisionId} já possui valores corretos`);
      return true;
    }
  } catch (error) {
    console.error(`Erro ao recalcular revisão ID ${revisionId}:`, error);
    return false;
  }
}
