import { useQuery } from "@tanstack/react-query";
import { useParams } from "wouter";

/**
 * Hook para acessar informações da loja ativa
 * Em páginas públicas da loja (store/:slug), usa o slug para buscar
 * Em páginas admin, usa a loja do usuário logado
 */
export function useActiveStore() {
  const { slug } = useParams();

  // Para páginas públicas da loja, buscamos pelo slug
  const { data: storeBySlug, isLoading: isLoadingStoreBySlug } = useQuery({
    queryKey: [`/api/public/stores/${slug}`],
    enabled: !!slug
  });

  // Para páginas administrativas, obtemos a loja do usuário logado
  const { data: userStore, isLoading: isLoadingUserStore } = useQuery({
    queryKey: ['/api/stores/me'],
    enabled: !slug
  });

  // Determinar qual loja usar baseado no contexto
  const store = slug ? storeBySlug : userStore;
  const isLoading = slug ? isLoadingStoreBySlug : isLoadingUserStore;

  return {
    store,
    isLoading
  };
}