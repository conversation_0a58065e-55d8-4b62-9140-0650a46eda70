-- Criação da tabela de variações de produtos
CREATE TABLE IF NOT EXISTS product_variations (
    id SERIAL PRIMARY KEY,
    product_id INTEGER NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    required BOOLEAN NOT NULL DEFAULT FALSE,
    multiple_choice BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Criação da tabela de opções de variação
CREATE TABLE IF NOT EXISTS variation_options (
    id SERIAL PRIMARY KEY,
    variation_id INTEGER NOT NULL REFERENCES product_variations(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    price DECIMAL(10, 2) NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Adicionando índices para melhorar performance
CREATE INDEX IF NOT EXISTS idx_product_variations_product_id ON product_variations(product_id);
CREATE INDEX IF NOT EXISTS idx_variation_options_variation_id ON variation_options(variation_id);

-- <PERSON><PERSON><PERSON> as políticas RLS para a tabela de variações de produtos
ALTER TABLE product_variations ENABLE ROW LEVEL SECURITY;

-- Política para permitir que o dono da loja possa ver, criar, atualizar e excluir suas variações
CREATE POLICY "Users can view their own store's product variations" 
ON product_variations 
FOR SELECT 
USING (
  product_id IN (
    SELECT p.id FROM products p
    JOIN stores s ON p.store_id = s.id
    JOIN profiles prof ON s.user_id = prof.id
    WHERE prof.id = auth.uid()
  )
);

CREATE POLICY "Users can insert product variations for their own store's products" 
ON product_variations 
FOR INSERT 
WITH CHECK (
  product_id IN (
    SELECT p.id FROM products p
    JOIN stores s ON p.store_id = s.id
    JOIN profiles prof ON s.user_id = prof.id
    WHERE prof.id = auth.uid()
  )
);

CREATE POLICY "Users can update their own store's product variations" 
ON product_variations 
FOR UPDATE 
USING (
  product_id IN (
    SELECT p.id FROM products p
    JOIN stores s ON p.store_id = s.id
    JOIN profiles prof ON s.user_id = prof.id
    WHERE prof.id = auth.uid()
  )
);

CREATE POLICY "Users can delete their own store's product variations" 
ON product_variations 
FOR DELETE 
USING (
  product_id IN (
    SELECT p.id FROM products p
    JOIN stores s ON p.store_id = s.id
    JOIN profiles prof ON s.user_id = prof.id
    WHERE prof.id = auth.uid()
  )
);

-- Adiciona as políticas RLS para a tabela de opções de variação
ALTER TABLE variation_options ENABLE ROW LEVEL SECURITY;

-- Política para permitir que o dono da loja possa ver, criar, atualizar e excluir suas opções de variação
CREATE POLICY "Users can view their own store's variation options" 
ON variation_options 
FOR SELECT 
USING (
  variation_id IN (
    SELECT pv.id FROM product_variations pv
    JOIN products p ON pv.product_id = p.id
    JOIN stores s ON p.store_id = s.id
    JOIN profiles prof ON s.user_id = prof.id
    WHERE prof.id = auth.uid()
  )
);

CREATE POLICY "Users can insert variation options for their own store's products" 
ON variation_options 
FOR INSERT 
WITH CHECK (
  variation_id IN (
    SELECT pv.id FROM product_variations pv
    JOIN products p ON pv.product_id = p.id
    JOIN stores s ON p.store_id = s.id
    JOIN profiles prof ON s.user_id = prof.id
    WHERE prof.id = auth.uid()
  )
);

CREATE POLICY "Users can update their own store's variation options" 
ON variation_options 
FOR UPDATE 
USING (
  variation_id IN (
    SELECT pv.id FROM product_variations pv
    JOIN products p ON pv.product_id = p.id
    JOIN stores s ON p.store_id = s.id
    JOIN profiles prof ON s.user_id = prof.id
    WHERE prof.id = auth.uid()
  )
);

CREATE POLICY "Users can delete their own store's variation options" 
ON variation_options 
FOR DELETE 
USING (
  variation_id IN (
    SELECT pv.id FROM product_variations pv
    JOIN products p ON pv.product_id = p.id
    JOIN stores s ON p.store_id = s.id
    JOIN profiles prof ON s.user_id = prof.id
    WHERE prof.id = auth.uid()
  )
);

-- Atualiza a tabela de itens de pedido para incluir o campo de opções selecionadas
ALTER TABLE order_items ADD COLUMN IF NOT EXISTS selected_options JSONB DEFAULT '[]';