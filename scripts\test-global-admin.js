import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Carregar variáveis de ambiente
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Erro: VITE_SUPABASE_URL ou VITE_SUPABASE_SERVICE_KEY não definidos.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function testGlobalAdminSetup() {
  console.log('🧪 Testando configuração do Dashboard Global Admin\n');

  try {
    // 1. Verificar se a coluna is_global_admin existe
    console.log('1️⃣ Verificando estrutura da tabela users...');
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, email, is_global_admin')
      .limit(1);

    if (usersError) {
      console.error('❌ Erro ao acessar tabela users:', usersError.message);
      if (usersError.message.includes('is_global_admin')) {
        console.log('💡 Execute: npm run setup:global-admin');
      }
      return;
    }

    console.log('✅ Tabela users acessível com campo is_global_admin');

    // 2. Verificar se existem usuários
    const { data: allUsers, error: allUsersError } = await supabase
      .from('users')
      .select('id, email, full_name, is_global_admin, created_at')
      .order('created_at', { ascending: false });

    if (allUsersError) {
      console.error('❌ Erro ao buscar usuários:', allUsersError.message);
      return;
    }

    console.log(`\n2️⃣ Usuários encontrados: ${allUsers.length}`);
    
    if (allUsers.length === 0) {
      console.log('⚠️ Nenhum usuário encontrado. Crie uma conta primeiro.');
      return;
    }

    // Mostrar usuários
    console.log('\n📋 Lista de usuários:');
    allUsers.forEach((user, index) => {
      const adminStatus = user.is_global_admin ? '👑 Super Admin' : '👤 Usuário';
      console.log(`${index + 1}. ${user.email} - ${adminStatus}`);
    });

    // 3. Verificar super-admins
    const globalAdmins = allUsers.filter(user => user.is_global_admin);
    console.log(`\n3️⃣ Super-administradores: ${globalAdmins.length}`);

    if (globalAdmins.length === 0) {
      console.log('⚠️ Nenhum super-administrador configurado.');
      console.log('💡 Para promover um usuário: npm run promote:global-admin <user_id>');
      console.log(`💡 Exemplo: npm run promote:global-admin ${allUsers[0].id}`);
    } else {
      console.log('✅ Super-administradores configurados:');
      globalAdmins.forEach(admin => {
        console.log(`   • ${admin.email} (ID: ${admin.id})`);
      });
    }

    // 4. Verificar lojas
    console.log('\n4️⃣ Verificando lojas...');
    const { data: stores, error: storesError } = await supabase
      .from('stores')
      .select('id, name, slug, user_id, created_at')
      .limit(5);

    if (storesError) {
      console.error('❌ Erro ao acessar lojas:', storesError.message);
    } else {
      console.log(`✅ ${stores.length} lojas encontradas`);
      if (stores.length > 0) {
        stores.forEach(store => {
          console.log(`   • ${store.name} (/${store.slug})`);
        });
      }
    }

    // 5. Verificar pedidos
    console.log('\n5️⃣ Verificando pedidos...');
    const { data: orders, error: ordersError } = await supabase
      .from('orders')
      .select('id, store_id, total, created_at')
      .limit(5);

    if (ordersError) {
      console.error('❌ Erro ao acessar pedidos:', ordersError.message);
    } else {
      console.log(`✅ ${orders.length} pedidos encontrados`);
    }

    // 6. Verificar assinaturas
    console.log('\n6️⃣ Verificando assinaturas...');
    const { data: subscriptions, error: subscriptionsError } = await supabase
      .from('subscriptions')
      .select('id, store_id, plan_type, status')
      .limit(5);

    if (subscriptionsError) {
      console.error('❌ Erro ao acessar assinaturas:', subscriptionsError.message);
    } else {
      console.log(`✅ ${subscriptions.length} assinaturas encontradas`);
      if (subscriptions.length > 0) {
        const planCounts = subscriptions.reduce((acc, sub) => {
          acc[sub.plan_type] = (acc[sub.plan_type] || 0) + 1;
          return acc;
        }, {});
        console.log('   Distribuição:', planCounts);
      }
    }

    // 7. Teste de API (se houver super-admin)
    if (globalAdmins.length > 0) {
      console.log('\n7️⃣ Testando APIs do Dashboard Global...');
      
      // Simular uma requisição para analytics
      try {
        const response = await fetch('http://localhost:5000/api/admin/global/analytics', {
          headers: {
            'Authorization': `Bearer fake-token-for-test`,
            'Content-Type': 'application/json'
          }
        });

        if (response.status === 401) {
          console.log('✅ API protegida corretamente (401 Unauthorized)');
        } else {
          console.log(`⚠️ Status inesperado da API: ${response.status}`);
        }
      } catch (error) {
        console.log('⚠️ Servidor não está rodando ou API não acessível');
        console.log('💡 Execute: npm run dev');
      }
    }

    // Resumo final
    console.log('\n📊 RESUMO:');
    console.log(`   • Usuários: ${allUsers.length}`);
    console.log(`   • Super-admins: ${globalAdmins.length}`);
    console.log(`   • Lojas: ${stores?.length || 0}`);
    console.log(`   • Pedidos: ${orders?.length || 0}`);
    console.log(`   • Assinaturas: ${subscriptions?.length || 0}`);

    if (globalAdmins.length > 0) {
      console.log('\n🎉 Dashboard Global Admin está pronto para uso!');
      console.log('🌐 Acesse: http://localhost:5000/admin/global');
    } else {
      console.log('\n⚠️ Configure um super-administrador para usar o Dashboard Global');
      console.log('💡 Execute: npm run promote:global-admin <user_id>');
    }

  } catch (error) {
    console.error('❌ Erro inesperado:', error);
  }
}

// Executar teste
testGlobalAdminSetup();
