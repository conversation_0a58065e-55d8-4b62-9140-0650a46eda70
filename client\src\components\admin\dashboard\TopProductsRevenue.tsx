import { Card } from "@/components/ui/card";
import { Crown, TrendingUp } from "lucide-react";

interface TopProductsRevenueProps {
  products: Array<{
    name: string;
    revenue: number;
    quantity: number;
    avgPrice: number;
  }>;
}

export function TopProductsRevenue({ products }: TopProductsRevenueProps) {
  return (
    <Card className="p-6">
      <div className="flex items-center gap-2 mb-4">
        <Crown className="h-5 w-5 text-yellow-600" />
        <h3 className="text-lg font-semibold">Top 5 Produtos Mais Lucrativos</h3>
      </div>
      
      {products.length === 0 ? (
        <p className="text-gray-500 text-center py-8">Nenhum produto vendido ainda</p>
      ) : (
        <div className="space-y-4">
          {products.map((product, index) => (
            <div key={product.name} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-bold ${
                  index === 0 ? 'bg-yellow-500 text-white' :
                  index === 1 ? 'bg-gray-400 text-white' :
                  index === 2 ? 'bg-orange-500 text-white' :
                  'bg-blue-500 text-white'
                }`}>
                  {index + 1}
                </div>
                <div>
                  <p className="font-medium text-sm">{product.name}</p>
                  <div className="flex items-center gap-3 text-xs text-gray-600">
                    <span>{product.quantity} vendidos</span>
                    <span>•</span>
                    <span>Média: R$ {product.avgPrice.toFixed(2)}</span>
                  </div>
                </div>
              </div>
              
              <div className="text-right">
                <div className="flex items-center gap-1">
                  <TrendingUp className="h-4 w-4 text-green-600" />
                  <span className="font-semibold text-green-600">
                    R$ {product.revenue.toFixed(2)}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </Card>
  );
}