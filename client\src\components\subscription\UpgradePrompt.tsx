import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Crown, AlertTriangle, X, Zap, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { useSubscription } from '@/context/SubscriptionContext';
import { PLAN_CONFIGS } from '@shared/schema';
import { cn } from '@/lib/utils';

interface UpgradePromptProps {
  feature?: string;
  title?: string;
  description?: string;
  onClose?: () => void;
  variant?: 'card' | 'alert' | 'modal';
  showFeatures?: boolean;
}

export function UpgradePrompt({ 
  feature,
  title,
  description,
  onClose,
  variant = 'card',
  showFeatures = true
}: UpgradePromptProps) {
  const { t } = useTranslation();
  const { createCheckoutSession, planConfig } = useSubscription();
  const [isLoading, setIsLoading] = useState(false);

  const handleUpgrade = async () => {
    setIsLoading(true);
    try {
      const checkoutUrl = await createCheckoutSession();
      if (checkoutUrl) {
        window.location.href = checkoutUrl;
      }
    } catch (error) {
      console.error('Erro ao criar checkout:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const premiumPlan = PLAN_CONFIGS.premium;
  const currentPlan = planConfig || PLAN_CONFIGS.free;

  const defaultTitle = title || t('subscription.upgrade_required');
  const defaultDescription = description || t('subscription.upgrade_description');

  if (variant === 'alert') {
    return (
      <Alert className="border-yellow-200 bg-yellow-50">
        <AlertTriangle className="h-4 w-4 text-yellow-600" />
        <AlertDescription className="flex items-center justify-between">
          <div>
            <span className="font-medium text-yellow-800">{defaultTitle}</span>
            <p className="text-yellow-700 text-sm mt-1">{defaultDescription}</p>
          </div>
          <Button 
            size="sm" 
            onClick={handleUpgrade}
            disabled={isLoading}
            className="ml-4 bg-yellow-600 hover:bg-yellow-700"
          >
            {isLoading ? (
              <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
            ) : (
              <>
                <Crown className="w-3 h-3 mr-1" />
                {t('subscription.upgrade')}
              </>
            )}
          </Button>
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Card className="border-yellow-200 bg-gradient-to-br from-yellow-50 to-orange-50">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="p-2 bg-yellow-100 rounded-full">
              <Crown className="w-5 h-5 text-yellow-600" />
            </div>
            <div>
              <CardTitle className="text-lg text-yellow-800">
                {defaultTitle}
              </CardTitle>
              <CardDescription className="text-yellow-700">
                {defaultDescription}
              </CardDescription>
            </div>
          </div>
          {onClose && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-yellow-600 hover:text-yellow-800"
            >
              <X className="w-4 h-4" />
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Comparação de planos */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="text-xs">
                {t('subscription.current')}
              </Badge>
              <span className="font-medium text-sm">{currentPlan.name}</span>
            </div>
            <div className="text-2xl font-bold text-gray-600">
              {currentPlan.price === 0 ? t('subscription.free') : `R$ ${currentPlan.price}`}
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Badge className="text-xs bg-yellow-600">
                {t('subscription.premium')}
              </Badge>
              <span className="font-medium text-sm">{premiumPlan.name}</span>
            </div>
            <div className="text-2xl font-bold text-yellow-700">
              R$ {premiumPlan.price}
              <span className="text-sm font-normal text-yellow-600">/{t('subscription.month')}</span>
            </div>
          </div>
        </div>

        {/* Recursos premium */}
        {showFeatures && (
          <div className="space-y-3">
            <h4 className="font-medium text-yellow-800 flex items-center gap-2">
              <Zap className="w-4 h-4" />
              {t('subscription.premium_features')}
            </h4>
            <div className="grid gap-2">
              {premiumPlan.features.slice(0, 4).map((feature, index) => (
                <div key={index} className="flex items-center gap-2 text-sm text-yellow-700">
                  <ArrowRight className="w-3 h-3 text-yellow-500" />
                  {feature}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Trial info */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-3">
          <div className="flex items-center gap-2 text-green-700">
            <Zap className="w-4 h-4" />
            <span className="font-medium text-sm">
              {t('subscription.trial_offer')}
            </span>
          </div>
          <p className="text-green-600 text-xs mt-1">
            {t('subscription.trial_description')}
          </p>
        </div>

        {/* Botão de upgrade */}
        <Button
          onClick={handleUpgrade}
          disabled={isLoading}
          className="w-full bg-gradient-to-r from-yellow-600 to-orange-600 hover:from-yellow-700 hover:to-orange-700 text-white"
          size="lg"
        >
          {isLoading ? (
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
              {t('subscription.processing')}
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <Crown className="w-4 h-4" />
              {t('subscription.start_trial')}
            </div>
          )}
        </Button>
      </CardContent>
    </Card>
  );
}

// Componente para bloqueio de funcionalidade
export function FeatureBlock({ 
  feature, 
  children 
}: { 
  feature: keyof typeof PLAN_CONFIGS.free.limits;
  children: React.ReactNode;
}) {
  const { isFeatureAvailable } = useSubscription();
  const { t } = useTranslation();

  const available = isFeatureAvailable(feature);

  if (available) {
    return <>{children}</>;
  }

  return (
    <div className="relative">
      <div className="opacity-50 pointer-events-none">
        {children}
      </div>
      <div className="absolute inset-0 flex items-center justify-center bg-white/80 backdrop-blur-sm rounded-lg">
        <UpgradePrompt 
          variant="card"
          title={t(`subscription.feature_blocked.${feature}.title`)}
          description={t(`subscription.feature_blocked.${feature}.description`)}
          showFeatures={false}
        />
      </div>
    </div>
  );
}

// Componente para limite excedido
export function LimitExceededPrompt({ 
  feature,
  current,
  limit
}: {
  feature: 'maxProducts' | 'maxOrdersPerMonth';
  current: number;
  limit: number;
}) {
  const { t } = useTranslation();

  return (
    <UpgradePrompt
      variant="alert"
      title={t(`subscription.limit_exceeded.${feature}.title`)}
      description={t(`subscription.limit_exceeded.${feature}.description`, { current, limit })}
    />
  );
}
