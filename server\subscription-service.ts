import { storage } from './storage';
import { 
  type Subscription, 
  type InsertSubscription, 
  type PlanType, 
  type SubscriptionStatus,
  PLAN_CONFIGS,
  getFeatureLimit,
  isFeatureAvailable,
  isLimitExceeded
} from '@shared/schema';
import { 
  stripe, 
  createStripeCustomer, 
  createCheckoutSession, 
  createCustomerPortalSession,
  cancelSubscription,
  reactivateSubscription,
  getSubscription,
  isStripeConfigured,
  STRIPE_CONFIG
} from './stripe-config';

export class SubscriptionService {
  // Obter assinatura ativa de uma loja
  async getActiveSubscription(storeId: number): Promise<Subscription | null> {
    try {
      const subscriptions = await storage.getSubscriptionsByStoreId(storeId);
      return subscriptions.find(sub => sub.status === 'active') || null;
    } catch (error) {
      console.error('Erro ao obter assinatura ativa:', error);
      return null;
    }
  }

  // Criar assinatura gratuita padrão para nova loja
  async createFreeSubscription(storeId: number): Promise<Subscription | null> {
    try {
      const subscriptionData: InsertSubscription = {
        storeId,
        planType: 'free',
        status: 'active',
      };

      const subscription = await storage.createSubscription(subscriptionData);
      return subscription;
    } catch (error) {
      console.error('Erro ao criar assinatura gratuita:', error);
      return null;
    }
  }

  // Iniciar processo de upgrade para plano premium
  async createPremiumCheckoutSession(
    storeId: number,
    userEmail: string,
    userName: string,
    interval: 'month' | 'year' = 'month'
  ): Promise<string | null> {
    if (!isStripeConfigured()) {
      throw new Error('Stripe não configurado');
    }

    try {
      // Obter ou criar assinatura atual
      let subscription = await this.getActiveSubscription(storeId);
      
      if (!subscription) {
        subscription = await this.createFreeSubscription(storeId);
        if (!subscription) {
          throw new Error('Erro ao criar assinatura');
        }
      }

      // Criar ou obter cliente no Stripe
      let stripeCustomerId = subscription.stripeCustomerId;
      
      if (!stripeCustomerId) {
        const stripeCustomer = await createStripeCustomer(userEmail, userName, storeId);
        if (!stripeCustomer) {
          throw new Error('Erro ao criar cliente no Stripe');
        }
        
        stripeCustomerId = stripeCustomer.id;
        
        // Atualizar assinatura com ID do cliente
        await storage.updateSubscription(subscription.id, {
          stripeCustomerId,
        });
      }

      // Selecionar price ID baseado no intervalo
      const premiumPriceId = interval === 'year'
        ? STRIPE_CONFIG.premiumYearlyPriceId
        : STRIPE_CONFIG.premiumMonthlyPriceId;

      if (!premiumPriceId) {
        throw new Error(`Price ID do plano premium ${interval === 'year' ? 'anual' : 'mensal'} não configurado`);
      }

      const checkoutSession = await createCheckoutSession(
        stripeCustomerId,
        premiumPriceId,
        storeId,
        interval,
        interval === 'month' ? 7 : 0 // Trial apenas para planos mensais
      );

      if (!checkoutSession) {
        throw new Error('Erro ao criar sessão de checkout');
      }

      return checkoutSession.url || null;
    } catch (error) {
      console.error('Erro ao criar sessão de checkout premium:', error);
      throw error;
    }
  }

  // Obter link do Customer Portal
  async getCustomerPortalUrl(storeId: number): Promise<string | null> {
    if (!isStripeConfigured()) {
      throw new Error('Stripe não configurado');
    }

    try {
      const subscription = await this.getActiveSubscription(storeId);
      
      if (!subscription?.stripeCustomerId) {
        throw new Error('Cliente Stripe não encontrado');
      }

      const returnUrl = `${process.env.BASE_URL || 'http://localhost:5000'}/admin/settings`;
      const portalUrl = await createCustomerPortalSession(subscription.stripeCustomerId, returnUrl);
      
      return portalUrl;
    } catch (error) {
      console.error('Erro ao obter URL do Customer Portal:', error);
      throw error;
    }
  }

  // Verificar se uma funcionalidade está disponível
  async isFeatureAvailableForStore(storeId: number, feature: keyof typeof PLAN_CONFIGS.free.limits): Promise<boolean> {
    try {
      const subscription = await this.getActiveSubscription(storeId);
      const planType = subscription?.planType || 'free';
      
      return isFeatureAvailable(planType as PlanType, feature);
    } catch (error) {
      console.error('Erro ao verificar disponibilidade de funcionalidade:', error);
      return false; // Em caso de erro, negar acesso
    }
  }

  // Verificar se um limite foi excedido
  async isLimitExceededForStore(
    storeId: number, 
    feature: 'maxProducts' | 'maxOrdersPerMonth', 
    currentCount: number
  ): Promise<boolean> {
    try {
      const subscription = await this.getActiveSubscription(storeId);
      const planType = subscription?.planType || 'free';
      
      return isLimitExceeded(planType as PlanType, feature, currentCount);
    } catch (error) {
      console.error('Erro ao verificar limite:', error);
      return true; // Em caso de erro, considerar limite excedido
    }
  }

  // Obter contagem atual de produtos
  async getCurrentProductCount(storeId: number): Promise<number> {
    try {
      const products = await storage.getProductsByStoreId(storeId);
      return products.length;
    } catch (error) {
      console.error('Erro ao obter contagem de produtos:', error);
      return 0;
    }
  }

  // Obter contagem de pedidos do mês atual
  async getCurrentMonthOrderCount(storeId: number): Promise<number> {
    try {
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
      
      const orders = await storage.getOrdersByStoreId(storeId);
      const monthOrders = orders.filter(order => {
        const orderDate = new Date(order.createdAt);
        return orderDate >= startOfMonth && orderDate <= endOfMonth;
      });
      
      return monthOrders.length;
    } catch (error) {
      console.error('Erro ao obter contagem de pedidos do mês:', error);
      return 0;
    }
  }

  // Sincronizar assinatura com dados do Stripe
  async syncSubscriptionWithStripe(subscriptionId: string): Promise<void> {
    if (!isStripeConfigured()) {
      console.warn('Stripe não configurado, não é possível sincronizar');
      return;
    }

    try {
      const stripeSubscription = await getSubscription(subscriptionId);
      if (!stripeSubscription) {
        console.error('Assinatura não encontrada no Stripe:', subscriptionId);
        return;
      }

      // Encontrar assinatura local
      const localSubscription = await storage.getSubscriptionByStripeId(subscriptionId);
      if (!localSubscription) {
        console.error('Assinatura local não encontrada:', subscriptionId);
        return;
      }

      // Determinar tipo de plano baseado no price ID
      let planType: PlanType = 'free';
      if (stripeSubscription.items.data.length > 0) {
        const priceId = stripeSubscription.items.data[0].price.id;
        if (priceId === STRIPE_CONFIG.premiumMonthlyPriceId || priceId === STRIPE_CONFIG.premiumYearlyPriceId) {
          planType = 'premium';
        }
      }

      // Mapear status do Stripe para nosso sistema
      const statusMap: Record<string, SubscriptionStatus> = {
        'active': 'active',
        'past_due': 'past_due',
        'canceled': 'canceled',
        'unpaid': 'unpaid',
        'incomplete': 'incomplete',
        'incomplete_expired': 'canceled',
        'trialing': 'active',
      };

      const status = statusMap[stripeSubscription.status] || 'canceled';

      // Atualizar assinatura local
      await storage.updateSubscription(localSubscription.id, {
        planType,
        status,
        currentPeriodStart: new Date(stripeSubscription.current_period_start * 1000),
        currentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),
        trialEnd: stripeSubscription.trial_end ? new Date(stripeSubscription.trial_end * 1000) : null,
        cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end,
      });

      console.log(`Assinatura ${subscriptionId} sincronizada com sucesso`);
    } catch (error) {
      console.error('Erro ao sincronizar assinatura:', error);
    }
  }

  // Obter informações de uso e limites para uma loja
  async getUsageInfo(storeId: number) {
    try {
      const subscription = await this.getActiveSubscription(storeId);
      const planType = subscription?.planType || 'free';
      const planConfig = PLAN_CONFIGS[planType as PlanType];

      const currentProductCount = await this.getCurrentProductCount(storeId);
      const currentOrderCount = await this.getCurrentMonthOrderCount(storeId);

      return {
        subscription,
        planConfig,
        usage: {
          products: {
            current: currentProductCount,
            limit: planConfig.limits.maxProducts,
            isLimitExceeded: isLimitExceeded(planType as PlanType, 'maxProducts', currentProductCount),
          },
          orders: {
            current: currentOrderCount,
            limit: planConfig.limits.maxOrdersPerMonth,
            isLimitExceeded: planConfig.limits.maxOrdersPerMonth !== -1 &&
                             isLimitExceeded(planType as PlanType, 'maxOrdersPerMonth', currentOrderCount),
          },
        },
        features: planConfig.limits,
      };
    } catch (error) {
      console.error('Erro ao obter informações de uso:', error);
      throw error;
    }
  }

  // Buscar detalhes de uma assinatura no Stripe (para admin global)
  async getStripeSubscriptionDetails(stripeSubscriptionId: string): Promise<any> {
    if (!isStripeConfigured() || !stripe) {
      throw new Error('Stripe não configurado');
    }

    try {
      const subscription = await stripe.subscriptions.retrieve(stripeSubscriptionId, {
        expand: ['latest_invoice', 'customer', 'items.data.price']
      });

      return {
        id: subscription.id,
        status: subscription.status,
        currentPeriodStart: new Date(subscription.current_period_start * 1000),
        currentPeriodEnd: new Date(subscription.current_period_end * 1000),
        cancelAtPeriodEnd: subscription.cancel_at_period_end,
        canceledAt: subscription.canceled_at ? new Date(subscription.canceled_at * 1000) : null,
        trialStart: subscription.trial_start ? new Date(subscription.trial_start * 1000) : null,
        trialEnd: subscription.trial_end ? new Date(subscription.trial_end * 1000) : null,
        customer: subscription.customer,
        latestInvoice: subscription.latest_invoice,
        items: subscription.items.data.map(item => ({
          id: item.id,
          priceId: item.price.id,
          productId: item.price.product,
          quantity: item.quantity,
          amount: item.price.unit_amount,
          currency: item.price.currency,
          interval: item.price.recurring?.interval,
          intervalCount: item.price.recurring?.interval_count
        }))
      };
    } catch (error) {
      console.error('Erro ao buscar detalhes da assinatura no Stripe:', error);
      throw error;
    }
  }
}

export const subscriptionService = new SubscriptionService();
