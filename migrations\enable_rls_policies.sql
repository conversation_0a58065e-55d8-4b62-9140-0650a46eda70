-- Enable Row Level Security
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE stores ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE store_visits ENABLE ROW LEVEL SECURITY;

-- Create policies for profiles table
CREATE POLICY "Allow public read access to profiles" 
  ON profiles FOR SELECT 
  USING (true);

CREATE POLICY "Allow authenticated users to insert their own profile" 
  ON profiles FOR INSERT 
  WITH CHECK (auth.uid() = id);

CREATE POLICY "Allow users to update their own profile" 
  ON profiles FOR UPDATE 
  USING (auth.uid() = id);

-- Create policies for users table
CREATE POLICY "Allow public read access to users" 
  ON users FOR SELECT 
  USING (true);

CREATE POLICY "Allow authenticated users to insert users" 
  ON users FOR INSERT 
  WITH CHECK (true);

CREATE POLICY "Allow users to update their own record" 
  ON users FOR UPDATE 
  USING (true);

-- Create policies for stores table
CREATE POLICY "Allow public read access to stores" 
  ON stores FOR SELECT 
  USING (true);

CREATE POLICY "Allow authenticated users to insert their store" 
  ON stores FOR INSERT 
  WITH CHECK (true);

CREATE POLICY "Allow users to update their own store" 
  ON stores FOR UPDATE 
  USING (true);

-- Create policies for all other tables
-- This is a simplified approach, in a production environment you might want more granular control
CREATE POLICY "Allow public read access" 
  ON categories FOR SELECT 
  USING (true);

CREATE POLICY "Allow authenticated users to insert" 
  ON categories FOR INSERT 
  WITH CHECK (true);

CREATE POLICY "Allow authenticated users to update" 
  ON categories FOR UPDATE 
  USING (true);

CREATE POLICY "Allow authenticated users to delete" 
  ON categories FOR DELETE 
  USING (true);

-- Repeat similar policies for products
CREATE POLICY "Allow public read access" 
  ON products FOR SELECT 
  USING (true);

CREATE POLICY "Allow authenticated users to insert" 
  ON products FOR INSERT 
  WITH CHECK (true);

CREATE POLICY "Allow authenticated users to update" 
  ON products FOR UPDATE 
  USING (true);

CREATE POLICY "Allow authenticated users to delete" 
  ON products FOR DELETE 
  USING (true);

-- Repeat similar policies for customers
CREATE POLICY "Allow public read access" 
  ON customers FOR SELECT 
  USING (true);

CREATE POLICY "Allow authenticated users to insert" 
  ON customers FOR INSERT 
  WITH CHECK (true);

CREATE POLICY "Allow authenticated users to update" 
  ON customers FOR UPDATE 
  USING (true);

CREATE POLICY "Allow authenticated users to delete" 
  ON customers FOR DELETE 
  USING (true);

-- Repeat similar policies for orders
CREATE POLICY "Allow public read access" 
  ON orders FOR SELECT 
  USING (true);

CREATE POLICY "Allow authenticated users to insert" 
  ON orders FOR INSERT 
  WITH CHECK (true);

CREATE POLICY "Allow authenticated users to update" 
  ON orders FOR UPDATE 
  USING (true);

CREATE POLICY "Allow authenticated users to delete" 
  ON orders FOR DELETE 
  USING (true);

-- Repeat similar policies for order_items
CREATE POLICY "Allow public read access" 
  ON order_items FOR SELECT 
  USING (true);

CREATE POLICY "Allow authenticated users to insert" 
  ON order_items FOR INSERT 
  WITH CHECK (true);

CREATE POLICY "Allow authenticated users to update" 
  ON order_items FOR UPDATE 
  USING (true);

CREATE POLICY "Allow authenticated users to delete" 
  ON order_items FOR DELETE 
  USING (true);

-- Repeat similar policies for store_visits
CREATE POLICY "Allow public read access" 
  ON store_visits FOR SELECT 
  USING (true);

CREATE POLICY "Allow authenticated users to insert" 
  ON store_visits FOR INSERT 
  WITH CHECK (true);

CREATE POLICY "Allow authenticated users to update" 
  ON store_visits FOR UPDATE 
  USING (true);

CREATE POLICY "Allow authenticated users to delete" 
  ON store_visits FOR DELETE 
  USING (true);