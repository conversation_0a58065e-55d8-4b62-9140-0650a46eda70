import { useState, useEffect } from 'react';
import { useLocation, useParams, useRoute } from 'wouter';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { ArrowLeft, Save, ChevronLeft, ChevronRight, Plus, Minus, Info } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Checkbox } from '@/components/ui/checkbox';
import { useTranslation } from '@/hooks/useTranslation';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { auth } from '@/lib/firebase';
import { useAdminOrder } from '@/context/AdminOrderContext';
import { useStore } from '@/context/StoreContext';

interface VariationOption {
  id: string;
  name: string;
  price: number;
  precoAdicional?: number;
  quantity?: number;
}

interface Variation {
  id: string;
  name: string;
  nomeGrupo?: string;
  required: boolean;
  obrigatorio?: boolean;
  multipleChoice: boolean;
  maxSelections?: number;
  maxSelecionados?: number;
  minSelections?: number;
  minSelecionados?: number;
  options: VariationOption[];
  opcoes?: VariationOption[];
}

type SelectedOption = {
  options: string[];
  required: boolean;
  multipleChoice: boolean;
  quantities: { [optionId: string]: number };
};

type SelectedOptions = {
  [variationId: string]: SelectedOption;
};

interface SelectedVariation {
  variationId: string;
  variationName: string;
  optionId: string;
  optionName: string;
  price: number;
  quantity?: number;
  isCustom?: boolean;
}

interface CustomOption {
  id: string;
  name: string;
  price: number;
  quantity: number;
}

export default function ProductDetailsPage() {
  const params = useParams();
  const { t } = useTranslation();
  const { toast } = useToast();
  const [, setLocation] = useLocation();
  const queryClient = useQueryClient();
  const [match, matchParams] = useRoute('/admin/orders/product-details/:id');
  const { addItem: addToAdminOrder, items, removeItem } = useAdminOrder();
  const { store } = useStore();

  const [quantity, setQuantity] = useState(1);
  const [observation, setObservation] = useState<string>('');
  const [selectedOptions, setSelectedOptions] = useState<SelectedOptions>({});
  const [basePrice, setBasePrice] = useState(0);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [productPrice, setProductPrice] = useState(0);
  const [canSaveItem, setCanSaveItem] = useState(false);
  const [customOptions, setCustomOptions] = useState<CustomOption[]>([]);
  const [newCustomOption, setNewCustomOption] = useState<CustomOption>({
    id: '',
    name: '',
    price: 0,
    quantity: 1
  });

  // Check if we're adding a product to a new order
  const [isNewOrderCreation, setIsNewOrderCreation] = useState(false);

  // Obter IDs do localStorage
  const orderId = localStorage.getItem('currentOrderId');
  const [itemId, setItemId] = useState<string | null>(localStorage.getItem('currentItemId'));
  const revisionId = localStorage.getItem('currentRevisionId');

  // Log para depuração
  console.log('IDs iniciais:', { orderId, itemId, revisionId });

  // Fetch product details
  const { data: product, isLoading: isLoadingProduct } = useQuery({
    queryKey: ['/api/products', params.id],
    enabled: !!params.id,
    refetchOnMount: true, // Forçar refetch sempre que o componente for montado
    staleTime: 0 // Considerar os dados sempre obsoletos
  });

  // Fetch revision item details if editing an existing item
  const { data: itemDetails, isLoading: isLoadingItem } = useQuery({
    queryKey: ['/api/orders/revisions', revisionId, 'items', itemId],
    refetchOnMount: true, // Forçar refetch sempre que o componente for montado
    staleTime: 0, // Considerar os dados sempre obsoletos
    refetchOnWindowFocus: true, // Refetch quando a janela receber foco
    queryFn: async () => {
      console.log(`Buscando detalhes do item ${itemId} da revisão ${revisionId}`);
      try {
        // Função interna para obter o UID do Firebase
        const getUid = async () => {
          if (auth.currentUser && auth.currentUser.uid) {
            return auth.currentUser.uid;
          }
          return '';
        };

        // Buscar os dados do item da revisão diretamente como JSON
        const response = await fetch(`/api/orders/revisions/${revisionId}/items/${itemId}?uid=${await getUid()}`);
        if (!response.ok) {
          throw new Error(`Falha ao buscar detalhes do item: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log('Detalhes do item recebidos (raw):', data);

        // Verificar se é um produto customizado (apenas se o productId for negativo)
        // Produtos do catálogo podem ter opções personalizadas, mas isso não os torna produtos customizados
        const isCustomProduct = data.productId && (typeof data.productId === 'number' && data.productId < 0);

        console.log('Verificando se é um produto customizado:', {
          productId: data.productId,
          isCustomProduct: isCustomProduct,
          item: data
        });

        // Se for um produto customizado real (não do catálogo), redirecionar
        if (isCustomProduct) {
          console.log('Item é um produto customizado real (não do catálogo), redirecionando para a página apropriada');

          // Salvar flag para indicar que é um produto customizado
          localStorage.setItem('isCustomProduct', 'true');

          // Armazenar informações do produto customizado
          if (data.productName) localStorage.setItem('customProductName', data.productName);
          if (data.unitPrice) localStorage.setItem('customProductPrice', String(data.unitPrice));
          if (data.quantity) localStorage.setItem('customProductQuantity', String(data.quantity));
          if (data.observation || data.productDescription) {
            localStorage.setItem('customProductDescription', data.observation || data.productDescription || '');
          }
          if (data.selectedVariations && Array.isArray(data.selectedVariations)) {
            localStorage.setItem('customProductVariations', JSON.stringify(data.selectedVariations));
          }

          // Redirecionar para a página de produto customizado
          setLocation('/admin/orders/custom-product');

          // Retornar dados vazios para evitar erros
          return null;
        }

        // Usar os dados já processados
        let itemData = data;

        // Verificar se as variações selecionadas existem e são um array
        if (!itemData.selectedVariations || !Array.isArray(itemData.selectedVariations)) {
          console.warn('selectedVariations não é um array válido:', itemData.selectedVariations);
          if (typeof itemData.selectedVariations === 'string') {
            try {
              itemData.selectedVariations = JSON.parse(itemData.selectedVariations);
              console.log('Variações convertidas de string para array:', itemData.selectedVariations);
            } catch (e) {
              console.error('Erro ao converter variações de string para array:', e);
              itemData.selectedVariations = [];
            }
          } else {
            itemData.selectedVariations = [];
          }
        }

        // Verificar se temos os campos necessários e logs detalhados
        console.log('Campos no item:', Object.keys(itemData));
        console.log('unitPrice:', itemData.unitPrice);
        console.log('selectedVariations:', itemData.selectedVariations);
        console.log('quantity:', itemData.quantity);
        console.log('observation:', itemData.observation);

        // Normalizar os dados do item para o formato esperado pelo frontend
        return {
          id: itemData.id,
          revisionId: parseInt(revisionId as string),
          productId: itemData.productId,
          quantity: itemData.quantity || 1,
          unitPrice: itemData.unitPrice || 0,
          subtotal: itemData.subtotal || 0,
          observation: itemData.observation || '',
          selectedVariations: itemData.selectedVariations || []
        };
      } catch (error) {
        console.error('Erro ao buscar detalhes do item:', error);
        throw error;
      }
    },
    enabled: !!revisionId && !!itemId
  });

  // Update item mutation
  const updateItemMutation = useMutation({
    mutationFn: async (data: any) => {
      if (itemId && revisionId) {
        // Log detalhado para depuração
        console.log('Iniciando atualização de item:', {
          itemId,
          revisionId,
          data
        });

        // Garantir que as quantidades das variações estejam corretas
        if (data.selectedVariations && Array.isArray(data.selectedVariations)) {
          // Verificar se todas as variações têm quantidades válidas
          data.selectedVariations.forEach((variation: any, index: number) => {
            // Garantir que a quantidade seja pelo menos 1
            if (!variation.quantity || variation.quantity < 1) {
              console.warn(`Corrigindo quantidade da variação ${index} (${variation.variationName} - ${variation.optionName}): ${variation.quantity} -> 1`);
              variation.quantity = 1;
            }

            console.log(`Variação ${index} pronta para envio:`, {
              variationId: variation.variationId,
              variationName: variation.variationName,
              optionId: variation.optionId,
              optionName: variation.optionName,
              price: variation.price,
              quantity: variation.quantity
            });
          });
        }

        // Preparar os dados para envio
        const itemDataToUpdate = {
          ...data,
          id: parseInt(itemId)
        };

        console.log('Enviando dados para atualização:', {
          items: [itemDataToUpdate]
        });

        // Função para obter o uid
        const getUid = async () => {
          if (auth.currentUser && auth.currentUser.uid) {
            return auth.currentUser.uid;
          }
          return '';
        };

        // Enviar o ID do item como parte dos dados em vez de na URL
        const response = await fetch(`/api/orders/revisions/${revisionId}/items?uid=${await getUid()}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            items: [itemDataToUpdate]
          })
        });

        // Verificar resposta
        if (!response.ok) {
          const errorText = await response.text();
          console.error('Resposta de erro da API:', {
            status: response.status,
            statusText: response.statusText,
            body: errorText
          });
          throw new Error(`Falha ao atualizar item: ${response.status} ${response.statusText} - ${errorText}`);
        }

        // Tentar processar a resposta como JSON
        try {
          const jsonResponse = await response.json();
          console.log('Resposta de sucesso da API (JSON):', jsonResponse);
          return jsonResponse;
        } catch (jsonError) {
          console.error('Erro ao processar resposta como JSON:', jsonError);
          const textResponse = await response.text();
          console.log('Resposta como texto:', textResponse);
          throw new Error('Resposta inválida do servidor');
        }
      }
      return Promise.reject(new Error('Item ID or Revision ID is missing'));
    },
    onSuccess: (response) => {
      console.log('Resposta de sucesso da API:', response);

      // Invalidar manualmente o cache para garantir que os novos dados sejam buscados
      if (revisionId && itemId) {
        queryClient.invalidateQueries({ queryKey: ['/api/orders/revisions', revisionId, 'items', itemId] });
        queryClient.invalidateQueries({ queryKey: ['/api/orders/revisions', revisionId] });
      }

      // Limpar flags do localStorage
      localStorage.removeItem('currentItemId');
      localStorage.removeItem('currentRevisionId');
      localStorage.removeItem('addingToRevision');
      localStorage.removeItem('isCustomProduct');

      toast({
        title: t('orders.itemUpdated') || 'Item atualizado',
        description: t('orders.itemUpdatedDesc') || 'O item foi atualizado com sucesso',
      });

      // Go back to order page
      if (orderId) {
        setLocation(`/admin/orders/${orderId}`);
      } else {
        setLocation('/admin/orders');
      }
    },
    onError: (error) => {
      console.error('Erro ao atualizar item:', error);

      toast({
        title: t('common.error') || 'Erro',
        description: String(error),
        variant: 'destructive',
      });
    }
  });



  // Check if we're adding a product to a new order, adding to a revision, or editing an existing item
  useEffect(() => {
    const adminOrderCreation = localStorage.getItem('adminOrderCreationProduct');
    const editingOrderItem = localStorage.getItem('editingOrderItem');
    const addingToRevision = localStorage.getItem('addingToRevision');
    const isCustomProduct = localStorage.getItem('isCustomProduct');

    console.log('Contexto da página de edição de produto:', {
      adminOrderCreation,
      editingOrderItem,
      addingToRevision,
      isCustomProduct,
      revisionId,
      itemId,
      orderId
    });

    // Se for um produto customizado, não devemos estar nesta página
    // Mas produtos do catálogo (com ID positivo) devem continuar aqui, mesmo que tenham isCustomProduct=true no localStorage
    const isCustomizedCatalogProduct = params.id && parseInt(params.id) > 0;

    if (isCustomProduct === 'true' && revisionId && itemId && !isCustomizedCatalogProduct) {
      console.log('ATENÇÃO: Tentando editar produto customizado na página de produtos do catálogo');
      toast({
        title: "Redirecionando...",
        description: "Este produto é personalizado e deve ser editado em outra página.",
        variant: "default",
      });

      // Redirecionar para a página de produto customizado
      setLocation('/admin/orders/custom-product');
      return;
    }

    // Verificar se estamos adicionando a uma revisão
    if (addingToRevision === 'true') {
      console.log('Adicionando produto a uma revisão existente');
      setIsNewOrderCreation(false);

      // Inicializar com o preço base do produto
      if (product) {
        setBasePrice(product.price || 0);
      }
    }
    // Verificar se estamos adicionando a um novo pedido
    else if (adminOrderCreation === 'true') {
      setIsNewOrderCreation(true);

      // Check if we're editing an existing item
      if (editingOrderItem === 'true') {
        const itemId = localStorage.getItem('editingOrderItemId');
        if (itemId) {
          // Get the item from the admin order context
          const itemToEdit = items.find(item => item.id.toString() === itemId);

          if (itemToEdit) {
            console.log('Encontrado item para editar:', itemToEdit);

            // Set initial values from the item
            setQuantity(itemToEdit.quantity || 1);
            setObservation(itemToEdit.observation || '');
            setBasePrice(itemToEdit.price || 0);

            // If the item has variations, process them now
            if (itemToEdit.selectedVariations && itemToEdit.selectedVariations.length > 0) {
              console.log('Item has variations to edit:', itemToEdit.selectedVariations);

              // We need to wait for the product to be loaded before processing variations
              if (product) {
                console.log('Produto já carregado, processando variações do item');
                processSelectedVariationsFromItem(itemToEdit, product);
              }
            }
          }
        }
      }
    }
  }, [items, product, revisionId, itemId, orderId, setLocation, toast]); // Add product as dependency to rerun when product is loaded

  // Função para processar as variações selecionadas de um item do pedido
  const processSelectedVariationsFromItem = (itemData: any, productData: any) => {
    console.log('Processando variações selecionadas do item do pedido');
    console.log('Dados do item:', itemData);
    console.log('Dados do produto:', productData);
    console.log('Contexto:', { isNewOrderCreation, revisionId, itemId });

    const initialOptions: SelectedOptions = {};
    const customOptionsList: CustomOption[] = [];

    // Garantir que temos um array de variações válido no produto
    if (!productData.variations || !Array.isArray(productData.variations)) {
      console.error('Produto não tem variações ou não é um array:', productData.variations);
      return;
    }

    // Garantir que temos um array de variações selecionadas válido no item
    let selectedVariations = itemData.selectedVariations || [];
    if (!Array.isArray(selectedVariations)) {
      console.error('Variações selecionadas não é um array:', selectedVariations);
      return;
    }

    console.log('Variações selecionadas do item:', selectedVariations);

    // Normalizar as variações selecionadas para garantir que todos os campos necessários estejam presentes
    const normalizedVariations = selectedVariations.map((selected: any) => ({
      variationId: selected.variationId || selected.variation_id || selected.variationid || '',
      variationName: selected.variationName || selected.variation_name || selected.variationname || '',
      optionId: selected.optionId || selected.option_id || selected.optionid || '',
      optionName: selected.optionName || selected.option_name || selected.optionname || '',
      price: parseFloat(selected.price) || 0,
      quantity: parseInt(selected.quantity) || 1,
      isCustom: Boolean(selected.isCustom || selected.is_custom || selected.iscustom || false)
    }));

    console.log('Variações normalizadas:', normalizedVariations);

    // Primeiro, inicializar todas as variações do produto
    productData.variations.forEach((variation: Variation) => {
      // Safe identifier for the variation
      const variationId = variation.id || variation.name || '';
      console.log('Inicializando variação do produto:', variationId);

      const quantities: { [optionId: string]: number } = {};

      // Initialize quantities for all options to 0
      const optionsList = variation.opcoes || variation.options;
      if (optionsList) {
        optionsList.forEach(option => {
          quantities[option.id || option.name] = 0;
        });
      }

      initialOptions[variationId] = {
        options: [],
        required: variation.required || variation.obrigatorio || false,
        multipleChoice: variation.multipleChoice || false,
        quantities
      };
    });

    // Depois, processar todas as variações selecionadas do item
    normalizedVariations.forEach((selected: any) => {
      console.log('Processando variação selecionada:', selected);

      // Verificar se é uma opção personalizada (opção custom/adicional, não uma variação do catálogo)
      // CORREÇÃO: verificar se isCustom está presente E variationId é 'custom'
      // Isso permite que variações regulares do produto com isCustom=true sejam tratadas corretamente
      if (selected.variationId === 'custom' && selected.isCustom) {
        console.log('Encontrada opção personalizada adicional:', selected);
        customOptionsList.push({
          id: selected.optionId,
          name: selected.optionName,
          price: selected.price || 0,
          quantity: selected.quantity || 1
        });
      }
      // Se não for opção personalizada adicional, deve ser uma variação regular ou uma opção custom de uma variação regular
      else {
        console.log('Processando variação regular em processSelectedVariationsFromItem:', selected);
        // Verificar se devemos tentar combinar pelo nome de variação em vez de usar o ID
        // Isso acontece quando temos produtos do catálogo com variações customizadas
        const matchById = productData.variations?.some(v => v.id === selected.variationId);
        const variationId = matchById ? selected.variationId :
                          (selected.variationId === 'custom' ? null : selected.variationId);
        const optionId = selected.optionId;

        // Verificar se a variação existe no produto
        if (initialOptions[variationId]) {
          console.log(`Encontrada variação ${variationId} no produto`);

          // Adicionar a opção à lista de opções selecionadas
          if (!initialOptions[variationId].options.includes(optionId)) {
            initialOptions[variationId].options.push(optionId);
          }

          // Definir a quantidade (preservando a quantidade original)
          // IMPORTANTE: Garantir que a quantidade seja pelo menos 1 para visibilidade
          const quantity = Math.max(1, selected.quantity || 1);
          initialOptions[variationId].quantities[optionId] = quantity;

          console.log(`Aplicada quantidade ${quantity} para opção ${optionId} da variação ${variationId}`);

          // Verificar novamente se a opção está na lista de opções selecionadas (garantia dupla)
          if (!initialOptions[variationId].options.includes(optionId)) {
            initialOptions[variationId].options.push(optionId);
            console.log(`Adicionada opção ${optionId} à lista de opções selecionadas da variação ${variationId} (verificação adicional)`);
          }

          // Verificação adicional para garantir que a quantidade foi aplicada corretamente
          console.log(`Verificação de quantidade após aplicação: ${initialOptions[variationId].quantities[optionId]}`);

          // Forçar a quantidade novamente para garantir
          initialOptions[variationId].quantities = {
            ...initialOptions[variationId].quantities,
            [optionId]: quantity
          };
        } else {
          console.warn(`Variação ${variationId} não encontrada no produto`);

          // Tentar encontrar a variação pelo nome
          const matchingVariation = productData.variations.find((v: any) =>
            v.name === selected.variationName || v.nomeGrupo === selected.variationName
          );

          if (matchingVariation) {
            const matchingVariationId = matchingVariation.id || matchingVariation.name;
            console.log(`Encontrada variação pelo nome: ${selected.variationName}, ID: ${matchingVariationId}`);

            // Tentar encontrar a opção pelo nome
            const options = matchingVariation.opcoes || matchingVariation.options || [];
            const matchingOption = options.find((o: any) =>
              o.name === selected.optionName
            );

            if (matchingOption) {
              const matchingOptionId = matchingOption.id || matchingOption.name;
              console.log(`Encontrada opção pelo nome: ${selected.optionName}, ID: ${matchingOptionId}`);

              // Adicionar a opção à lista de opções selecionadas
              if (!initialOptions[matchingVariationId].options.includes(matchingOptionId)) {
                initialOptions[matchingVariationId].options.push(matchingOptionId);
              }

              // Definir a quantidade
              const quantity = Math.max(1, selected.quantity || 1);
              initialOptions[matchingVariationId].quantities[matchingOptionId] = quantity;

              console.log(`Aplicada quantidade ${quantity} para opção ${matchingOptionId} da variação ${matchingVariationId}`);
            }
          }
        }
      }
    });

    console.log('Opções inicializadas:', initialOptions);
    console.log('Opções personalizadas:', customOptionsList);

    // Log detalhado de todas as quantidades para depuração
    console.log('Resumo de quantidades por variação (processSelectedVariationsFromItem):');
    Object.keys(initialOptions).forEach(varId => {
      const variation = initialOptions[varId];
      console.log(`Variação ${varId}:`);
      console.log(`- Opções selecionadas: ${variation.options.join(', ')}`);
      console.log('- Quantidades:');
      Object.keys(variation.quantities).forEach(optId => {
        console.log(`  - ${optId}: ${variation.quantities[optId]}`);

        // Verificar se a opção tem quantidade > 0 mas não está na lista de opções selecionadas
        if (variation.quantities[optId] > 0 && !variation.options.includes(optId)) {
          console.log(`  ⚠️ CORREÇÃO: Opção ${optId} tem quantidade ${variation.quantities[optId]} mas não está na lista de opções selecionadas. Adicionando...`);
          variation.options.push(optId);
        }
      });
    });

    setSelectedOptions(initialOptions);
    setCustomOptions(customOptionsList);
  };

  useEffect(() => {
    if (product) {
      setBasePrice(product.price || 0);
      setProductPrice(product.price || 0);

      // Initialize selected options structure
      if (product.variations && product.variations.length > 0) {
        const initialOptions: SelectedOptions = {};
        product.variations.forEach((variation: Variation) => {
          // Safe identifier for the variation
          const variationId = variation.id || variation.name || '';

          const quantities: { [optionId: string]: number } = {};

          // Initialize quantities for all options to 0
          const optionsList = variation.opcoes || variation.options;
          if (optionsList) {
            optionsList.forEach(option => {
              quantities[option.id || option.name] = 0;
            });
          }

          initialOptions[variationId] = {
            options: [],
            required: variation.required || variation.obrigatorio || false,
            multipleChoice: variation.multipleChoice || false,
            quantities
          };
        });
        setSelectedOptions(initialOptions);
      }

      // Reset the image index
      setCurrentImageIndex(0);
    }
  }, [product]);

  useEffect(() => {
    // Apenas executamos este efeito quando ambos os dados estiverem disponíveis
    if (itemDetails && product) {
      console.log('Processando detalhes do item com produto:', itemDetails);
      console.log('Tipo de itemDetails:', typeof itemDetails);
      console.log('Propriedades de itemDetails:', Object.keys(itemDetails));
      console.log('Produto:', product);

      // Normalizar os dados do item
      const normalizedItem = {
        ...itemDetails,
        quantity: itemDetails.quantity || 1,
        observation: itemDetails.observation || '',
        unitPrice: itemDetails.unitPrice || itemDetails.unit_price || (product?.price || 0),
        selectedVariations: itemDetails.selectedVariations || itemDetails.selected_variations || []
      };

      console.log('Item normalizado:', normalizedItem);
      console.log('Variações selecionadas (JSON):', JSON.stringify(normalizedItem.selectedVariations));

      // Definir os valores nos estados
      setQuantity(normalizedItem.quantity);
      setObservation(normalizedItem.observation);

      // Garantir que o preço base do item seja usado corretamente
      // Verifica valores numéricos válidos para o preço unitário
      const unitPrice = parseFloat(normalizedItem.unitPrice);
      setBasePrice(isNaN(unitPrice) || unitPrice <= 0 ? product?.price || 0 : unitPrice);

      console.log('Preço base definido:', unitPrice, 'valor válido:', !isNaN(unitPrice) && unitPrice > 0);
      console.log('Variações selecionadas:', normalizedItem.selectedVariations);

      // Verificar se o item tem variações selecionadas
      if (normalizedItem.selectedVariations && normalizedItem.selectedVariations.length > 0) {
        // Processar as variações selecionadas
        processSelectedVariations(normalizedItem, product);
      } else {
        console.warn('Nenhuma variação selecionada encontrada no item. Exibindo produto sem variações selecionadas.');
      }
    } else if (!itemDetails && product) {
      // Se não temos detalhes do item mas temos o produto, inicializar com os valores padrão
      console.log('Inicializando com valores padrão do produto');
      setQuantity(1);
      setObservation('');
      setBasePrice(product.price || 0);
    } else {
      console.log('Aguardando dados completos:',
                 itemDetails ? 'itemDetails carregado' : 'itemDetails não carregado',
                 product ? 'produto carregado' : 'produto não carregado');
    }
  }, [itemDetails, product]);

  // Função para processar as variações selecionadas
  const processSelectedVariations = (itemData: any, productData: any) => {
    console.log('Processando variações selecionadas');
    console.log('Dados do item:', itemData);
    console.log('Dados do produto:', productData);
    console.log('Contexto:', { isNewOrderCreation, revisionId, itemId });

    const initialOptions: SelectedOptions = {};
    const customOptionsList: CustomOption[] = [];

    // Garantir que temos um array de variações válido
    if (!productData.variations || !Array.isArray(productData.variations)) {
      console.error('Produto não tem variações ou não é um array:', productData.variations);
      return;
    }

    // Garantir que temos um array de variações selecionadas válido
    let selectedVariations = itemData.selectedVariations || itemData.selected_variations || [];
    if (!Array.isArray(selectedVariations)) {
      try {
        // Tentar interpretar como JSON se for string
        if (typeof selectedVariations === 'string') {
          selectedVariations = JSON.parse(selectedVariations);
        } else {
          selectedVariations = [];
        }
      } catch (e) {
        console.error('Erro ao interpretar variações selecionadas:', e);
        selectedVariations = [];
      }
    }

    console.log('Variações selecionadas normalizadas:', selectedVariations);

    // Para depuração mais detalhada com todos os campos
    selectedVariations.forEach((selected: any, index: number) => {
      console.log(`Variação selecionada ${index} (detalhada):`, JSON.stringify(selected, null, 2));
    });

    // Primeiro, inicializar todas as variações do produto
    productData.variations.forEach((variation: Variation) => {
      // Safe identifier for the variation
      const variationId = variation.id || variation.name || '';
      console.log('Inicializando variação:', variationId);

      const quantities: { [optionId: string]: number } = {};

      // Initialize quantities for all options to 0
      const optionsList = variation.opcoes || variation.options;
      if (optionsList) {
        optionsList.forEach(option => {
          quantities[option.id || option.name] = 0;
        });
      }

      initialOptions[variationId] = {
        options: [],
        required: variation.required || variation.obrigatorio || false,
        multipleChoice: variation.multipleChoice || false,
        quantities
      };
    });

    // Depois, processar todas as variações selecionadas
    selectedVariations.forEach((selected: any) => {
      // Normalizar os campos da variação selecionada com mais campos de fallback
      const normalizedSelected = {
        variationId: selected.variationId || selected.variation_id || selected.variationid || '',
        variationName: selected.variationName || selected.variation_name || selected.variationname || '',
        optionId: selected.optionId || selected.option_id || selected.optionid || '',
        optionName: selected.optionName || selected.option_name || selected.optionname || '',
        price: parseFloat(selected.price) || 0,
        quantity: parseInt(selected.quantity) || 1,
        isCustom: Boolean(selected.isCustom || selected.is_custom || selected.iscustom || false)
      };

      console.log('Processando seleção normalizada:', normalizedSelected);

      // Verificar se é uma opção personalizada (apenas se for 'custom' E isCustom=true)
      if (normalizedSelected.variationId === 'custom' && normalizedSelected.isCustom) {
        console.log('Encontrada opção personalizada:', normalizedSelected);
        customOptionsList.push({
          id: normalizedSelected.optionId,
          name: normalizedSelected.optionName,
          price: normalizedSelected.price,
          quantity: normalizedSelected.quantity
        });
      }
      // Se não for opção personalizada ou se for uma variação regular com isCustom=true
      else if (normalizedSelected.variationId && initialOptions[normalizedSelected.variationId]) {
        console.log('Processando variação regular:', normalizedSelected);
        // Se a variação existe nas opções inicializadas
        console.log('Encontrada opção para variação', normalizedSelected.variationId, ':', normalizedSelected.optionId);

        // Validar se o optionId existe no produto
        const variation = productData.variations.find((v: any) =>
          v.id === normalizedSelected.variationId || v.name === normalizedSelected.variationId
        );

        if (variation) {
          const options = variation.options || variation.opcoes || [];
          const optionExists = options.some((o: any) =>
            o.id === normalizedSelected.optionId || o.name === normalizedSelected.optionId
          );

          if (optionExists) {
            // Certifique-se de que não estamos duplicando opções
            if (!initialOptions[normalizedSelected.variationId].options.includes(normalizedSelected.optionId)) {
              initialOptions[normalizedSelected.variationId].options.push(normalizedSelected.optionId);
            }

            // Set quantity for this option - usar um valor mínimo de 1 para garantir visibilidade
            // IMPORTANTE: Preservar a quantidade original da variação
            const quantity = Math.max(1, normalizedSelected.quantity);
            initialOptions[normalizedSelected.variationId].quantities[normalizedSelected.optionId] = quantity;

            console.log(`Aplicada quantidade ${quantity} para opção ${normalizedSelected.optionId} da variação ${normalizedSelected.variationId}`);

            // Garantir que a opção está na lista de opções selecionadas
            if (!initialOptions[normalizedSelected.variationId].options.includes(normalizedSelected.optionId)) {
              initialOptions[normalizedSelected.variationId].options.push(normalizedSelected.optionId);
              console.log(`Adicionada opção ${normalizedSelected.optionId} à lista de opções selecionadas da variação ${normalizedSelected.variationId}`);
            }

            // Verificação adicional para garantir que a quantidade foi aplicada corretamente
            console.log(`Verificação de quantidade após aplicação: ${initialOptions[normalizedSelected.variationId].quantities[normalizedSelected.optionId]}`);

            // Forçar a quantidade novamente para garantir
            initialOptions[normalizedSelected.variationId].quantities = {
              ...initialOptions[normalizedSelected.variationId].quantities,
              [normalizedSelected.optionId]: quantity
            };
          } else {
            console.warn('Opção não encontrada na variação:', normalizedSelected.optionId);

            // Tentar encontrar opção pelo nome caso ID não seja encontrado
            const matchingOption = options.find((o: any) =>
              o.name === normalizedSelected.optionName
            );

            if (matchingOption) {
              const matchingOptionId = matchingOption.id || matchingOption.name;
              console.log('Encontrada opção pelo nome:', matchingOption.name, 'com ID:', matchingOptionId);

              // Adicione a opção encontrada
              if (!initialOptions[normalizedSelected.variationId].options.includes(matchingOptionId)) {
                initialOptions[normalizedSelected.variationId].options.push(matchingOptionId);
              }

              // Defina a quantidade
              const quantity = Math.max(1, normalizedSelected.quantity);
              initialOptions[normalizedSelected.variationId].quantities[matchingOptionId] = quantity;

              console.log(`Aplicada quantidade ${quantity} para opção ${matchingOptionId} (encontrada por nome)`);
            }
          }
        }
      } else {
        console.warn('Variação não encontrada no produto:', normalizedSelected.variationId);

        // Tentar encontrar por nome
        let found = false;
        for (const variationId in initialOptions) {
          const variation = productData.variations.find((v: Variation) =>
            (v.id === variationId || v.name === variationId) &&
            (v.name === normalizedSelected.variationName || v.nomeGrupo === normalizedSelected.variationName)
          );

          if (variation) {
            console.log('Encontrada variação por nome:', variationId);

            // Tentar encontrar a opção nesta variação
            const options = variation.options || variation.opcoes || [];
            const matchingOption = options.find((o: any) =>
              o.name === normalizedSelected.optionName
            );

            if (matchingOption) {
              const optionId = matchingOption.id || matchingOption.name;

              // Certifique-se de que não estamos duplicando opções
              if (!initialOptions[variationId].options.includes(optionId)) {
                initialOptions[variationId].options.push(optionId);
              }

              // Set quantity for this option - usar um valor mínimo de 1
              const quantity = Math.max(1, normalizedSelected.quantity);
              initialOptions[variationId].quantities[optionId] = quantity;

              console.log(`Aplicada quantidade ${quantity} para opção ${optionId} (encontrada por nome)`);
              found = true;
              break;
            }
          }
        }

        if (!found) {
          console.warn('Não foi possível encontrar uma correspondência para a variação:', normalizedSelected.variationName);
        }
      }
    });

    console.log('Opções inicializadas:', JSON.stringify(initialOptions, null, 2));
    console.log('Opções personalizadas:', customOptionsList);

    // Log detalhado de todas as quantidades para depuração
    console.log('Resumo de quantidades por variação:');
    Object.keys(initialOptions).forEach(varId => {
      const variation = initialOptions[varId];
      console.log(`Variação ${varId}:`);
      console.log(`- Opções selecionadas: ${variation.options.join(', ')}`);
      console.log('- Quantidades:');
      Object.keys(variation.quantities).forEach(optId => {
        console.log(`  - ${optId}: ${variation.quantities[optId]}`);

        // Verificar se a opção tem quantidade > 0 mas não está na lista de opções selecionadas
        if (variation.quantities[optId] > 0 && !variation.options.includes(optId)) {
          console.log(`  ⚠️ CORREÇÃO: Opção ${optId} tem quantidade ${variation.quantities[optId]} mas não está na lista de opções selecionadas. Adicionando...`);
          variation.options.push(optId);
        }
      });
    });

    setSelectedOptions(initialOptions);
    setCustomOptions(customOptionsList);
  };

  // Function to handle option selection
  const handleOptionSelect = (variationId: string, optionId: string) => {
    setSelectedOptions(prev => {
      const variation = { ...prev[variationId] };
      const currentQuantity = variation.quantities[optionId] || 0;

      // If multiple choice, add to list if not already there
      if (variation.multipleChoice) {
        if (!variation.options.includes(optionId)) {
          variation.options = [...variation.options, optionId];
          // Set initial quantity to 1 when selected if it's not already set
          if (currentQuantity === 0) {
            variation.quantities = { ...variation.quantities, [optionId]: 1 };
          }
        } else {
          // If already selected and quantity is 0, set to 1
          if (currentQuantity === 0) {
            variation.quantities = { ...variation.quantities, [optionId]: 1 };
          } else {
            // If already selected and quantity > 0, remove from selection
            variation.options = variation.options.filter(id => id !== optionId);
            variation.quantities = { ...variation.quantities, [optionId]: 0 };
          }
        }
      } else {
        // If not multiple choice, check if already selected
        if (variation.options.includes(optionId) && currentQuantity > 0) {
          // If already selected, deselect it
          variation.options = [];
          variation.quantities = { ...variation.quantities, [optionId]: 0 };
        } else {
          // Otherwise select it and deselect others
          variation.options = [optionId];

          // Reset all quantities to 0
          const resetQuantities = { ...variation.quantities };
          Object.keys(resetQuantities).forEach(key => {
            resetQuantities[key] = 0;
          });

          // Set selected option quantity to 1
          variation.quantities = { ...resetQuantities, [optionId]: 1 };
        }
      }

      return { ...prev, [variationId]: variation };
    });
  };

  // Function to increase quantity for a variation option - no validation for this screen
  const increaseQuantity = (variationId: string, optionId: string) => {
    setSelectedOptions(prev => {
      const variation = { ...prev[variationId] };

      // Increase the quantity
      const newQuantities = { ...variation.quantities };
      const currentQuantity = newQuantities[optionId] || 0;
      newQuantities[optionId] = currentQuantity + 1;

      // Add to options list if not already there
      if (!variation.options.includes(optionId)) {
        // If it's not a multiple choice variation, clear other selections
        if (!variation.multipleChoice) {
          variation.options = [optionId];

          // Reset quantities for other options
          Object.keys(newQuantities).forEach(key => {
            if (key !== optionId) {
              newQuantities[key] = 0;
            }
          });
        } else {
          // For multiple choice, just add to the list
          variation.options = [...variation.options, optionId];
        }
      }

      variation.quantities = newQuantities;

      console.log(`Aumentada quantidade para ${newQuantities[optionId]} na opção ${optionId} da variação ${variationId}`);
      console.log('Estado atual das opções após aumento:', {
        variationId,
        optionId,
        options: variation.options,
        quantities: variation.quantities,
        isNewOrder: isNewOrderCreation
      });

      return { ...prev, [variationId]: variation };
    });
  };

  // Function to decrease quantity for a variation option - no validation for this screen
  const decreaseQuantity = (variationId: string, optionId: string) => {
    setSelectedOptions(prev => {
      const variation = { ...prev[variationId] };
      const currentQuantity = variation.quantities[optionId] || 0;

      if (currentQuantity > 0) {
        const newQuantities = { ...variation.quantities };
        newQuantities[optionId] = currentQuantity - 1;

        // If quantity reached 0, remove from options list
        if (newQuantities[optionId] === 0 && variation.options.includes(optionId)) {
          variation.options = variation.options.filter(id => id !== optionId);
          console.log(`Removida opção ${optionId} da variação ${variationId} por ter quantidade zero`);
        }

        variation.quantities = newQuantities;

        console.log(`Diminuída quantidade para ${newQuantities[optionId]} na opção ${optionId} da variação ${variationId}`);
        console.log('Estado atual das opções após diminuição:', {
          variationId,
          optionId,
          options: variation.options,
          quantities: variation.quantities,
          isNewOrder: isNewOrderCreation
        });
      } else {
        console.log(`Quantidade já é zero para opção ${optionId} da variação ${variationId}`);
      }

      return { ...prev, [variationId]: variation };
    });
  };

  // Function to navigate to next image
  const nextImage = () => {
    if (!product?.images) return;
    setCurrentImageIndex((prev) => (prev + 1) % product.images!.length);
  };

  // Function to navigate to previous image
  const prevImage = () => {
    if (!product?.images) return;
    setCurrentImageIndex((prev) => (prev - 1 + product.images!.length) % product.images!.length);
  };

  // Function to handle adding a new custom option
  const handleAddCustomOption = () => {
    if (!newCustomOption.name.trim()) {
      toast({
        title: t('common.error') || 'Erro',
        description: t('products.customOptionNameRequired') || 'O nome da opção é obrigatório',
        variant: "destructive",
      });
      return;
    }

    // Generate a unique ID for the new option
    // Usando um formato que não depende de timestamp completo para evitar valores muito grandes
    const newOption = {
      ...newCustomOption,
      id: `custom-${Math.floor(Math.random() * 2000000000 + 1)}-${Math.random().toString(36).substring(2, 9)}`
    };

    setCustomOptions(prev => [...prev, newOption]);

    // Reset the form
    setNewCustomOption({
      id: '',
      name: '',
      price: 0,
      quantity: 1
    });
  };

  // Function to handle removing a custom option
  const handleRemoveCustomOption = (id: string) => {
    setCustomOptions(prev => prev.filter(option => option.id !== id));
  };

  // Function to handle updating a custom option quantity
  const handleUpdateCustomOptionQuantity = (id: string, newQuantity: number) => {
    setCustomOptions(prev => prev.map(option =>
      option.id === id ? { ...option, quantity: Math.max(1, newQuantity) } : option
    ));
  };

  // For this screen, we always allow saving regardless of variation requirements
  useEffect(() => {
    // Always allow saving for this screen
    setCanSaveItem(true);
  }, []);

  // Calculate total price based on selected options and quantities
  useEffect(() => {
    if (!product) return;

    let totalPrice = basePrice * quantity;

    // Add prices of selected options based on quantities
    if (product.variations) {
      product.variations.forEach(variation => {
        // Safe identifier for variation
        const variationId = variation.id || variation.name || '';

        if (selectedOptions[variationId]) {
          const selectedIds = selectedOptions[variationId].options;
          const quantities = selectedOptions[variationId].quantities;

          // Usar opcoes ou options, dependendo de qual estiver disponível
          const optionsList = variation.opcoes || variation.options;
          if (optionsList) {
            optionsList.forEach(option => {
              const optionIdentifier = option.id || option.name;
              if (selectedIds.includes(optionIdentifier)) {
                const optionQuantity = quantities[optionIdentifier] || 0;
                // Usar price ou precoAdicional, dependendo de qual estiver disponível
                const additionalPrice = option.precoAdicional || option.price || 0;
                totalPrice += additionalPrice * optionQuantity * quantity;
              }
            });
          }
        }
      });
    }

    // Add prices from custom options - sem multiplicar pela quantidade do produto
    customOptions.forEach(option => {
      // Para opções "outros", não multiplicamos pela quantidade do produto
      totalPrice += option.price * option.quantity;
    });

    setProductPrice(totalPrice);
  }, [product, selectedOptions, quantity, basePrice, customOptions]);

  const mapSelectedVariationsToArray = (): SelectedVariation[] => {
    if (!product) return [];

    const result: SelectedVariation[] = [];

    console.log('Mapeando variações selecionadas para array:', {
      selectedOptions,
      isNewOrderCreation
    });

    // Add regular product variations
    product.variations.forEach((variation: Variation) => {
      // Safe identifier for variation
      const variationId = variation.id || variation.name || '';

      console.log(`Processando variação ${variationId}:`, {
        options: selectedOptions[variationId]?.options || [],
        quantities: selectedOptions[variationId]?.quantities || {}
      });

      if (selectedOptions[variationId]?.options.length > 0) {
        // For each selected option of this variation
        selectedOptions[variationId].options.forEach(optionId => {
          // Use opcoes or options, depending on which is available
          const optionsList = variation.opcoes || variation.options;
          if (!optionsList) return;

          // Find the option by ID or name
          const option = optionsList.find(opt => (opt.id || opt.name) === optionId);
          if (option) {
            // Get the quantity for this option
            const quantity = selectedOptions[variationId].quantities[optionId] || 0;

            console.log(`Opção ${optionId} (${option.name}) da variação ${variationId}:`, {
              quantity,
              price: option.precoAdicional || option.price || 0
            });

            // Only add options with quantity > 0
            if (quantity > 0) {
              const variationItem = {
                variationId: variation.id || variationId,
                variationName: variation.nomeGrupo || variation.name,
                optionId: option.id || option.name,
                optionName: option.name,
                price: option.precoAdicional || option.price || 0,
                quantity: quantity
              };

              result.push(variationItem);
              console.log('Adicionada variação ao resultado:', variationItem);
            } else {
              console.log(`Opção ${optionId} ignorada por ter quantidade zero`);
            }
          }
        });
      }
    });

    // Add custom options as variations
    if (customOptions.length > 0) {
      console.log('Adicionando opções personalizadas:', customOptions);
      customOptions.forEach(option => {
        // Garantir que o ID seja único e consistente
        // Usando um formato que não depende de timestamp completo para evitar valores muito grandes
        const optionId = option.id || `custom-${Math.floor(Math.random() * 2000000000 + 1)}-${Math.random().toString(36).substring(2, 9)}`;

        const customItem = {
          variationId: 'custom',
          variationName: t('products.other') || 'Outros',
          optionId: optionId,
          optionName: option.name,
          price: option.price,
          quantity: option.quantity,
          isCustom: true
        };

        result.push(customItem);
        console.log('Adicionada opção personalizada ao resultado:', customItem);
      });
    }

    console.log('Resultado final do mapeamento:', result);
    return result;
  };

  const handleSaveItem = () => {
    if (!product) return;

    const selectedVariations = mapSelectedVariationsToArray();

    // Log para depuração
    console.log('Mapeamento de variações selecionadas:', selectedVariations);
    console.log('Opções personalizadas:', customOptions);
    console.log('Estado atual das opções selecionadas:', selectedOptions);
    console.log('Contexto de salvamento:', { isNewOrderCreation, revisionId, itemId });

    // Verificar se todas as variações têm quantidades corretas
    selectedVariations.forEach(variation => {
      console.log(`Verificando variação ${variation.variationName}, opção ${variation.optionName}:`, {
        quantidade: variation.quantity,
        preço: variation.price
      });
    });

    // Garantir que as quantidades das variações estejam corretas
    const validatedVariations = selectedVariations.map(variation => {
      // Garantir que a quantidade seja pelo menos 1
      if (!variation.quantity || variation.quantity < 1) {
        console.warn(`Corrigindo quantidade da variação ${variation.variationName} - ${variation.optionName}: ${variation.quantity} -> 1`);
        return { ...variation, quantity: 1 };
      }
      return variation;
    });

    const itemData = {
      productId: product.id,
      productName: product.name,
      productImage: product.images?.[0] || null,
      quantity,
      unitPrice: basePrice,
      subtotal: productPrice,
      selectedVariations: validatedVariations,
      observation
    };

    // Log para depuração
    console.log('Dados do item a serem enviados:', itemData);

    // Verificar se estamos adicionando a uma revisão ou editando um item existente
    const addingToRevision = localStorage.getItem('addingToRevision') === 'true';

    // Verificar se estamos editando um item existente
    // Garantir que itemId seja um valor válido (não null, undefined ou string vazia)
    const isEditingExistingItem = Boolean(itemId && revisionId);

    // Log para depuração detalhada do contexto de salvamento
    console.log('Contexto detalhado de salvamento:', {
      isNewOrderCreation,
      addingToRevision,
      isEditingExistingItem,
      itemId,
      revisionId,
      orderId,
      localStorage: {
        addingToRevision: localStorage.getItem('addingToRevision'),
        currentItemId: localStorage.getItem('currentItemId'),
        currentRevisionId: localStorage.getItem('currentRevisionId'),
        currentOrderId: localStorage.getItem('currentOrderId')
      }
    });

    // Check if we're adding a new product to an order, adding to a revision, or editing an existing item
    if (isEditingExistingItem) {
      console.log('Atualizando item existente:', itemId, 'na revisão:', revisionId);

      // Adicionar o ID do item aos dados para garantir que o endpoint PATCH funcione corretamente
      const itemDataWithId = {
        ...itemData,
        id: parseInt(itemId as string)
      };

      console.log('Dados do item com ID para atualização:', itemDataWithId);
      updateItemMutation.mutate(itemDataWithId);
    }
    else if (isNewOrderCreation) {
      const isEditing = localStorage.getItem('editingOrderItem') === 'true';
      const editingItemId = localStorage.getItem('editingOrderItemId');

      if (isEditing && editingItemId) {
        // First remove the old item
        const itemId = parseInt(editingItemId);
        removeItem(itemId);

        // Then add the updated item
        addToAdminOrder({
          productId: product.id,
          name: product.name,
          price: basePrice,
          image: product.images && product.images.length > 0 ? product.images[0] : undefined,
          quantity,
          selectedVariations: validatedVariations,
          observation: observation.trim() || undefined
        });

        // Show success message
        toast({
          title: t('products.itemUpdated') || 'Item atualizado',
          description: t('products.itemUpdatedDesc') || 'O item foi atualizado com sucesso',
          variant: "success",
        });
      } else {
        // Add new item to admin order context
        addToAdminOrder({
          productId: product.id,
          name: product.name,
          price: basePrice,
          image: product.images && product.images.length > 0 ? product.images[0] : undefined,
          quantity,
          selectedVariations: validatedVariations,
          observation: observation.trim() || undefined
        });

        // Show success message
        toast({
          title: t('products.productAddedToast') || 'Produto adicionado',
          description: t('products.regularProductAddedToOrder') || 'O produto foi adicionado ao pedido',
          variant: "success",
        });
      }

      // Clear localStorage flags
      localStorage.removeItem('adminOrderCreationProduct');
      localStorage.removeItem('currentProductId');
      localStorage.removeItem('editingOrderItem');
      localStorage.removeItem('editingOrderItemId');

      // Navigate back to new order page
      setLocation('/admin/orders/new');
    }
    // Adicionar novo produto a uma revisão existente
    else if (addingToRevision && revisionId) {
      // Criar uma mutation para adicionar um novo item à revisão
      const addItemToRevisionMutation = async () => {
        try {
          // Função para obter o uid
          const getUid = async () => {
            if (auth.currentUser && auth.currentUser.uid) {
              return auth.currentUser.uid;
            }
            return '';
          };

          // Enviar o novo item para a API
          const response = await fetch(`/api/orders/revisions/${revisionId}/items?uid=${await getUid()}`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              item: itemData
            })
          });

          if (!response.ok) {
            throw new Error(`Falha ao adicionar item: ${response.status} ${response.statusText}`);
          }

          const result = await response.json();
          console.log('Resposta da API ao adicionar item:', result);

          // Invalidar consultas para atualizar os dados
          if (revisionId) {
            queryClient.invalidateQueries({ queryKey: [`/api/orders/revisions/${revisionId}`] });
          }
          if (orderId) {
            queryClient.invalidateQueries({ queryKey: [`/api/orders/${orderId}/revisions`] });
          }

          // Mostrar mensagem de sucesso
          toast({
            title: t('products.productAddedToast') || 'Produto adicionado',
            description: t('products.productAddedToRevision') || 'O produto foi adicionado à revisão do pedido',
            variant: "success",
          });

          // Limpar flags do localStorage
          localStorage.removeItem('addingToRevision');
          localStorage.removeItem('currentProductId');
          localStorage.removeItem('currentRevisionId');

          // Navegar de volta para a página de detalhes do pedido
          if (orderId) {
            setLocation(`/admin/orders/${orderId}`);
          } else {
            setLocation('/admin/orders');
          }
        } catch (error) {
          console.error('Erro ao adicionar item à revisão:', error);
          toast({
            title: t('common.error') || 'Erro',
            description: error instanceof Error ? error.message : String(error),
            variant: "destructive",
          });
        }
      };

      // Executar a mutation
      addItemToRevisionMutation();
    }
  };

  const handleGoBack = () => {
    // Verificar se estamos adicionando a uma revisão
    const addingToRevision = localStorage.getItem('addingToRevision') === 'true';

    if (addingToRevision) {
      // Se estamos adicionando a uma revisão, voltar para a página de detalhes do pedido
      localStorage.removeItem('addingToRevision');
      localStorage.removeItem('currentProductId');
      localStorage.removeItem('currentRevisionId');

      if (orderId) {
        setLocation(`/admin/orders/${orderId}`);
      } else {
        // Tentar obter o ID do pedido do localStorage
        const storedOrderId = localStorage.getItem('currentOrderId');
        if (storedOrderId) {
          setLocation(`/admin/orders/${storedOrderId}`);
        } else {
          // Fallback
          setLocation('/admin/orders');
        }
      }
    } else if (isNewOrderCreation) {
      // If we're adding a new product, go back to the new order page
      localStorage.removeItem('adminOrderCreationProduct');
      localStorage.removeItem('currentProductId');
      localStorage.removeItem('editingOrderItem');
      localStorage.removeItem('editingOrderItemId');
      setLocation('/admin/orders/new');
    } else if (orderId) {
      // If we're editing an existing item, go back to the order page
      setLocation(`/admin/orders/${orderId}`);
    } else {
      // Fallback
      setLocation('/admin/orders');
    }
  };

  if (isLoadingProduct || isLoadingItem) {
    return (
      <div className="p-4 flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
          <p>{t('common.loading') || 'Carregando...'}</p>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="p-4">
        <div className="text-center py-8">
          <h2 className="text-xl font-semibold mb-2">{t('products.notFound') || 'Produto não encontrado'}</h2>
          <Button onClick={handleGoBack}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('common.back') || 'Voltar'}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container max-w-4xl pb-20 px-4 sm:px-6">
      <div className="sticky top-0 pt-4 pb-3 bg-background z-10">
        <div className="flex items-center justify-between">
          <Button variant="ghost" size="sm" onClick={handleGoBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t('common.back') || 'Voltar'}
          </Button>

          {/* Botão de salvar - texto diferente dependendo do contexto */}
          {(isNewOrderCreation || localStorage.getItem('addingToRevision') === 'true' || (itemId && revisionId)) && (
            <Button onClick={handleSaveItem} size="sm">
              {itemId && revisionId ? (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  {t('common.save') || 'Salvar'}
                </>
              ) : (
                <>
                  <Plus className="h-4 w-4 mr-2" />
                  {localStorage.getItem('addingToRevision') === 'true'
                    ? (t('products.addToRevision') || 'Adicionar à Revisão')
                    : (t('products.addToOrder') || 'Adicionar ao Pedido')
                  }
                </>
              )}
            </Button>
          )}
        </div>
      </div>

      <div className="space-y-8 pt-2">
        {/* Product header section */}
        <div>
          <h1 className="text-2xl font-bold mb-2">{product.name}</h1>
          {product.description && (
            <p className="text-muted-foreground mb-4">{product.description}</p>
          )}

          {/* Image carousel */}
          {product.images && product.images.length > 0 && (
            <div className="relative w-full h-64 bg-gray-100 rounded overflow-hidden mb-6">
              <img
                src={product.images[currentImageIndex]}
                alt={product.name}
                className="w-full h-full object-cover"
              />

              {product.images.length > 1 && (
                <>
                  <Button
                    variant="secondary"
                    size="icon"
                    className="absolute left-2 top-1/2 transform -translate-y-1/2 rounded-full opacity-70 hover:opacity-100"
                    onClick={prevImage}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="secondary"
                    size="icon"
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 rounded-full opacity-70 hover:opacity-100"
                    onClick={nextImage}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>

                  {/* Image indicators */}
                  <div className="absolute bottom-2 left-0 right-0 flex justify-center space-x-1">
                    {product.images.map((_, index) => (
                      <div
                        key={index}
                        className={`w-2 h-2 rounded-full cursor-pointer ${index === currentImageIndex ? 'bg-primary' : 'bg-gray-300'}`}
                        onClick={() => setCurrentImageIndex(index)}
                      />
                    ))}
                  </div>
                </>
              )}
            </div>
          )}
        </div>

        {/* Price and Quantity section */}
        <div>
          <h2 className="text-lg font-semibold mb-4">{t('products.priceAndQuantity') || 'Preço e Quantidade'}</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
            {/* Price input - colocado primeiro conforme solicitado */}
            <div className="md:col-span-2">
              <label htmlFor="basePrice" className="text-sm font-medium mb-2 block">{t('products.price') || 'Preço'}</label>
              <div>
                <div className="relative">
                  <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                    {store?.currency || 'R$'}
                  </span>
                  <Input
                    id="basePrice"
                    type="number"
                    min="0"
                    step="0.01"
                    value={basePrice || ''}
                    onChange={(e) => {
                      const newValue = parseFloat(e.target.value);
                      setBasePrice(isNaN(newValue) ? 0 : newValue);
                    }}
                    className="text-left pl-10"
                    placeholder={product.price.toString()}
                  />
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {t('products.originalPrice') || 'Preço original'}: {formatCurrency(product.price, store?.currency)}
                </p>
              </div>
            </div>

            {/* Quantity controls - colocado depois do preço */}
            <div>
              <label htmlFor="quantity" className="text-sm font-medium mb-2 block">{t('products.quantity') || 'Quantidade'}</label>
              <div className="flex items-center">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setQuantity(prev => Math.max(1, prev - 1))}
                  disabled={quantity <= 1}
                >
                  <Minus className="h-4 w-4" />
                </Button>
                <div className="w-16 mx-2">
                  <Input
                    id="quantity"
                    type="number"
                    min="1"
                    value={quantity}
                    onChange={(e) => setQuantity(Math.max(1, parseInt(e.target.value) || 1))}
                    className="text-center"
                  />
                </div>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setQuantity(prev => prev + 1)}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Variations section */}
        {product.variations && product.variations.length > 0 && (
          <div className="space-y-6">
            <h2 className="text-lg font-semibold">{t('products.options') || 'Opções'}</h2>

            {product.variations.map((variation: Variation) => {
              // Safe identifier for variation
              const variationId = variation.id || variation.name || '';
              const variationName = variation.nomeGrupo || variation.name;
              const isRequired = variation.required || variation.obrigatorio || false;
              const isMultipleChoice = variation.multipleChoice || false;
              const maxSelections = variation.maxSelections || variation.maxSelecionados || 1;
              const minSelections = variation.minSelections || variation.minSelecionados || 0;

              return (
                <div key={variationId} className="border rounded-md p-4">
                  <div className="flex justify-between mb-3">
                    <Label className="text-base font-medium">
                      {variationName}
                      {isRequired && <span className="text-red-500 ml-1">*</span>}
                    </Label>
                    {maxSelections > 1 && (
                      <span className="text-xs text-muted-foreground">
                        {t('storefront.selectMultiple') || 'Selecione múltiplos'}
                      </span>
                    )}
                  </div>

                  {/* Variation requirements info banner */}
                  <div className="mb-4">
                    <div className="bg-muted/40 p-3 rounded-md flex items-center text-sm">
                      <Info className="h-4 w-4 mr-2 flex-shrink-0" />
                      <div>
                        {isRequired ?
                          (t('storefront.requiredVariation') || 'Seleção obrigatória') :
                          (t('storefront.optionalVariation') || 'Seleção opcional')}

                        {' • '}

                        {isMultipleChoice ?
                          (t('storefront.selectUpTo', { count: maxSelections }) || `Selecione até ${maxSelections}`) :
                          (t('storefront.selectOne') || 'Selecione um')}

                        {minSelections > 0 && isMultipleChoice &&
                          (` • ${t('storefront.selectAtLeast', { count: minSelections }) || `Selecione pelo menos ${minSelections}`}`)}
                      </div>
                    </div>
                  </div>

                  {/* Variation options as list with quantity buttons */}
                  <div className="space-y-2">
                    {(variation.opcoes || variation.options) && (variation.opcoes || variation.options).map((option) => {
                      // Safe identifier for option
                      const optionId = option.id || option.name;

                      const isSelected = selectedOptions[variationId]?.options.includes(optionId);
                      const quantity = selectedOptions[variationId]?.quantities[optionId] || 0;
                      const additionalPrice = option.precoAdicional || option.price || 0;

                      // Log para depuração das quantidades
                      console.log(`Renderizando opção ${option.name} (${optionId}) da variação ${variationName} (${variationId}):`, {
                        isSelected,
                        quantity,
                        directQuantity: selectedOptions[variationId]?.quantities[optionId],
                        selectedOptions: selectedOptions[variationId]?.options || [],
                        allQuantities: selectedOptions[variationId]?.quantities || {}
                      });

                      return (
                        <div
                          key={optionId}
                          className={`border ${isSelected ? 'border-primary bg-primary/5' : 'border-border'} rounded-md p-3 flex justify-between items-center`}
                        >
                          <div className="flex-1">
                            <div className="font-medium">
                              {option.name}
                              {additionalPrice > 0 && (
                                <span className="text-sm font-normal text-muted-foreground ml-2">
                                  + {formatCurrency(additionalPrice, store?.currency)}
                                </span>
                              )}
                            </div>
                          </div>

                          <div className="flex items-center gap-2">
                            {/* Quantity controls - estilo consistente com as opções personalizadas */}
                            <div className="flex items-center border rounded-md overflow-hidden">
                              <Button
                                type="button"
                                size="icon"
                                variant="ghost"
                                className="h-8 w-8 rounded-none"
                                onClick={() => decreaseQuantity(variationId, optionId)}
                                disabled={quantity === 0}
                              >
                                <Minus className="h-3 w-3" />
                              </Button>

                              <span className="w-8 text-center font-medium">
                                {selectedOptions[variationId]?.quantities[optionId] || 0}
                              </span>

                              <Button
                                type="button"
                                size="icon"
                                variant="ghost"
                                className="h-8 w-8 rounded-none"
                                onClick={() => {
                                  if (quantity === 0) {
                                    // If quantity is 0, first select the option
                                    handleOptionSelect(variationId, optionId);
                                  } else {
                                    // Otherwise just increase the quantity
                                    increaseQuantity(variationId, optionId);
                                  }
                                }}
                              >
                                <Plus className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {/* Custom options section */}
        <div className="space-y-6">
          <h2 className="text-lg font-semibold">{t('products.other') || 'Outros'}</h2>
          <div className="border rounded-md p-4">
            <p className="text-sm text-muted-foreground mb-4">
              {t('products.customOptionsDescription') || 'Adicione opções personalizadas como texto livre, valor e quantidade.'}
            </p>

            {/* Form to add new custom option */}
            <div className="space-y-4 mb-6">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                {/* Descrição - ocupa mais espaço */}
                <div className="col-span-1 md:col-span-2">
                  <Label htmlFor="customOptionName" className="mb-2 block text-sm font-medium">
                    {t('products.customOptionName') || 'Descrição'}
                  </Label>
                  <Input
                    id="customOptionName"
                    value={newCustomOption.name}
                    onChange={(e) => setNewCustomOption(prev => ({ ...prev, name: e.target.value }))}
                    placeholder={t('products.customOptionNamePlaceholder') || 'Ex: Decoração especial'}
                    className="w-full"
                  />
                </div>

                {/* Preço - colocado antes da quantidade */}
                <div>
                  <Label htmlFor="customOptionPrice" className="mb-2 block text-sm font-medium">
                    {t('products.price') || 'Preço'}
                  </Label>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                      {store?.currency || 'R$'}
                    </span>
                    <Input
                      id="customOptionPrice"
                      type="number"
                      min="0"
                      step="0.01"
                      value={newCustomOption.price}
                      onChange={(e) => {
                        const value = parseFloat(e.target.value);
                        setNewCustomOption(prev => ({ ...prev, price: isNaN(value) ? 0 : value }));
                      }}
                      className="w-full pl-10"
                    />
                  </div>
                </div>

                {/* Quantidade */}
                <div className="flex items-end">
                  <Button
                    type="button"
                    onClick={handleAddCustomOption}
                    disabled={!newCustomOption.name.trim()}
                    className="w-full"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    {t('products.addCustomOption') || 'Adicionar'}
                  </Button>
                </div>
              </div>
            </div>

            {/* List of added custom options */}
            {customOptions.length > 0 ? (
              <div className="space-y-2">
                {customOptions.map(option => (
                  <div
                    key={option.id}
                    className="border border-border rounded-md p-3 flex flex-wrap md:flex-nowrap justify-between items-center gap-2"
                  >
                    <div className="flex-1 min-w-0">
                      <div className="font-medium truncate">
                        {option.name}
                        <span className="text-sm font-normal text-muted-foreground ml-2">
                          {formatCurrency(option.price, store?.currency)}
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center gap-2 ml-auto">
                      <div className="flex items-center border rounded-md overflow-hidden">
                        <Button
                          type="button"
                          size="icon"
                          variant="ghost"
                          className="h-8 w-8 rounded-none"
                          onClick={() => handleUpdateCustomOptionQuantity(option.id, option.quantity - 1)}
                          disabled={option.quantity <= 1}
                        >
                          <Minus className="h-3 w-3" />
                        </Button>

                        <span className="w-8 text-center">{option.quantity}</span>

                        <Button
                          type="button"
                          size="icon"
                          variant="ghost"
                          className="h-8 w-8 rounded-none"
                          onClick={() => handleUpdateCustomOptionQuantity(option.id, option.quantity + 1)}
                        >
                          <Plus className="h-3 w-3" />
                        </Button>
                      </div>

                      <Button
                        type="button"
                        size="icon"
                        variant="outline"
                        className="h-8 w-8 text-destructive hover:text-destructive hover:bg-destructive/10"
                        onClick={() => handleRemoveCustomOption(option.id)}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4">
                          <path d="M3 6h18"></path>
                          <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                          <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                        </svg>
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4 text-muted-foreground">
                {t('products.noCustomOptions') || 'Nenhuma opção personalizada adicionada'}
              </div>
            )}
          </div>
        </div>

        {/* Price summary - shows base price and additions */}
        <div className="bg-gray-50 p-4 rounded-lg mb-4">
          <h3 className="font-medium text-base mb-2">{t('storefront.priceSummary') || 'Resumo de preço'}</h3>
          <div className="space-y-3 text-sm">
            {/* Preço base e quantidade */}
            <div className="bg-white p-3 rounded-md border border-gray-100">
              <div className="flex justify-between font-medium">
                <span>{t('products.price') || 'Preço'} ({quantity}x)</span>
                <span>{formatCurrency(basePrice * quantity, store?.currency)}</span>
              </div>

              {/* Original price (if different) */}
              {basePrice !== product.price && (
                <div className="flex justify-between text-muted-foreground text-xs mt-1">
                  <span>{t('products.originalPrice') || 'Preço original'}</span>
                  <span className="line-through">{formatCurrency(product.price * quantity, store?.currency)}</span>
                </div>
              )}
            </div>

            {/* Variações selecionadas - agrupadas por tipo de variação */}
            {product.variations && product.variations.some(variation => {
              const variationId = variation.id || variation.name || '';
              const selectedIds = selectedOptions[variationId]?.options || [];
              return selectedIds.length > 0;
            }) && (
              <div className="bg-white p-3 rounded-md border border-gray-100">
                <div className="font-medium mb-2">{t('products.variations') || 'Variações'}</div>

                {product.variations.map(variation => {
                  // Safe identifier for the variation
                  const variationId = variation.id || variation.name || '';
                  const variationName = variation.nomeGrupo || variation.name;

                  // Only include variations with selected options
                  const selectedIds = selectedOptions[variationId]?.options || [];
                  if (!selectedIds.length) return null;

                  const optionsList = variation.opcoes || variation.options;
                  if (!optionsList) return null;

                  // Filtrar apenas opções selecionadas com quantidade > 0
                  const selectedVariationOptions = optionsList
                    .filter(option => {
                      const optionId = option.id || option.name;
                      const optionQuantity = selectedOptions[variationId]?.quantities[optionId] || 0;
                      return selectedIds.includes(optionId) && optionQuantity > 0;
                    });

                  if (selectedVariationOptions.length === 0) return null;

                  return (
                    <div key={variationId} className="mb-2 last:mb-0">
                      <div className="text-muted-foreground text-xs mb-1">{variationName}</div>

                      {optionsList.map(option => {
                        const optionId = option.id || option.name;
                        if (!selectedIds.includes(optionId)) return null;

                        const optionQuantity = selectedOptions[variationId]?.quantities[optionId] || 0;
                        const additionalPrice = option.precoAdicional || option.price || 0;

                        if (optionQuantity <= 0 || additionalPrice <= 0) return null;

                        return (
                          <div key={optionId} className="flex justify-between text-sm">
                            <span>
                              {option.name} ({optionQuantity}x)
                            </span>
                            <span>
                              + {formatCurrency(additionalPrice * optionQuantity * quantity, store?.currency)}
                            </span>
                          </div>
                        );
                      })}
                    </div>
                  );
                })}
              </div>
            )}

            {/* Custom options - em seção separada */}
            {customOptions.length > 0 && (
              <div className="bg-white p-3 rounded-md border border-gray-100">
                <div className="font-medium mb-2">{t('products.other') || 'Outros'}</div>

                {customOptions.map(option => (
                  <div key={option.id} className="flex justify-between text-sm">
                    <span>
                      {option.name} ({option.quantity}x)
                    </span>
                    <span>
                      + {formatCurrency(option.price * option.quantity, store?.currency)}
                    </span>
                  </div>
                ))}
              </div>
            )}

            {/* Total line with divider */}
            <div className="bg-primary/5 p-3 rounded-md border border-primary/10 font-medium flex justify-between">
              <span>{t('storefront.totalPrice') || 'Preço total'}</span>
              <span>{formatCurrency(productPrice, store?.currency)}</span>
            </div>
          </div>
        </div>

        {/* Observation field */}
        <div>
          <Label htmlFor="observation" className="text-lg font-semibold mb-2 block">
            {t('products.observation') || 'Observação'}
          </Label>
          <Textarea
            id="observation"
            placeholder={t('products.addObservation') || 'Adicione uma observação...'}
            value={observation}
            onChange={(e) => setObservation(e.target.value)}
            rows={3}
            className="w-full"
            maxLength={500}
          />
          <div className="flex justify-end mt-1">
            <span className="text-xs text-gray-500">
              {observation.length}/500
            </span>
          </div>
        </div>

        {/* Total section */}
        <div className="sticky bottom-0 bg-background pt-4 pb-6 border-t">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm text-muted-foreground">
                {t('products.total') || 'Total'}
              </div>
              <div className="text-2xl font-bold">
                {formatCurrency(productPrice, store?.currency)}
              </div>
            </div>

            <Button
              size="lg"
              onClick={handleSaveItem}
              disabled={!isNewOrderCreation && updateItemMutation.isPending}
            >
              {isNewOrderCreation ? (
                <>
                  <Plus className="h-4 w-4 mr-2" />
                  {t('products.addToOrder') || 'Adicionar ao Pedido'}
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  {t('common.save') || 'Salvar'}
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}