# Doce Menu Platform

A multilingual e-commerce platform designed for creating and managing online product catalogs with advanced features.

## Project Overview

Doce Menu allows business owners to create custom online stores with unique URLs (app.docemenu.com.br/[store-name]). Each store can manage their products, orders, and customers through an admin dashboard.

## Key Features

- **Multilingual Support**: Available in Portuguese and English
- **Store Management**: Create and customize your online store
- **Product Catalog**: Manage products and categories
- **Order Processing**: Track and manage customer orders
- **Customer Database**: Keep track of your customers
- **Analytics Dashboard**: View monthly visits and sales data
- **Custom Store URLs**: Each store gets a unique URL (app.docemenu.com.br/[store-name])
- **Theme Customization**: Customize colors and branding

## Technology Stack

- **Frontend**: React with Tailwind CSS and shadcn/ui components
- **Backend**: Express (Node.js)
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: Supabase Auth
- **Deployment**: Firebase Hosting
- **i18n**: React-i18next for multilingual support

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- PostgreSQL database
- Supabase account for authentication

### Setup and Installation

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
3. Set up environment variables (see `.env.example`)
4. Start the development server:
   ```
   npm run dev
   ```

## Project Structure

- `/client` - Frontend React application
- `/server` - Backend Express server
- `/shared` - Shared code between frontend and backend
- `/docs` - Project documentation

## Deployment

See the [Firebase Hosting Guide](FIREBASE_HOSTING.md) for complete deployment instructions.

Quick deployment:

1. Ensure you have Firebase CLI installed and are logged in
2. Run the deployment script:
   ```
   ./deploy.sh
   ```

## Environment Variables

The application uses the following environment variables:

- `DATABASE_URL` - PostgreSQL connection string
- `VITE_SUPABASE_URL` - Your Supabase project URL
- `VITE_SUPABASE_ANON_KEY` - Your Supabase anonymous key
- `VITE_APP_BASE_URL` - Base URL for the application (e.g., https://app.docemenu.com.br)

## Documentation

- [Firebase Hosting Guide](FIREBASE_HOSTING.md) - How to deploy to Firebase Hosting
- [Deployment Guide](DEPLOYMENT.md) - General deployment guide

## License

MIT License

## Contact

For questions or support, please contact:
- Email: <EMAIL>