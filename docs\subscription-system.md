# Sistema de Planos de Assinatura - Doce Menu

## Visão Geral

O sistema de assinaturas do Doce Menu permite que os usuários escolham entre dois planos:

### Plano Gratuito (Free)
- **Preço**: Gratuito
- **Produtos**: Até 10 produtos cadastrados
- **Pedidos**: Máximo 5 pedidos por mês
- **Funcionalidades limitadas**:
  - Sem geração de PDFs
  - Sem relatórios/analytics
  - Sem integração WhatsApp
  - Sem sistema de cupons
  - Sem personalização avançada

### Plano Premium (R$ 29,90/mês)
- **Preço**: R$ 29,90 por mês
- **Produtos**: Até 50 produtos cadastrados
- **Pedidos**: Ilimitados
- **Funcionalidades completas**:
  - Geração de PDFs
  - Relatórios e analytics completos
  - Integração WhatsApp
  - Sistema de cupons de desconto
  - Personalização visual avançada
  - **Trial**: 7 dias grátis

## Arquitetura Técnica

### Backend

#### Tabela de Assinaturas
```sql
CREATE TABLE subscriptions (
  id SERIAL PRIMARY KEY,
  store_id INTEGER NOT NULL REFERENCES stores(id),
  stripe_customer_id VARCHAR(255),
  stripe_subscription_id VARCHAR(255),
  plan_type VARCHAR(50) NOT NULL DEFAULT 'free',
  status VARCHAR(50) NOT NULL DEFAULT 'active',
  current_period_start TIMESTAMP WITH TIME ZONE,
  current_period_end TIMESTAMP WITH TIME ZONE,
  trial_end TIMESTAMP WITH TIME ZONE,
  cancel_at_period_end BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Serviços Principais

1. **SubscriptionService** (`server/subscription-service.ts`)
   - Gerencia lógica de assinaturas
   - Verifica limites e funcionalidades
   - Integra com Stripe

2. **Stripe Config** (`server/stripe-config.ts`)
   - Configuração do Stripe
   - Funções para checkout, customer portal, webhooks

3. **Middleware de Verificação** (`server/subscription-middleware.ts`)
   - `requireFeature()`: Verifica se funcionalidade está disponível
   - `checkLimit()`: Verifica limites numéricos
   - `requireActiveSubscription()`: Verifica assinatura ativa

#### Rotas da API

- `GET /api/subscriptions/usage` - Informações de uso
- `POST /api/subscriptions/create-checkout` - Criar sessão de checkout
- `GET /api/subscriptions/portal` - URL do Customer Portal
- `POST /api/subscriptions/webhook` - Webhook do Stripe
- `GET /api/subscriptions/status` - Status da assinatura

### Frontend

#### Contextos React

1. **SubscriptionContext** (`client/src/context/SubscriptionContext.tsx`)
   - Gerencia estado global de assinatura
   - Hooks para verificação de funcionalidades
   - Integração com React Query

#### Componentes

1. **PlanSelector** (`client/src/components/subscription/PlanSelector.tsx`)
   - Seleção de planos com design iOS
   - Indicadores de uso
   - Badges de plano atual

2. **UpgradePrompt** (`client/src/components/subscription/UpgradePrompt.tsx`)
   - Prompts de upgrade
   - Bloqueio de funcionalidades
   - Alertas de limite excedido

3. **SubscriptionSettings** (`client/src/components/subscription/SubscriptionSettings.tsx`)
   - Página de configurações de assinatura
   - Gerenciamento via Customer Portal

#### Hooks Personalizados

```typescript
// Verificar funcionalidade específica
const canGeneratePdf = useFeatureCheck('allowPdfGeneration');

// Verificar limite específico
const { current, limit, exceeded } = useLimitCheck('maxProducts');

// Contexto completo
const { 
  subscription, 
  usageInfo, 
  canAddProduct, 
  createCheckoutSession 
} = useSubscription();
```

## Integração com Stripe

### Configuração Necessária

1. **Variáveis de Ambiente**:
```env
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
STRIPE_PREMIUM_PRICE_ID=price_...
STRIPE_SUCCESS_URL=http://localhost:5000/admin/settings?subscription=success
STRIPE_CANCEL_URL=http://localhost:5000/admin/settings?subscription=canceled
```

2. **Produtos no Stripe Dashboard**:
   - Criar produto "Doce Menu Premium"
   - Definir preço recorrente mensal de R$ 29,90
   - Configurar trial de 7 dias

3. **Webhooks**:
   - URL: `https://your-domain.com/api/subscriptions/webhook`
   - Eventos: `customer.subscription.*`, `invoice.payment_*`

### Fluxo de Pagamento

1. **Upgrade para Premium**:
   ```typescript
   const checkoutUrl = await createCheckoutSession();
   window.location.href = checkoutUrl;
   ```

2. **Webhook de Confirmação**:
   - Stripe envia evento `customer.subscription.created`
   - Sistema sincroniza dados locais
   - Atualiza status da assinatura

3. **Customer Portal**:
   ```typescript
   const portalUrl = await getCustomerPortalUrl();
   window.open(portalUrl, '_blank');
   ```

## Validações e Limitações

### Middleware de Verificação

```typescript
// Verificar limite de produtos antes de criar
app.post("/api/products", requireAuth, checkProductLimit, async (req, res) => {
  // Lógica de criação
});

// Verificar funcionalidade de cupons
app.post("/admin/cupons", requireAuth, requireCoupons, async (req, res) => {
  // Lógica de criação
});
```

### Verificações no Frontend

```typescript
// Bloquear componente se funcionalidade não disponível
<FeatureBlock feature="allowPdfGeneration">
  <PDFGenerator />
</FeatureBlock>

// Mostrar prompt de upgrade quando limite excedido
{isLimitExceeded('maxProducts', currentCount) && (
  <LimitExceededPrompt 
    feature="maxProducts" 
    current={currentCount} 
    limit={limit} 
  />
)}
```

## Estados de Assinatura

- **active**: Assinatura ativa e funcionando
- **past_due**: Pagamento em atraso
- **canceled**: Assinatura cancelada
- **unpaid**: Pagamento não realizado
- **incomplete**: Assinatura incompleta

## Sincronização de Dados

### Webhooks do Stripe

O sistema processa os seguintes eventos:

1. `customer.subscription.created` - Nova assinatura
2. `customer.subscription.updated` - Assinatura atualizada
3. `customer.subscription.deleted` - Assinatura cancelada
4. `invoice.payment_succeeded` - Pagamento bem-sucedido
5. `invoice.payment_failed` - Falha no pagamento

### Fallback Manual

Em caso de falha nos webhooks, o sistema pode sincronizar manualmente:

```typescript
await subscriptionService.syncSubscriptionWithStripe(subscriptionId);
```

## Testes e Desenvolvimento

### Ambiente de Teste

1. Use chaves de teste do Stripe (`sk_test_` e `pk_test_`)
2. Configure webhooks para ambiente local usando ngrok
3. Use cartões de teste do Stripe para simular pagamentos

### Cartões de Teste

- **Sucesso**: 4242 4242 4242 4242
- **Falha**: 4000 0000 0000 0002
- **3D Secure**: 4000 0025 0000 3155

## Monitoramento

### Logs Importantes

- Criação de assinaturas
- Sincronização via webhook
- Verificações de limite
- Erros de pagamento

### Métricas Sugeridas

- Taxa de conversão Free → Premium
- Churn rate
- Uso médio por plano
- Receita recorrente mensal (MRR)

## Troubleshooting

### Problemas Comuns

1. **Webhook não recebido**:
   - Verificar URL e assinatura
   - Checar logs do Stripe Dashboard

2. **Assinatura não sincronizada**:
   - Executar sincronização manual
   - Verificar dados no Stripe

3. **Limite não aplicado**:
   - Verificar middleware nas rotas
   - Confirmar dados de assinatura

### Comandos Úteis

```bash
# Verificar status de assinatura
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:5000/api/subscriptions/status

# Forçar sincronização
curl -X POST -H "Authorization: Bearer $TOKEN" \
  http://localhost:5000/api/subscriptions/sync/$SUBSCRIPTION_ID
```
