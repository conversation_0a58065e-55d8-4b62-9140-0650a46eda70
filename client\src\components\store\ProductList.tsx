import { useState, useMemo, useRef, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useLocation } from 'wouter';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, X, Navigation } from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import ProductCard, { Product } from '@/components/store/ProductCard';
import { formatCurrency } from '@/lib/utils'; // Added import
import { openStoreLocation, hasValidAddress } from '@/lib/locationUtils';

// Função para ajustar o brilho de uma cor hexadecimal
function adjustColorBrightness(hexColor: string, percent: number): string {
  // Verifica se o hexColor está no formato correto
  if (!hexColor || hexColor.indexOf('#') !== 0) {
    return hexColor;
  }

  // Remove o # e converte para o formato longo se for curto
  let hex = hexColor.slice(1);
  if (hex.length === 3) {
    hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
  }

  // Converte hex para RGB
  const r = parseInt(hex.slice(0, 2), 16);
  const g = parseInt(hex.slice(2, 4), 16);
  const b = parseInt(hex.slice(4, 6), 16);

  // Ajusta o brilho
  const adjustBrightness = (color: number) => {
    return Math.max(0, Math.min(255, Math.round(color * (1 + percent / 100))));
  };

  const newR = adjustBrightness(r);
  const newG = adjustBrightness(g);
  const newB = adjustBrightness(b);

  // Converte RGB de volta para hex
  const toHex = (c: number) => {
    const hex = c.toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  };

  return `#${toHex(newR)}${toHex(newG)}${toHex(newB)}`;
}

interface ProductListProps {
  storeSlug: string;
  isHeaderVisible?: boolean;
  headerHeight?: number;
}

export function ProductList({ storeSlug, isHeaderVisible = true, headerHeight = 0 }: ProductListProps) {
  const { t } = useTranslation();
  const [, navigate] = useLocation();
  const [searchQuery, setSearchQuery] = useState('');
  const [activeCategory, setActiveCategory] = useState<string | null>(null);
  const [showCategoryGroups, setShowCategoryGroups] = useState(true);
  const [showSearch, setShowSearch] = useState(false);

  // Fetch products
  const { data: products, isLoading: isLoadingProducts } = useQuery({
    queryKey: [`/api/public/stores/${storeSlug}/products`],
  });

  // Fetch categories
  const { data: categories, isLoading: isLoadingCategories } = useQuery({
    queryKey: [`/api/public/stores/${storeSlug}/categories`],
  });

  // Handler for product click - redirects to product detail page
  const handleProductClick = (product: Product) => {
    navigate(`/${storeSlug}/product/${product.id}`);
  };

  // Handler for opening store location
  const handleOpenLocation = () => {
    if (store && hasValidAddress(store)) {
      openStoreLocation(store);
    }
  };

  // Fetch store data to check layout and get currency
  const { data: store } = useQuery({
    queryKey: [`/api/public/stores/${storeSlug}`],
  });

  // Verificar qual layout está ativo (1 = Grid, 2 = List)
  const isListLayout = store?.layout === 2; // Layout tipo 2 é o layout em lista
  console.log('Layout da loja:', store?.layout, 'isListLayout:', isListLayout);

  // Mapear os produtos da API para o formato esperado pelo componente
  const mappedProducts = products
    ? products.map((product: any) => ({
        ...product,
        hasVariations: product.hasVariations || false, // Garantir que hasVariations exista
        variations: product.variations || []
      }))
    : [];

  // Filter products by category and search query
  const filteredProducts = mappedProducts.filter((product: Product) => {
    const matchesCategory = activeCategory === null ||
      (product.categoryId && product.categoryId.toString() === activeCategory);

    const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (product.description && product.description.toLowerCase().includes(searchQuery.toLowerCase()));

    return matchesCategory && matchesSearch;
  });

  // Organizar produtos por categorias visíveis quando não está filtrando
  const visibleCategories = useMemo(() => {
    if (!categories) return [];
    return categories
      .filter((category: any) => category.visible)
      .sort((a: any, b: any) => a.displayOrder - b.displayOrder);
  }, [categories]);

  // Agrupar produtos por categoria
  const productsByCategory = useMemo(() => {
    if (!visibleCategories.length || !mappedProducts.length) return [];

    return visibleCategories.map((category: any) => {
      // Se uma categoria está selecionada, filtrar apenas produtos dessa categoria
      const categoryProducts = activeCategory === null ?
        mappedProducts.filter((product: any) => product.categoryId === category.id) :
        mappedProducts.filter((product: any) =>
          product.categoryId === category.id &&
          product.categoryId.toString() === activeCategory
        );

      return {
        category,
        products: categoryProducts
      };
    }).filter(group => activeCategory === null || group.category.id.toString() === activeCategory || group.products.length > 0); // Mostrar categoria vazia se for a selecionada
  }, [visibleCategories, mappedProducts, activeCategory]);

  // Referência para o container de categorias para calcular sua altura
  const categoriesRef = useRef<HTMLDivElement>(null);
  const [categoriesHeight, setCategoriesHeight] = useState(0);

  // Atualiza a altura do container de categorias quando ele muda
  useEffect(() => {
    if (categoriesRef.current) {
      setCategoriesHeight(categoriesRef.current.offsetHeight);
    }
  }, [categories, isHeaderVisible]);

  return (
    <div>


      {/* Botão Mais Informações */}
      <div className="mb-4 flex justify-end">
        <Button
          variant="outline"
          size="sm"
          onClick={() => navigate(`/${storeSlug}/info`)}
          className="text-xs px-3 py-1 h-7"
        >
          {t('storefront.moreInfo')}
        </Button>
      </div>

      {/* Categories Dropdown e Search - na mesma linha responsiva */}
      <div
        ref={categoriesRef}
        data-categories-section
        className={`mb-8 ${!isHeaderVisible ? 'fixed top-0 left-0 right-0 z-40 bg-white shadow-md pt-4 pb-2 px-4' : ''} transition-all duration-300`}
        style={{
          position: !isHeaderVisible ? 'fixed' : 'relative',
          top: !isHeaderVisible ? '0' : 'auto',
          left: !isHeaderVisible ? '0' : 'auto',
          right: !isHeaderVisible ? '0' : 'auto',
          zIndex: !isHeaderVisible ? 40 : 'auto',
          backgroundColor: !isHeaderVisible ? 'white' : 'transparent',
          boxShadow: !isHeaderVisible ? '0 2px 4px rgba(0,0,0,0.1)' : 'none',
          paddingTop: !isHeaderVisible ? '1rem' : '0',
          paddingBottom: !isHeaderVisible ? '0.5rem' : '0',
          paddingLeft: !isHeaderVisible ? '1rem' : '0',
          paddingRight: !isHeaderVisible ? '1rem' : '0',
          transform: isHeaderVisible ? 'translateY(0)' : `translateY(0px)`,
          transition: 'all 0.3s ease-in-out'
        }}
      >
        {/* Mobile Layout */}
        <div className="sm:hidden w-full space-y-4">
          {!showSearch ? (
            /* Categories Dropdown e Search Icon - mesma linha no mobile quando busca está fechada */
            <div className="flex gap-3 items-end">
              {/* Categories Dropdown */}
              <div className="flex-1">
                <label htmlFor="category-select-mobile" className="block text-sm font-medium text-gray-700 mb-2">

                </label>
                <select
                  id="category-select-mobile"
                  value={activeCategory || ''}
                  onChange={(e) => setActiveCategory(e.target.value || null)}
                  className="w-full px-4 py-3 text-sm border border-gray-200 rounded-xl bg-white text-gray-700 focus:outline-none focus:ring-2 focus:border-transparent shadow-sm transition-all duration-200"
                  style={{
                    focusRingColor: store?.colors?.primary || '#2ECC71',
                    borderColor: activeCategory ? store?.colors?.primary || '#2ECC71' : '#e5e7eb'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = store?.colors?.primary || '#2ECC71';
                    e.currentTarget.style.boxShadow = `0 0 0 3px ${store?.colors?.primary || '#2ECC71'}20`;
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = activeCategory ? store?.colors?.primary || '#2ECC71' : '#e5e7eb';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                >
                  <option value="">{t('storefront.categories')}</option>
                  {isLoadingCategories ? (
                    <option disabled>{t('storefront.loadingCategories')}</option>
                  ) : (
                    categories?.filter((category: any) => category.visible).map((category: any) => (
                      <option key={category.id} value={category.id.toString()}>
                        {category.name}
                      </option>
                    ))
                  )}
                </select>
              </div>

              {/* Search Icon Button */}
              <div className="flex-shrink-0">
                <Button
                  variant="outline"
                  size="icon"
                  className="h-12 w-12 rounded-xl border-gray-200 shadow-sm"
                  onClick={() => setShowSearch(true)}
                  style={{
                    borderColor: '#e5e7eb',
                    backgroundColor: 'white'
                  }}
                >
                  <Search className="h-5 w-5 text-gray-500" />
                </Button>
              </div>
            </div>
          ) : (
            /* Search Input e Close Button - mesma linha quando busca está aberta */
            <div className="flex gap-3 items-end animate-in slide-in-from-top-2 duration-200">
              {/* Search Input */}
              <div className="flex-1">
                <label htmlFor="search-products-mobile" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('storefront.searchProducts')}
                </label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    id="search-products-mobile"
                    placeholder={t('storefront.searchProducts')}
                    className="pl-10 py-3 rounded-xl"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    autoFocus
                  />
                </div>
              </div>

              {/* Close Search Button */}
              <div className="flex-shrink-0">
                <Button
                  variant="outline"
                  size="icon"
                  className="h-12 w-12 rounded-xl border-gray-200 shadow-sm"
                  onClick={() => {
                    setShowSearch(false);
                    setSearchQuery(''); // Limpar busca ao fechar
                  }}
                  style={{
                    borderColor: store?.colors?.primary || '#2ECC71',
                    backgroundColor: `${store?.colors?.primary || '#2ECC71'}10`
                  }}
                >
                  <X className="h-5 w-5" style={{ color: store?.colors?.primary || '#2ECC71' }} />
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Desktop Layout */}
        <div className="hidden sm:flex w-full gap-4 items-end">
          {/* Categories Dropdown */}
          <div className="w-1/2">
            <label htmlFor="category-select-desktop" className="block text-sm font-medium text-gray-700 mb-2">

            </label>
            <select
              id="category-select-desktop"
              value={activeCategory || ''}
              onChange={(e) => setActiveCategory(e.target.value || null)}
              className="w-full px-4 py-3 text-sm border border-gray-200 rounded-xl bg-white text-gray-700 focus:outline-none focus:ring-2 focus:border-transparent shadow-sm transition-all duration-200"
              style={{
                focusRingColor: store?.colors?.primary || '#2ECC71',
                borderColor: activeCategory ? store?.colors?.primary || '#2ECC71' : '#e5e7eb'
              }}
              onFocus={(e) => {
                e.currentTarget.style.borderColor = store?.colors?.primary || '#2ECC71';
                e.currentTarget.style.boxShadow = `0 0 0 3px ${store?.colors?.primary || '#2ECC71'}20`;
              }}
              onBlur={(e) => {
                e.currentTarget.style.borderColor = activeCategory ? store?.colors?.primary || '#2ECC71' : '#e5e7eb';
                e.currentTarget.style.boxShadow = 'none';
              }}
            >
              <option value="">{t('storefront.categories')}</option>
              {isLoadingCategories ? (
                <option disabled>{t('storefront.loadingCategories')}</option>
              ) : (
                categories?.filter((category: any) => category.visible).map((category: any) => (
                  <option key={category.id} value={category.id.toString()}>
                    {category.name}
                  </option>
                ))
              )}
            </select>
          </div>

          {/* Search */}
          <div className="w-1/2">
            <label htmlFor="search-products-desktop" className="block text-sm font-medium text-gray-700 mb-2">
              {t('storefront.searchProducts')}
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                id="search-products-desktop"
                placeholder={t('storefront.searchProducts')}
                className="pl-10 py-3 rounded-xl"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Products */}
      {isLoadingProducts ? (
        // Skeleton loaders for products
        <div className={isListLayout ? "space-y-4" : "grid grid-cols-2 gap-4 sm:grid-cols-3 lg:grid-cols-4"}>
          {Array(8).fill(0).map((_, index) => (
            <div key={index} className="bg-white rounded-lg shadow overflow-hidden">
              <Skeleton className="h-40 w-full" />
              <div className="p-4">
                <Skeleton className="h-5 w-3/4 mb-2" />
                <div className="mt-2 flex justify-between items-center">
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-8 w-8 rounded-full" />
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : searchQuery || activeCategory ? (
        // Mostrar produtos filtrados quando há busca ou categoria selecionada
        <div>
          {/* Exibir o cabeçalho da categoria selecionada */}
          {activeCategory && categories && (
            <div className="mb-6">
              {categories.filter((cat: any) => cat.id.toString() === activeCategory).map((category: any) => (
                <div key={category.id} className="mb-4">
                  <div className="flex items-center space-x-3 mb-2">
                    {category.logo && (
                      <img
                        src={category.logo}
                        alt={category.name}
                        className="w-10 h-10 object-cover rounded-full"
                      />
                    )}
                    <h2 className="text-xl font-semibold text-neutral-dark">{category.name}</h2>
                  </div>

                  {/* Descrição da categoria */}
                  {category.description && (
                    <p className="text-sm text-muted-foreground mb-4 pl-2">
                      {category.description}
                    </p>
                  )}
                </div>
              ))}
            </div>
          )}

          <div className={isListLayout ? "space-y-4" : "grid grid-cols-2 gap-4 sm:grid-cols-3 lg:grid-cols-4"}>
            {filteredProducts.length > 0 ? (
              filteredProducts.map((product: any) => (
                isListLayout ? (
                  <div key={product.id} className="w-full bg-white rounded-lg shadow overflow-hidden cursor-pointer transition-transform hover:scale-[1.01] flex border border-gray-100" onClick={() => handleProductClick(product)}>
                    <div className="w-24 h-24 sm:w-28 sm:h-28 flex-shrink-0 relative">
                      {product.images && product.images.length > 0 ? (
                        <img src={product.images[0]} alt={product.name} className="w-full h-full object-cover" />
                      ) : (
                        <div className="w-full h-full bg-muted flex items-center justify-center text-muted-foreground">
                          <span className="text-xs">{t('storefront.noImage')}</span>
                        </div>
                      )}
                      {product.hasVariations && (
                        <div className="absolute top-1 left-1 bg-secondary text-white text-[10px] px-1.5 py-0.5 rounded-full">
                          {t('storefront.hasOptions')}
                        </div>
                      )}
                    </div>
                    <div className="p-3 flex-1 flex flex-col justify-between overflow-hidden">
                      <div>
                        <h3 className="font-medium text-neutral-dark line-clamp-2 text-sm sm:text-base">{product.name}</h3>
                        {product.description && (
                          <p className="text-muted-foreground text-xs line-clamp-1 mt-0.5">{product.description}</p>
                        )}
                      </div>
                      <div className="mt-1 flex justify-between items-center">
                        <p className="font-bold text-primary">{formatCurrency(product.price, store?.currency || 'R$')}</p>
                      </div>
                    </div>
                  </div>
                ) : (
                  <ProductCard
                    key={product.id}
                    product={product}
                    onClick={handleProductClick}
                    currency={store?.currency} // Added currency prop
                  />
                )
              ))
            ) : (
              <div className="col-span-full py-10 text-center">
                <p className="text-muted-foreground">
                  {searchQuery
                    ? `${t('storefront.noProductsFound')} "${searchQuery}"`
                    : t('storefront.noProductsInCategory')}
                </p>
              </div>
            )}
          </div>
        </div>
      ) : isListLayout ? (
        // Layout tipo 2 - List view (lista vertical)
        <div className="space-y-8">
          {productsByCategory.length > 0 ? (
            productsByCategory.map((group: any) => (
              <div key={group.category.id} className="mb-8">
                {/* Cabeçalho da categoria */}
                <div className="flex items-center mb-4">
                  <div className="flex items-center space-x-3">
                    {group.category.logo && (
                      <img
                        src={group.category.logo}
                        alt={group.category.name}
                        className="w-10 h-10 object-cover rounded-full"
                      />
                    )}
                    <h2 className="text-xl font-semibold text-neutral-dark">{group.category.name}</h2>
                  </div>
                </div>

                {/* Descrição da categoria */}
                {group.category.description && (
                  <p className="text-sm text-muted-foreground mb-4 pl-2">
                    {group.category.description}
                  </p>
                )}

                {/* Lista vertical de produtos */}
                <div className="space-y-4">
                  {group.products.map((product: any) => (
                    <div key={product.id} className="w-full bg-white rounded-lg shadow overflow-hidden cursor-pointer transition-transform hover:scale-[1.01] flex border border-gray-100" onClick={() => handleProductClick(product)}>
                      <div className="w-24 h-24 sm:w-28 sm:h-28 flex-shrink-0 relative">
                        {product.images && product.images.length > 0 ? (
                          <img src={product.images[0]} alt={product.name} className="w-full h-full object-cover" />
                        ) : (
                          <div className="w-full h-full bg-muted flex items-center justify-center text-muted-foreground">
                            <span className="text-xs">{t('storefront.noImage')}</span>
                          </div>
                        )}
                        {product.hasVariations && (
                          <div className="absolute top-1 left-1 bg-secondary text-white text-[10px] px-1.5 py-0.5 rounded-full">
                            {t('storefront.hasOptions')}
                          </div>
                        )}
                      </div>
                      <div className="p-3 flex-1 flex flex-col justify-between overflow-hidden">
                        <div>
                          <h3 className="font-medium text-neutral-dark line-clamp-2 text-sm sm:text-base">{product.name}</h3>
                          {product.description && (
                            <p className="text-muted-foreground text-xs line-clamp-1 mt-0.5">{product.description}</p>
                          )}
                        </div>
                        <div className="mt-1 flex justify-between items-center">
                          <p className="font-bold text-primary">{formatCurrency(product.price, store?.currency || 'R$')}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))
          ) : (
            <div className="py-10 text-center">
              <p className="text-muted-foreground">
                {t('storefront.noProductsAvailable')}
              </p>
            </div>
          )}
        </div>
      ) : (
        // Layout tipo 1 - Grid view (grade de produtos)
        <div className="space-y-8">
          {productsByCategory.length > 0 ? (
            productsByCategory.map((group: any) => (
              <div key={group.category.id} className="mb-8">
                {/* Cabeçalho da categoria */}
                <div className="flex items-center space-x-3 mb-2">
                  {group.category.logo && (
                    <img
                      src={group.category.logo}
                      alt={group.category.name}
                      className="w-10 h-10 object-cover rounded-full"
                    />
                  )}
                  <h2 className="text-xl font-semibold text-neutral-dark">{group.category.name}</h2>
                </div>

                {/* Descrição da categoria */}
                {group.category.description && (
                  <p className="text-sm text-muted-foreground mb-4 pl-2">
                    {group.category.description}
                  </p>
                )}

                {/* Grade de produtos da categoria */}
                <div className={isListLayout ? "space-y-4" : "grid grid-cols-2 gap-4 sm:grid-cols-3 lg:grid-cols-4"}>
                  {group.products.map((product: any) => (
                    isListLayout ? (
                      <div key={product.id} className="w-full bg-white rounded-lg shadow overflow-hidden cursor-pointer transition-transform hover:scale-[1.01] flex border border-gray-100" onClick={() => handleProductClick(product)}>
                        <div className="w-24 h-24 sm:w-28 sm:h-28 flex-shrink-0 relative">
                          {product.images && product.images.length > 0 ? (
                            <img src={product.images[0]} alt={product.name} className="w-full h-full object-cover" />
                          ) : (
                            <div className="w-full h-full bg-muted flex items-center justify-center text-muted-foreground">
                              <span className="text-xs">{t('storefront.noImage')}</span>
                            </div>
                          )}
                          {product.hasVariations && (
                            <div className="absolute top-1 left-1 bg-secondary text-white text-[10px] px-1.5 py-0.5 rounded-full">
                              {t('storefront.hasOptions')}
                            </div>
                          )}
                        </div>
                        <div className="p-3 flex-1 flex flex-col justify-between overflow-hidden">
                          <div>
                            <h3 className="font-medium text-neutral-dark line-clamp-2 text-sm sm:text-base">{product.name}</h3>
                            {product.description && (
                              <p className="text-muted-foreground text-xs line-clamp-1 mt-0.5">{product.description}</p>
                            )}
                          </div>
                          <div className="mt-1 flex justify-between items-center">
                            <p className="font-bold text-primary">{formatCurrency(product.price, store?.currency || 'R$')}</p>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <ProductCard
                        key={product.id}
                        product={product}
                        onClick={handleProductClick}
                        currency={store?.currency} // Added currency prop
                      />
                    )
                  ))}
                </div>
              </div>
            ))
          ) : (
            <div className="py-10 text-center">
              <p className="text-muted-foreground">
                {t('storefront.noProductsAvailable')}
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default ProductList;