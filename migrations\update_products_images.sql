-- Renomeia a coluna image para images e define como JSON
ALTER TABLE products 
  DROP COLUMN IF EXISTS image;

-- Adiciona a nova coluna images como JSON
ALTER TABLE products 
  ADD COLUMN images JSONB NOT NULL DEFAULT '[]';

-- Atualiza os produtos existentes para converterem a imagem em um array
-- UPDATE products 
--   SET images = CASE 
--     WHEN image IS NOT NULL AND image != '' THEN jsonb_build_array(image)
--     ELSE '[]'::jsonb
--   END;