import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { config } from 'dotenv';

// Carrega variáveis de ambiente do .env
config();

// Obtém as variáveis do Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Erro: VITE_SUPABASE_URL ou VITE_SUPABASE_SERVICE_KEY não definidos.');
  console.error('Por favor, defina essas variáveis no arquivo .env');
  process.exit(1);
}

console.log(`Conectando ao Supabase: ${supabaseUrl}`);

// Inicializar cliente Supabase com a service key (permissões administrativas)
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function recreateTables() {
  console.log('Iniciando criação das tabelas no Supabase...');
  
  try {
    // Verificar a conexão com o Supabase
    try {
      const { data, error } = await supabase.auth.getSession();
      if (error) throw error;
      console.log('Conexão com Supabase estabelecida com sucesso');
    } catch (connectionError) {
      console.error('Erro ao conectar ao Supabase:', connectionError);
      process.exit(1);
    }
    
    console.log('Criando bucket de armazenamento para imagens...');
    try {
      const { data, error } = await supabase.storage.createBucket('images', {
        public: true,
        fileSizeLimit: 10485760 // 10MB
      });
      
      if (error && error.message !== 'Bucket already exists') {
        console.error('Erro ao criar bucket:', error);
      } else {
        console.log('Bucket criado ou já existente');
      }
    } catch (bucketError) {
      console.error('Erro ao criar bucket:', bucketError);
    }
    
    console.log('\nPara concluir a configuração do Supabase, execute os seguintes comandos SQL no console do Supabase:');
    console.log('\n1. Abra o dashboard do seu projeto Supabase em ' + supabaseUrl);
    console.log('2. Vá para SQL Editor > Nova consulta');
    console.log('3. Cole o conteúdo do arquivo recreate_tables.sql');
    console.log('4. Execute a consulta');
    console.log('\nIsso criará as tabelas necessárias para o projeto.');
    
    const sqlFilePath = path.join(process.cwd(), 'recreate_tables.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    
    console.log('\nConteúdo do SQL que deve ser executado:');
    console.log('---------------------------------------');
    console.log(sqlContent.substring(0, 500) + '\n...[truncado]\n');
    console.log('---------------------------------------');
    
    console.log('\nConfiguração básica do Supabase concluída!');
  } catch (error) {
    console.error('Falha ao configurar Supabase:', error);
    process.exit(1);
  }
}

// Executar o script
recreateTables().then(() => {
  console.log('Script finalizado.');
  process.exit(0);
}).catch(err => {
  console.error('Erro fatal:', err);
  process.exit(1);
});
