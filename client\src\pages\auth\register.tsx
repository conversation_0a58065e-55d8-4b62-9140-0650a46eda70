import { useEffect } from 'react';
import { useLocation } from 'wouter';
import AuthForm from '@/components/auth/AuthForm';
import { useAuth } from '@/context/FirebaseAuthContext';

export default function Register() {
  const [, setLocation] = useLocation();
  const { isAuthenticated } = useAuth();

  // Redirect to admin dashboard if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      setLocation('/admin');
    }
  }, [isAuthenticated, setLocation]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <div className="flex items-center justify-center min-h-screen px-4 py-12">
        <div className="w-full max-w-md">
          <AuthForm mode="register" />
        </div>
      </div>
    </div>
  );
}
