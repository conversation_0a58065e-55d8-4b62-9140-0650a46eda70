import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import 'dotenv/config';

// Obter variáveis do Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Supabase URL ou Service Key não encontrados nas variáveis de ambiente!');
  process.exit(1);
}

console.log(`Conectando ao Supabase: ${supabaseUrl}`);

// Inicializar cliente Supabase com service key
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Ler o arquivo SQL
const sqlFilePath = path.join(process.cwd(), 'recreate_tables.sql');
let sqlContent: string;

try {
  sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
  console.log('Arquivo SQL lido com sucesso!');
} catch (error) {
  console.error('Erro ao ler o arquivo SQL:', error);
  process.exit(1);
}

// Função para executar o SQL via Supabase
async function executeSql(query: string): Promise<any> {
  try {
    const { data, error } = await supabase.rpc('pg_execute_sql', { query });
    
    if (error) {
      throw error;
    }
    
    return data;
  } catch (error) {
    console.error('Erro ao executar SQL:', error);
    throw error;
  }
}

// Função para verificar se existem tabelas no banco
async function checkExistingTables(): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from('pg_catalog.pg_tables')
      .select('tablename')
      .eq('schemaname', 'public')
      .limit(1);
    
    if (error) {
      console.error('Erro ao verificar tabelas existentes:', error);
      return false;
    }
    
    return data && data.length > 0;
  } catch (error) {
    console.error('Erro ao verificar tabelas existentes:', error);
    return false;
  }
}

// Função principal para recriar tabelas
async function recreateTables() {
  console.log('Iniciando recriação das tabelas no Supabase...');
  
  try {
    // Dividir o conteúdo SQL em declarações individuais
    // Esta abordagem simples funciona para nosso script, 
    // mas scripts SQL mais complexos precisariam de um parser adequado
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0);
    
    console.log(`Encontradas ${statements.length} declarações SQL para executar.`);
    
    // Executar cada declaração individualmente
    for (let i = 0; i < statements.length; i++) {
      const stmt = statements[i];
      
      // Remover comentários de linha e adicionar ponto e vírgula novamente
      const cleanStmt = stmt
        .split('\n')
        .map(line => line.split('--')[0].trim())
        .filter(line => line.length > 0)
        .join('\n') + ';';
      
      // Pular se for uma linha vazia após processamento
      if (cleanStmt === ';') continue;
      
      console.log(`Executando declaração ${i+1}/${statements.length}: ${cleanStmt.slice(0, 50)}...`);
      
      try {
        await executeSql(cleanStmt);
        console.log(`✓ Declaração ${i+1} executada com sucesso!`);
      } catch (error) {
        console.error(`❌ Erro ao executar declaração ${i+1}:`, error);
        // Continuar para tentar executar as próximas declarações
      }
    }
    
    console.log('✅ Processo de recriação de tabelas concluído!');
    console.log('Algumas declarações podem ter falhado, verifique os logs acima.');
    
    // Verificar se pelo menos as tabelas principais foram criadas
    try {
      const { data: usersData } = await supabase.from('users').select('count');
      console.log('✓ Tabela "users" criada e disponível.');
      
      const { data: storesData } = await supabase.from('stores').select('count');
      console.log('✓ Tabela "stores" criada e disponível.');
      
      console.log('✅ Verificação de tabelas concluída!'); 
    } catch (error) {
      console.error('❌ Erro ao verificar tabelas recém-criadas:', error);
    }
    
  } catch (error) {
    console.error('Erro durante a recriação das tabelas:', error);
  }
}

// Executar a função principal
recreateTables();
