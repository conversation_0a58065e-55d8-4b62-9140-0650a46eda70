# Instrucoes Detalhadas para Agente Replit - Criacao de Pagina de Loja

## Objetivo:
Criar uma pagina de loja responsiva, focada em mobile first, que permita que os usuarios escolham produtos organizados por categorias, com suporte para variacoes obrigatorias e opcionais, sem modais (tudo direto no fluxo da pagina).

## Estrutura geral do layout

1. Header fixo no topo da pagina:
   - Exibir:
     - Logo da loja.
     - Nome da loja.
     - Status de funcionamento (Aberto/Fechado).
     - Botao de WhatsApp para contato.

2. Menu de Categorias:
   - <PERSON><PERSON><PERSON> horizontais rolave<PERSON>, listando as categorias dos produtos.
   - Clicar em uma categoria faz scroll automatico ate a secao correspondente da pagina.

3. Listagem de Produtos agrupados por Categoria:
   - Cada categoria deve exibir:
     - Nome da Categoria.
     - Produtos listados em scroll vertical.

4. Para cada Produto, exibir diretamente:
   - Nome do produto.
   - Preco base do produto.
   - Breve descricao (opcional).
   - Imagem (opcional).
   - Grupos de variacoes, exibidos como listas abertas:
     - Cada grupo de variacao deve ter:
       - Nome do grupo.
       - Informacao se e obrigatorio ou opcional.
       - Numero minimo e maximo de selecoes permitido.
       - Lista de opcoes para o usuario selecionar.
     - Exibir variacoes como:
       - Botoes de selecao unicos (radio button) para escolha unica.
       - Caixas de selecao (checkbox) para multiplas escolhas.

   - Campo de Observacoes (input de texto opcional para observacoes do cliente).

   - Botao "Adicionar ao Carrinho":
     - Validacao obrigatoria:
       - Nao permitir adicionar se variacoes obrigatorias nao forem selecionadas corretamente.
       - Verificar se o numero minimo/maximo de selecoes foi respeitado.
     - Atualizar o preco final conforme adicionais escolhidos (se aplicavel).

5. Rodape flutuante (opcional):
   - Exibir carrinho pequeno fixo:
     - Numero de itens.
     - Total acumulado do pedido.

## Regras de funcionamento das Variacoes:

- Cada grupo de variacoes deve possuir propriedades:
  - Obrigatoriedade (true/false).
  - Selecao minima.
  - Selecao maxima.
- Se o cliente nao respeitar uma obrigatoriedade, exibir mensagem de aviso e bloquear a adicao ao carrinho.
- Permitir variacoes que alteram o valor do produto.

## Design Responsivo:

- Interface otimizada para dispositivos moveis.
- Elementos grandes para toque com o dedo.
- Fonte legivel em telas pequenas.
- Scroll vertical suave.

## Exemplo visual de fluxo de produto:

X-Burguer Tradicional
Preco: R$ 22,00
Descricao: Pao artesanal, carne 150g, queijo prato.

Escolha o Tamanho (Obrigatorio, Escolha 1):
- ( ) P (R$ 22,00)
- ( ) M (R$ 25,00)
- ( ) G (R$ 28,00)

Escolha o Recheio (Obrigatorio, Escolha 1):
- ( ) Queijo Prato
- ( ) Queijo Cheddar
- ( ) Queijo Mucarela

Escolha os Adicionais (Opcional, Maximo 3):
- [ ] Bacon (+R$ 4,00)
- [ ] Ovo (+R$ 2,00)
- [ ] Cebola caramelizada (+R$ 3,00)

Observacoes:
[Campo de texto livre]

[Adicionar ao Carrinho]

## Estrutura de dados esperada (sugestao):

{
  "nome": "X-Burguer Tradicional",
  "descricao": "Pao artesanal, carne 150g, queijo prato.",
  "preco": 22.00,
  "imagem": "url_da_imagem",
  "variacoes": [
    {
      "nomeGrupo": "Escolha o Tamanho",
      "obrigatorio": true,
      "minSelecionados": 1,
      "maxSelecionados": 1,
      "opcoes": [
        {"nome": "P", "precoAdicional": 0},
        {"nome": "M", "precoAdicional": 3},
        {"nome": "G", "precoAdicional": 6}
      ]
    },
    {
      "nomeGrupo": "Escolha o Recheio",
      "obrigatorio": true,
      "minSelecionados": 1,
      "maxSelecionados": 1,
      "opcoes": [
        {"nome": "Queijo Prato", "precoAdicional": 0},
        {"nome": "Queijo Cheddar", "precoAdicional": 0},
        {"nome": "Queijo Mucarela", "precoAdicional": 0}
      ]
    },
    {
      "nomeGrupo": "Escolha os Adicionais",
      "obrigatorio": false,
      "minSelecionados": 0,
      "maxSelecionados": 3,
      "opcoes": [
        {"nome": "Bacon", "precoAdicional": 4},
        {"nome": "Ovo", "precoAdicional": 2},
        {"nome": "Cebola caramelizada", "precoAdicional": 3}
      ]
    }
  ]
}

## Check-list para o Agente:
- Layout mobile first.
- Produtos agrupados por categoria.
- Listagem de produtos com variacoes visiveis diretamente na pagina.
- Sem abertura de modal.
- Variacoes obrigatorias e opcionais com limites de selecao.
- Campo para observacoes do cliente.
- Botao "Adicionar ao Carrinho" com validacoes.
- Rodape flutuante com resumo de carrinho (opcional).
- Validacao de regras de variacao.
- Scroll vertical suave.

