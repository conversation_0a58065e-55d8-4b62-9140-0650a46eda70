import { supabase } from './db';
import { IStorage } from './storage';
import {
  type User, type InsertUser, type UpsertUser, type Store, type InsertStore, type Category, type InsertCategory,
  type Product, type InsertProduct, type Customer, type InsertCustomer, type Order, type InsertOrder,
  type OrderItem, type InsertOrderItem, type StoreVisit, type InsertStoreVisit,
  type ProductVariation, type InsertProductVariation, type VariationOption, type InsertVariationOption,
  type CartItem, type InsertCartItem, type OrderRevision, type InsertOrderRevision,
  type OrderRevisionItem, type InsertOrderRevisionItem
} from '@shared/schema';

export class SupabaseStorage implements IStorage {

  // User methods - updated for Firebase Auth
  async getUser(id: number): Promise<User | undefined> {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', id)
      .single();

    if (error || !data) return undefined;
    return data as User;
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    if (!email) return undefined;

    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('email', email)
      .single();

    if (error || !data) return undefined;
    return data as User;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('username', username)
      .single();

    if (error || !data) return undefined;
    return data as User;
  }

  async getUserByFirebaseUid(firebaseUid: string): Promise<User | undefined> {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('firebase_uid', firebaseUid)
      .single();

    if (error || !data) return undefined;
    return data as User;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const { data, error } = await supabase
      .from('users')
      .insert({
        username: insertUser.username,
        email: insertUser.email,
        password: insertUser.password,
        full_name: insertUser.fullName,
        first_name: insertUser.firstName,
        last_name: insertUser.lastName,
        bio: insertUser.bio,
        profile_image_url: insertUser.profileImageUrl,
        firebase_uid: insertUser.firebaseUid,
        created_at: new Date(),
        updated_at: new Date()
      })
      .select('*')
      .single();

    if (error) {
      console.error('Erro ao criar usuário:', error);
      throw error;
    }

    return this.mapUserFromSupabase(data);
  }

  async createUserWithFirebaseUid(userData: Partial<User> & { fullName?: string }): Promise<User> {
    // Gera uma sequência numérica para id se não for fornecido
    const fullName = userData.fullName || userData.username || `Usuario`;

    const { data, error } = await supabase
      .from('users')
      .insert({
        username: userData.username || `user-${Date.now()}`,
        email: userData.email,
        first_name: userData.firstName,
        last_name: userData.lastName,
        bio: userData.bio,
        profile_image_url: userData.profileImageUrl,
        firebase_uid: userData.firebaseUid,
        full_name: fullName, // Campo obrigatório
        created_at: new Date(),
        updated_at: new Date()
      })
      .select('*')
      .single();

    if (error) {
      console.error('Erro ao criar usuário com Firebase UID:', error);
      throw error;
    }

    return this.mapUserFromSupabase(data);
  }

  async upsertUser(userData: UpsertUser): Promise<User> {
    const { data, error } = await supabase
      .from('users')
      .upsert({
        id: userData.id,
        username: userData.username,
        email: userData.email,
        password: userData.password,
        full_name: userData.fullName,
        first_name: userData.firstName,
        last_name: userData.lastName,
        bio: userData.bio,
        profile_image_url: userData.profileImageUrl,
        firebase_uid: userData.firebaseUid,
        updated_at: new Date()
      })
      .select('*')
      .single();

    if (error) {
      console.error('Erro ao upsert usuário:', error);
      throw error;
    }

    return this.mapUserFromSupabase(data);
  }

  async updateUserByFirebaseUid(firebaseUid: string, userData: Partial<User>): Promise<User | undefined> {
    const updateData: any = {};

    // Mapear campos para o formato snake_case do Supabase
    if (userData.username) updateData.username = userData.username;
    if (userData.email) updateData.email = userData.email;
    if (userData.firstName) updateData.first_name = userData.firstName;
    if (userData.lastName) updateData.last_name = userData.lastName;
    if (userData.fullName) updateData.full_name = userData.fullName;
    if (userData.bio) updateData.bio = userData.bio;
    if (userData.profileImageUrl) updateData.profile_image_url = userData.profileImageUrl;

    updateData.updated_at = new Date();

    const { data, error } = await supabase
      .from('users')
      .update(updateData)
      .eq('firebase_uid', firebaseUid)
      .select('*')
      .single();

    if (error || !data) {
      console.error('Erro ao atualizar usuário por Firebase UID:', error);
      return undefined;
    }

    return this.mapUserFromSupabase(data);
  }

  // Helper para mapear dados do formato do Supabase para o nosso schema
  private mapUserFromSupabase(data: any): User {
    return {
      id: data.id,
      username: data.username,
      email: data.email,
      password: data.password,
      fullName: data.full_name,
      firstName: data.first_name,
      lastName: data.last_name,
      bio: data.bio,
      profileImageUrl: data.profile_image_url,
      firebaseUid: data.firebase_uid,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    };
  }

  // Store methods
  async getStore(id: number): Promise<Store | undefined> {
    const { data, error } = await supabase
      .from('stores')
      .select('*')
      .eq('id', id)
      .single();

    if (error || !data) return undefined;
    return this.mapStoreFromSupabase(data);
  }

  async getStoreBySlug(slug: string): Promise<Store | undefined> {
    const { data, error } = await supabase
      .from('stores')
      .select('*')
      .eq('slug', slug)
      .single();

    if (error || !data) return undefined;
    return this.mapStoreFromSupabase(data);
  }

  async getStoreByUserId(userId: number): Promise<Store | undefined> {
    const { data, error } = await supabase
      .from('stores')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error || !data) return undefined;
    return this.mapStoreFromSupabase(data);
  }

  async getStoreByFirebaseUid(firebaseUid: string): Promise<Store | undefined> {
    try {
      console.log(`Looking for store with Firebase UID: ${firebaseUid}`);

      // Primeiro obtemos o ID do usuário com base no Firebase UID
      const user = await this.getUserByFirebaseUid(firebaseUid);

      if (!user) {
        console.log(`Usuário não encontrado para Firebase UID: ${firebaseUid}`);
        return undefined;
      }

      console.log(`Encontrado usuário com ID: ${user.id} para Firebase UID: ${firebaseUid}`);

      // Agora buscamos a loja pelo ID do usuário
      const store = await this.getStoreByUserId(user.id);

      if (!store) {
        console.log(`Loja não encontrada para usuário com ID: ${user.id}`);
      } else {
        console.log(`Encontrada loja com ID: ${store.id} para usuário com ID: ${user.id}`);
      }

      return store;
    } catch (error) {
      console.error('Erro ao buscar loja por Firebase UID:', error);
      throw error;
    }
  }

  async createStore(insertStore: InsertStore): Promise<Store> {
    const { data, error } = await supabase
      .from('stores')
      .insert({
        name: insertStore.name,
        slug: insertStore.slug,
        description: insertStore.description,
        logo: insertStore.logo,
        header_image: insertStore.headerImage,
        country_code: insertStore.countryCode,
        whatsapp: insertStore.whatsapp,
        instagram: insertStore.instagram,
        currency: insertStore.currency,
        user_id: insertStore.userId,
        colors: insertStore.colors,
        payment_methods: insertStore.paymentMethods,
        delivery_settings: insertStore.deliverySettings,
        layout_type: insertStore.layout,
        layout_settings: insertStore.layoutSettings,
        created_at: new Date(),
        updated_at: new Date()
      })
      .select('*')
      .single();

    if (error) {
      console.error('Erro ao criar loja:', error);
      throw error;
    }

    return this.mapStoreFromSupabase(data);
  }

  async updateStore(id: number, storeUpdate: Partial<Store>): Promise<Store | undefined> {
    const updateData: any = {};

    console.log('updateStore - Original storeUpdate:', JSON.stringify(storeUpdate, null, 2));

    // Mapear campos para o formato snake_case do Supabase
    if (storeUpdate.name !== undefined) updateData.name = storeUpdate.name;
    if (storeUpdate.slug !== undefined) updateData.slug = storeUpdate.slug;
    if (storeUpdate.description !== undefined) updateData.description = storeUpdate.description;
    if (storeUpdate.logo !== undefined) updateData.logo = storeUpdate.logo;
    if (storeUpdate.headerImage !== undefined) updateData.header_image = storeUpdate.headerImage;
    if (storeUpdate.countryCode !== undefined) updateData.country_code = storeUpdate.countryCode;
    if (storeUpdate.whatsapp !== undefined) updateData.whatsapp = storeUpdate.whatsapp;
    if (storeUpdate.instagram !== undefined) updateData.instagram = storeUpdate.instagram;
    if (storeUpdate.currency !== undefined) updateData.currency = storeUpdate.currency;
    if (storeUpdate.colors !== undefined) updateData.colors = storeUpdate.colors;
    if (storeUpdate.paymentMethods !== undefined) updateData.payment_methods = storeUpdate.paymentMethods;
    if (storeUpdate.deliverySettings !== undefined) updateData.delivery_settings = storeUpdate.deliverySettings;
    if (storeUpdate.layout !== undefined) updateData.layout_type = storeUpdate.layout;
    if (storeUpdate.layoutSettings !== undefined) updateData.layout_settings = storeUpdate.layoutSettings;

    // Campos de endereço da loja
    console.log('updateStore - Address fields in storeUpdate:', {
      addressStreet: storeUpdate.addressStreet,
      addressNumber: storeUpdate.addressNumber,
      addressComplement: storeUpdate.addressComplement,
      addressNeighborhood: storeUpdate.addressNeighborhood,
      addressCity: storeUpdate.addressCity,
      addressState: storeUpdate.addressState,
      contactEmail: storeUpdate.contactEmail
    });

    if (storeUpdate.addressStreet !== undefined) updateData.address_street = storeUpdate.addressStreet;
    if (storeUpdate.addressNumber !== undefined) updateData.address_number = storeUpdate.addressNumber;
    if (storeUpdate.addressComplement !== undefined) updateData.address_complement = storeUpdate.addressComplement;
    if (storeUpdate.addressNeighborhood !== undefined) updateData.address_neighborhood = storeUpdate.addressNeighborhood;
    if (storeUpdate.addressCity !== undefined) updateData.address_city = storeUpdate.addressCity;
    if (storeUpdate.addressState !== undefined) updateData.address_state = storeUpdate.addressState;
    if (storeUpdate.contactEmail !== undefined) updateData.contact_email = storeUpdate.contactEmail;

    updateData.updated_at = new Date();

    console.log('updateStore - Final updateData for Supabase:', JSON.stringify(updateData, null, 2));

    console.log(`Executando atualização na tabela 'stores' para ID: ${id}`);
    const { data, error } = await supabase
      .from('stores')
      .update(updateData)
      .eq('id', id)
      .select('*')
      .single();

    if (error) {
      console.error('Erro ao atualizar loja:', error);
      console.error('Detalhes do erro:', JSON.stringify(error, null, 2));
      return undefined;
    }

    if (!data) {
      console.error('Nenhum dado retornado após atualização da loja');
      return undefined;
    }

    console.log('Dados retornados após atualização:', JSON.stringify(data, null, 2));
    return this.mapStoreFromSupabase(data);
  }

  private mapStoreFromSupabase(data: any): Store {
    console.log('mapStoreFromSupabase - Dados brutos do Supabase:', JSON.stringify(data, null, 2));

    const mappedStore = {
      id: data.id,
      name: data.name,
      slug: data.slug,
      description: data.description,
      logo: data.logo,
      headerImage: data.header_image,
      countryCode: data.country_code,
      whatsapp: data.whatsapp,
      instagram: data.instagram,
      currency: data.currency,
      colors: data.colors,
      paymentMethods: data.payment_methods,
      userId: data.user_id,
      layout: data.layout_type,
      layoutSettings: data.layout_settings,
      deliverySettings: data.delivery_settings,
      // Campos de endereço da loja
      addressStreet: data.address_street,
      addressNumber: data.address_number,
      addressComplement: data.address_complement,
      addressNeighborhood: data.address_neighborhood,
      addressCity: data.address_city,
      addressState: data.address_state,
      contactEmail: data.contact_email,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    };

    console.log('mapStoreFromSupabase - Campos de endereço mapeados:', {
      addressStreet: mappedStore.addressStreet,
      addressNumber: mappedStore.addressNumber,
      addressComplement: mappedStore.addressComplement,
      addressNeighborhood: mappedStore.addressNeighborhood,
      addressCity: mappedStore.addressCity,
      addressState: mappedStore.addressState,
      contactEmail: mappedStore.contactEmail
    });

    return mappedStore;
  }

  // Implementação dos demais métodos.
  // Como temos uma grande quantidade de métodos, a implementação completa seria muito extensa.
  // Vou implementar os métodos básicos mais utilizados e adicionar stubs para o restante.

  // Product methods
  async getProduct(id: number): Promise<Product | undefined> {
    const { data, error } = await supabase
      .from('products')
      .select('*')
      .eq('id', id)
      .single();

    if (error || !data) return undefined;
    return this.mapProductFromSupabase(data);
  }

  async getProductsByStoreId(storeId: number): Promise<Product[]> {
    const { data, error } = await supabase
      .from('products')
      .select('*')
      .eq('store_id', storeId);

    if (error || !data) return [];
    return data.map(this.mapProductFromSupabase);
  }

  async getProductsByCategoryId(categoryId: number): Promise<Product[]> {
    const { data, error } = await supabase
      .from('products')
      .select('*')
      .eq('category_id', categoryId);

    if (error || !data) return [];
    return data.map(this.mapProductFromSupabase);
  }

  async createProduct(insertProduct: InsertProduct): Promise<Product> {
    const { data, error } = await supabase
      .from('products')
      .insert({
        name: insertProduct.name,
        description: insertProduct.description,
        price: insertProduct.price,
        images: insertProduct.images,
        in_stock: insertProduct.inStock,
        has_variations: insertProduct.hasVariations,
        variations: insertProduct.variations,
        store_id: insertProduct.storeId,
        category_id: insertProduct.categoryId,
        visible: insertProduct.visible,
        created_at: new Date()
      })
      .select('*')
      .single();

    if (error) {
      console.error('Erro ao criar produto:', error);
      throw error;
    }

    return this.mapProductFromSupabase(data);
  }

  async updateProduct(id: number, productUpdate: Partial<Product>): Promise<Product | undefined> {
    const updateData: any = {};

    // Mapear campos para o formato snake_case do Supabase
    if (productUpdate.name !== undefined) updateData.name = productUpdate.name;
    if (productUpdate.description !== undefined) updateData.description = productUpdate.description;
    if (productUpdate.price !== undefined) updateData.price = productUpdate.price;
    if (productUpdate.images !== undefined) updateData.images = productUpdate.images;
    if (productUpdate.inStock !== undefined) updateData.in_stock = productUpdate.inStock;
    if (productUpdate.hasVariations !== undefined) updateData.has_variations = productUpdate.hasVariations;
    if (productUpdate.variations !== undefined) updateData.variations = productUpdate.variations;
    if (productUpdate.categoryId !== undefined) updateData.category_id = productUpdate.categoryId;
    if (productUpdate.visible !== undefined) updateData.visible = productUpdate.visible;

    const { data, error } = await supabase
      .from('products')
      .update(updateData)
      .eq('id', id)
      .select('*')
      .single();

    if (error || !data) {
      console.error('Erro ao atualizar produto:', error);
      return undefined;
    }

    return this.mapProductFromSupabase(data);
  }

  async deleteProduct(id: number): Promise<boolean> {
    const { error } = await supabase
      .from('products')
      .delete()
      .eq('id', id);

    return !error;
  }

  private mapProductFromSupabase(data: any): Product {
    return {
      id: data.id,
      name: data.name,
      description: data.description,
      price: data.price,
      images: data.images,
      inStock: data.in_stock,
      hasVariations: data.has_variations,
      variations: data.variations,
      storeId: data.store_id,
      categoryId: data.category_id,
      visible: data.visible,
      createdAt: new Date(data.created_at)
    };
  }

  // Category methods
  async getCategory(id: number): Promise<Category | undefined> {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .eq('id', id)
      .single();

    if (error || !data) return undefined;
    return this.mapCategoryFromSupabase(data);
  }

  async getCategoriesByStoreId(storeId: number): Promise<Category[]> {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .eq('store_id', storeId)
      .order('display_order', { ascending: true });

    if (error || !data) return [];
    return data.map(this.mapCategoryFromSupabase);
  }

  async createCategory(insertCategory: InsertCategory): Promise<Category> {
    const { data, error } = await supabase
      .from('categories')
      .insert({
        name: insertCategory.name,
        description: insertCategory.description,
        logo: insertCategory.logo,
        store_id: insertCategory.storeId,
        display_order: insertCategory.displayOrder,
        visible: insertCategory.visible
      })
      .select('*')
      .single();

    if (error) {
      console.error('Erro ao criar categoria:', error);
      throw error;
    }

    return this.mapCategoryFromSupabase(data);
  }

  async updateCategory(id: number, categoryUpdate: Partial<Category>): Promise<Category | undefined> {
    const updateData: any = {};

    if (categoryUpdate.name !== undefined) updateData.name = categoryUpdate.name;
    if (categoryUpdate.description !== undefined) updateData.description = categoryUpdate.description;
    if (categoryUpdate.logo !== undefined) updateData.logo = categoryUpdate.logo;
    if (categoryUpdate.displayOrder !== undefined) updateData.display_order = categoryUpdate.displayOrder;
    if (categoryUpdate.visible !== undefined) updateData.visible = categoryUpdate.visible;

    const { data, error } = await supabase
      .from('categories')
      .update(updateData)
      .eq('id', id)
      .select('*')
      .single();

    if (error || !data) {
      console.error('Erro ao atualizar categoria:', error);
      return undefined;
    }

    return this.mapCategoryFromSupabase(data);
  }

  async deleteCategory(id: number): Promise<boolean> {
    const { error } = await supabase
      .from('categories')
      .delete()
      .eq('id', id);

    return !error;
  }

  private mapCategoryFromSupabase(data: any): Category {
    return {
      id: data.id,
      name: data.name,
      description: data.description,
      logo: data.logo,
      storeId: data.store_id,
      displayOrder: data.display_order,
      visible: data.visible
    };
  }

  // Product Variation methods
  async getProductVariationsByProductId(productId: number): Promise<ProductVariation[]> {
    const { data, error } = await supabase
      .from('product_variations')
      .select('*')
      .eq('product_id', productId);

    if (error || !data) return [];
    return data.map(this.mapProductVariationFromSupabase);
  }

  async createProductVariation(insertVariation: InsertProductVariation): Promise<ProductVariation> {
    const { data, error } = await supabase
      .from('product_variations')
      .insert({
        name: insertVariation.name,
        description: insertVariation.description,
        product_id: insertVariation.productId,
        required: insertVariation.required,
        multiple_choice: insertVariation.multipleChoice,
        min_selections: insertVariation.minSelections,
        max_selections: insertVariation.maxSelections,
        created_at: new Date()
      })
      .select('*')
      .single();

    if (error) {
      console.error('Erro ao criar variação de produto:', error);
      throw error;
    }

    return this.mapProductVariationFromSupabase(data);
  }

  async updateProductVariation(id: number, variationUpdate: Partial<ProductVariation>): Promise<ProductVariation | undefined> {
    console.log('DEBUG - updateProductVariation - ID:', id);
    console.log('DEBUG - updateProductVariation - Dados recebidos:', JSON.stringify(variationUpdate));

    const updateData: any = {};

    if (variationUpdate.name !== undefined) updateData.name = variationUpdate.name;
    if (variationUpdate.description !== undefined) updateData.description = variationUpdate.description;
    if (variationUpdate.required !== undefined) updateData.required = variationUpdate.required;
    if (variationUpdate.multipleChoice !== undefined) updateData.multiple_choice = variationUpdate.multipleChoice;

    // Garantir conversão explícita para números para os campos de seleções
    if (variationUpdate.minSelections !== undefined) {
      const minValue = Number(variationUpdate.minSelections);
      updateData.min_selections = isNaN(minValue) ? 0 : minValue;
      console.log('DEBUG - minSelections convertido:', updateData.min_selections);
    }

    if (variationUpdate.maxSelections !== undefined) {
      const maxValue = Number(variationUpdate.maxSelections);
      updateData.max_selections = isNaN(maxValue) ? 1 : maxValue;
      console.log('DEBUG - maxSelections convertido:', updateData.max_selections);
    }

    console.log('DEBUG - updateProductVariation - Dados formatados para DB:', JSON.stringify(updateData));

    const { data, error } = await supabase
      .from('product_variations')
      .update(updateData)
      .eq('id', id)
      .select('*')
      .single();

    if (error || !data) {
      console.error('Erro ao atualizar variação de produto:', error);
      return undefined;
    }

    console.log('DEBUG - updateProductVariation - Resultado:', JSON.stringify(data));

    return this.mapProductVariationFromSupabase(data);
  }

  async deleteProductVariation(id: number): Promise<boolean> {
    // Primeiro deletar todas as opções associadas a esta variação
    await supabase
      .from('variation_options')
      .delete()
      .eq('variation_id', id);

    // Depois deletar a própria variação
    const { error } = await supabase
      .from('product_variations')
      .delete()
      .eq('id', id);

    return !error;
  }

  private mapProductVariationFromSupabase(data: any): ProductVariation {
    return {
      id: data.id,
      name: data.name,
      description: data.description,
      productId: data.product_id,
      required: data.required,
      multipleChoice: data.multiple_choice,
      minSelections: data.min_selections,
      maxSelections: data.max_selections,
      createdAt: new Date(data.created_at)
    };
  }

  // Para os demais métodos, implementaremos conforme necessário
  // Estes são os métodos mais frequentemente utilizados

  // Métodos não implementados completamente ainda
  async getVariationOptionsByVariationId(variationId: number): Promise<VariationOption[]> {
    // Buscar opções de variação pelo ID da variação
    const { data, error } = await supabase
      .from('variation_options')
      .select('*')
      .eq('variation_id', variationId);

    if (error) {
      console.error('Erro ao buscar opções de variação:', error);
      return [];
    }

    // Mapear para o formato esperado pela aplicação (compatibilidade)
    return data.map(option => ({
      id: String(option.id),
      name: option.name,
      precoAdicional: option.additional_price,
      price: option.additional_price // Adicionando price para compatibilidade com o frontend
    }));
  }

  async createVariationOption(option: InsertVariationOption): Promise<VariationOption> {
    console.log('Criando opção de variação no Supabase:', option);

    const { data, error } = await supabase
      .from('variation_options')
      .insert({
        variation_id: option.variationId,
        name: option.name,
        additional_price: option.price
      })
      .select()
      .single();

    if (error) {
      console.error('Erro ao criar opção de variação:', error);
      throw new Error(`Erro ao criar opção de variação: ${error.message}`);
    }

    console.log('Opção de variação criada:', data);

    // Mapear para o formato esperado pela aplicação
    return {
      id: String(data.id),
      name: data.name,
      precoAdicional: data.additional_price,
      price: data.additional_price // Adicionando price para compatibilidade com o frontend
    };
  }

  async updateVariationOption(id: number, option: Partial<VariationOption>): Promise<VariationOption | undefined> {
    console.log('Atualizando opção de variação no Supabase, ID:', id, 'Dados:', option);

    // Preparar os dados para atualização
    const updateData: any = {};
    if (option.name !== undefined) updateData.name = option.name;
    if (option.precoAdicional !== undefined) updateData.additional_price = option.precoAdicional;

    const { data, error } = await supabase
      .from('variation_options')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Erro ao atualizar opção de variação:', error);
      return undefined;
    }

    if (!data) {
      console.log('Nenhuma opção de variação encontrada com ID:', id);
      return undefined;
    }

    console.log('Opção de variação atualizada:', data);

    // Mapear para o formato esperado pela aplicação
    return {
      id: String(data.id),
      name: data.name,
      precoAdicional: data.additional_price,
      price: data.additional_price // Adicionando price para compatibilidade com o frontend
    };
  }

  async deleteVariationOption(id: number): Promise<boolean> {
    console.log('Excluindo opção de variação no Supabase, ID:', id);

    const { error } = await supabase
      .from('variation_options')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Erro ao excluir opção de variação:', error);
      return false;
    }

    console.log('Opção de variação excluída com sucesso, ID:', id);
    return true;
  }

  async getCustomer(id: number): Promise<Customer | undefined> {
    const { data, error } = await supabase
      .from('customers')
      .select('*')
      .eq('id', id)
      .single();

    if (error || !data) {
      console.error('Erro ao obter cliente:', error);
      return undefined;
    }

    return {
      id: data.id,
      storeId: data.store_id,
      name: data.name,
      email: data.email,
      phone: data.whatsapp,
      countryCode: data.country_code,
      createdAt: new Date(data.created_at)
    };
  }

  async getCustomersByStoreId(storeId: number): Promise<Customer[]> {
    const { data, error } = await supabase
      .from('customers')
      .select('*')
      .eq('store_id', storeId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Erro ao obter clientes da loja:', error);
      return [];
    }

    return data.map(customer => ({
      id: customer.id,
      storeId: customer.store_id,
      name: customer.name,
      email: customer.email,
      phone: customer.whatsapp,
      countryCode: customer.country_code,
      createdAt: new Date(customer.created_at)
    }));
  }

  async getCustomerByEmail(storeId: number, email: string): Promise<Customer | undefined> {
    if (!email) return undefined;

    console.log(`Buscando cliente para loja ${storeId} com email: "${email}"`);

    // Normalizando o email para minúsculas para garantir que a comparação seja case-insensitive
    const normalizedEmail = email.toLowerCase().trim();
    console.log(`Email normalizado para busca: "${normalizedEmail}"`);

    // Primeiro buscar todos os clientes da loja para debug
    const { data: allCustomers, error: allError } = await supabase
      .from('customers')
      .select('*')
      .eq('store_id', storeId);

    if (allCustomers) {
      console.log(`Total de clientes na loja ${storeId}: ${allCustomers.length}`);
      console.log('Emails existentes:', allCustomers.map(c => `"${c.email?.toLowerCase()?.trim() || ''}"`).join(', '));

      // Verificar manualmente se há correspondência
      const matchingCustomers = allCustomers.filter(c =>
        c.email && c.email.toLowerCase().trim() === normalizedEmail
      );

      if (matchingCustomers.length > 0) {
        console.log(`Encontradas ${matchingCustomers.length} correspondências exatas de email:`,
          matchingCustomers.map(c => `${c.id}: ${c.email}`));
      }
    }

    // Agora buscar o cliente específico
    const { data, error } = await supabase
      .from('customers')
      .select('*')
      .eq('store_id', storeId)
      .ilike('email', normalizedEmail) // Usando ilike para comparação case-insensitive
      .maybeSingle();

    if (error) {
      console.error('Erro ao buscar cliente por email:', error);
      return undefined;
    }

    if (!data) {
      console.log(`Nenhum cliente encontrado com email "${normalizedEmail}"`);

      // Tentar uma busca mais flexível
      try {
        const { data: fuzzyResults } = await supabase
          .from('customers')
          .select('*')
          .eq('store_id', storeId);

        if (fuzzyResults && fuzzyResults.length > 0) {
          // Verificar manualmente se há correspondência aproximada
          const potentialMatches = fuzzyResults.filter(c =>
            c.email && normalizedEmail &&
            (c.email.toLowerCase().includes(normalizedEmail) ||
             normalizedEmail.includes(c.email.toLowerCase()))
          );

          if (potentialMatches.length > 0) {
            console.log('Possíveis correspondências aproximadas por email:',
              potentialMatches.map(c => `${c.id}: ${c.email}`));

            // Usar a primeira correspondência aproximada
            const bestMatch = potentialMatches[0];
            console.log('Usando a melhor correspondência aproximada:', bestMatch);

            return {
              id: bestMatch.id,
              storeId: bestMatch.store_id,
              name: bestMatch.name,
              email: bestMatch.email,
              phone: bestMatch.whatsapp,
              countryCode: bestMatch.country_code,
              createdAt: new Date(bestMatch.created_at)
            };
          }
        }
      } catch (fuzzyError) {
        console.error('Erro na busca aproximada por email:', fuzzyError);
      }

      return undefined;
    }

    console.log('Cliente encontrado pelo email:', data);

    return {
      id: data.id,
      storeId: data.store_id,
      name: data.name,
      email: data.email,
      phone: data.whatsapp,
      countryCode: data.country_code,
      createdAt: new Date(data.created_at)
    };
  }

  async getCustomerByPhone(storeId: number, phone: string): Promise<Customer | undefined> {
    if (!phone) return undefined;

    console.log(`Buscando cliente para loja ${storeId} com telefone: "${phone}"`);

    // Limpar o número de telefone para comparação (remover espaços, traços, parênteses)
    const cleanPhone = phone.replace(/[\s\-\(\)\+]/g, '').trim();
    console.log(`Telefone normalizado para busca: "${cleanPhone}"`);

    // Primeiro buscar todos os clientes da loja para debug
    const { data: allCustomers, error: allError } = await supabase
      .from('customers')
      .select('*')
      .eq('store_id', storeId);

    if (allCustomers) {
      console.log(`Total de clientes na loja ${storeId}: ${allCustomers.length}`);

      // Mostrar telefones existentes com formatação para debug
      const formattedPhones = allCustomers.map(c => {
        const cleanStoredPhone = c.whatsapp ? c.whatsapp.replace(/[\s\-\(\)\+]/g, '').trim() : '';
        return `"${cleanStoredPhone}" (original: "${c.whatsapp || ''}")`;
      });
      console.log('Telefones existentes:', formattedPhones.join(', '));

      // Verificar manualmente se há correspondência exata
      const matchingCustomers = allCustomers.filter(c => {
        if (!c.whatsapp) return false;
        const cleanStoredPhone = c.whatsapp.replace(/[\s\-\(\)\+]/g, '').trim();
        return cleanStoredPhone === cleanPhone;
      });

      if (matchingCustomers.length > 0) {
        console.log(`Encontradas ${matchingCustomers.length} correspondências exatas de telefone:`,
          matchingCustomers.map(c => `${c.id}: ${c.whatsapp}`));
      }
    }

    // Agora buscar o cliente específico
    const { data, error } = await supabase
      .from('customers')
      .select('*')
      .eq('store_id', storeId)
      .eq('whatsapp', cleanPhone)  // Busca exata com telefone limpo
      .maybeSingle();

    if (error) {
      console.error('Erro ao buscar cliente por telefone:', error);
      return undefined;
    }

    if (!data) {
      console.log(`Nenhum cliente encontrado com telefone exato "${cleanPhone}"`);

      // Tentar uma busca mais flexível (verificar se o telefone contém)
      try {
        const { data: fuzzyResults, error: fuzzyError } = await supabase
          .from('customers')
          .select('*')
          .eq('store_id', storeId);

        if (!fuzzyError && fuzzyResults) {
          // Verificar se algum dos telefones contém o número procurado ou vice-versa
          const potentialMatches = fuzzyResults.filter(c => {
            if (!c.whatsapp) return false;
            const cleanStoredPhone = c.whatsapp.replace(/[\s\-\(\)\+]/g, '').trim();

            // Verificar se um é substring do outro (para lidar com códigos de país, etc)
            const isMatch = cleanStoredPhone.includes(cleanPhone) ||
                           cleanPhone.includes(cleanStoredPhone);

            // Para números muito curtos, exigir correspondência exata para evitar falsos positivos
            if (cleanPhone.length < 8 || cleanStoredPhone.length < 8) {
              return cleanStoredPhone === cleanPhone;
            }

            return isMatch;
          });

          if (potentialMatches.length > 0) {
            console.log('Possíveis correspondências encontradas por telefone:',
              potentialMatches.map(c => `${c.id}: ${c.whatsapp} (limpo: ${c.whatsapp.replace(/[\s\-\(\)\+]/g, '').trim()})`));

            // Usar a primeira correspondência
            const bestMatch = potentialMatches[0];
            console.log('Usando a melhor correspondência de telefone:', bestMatch);

            return {
              id: bestMatch.id,
              storeId: bestMatch.store_id,
              name: bestMatch.name,
              email: bestMatch.email,
              phone: bestMatch.whatsapp,
              countryCode: bestMatch.country_code,
              createdAt: new Date(bestMatch.created_at)
            };
          }
        }
      } catch (fuzzyError) {
        console.error('Erro na busca aproximada por telefone:', fuzzyError);
      }

      return undefined;
    }

    console.log('Cliente encontrado pelo telefone exato:', data);

    return {
      id: data.id,
      storeId: data.store_id,
      name: data.name,
      email: data.email,
      phone: data.whatsapp,
      countryCode: data.country_code,
      createdAt: new Date(data.created_at)
    };
  }

  async createCustomer(customer: InsertCustomer): Promise<Customer> {
    console.log('Dados do cliente recebidos:', customer);

    // Normalizar os dados antes de salvar
    const normalizedEmail = customer.email ? customer.email.toLowerCase().trim() : '';
    const normalizedPhone = customer.phone ? customer.phone.replace(/[\s\-\(\)\+]/g, '').trim() : '';

    console.log('Dados normalizados para criação:');
    console.log('- Email original:', customer.email, '-> normalizado:', normalizedEmail);
    console.log('- Telefone original:', customer.phone, '-> normalizado:', normalizedPhone);

    // Verificar novamente se o cliente já existe com os dados normalizados
    if (normalizedEmail) {
      const existingByEmail = await this.getCustomerByEmail(customer.storeId, normalizedEmail);
      if (existingByEmail) {
        console.log('Cliente já existe com este email normalizado. Retornando cliente existente:', existingByEmail);
        return existingByEmail;
      }
    }

    if (normalizedPhone) {
      const existingByPhone = await this.getCustomerByPhone(customer.storeId, normalizedPhone);
      if (existingByPhone) {
        console.log('Cliente já existe com este telefone normalizado. Retornando cliente existente:', existingByPhone);
        return existingByPhone;
      }
    }

    const { data, error } = await supabase
      .from('customers')
      .insert({
        store_id: customer.storeId,
        name: customer.name,
        email: normalizedEmail, // Usar email normalizado
        whatsapp: normalizedPhone, // Usar telefone normalizado
        country_code: customer.countryCode || '+55', // Padrão para Brasil
        created_at: new Date()
      })
      .select('*')
      .single();

    if (error) {
      console.error('Erro ao criar cliente:', error);
      throw error;
    }

    console.log('Cliente criado com sucesso:', data);

    // Mapear os dados do Supabase para o formato do nosso schema
    return {
      id: data.id,
      storeId: data.store_id,
      name: data.name,
      email: data.email,
      phone: data.whatsapp,
      countryCode: data.country_code,
      createdAt: new Date(data.created_at)
    };
  }

  async updateCustomer(id: number, customerUpdate: Partial<Customer>): Promise<Customer | undefined> {
    console.log('Atualizando cliente:', id, customerUpdate);

    const updateData: any = {};

    // Normalizar os dados antes de salvar
    if (customerUpdate.name !== undefined) {
      updateData.name = customerUpdate.name;
    }

    if (customerUpdate.email !== undefined) {
      // Normalizar email
      const normalizedEmail = customerUpdate.email.toLowerCase().trim();
      updateData.email = normalizedEmail;
      console.log(`Email normalizado para atualização: "${normalizedEmail}" (original: "${customerUpdate.email}")`);
    }

    if (customerUpdate.phone !== undefined) {
      // Normalizar telefone
      const normalizedPhone = customerUpdate.phone.replace(/[\s\-\(\)\+]/g, '').trim();
      updateData.whatsapp = normalizedPhone; // Mapeando para o campo correto no banco
      console.log(`Telefone normalizado para atualização: "${normalizedPhone}" (original: "${customerUpdate.phone}")`);
    }

    if (customerUpdate.countryCode !== undefined) {
      updateData.country_code = customerUpdate.countryCode; // Adicionando código do país
    }

    console.log('Dados normalizados para atualização:', updateData);

    // Verificar se há algo para atualizar
    if (Object.keys(updateData).length === 0) {
      console.log('Nenhum dado para atualizar');

      // Retornar o cliente atual
      const { data: currentData } = await supabase
        .from('customers')
        .select('*')
        .eq('id', id)
        .single();

      if (currentData) {
        return {
          id: currentData.id,
          storeId: currentData.store_id,
          name: currentData.name,
          email: currentData.email,
          phone: currentData.whatsapp,
          countryCode: currentData.country_code,
          createdAt: new Date(currentData.created_at)
        };
      }

      return undefined;
    }

    const { data, error } = await supabase
      .from('customers')
      .update(updateData)
      .eq('id', id)
      .select('*')
      .single();

    if (error) {
      console.error('Erro ao atualizar cliente:', error);
      return undefined;
    }

    if (!data) {
      console.error('Cliente não encontrado após atualização');
      return undefined;
    }

    console.log('Cliente atualizado com sucesso:', data);

    // Mapear os dados do Supabase para o formato do nosso schema
    return {
      id: data.id,
      storeId: data.store_id,
      name: data.name,
      email: data.email,
      phone: data.whatsapp,
      countryCode: data.country_code,
      createdAt: new Date(data.created_at)
    };
  }

  async getOrder(id: number): Promise<Order | undefined> {
    const { data, error } = await supabase
      .from('orders')
      .select('*')
      .eq('id', id)
      .single();

    if (error || !data) {
      console.error('Erro ao obter pedido:', error);
      return undefined;
    }

    return {
      id: data.id,
      storeId: data.store_id,
      customerId: data.customer_id,
      status: data.status,
      total: data.total,
      paymentMethod: data.payment_method,
      notes: data.notes,
      receivingMethod: data.receiving_method,
      receivingDate: new Date(data.receiving_date),
      receivingTime: data.receiving_time,
      deliveryAddress: data.delivery_address,
      subtotal: data.subtotal,
      deliveryFee: data.delivery_fee,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    };
  }

  async getOrdersByStoreId(storeId: number): Promise<Order[]> {
    const { data, error } = await supabase
      .from('orders')
      .select('*')
      .eq('store_id', storeId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Erro ao obter pedidos da loja:', error);
      return [];
    }

    return data.map(order => ({
      id: order.id,
      storeId: order.store_id,
      customerId: order.customer_id,
      status: order.status,
      total: order.total,
      paymentMethod: order.payment_method,
      notes: order.notes,
      receivingMethod: order.receiving_method,
      receivingDate: new Date(order.receiving_date),
      receivingTime: order.receiving_time,
      deliveryAddress: order.delivery_address,
      subtotal: order.subtotal,
      deliveryFee: order.delivery_fee,
      createdAt: new Date(order.created_at),
      updatedAt: new Date(order.updated_at)
    }));
  }

  async getOrdersByCustomerId(customerId: number): Promise<Order[]> {
    const { data, error } = await supabase
      .from('orders')
      .select('*')
      .eq('customer_id', customerId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Erro ao obter pedidos do cliente:', error);
      return [];
    }

    return data.map(order => ({
      id: order.id,
      storeId: order.store_id,
      customerId: order.customer_id,
      status: order.status,
      total: order.total,
      paymentMethod: order.payment_method,
      notes: order.notes,
      receivingMethod: order.receiving_method,
      receivingDate: new Date(order.receiving_date),
      receivingTime: order.receiving_time,
      deliveryAddress: order.delivery_address,
      subtotal: order.subtotal,
      deliveryFee: order.delivery_fee,
      createdAt: new Date(order.created_at),
      updatedAt: new Date(order.updated_at)
    }));
  }

  async createOrder(order: InsertOrder): Promise<Order> {
    const { data, error } = await supabase
      .from('orders')
      .insert({
        store_id: order.storeId,
        customer_id: order.customerId,
        status: order.status,
        total: order.total,
        payment_method: order.paymentMethod,
        notes: order.notes,
        receiving_method: order.receivingMethod,
        receiving_date: order.receivingDate,
        receiving_time: order.receivingTime,
        delivery_address: order.deliveryAddress,
        subtotal: order.subtotal,
        delivery_fee: order.deliveryFee,
        created_at: new Date(),
        updated_at: new Date()
      })
      .select('*')
      .single();

    if (error) {
      console.error('Erro ao criar pedido:', error);
      throw error;
    }

    // Mapear os dados do Supabase para o formato do nosso schema
    return {
      id: data.id,
      storeId: data.store_id,
      customerId: data.customer_id,
      status: data.status,
      total: data.total,
      paymentMethod: data.payment_method,
      notes: data.notes,
      receivingMethod: data.receiving_method,
      receivingDate: new Date(data.receiving_date),
      receivingTime: data.receiving_time,
      deliveryAddress: data.delivery_address,
      subtotal: data.subtotal,
      deliveryFee: data.delivery_fee,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    };
  }

  async updateOrderStatus(id: number, status: string): Promise<Order | undefined> {
    const { data, error } = await supabase
      .from('orders')
      .update({
        status,
        updated_at: new Date()
      })
      .eq('id', id)
      .select('*')
      .single();

    if (error || !data) {
      console.error('Erro ao atualizar status do pedido:', error);
      return undefined;
    }

    return {
      id: data.id,
      storeId: data.store_id,
      customerId: data.customer_id,
      status: data.status,
      total: data.total,
      paymentMethod: data.payment_method,
      notes: data.notes,
      receivingMethod: data.receiving_method,
      receivingDate: new Date(data.receiving_date),
      receivingTime: data.receiving_time,
      deliveryAddress: data.delivery_address,
      subtotal: data.subtotal,
      deliveryFee: data.delivery_fee,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    };
  }

  async getOrderItemsByOrderId(orderId: number): Promise<OrderItem[]> {
    const { data, error } = await supabase
      .from('order_items')
      .select('*')
      .eq('order_id', orderId);

    if (error) {
      console.error('Erro ao obter itens do pedido:', error);
      return [];
    }

    return data.map(item => ({
      id: item.id,
      orderId: item.order_id,
      productId: item.product_id,
      quantity: item.quantity,
      price: item.price,
      selectedOptions: item.selected_options,
      observation: item.observation,
      productName: item.product_name,
      productDescription: item.product_description,
      isCustomProduct: item.is_custom_product,
      name: item.product_name // Adicionando o campo name para compatibilidade com a interface do cliente
    }));
  }

  async createOrderItem(orderItem: InsertOrderItem): Promise<OrderItem> {
    console.log('Criando item de pedido:', orderItem);

    // Verificar se é um produto personalizado
    const isCustomProduct = orderItem.productId === -1 || (orderItem as any).isCustomProduct;

    const insertData: any = {
      order_id: orderItem.orderId,
      product_id: orderItem.productId,
      quantity: orderItem.quantity,
      price: orderItem.price,
      selected_options: orderItem.selectedOptions,
      observation: orderItem.observation
    };

    // Adicionar campos para produtos personalizados
    if (isCustomProduct) {
      insertData.is_custom_product = true;
      insertData.product_name = (orderItem as any).productName || 'Produto Personalizado';
      insertData.product_description = (orderItem as any).productDescription || '';
    }

    console.log('Dados para inserção:', insertData);

    const { data, error } = await supabase
      .from('order_items')
      .insert(insertData)
      .select('*')
      .single();

    if (error) {
      console.error('Erro ao criar item do pedido:', error);
      throw error;
    }

    console.log('Item de pedido criado com sucesso:', data);

    // Mapear os dados do Supabase para o formato do nosso schema
    return {
      id: data.id,
      orderId: data.order_id,
      productId: data.product_id,
      quantity: data.quantity,
      price: data.price,
      selectedOptions: data.selected_options,
      observation: data.observation,
      productName: data.product_name,
      productDescription: data.product_description,
      isCustomProduct: data.is_custom_product
    };
  }

  async recordStoreVisit(storeVisit: InsertStoreVisit): Promise<StoreVisit> {
    console.log(`Successfully recorded store visit for store ID: ${storeVisit.storeId}`);

    const { data, error } = await supabase
      .from('store_visits')
      .insert({
        store_id: storeVisit.storeId,
        visitor_ip: storeVisit.visitorIp || '',
        session_id: storeVisit.sessionId || '',
        user_agent: storeVisit.userAgent || '',
        referrer: storeVisit.referrer || '',
      })
      .select('*')
      .single();

    if (error) {
      console.error('Erro ao registrar visita:', error);
      // Retornamos um objeto mínimo mesmo em caso de erro para não quebrar a aplicação
      return {
        id: 0,
        storeId: storeVisit.storeId,
        visitorIp: storeVisit.visitorIp || '',
        sessionId: storeVisit.sessionId || '',
        userAgent: storeVisit.userAgent || '',
        referrer: storeVisit.referrer || '',
        createdAt: new Date(),
      };
    }

    return {
      id: data.id,
      storeId: data.store_id,
      visitorIp: data.visitor_ip,
      sessionId: data.session_id,
      userAgent: data.user_agent,
      referrer: data.referrer,
      createdAt: new Date(data.created_at),
    };
  }

  async getStoreVisitsByStoreId(storeId: number, startDate?: Date, endDate?: Date): Promise<StoreVisit[]> {
    let query = supabase
      .from('store_visits')
      .select('*')
      .eq('store_id', storeId);

    if (startDate) {
      query = query.gte('created_at', startDate.toISOString());
    }

    if (endDate) {
      query = query.lte('created_at', endDate.toISOString());
    }

    const { data, error } = await query;

    if (error) {
      console.error('Erro ao obter visitas da loja:', error);
      return [];
    }

    return data.map(visit => ({
      id: visit.id,
      storeId: visit.store_id,
      visitorIp: visit.visitor_ip,
      sessionId: visit.session_id,
      userAgent: visit.user_agent,
      referrer: visit.referrer,
      createdAt: new Date(visit.created_at),
    }));
  }

  async getMonthlyVisitCount(storeId: number): Promise<number> {
    // Calcula o primeiro dia do mês atual
    const now = new Date();
    const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    // Busca todas as visitas deste mês
    const { data, error } = await supabase
      .from('store_visits')
      .select('id')
      .eq('store_id', storeId)
      .gte('created_at', firstDayOfMonth.toISOString());

    if (error) {
      console.error('Erro ao contar visitas mensais:', error);
      return 0;
    }

    // Retorna o número de visitas
    return data.length;
  }

  async getMonthlyOrderCount(storeId: number): Promise<number> {
    // Calcula o primeiro dia do mês atual
    const now = new Date();
    const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    // Busca todas os pedidos deste mês
    const { data, error } = await supabase
      .from('orders')
      .select('id')
      .eq('store_id', storeId)
      .gte('created_at', firstDayOfMonth.toISOString());

    if (error) {
      console.error('Erro ao contar pedidos mensais:', error);
      return 0;
    }

    // Retorna o número de pedidos
    return data.length;
  }

  async getCartItemsBySessionId(storeId: number, sessionId: string): Promise<CartItem[]> {
    // Implementação pendente
    return [];
  }

  async getCartItemsByUserId(storeId: number, userId: string): Promise<CartItem[]> {
    // Implementação pendente
    return [];
  }

  async createCartItem(cartItem: InsertCartItem): Promise<CartItem> {
    // Implementação pendente
    return {} as CartItem;
  }

  async updateCartItem(id: number, cartItem: Partial<CartItem>): Promise<CartItem | undefined> {
    // Implementação pendente
    return undefined;
  }

  async deleteCartItem(id: number): Promise<boolean> {
    // Implementação pendente
    return true;
  }

  async clearCartItems(storeId: number, sessionId: string): Promise<boolean> {
    // Implementação pendente
    return true;
  }

  // Order Revision methods
  async getOrderRevision(id: number): Promise<OrderRevision | undefined> {
    const { data, error } = await supabase
      .from('order_revisions')
      .select('*')
      .eq('id', id)
      .single();

    if (error || !data) return undefined;

    return {
      id: data.id,
      orderId: data.order_id,
      revisionNumber: data.revision_number,
      status: data.status,
      receivingMethod: data.receiving_method,
      receivingDate: data.receiving_date ? new Date(data.receiving_date) : null,
      receivingTime: data.receiving_time,
      deliveryAddress: data.delivery_address,
      paymentMethod: data.payment_method,
      subtotal: data.subtotal,
      deliveryFee: data.delivery_fee,
      total: data.total,
      notes: data.notes,
      createdBy: data.created_by,
      isCurrent: data.is_current,
      createdAt: new Date(data.created_at)
    };
  }

  async getOrderRevisionsByOrderId(orderId: number): Promise<OrderRevision[]> {
    const { data, error } = await supabase
      .from('order_revisions')
      .select('*')
      .eq('order_id', orderId)
      .order('revision_number', { ascending: false });

    if (error || !data) return [];

    // Log para depuração
    if (data.length > 0) {
      console.log('Exemplo de dados de revisão obtidos do banco:', {
        id: data[0].id,
        discount_type: data[0].discount_type,
        original_percentage: data[0].original_percentage
      });
    }

    return data.map(item => ({
      id: item.id,
      orderId: item.order_id,
      revisionNumber: item.revision_number,
      status: item.status,
      receivingMethod: item.receiving_method,
      receivingDate: item.receiving_date ? new Date(item.receiving_date) : null,
      receivingTime: item.receiving_time,
      deliveryAddress: item.delivery_address,
      paymentMethod: item.payment_method,
      subtotal: item.subtotal,
      deliveryFee: item.delivery_fee,
      discount: item.discount || 0,
      discountType: item.discount_type || 'fixed',
      originalPercentage: item.original_percentage || null,
      couponId: item.coupon_id || null,
      couponCode: item.coupon_code || null,
      couponType: item.coupon_type || null,
      total: item.total,
      notes: item.notes,
      createdBy: item.created_by,
      isCurrent: item.is_current,
      createdAt: new Date(item.created_at)
    }));
  }

  async getOrderRevisionItems(revisionId: number): Promise<OrderRevisionItem[]> {
    const { data, error } = await supabase
      .from('order_revision_items')
      .select('*')
      .eq('revision_id', revisionId);

    if (error || !data) return [];

    return data.map(item => ({
      id: item.id,
      revisionId: item.revision_id,
      productId: item.product_id,
      productName: item.product_name,
      productDescription: item.product_description,
      productImage: item.product_image,
      quantity: item.quantity,
      unitPrice: item.unit_price,
      subtotal: item.subtotal,
      selectedVariations: item.selected_variations,
      observation: item.observation
    }));
  }

  async createOrderRevision(insertRevision: InsertOrderRevision): Promise<OrderRevision> {
    const { data, error } = await supabase
      .from('order_revisions')
      .insert({
        order_id: insertRevision.orderId,
        revision_number: insertRevision.revisionNumber,
        status: insertRevision.status,
        receiving_method: insertRevision.receivingMethod,
        receiving_date: insertRevision.receivingDate,
        receiving_time: insertRevision.receivingTime,
        delivery_address: insertRevision.deliveryAddress,
        payment_method: insertRevision.paymentMethod,
        subtotal: insertRevision.subtotal,
        delivery_fee: insertRevision.deliveryFee,
        total: insertRevision.total,
        notes: insertRevision.notes,
        created_by: insertRevision.createdBy,
        is_current: insertRevision.isCurrent,
        created_at: new Date()
      })
      .select('*')
      .single();

    if (error) {
      console.error('Erro ao criar revisão de pedido:', error);
      throw error;
    }

    return {
      id: data.id,
      orderId: data.order_id,
      revisionNumber: data.revision_number,
      status: data.status,
      receivingMethod: data.receiving_method,
      receivingDate: data.receiving_date ? new Date(data.receiving_date) : null,
      receivingTime: data.receiving_time,
      deliveryAddress: data.delivery_address,
      paymentMethod: data.payment_method,
      subtotal: data.subtotal,
      deliveryFee: data.delivery_fee,
      discount: data.discount || 0,
      discountType: data.discount_type || 'fixed',
      originalPercentage: data.original_percentage || null,
      couponId: data.coupon_id || null,
      couponCode: data.coupon_code || null,
      couponType: data.coupon_type || null,
      total: data.total,
      notes: data.notes,
      createdBy: data.created_by,
      isCurrent: data.is_current,
      createdAt: new Date(data.created_at)
    };
  }

  async createOrderRevisionItem(insertItem: InsertOrderRevisionItem): Promise<OrderRevisionItem> {
    const { data, error } = await supabase
      .from('order_revision_items')
      .insert({
        revision_id: insertItem.revisionId,
        product_id: insertItem.productId,
        product_name: insertItem.productName,
        product_description: insertItem.productDescription,
        product_image: insertItem.productImage,
        quantity: insertItem.quantity,
        unit_price: insertItem.unitPrice,
        subtotal: insertItem.subtotal,
        selected_variations: insertItem.selectedVariations,
        observation: insertItem.observation
      })
      .select('*')
      .single();

    if (error) {
      console.error('Erro ao criar item de revisão de pedido:', error);
      throw error;
    }

    return {
      id: data.id,
      revisionId: data.revision_id,
      productId: data.product_id,
      productName: data.product_name,
      productDescription: data.product_description,
      productImage: data.product_image,
      quantity: data.quantity,
      unitPrice: data.unit_price,
      subtotal: data.subtotal,
      selectedVariations: data.selected_variations,
      observation: data.observation
    };
  }

  async getLatestRevisionNumber(orderId: number): Promise<number> {
    const { data, error } = await supabase
      .from('order_revisions')
      .select('revision_number')
      .eq('order_id', orderId)
      .order('revision_number', { ascending: false })
      .limit(1)
      .single();

    if (error) {
      console.log('Nenhuma revisão encontrada para o pedido:', orderId);
      return 0; // Se não houver revisões anteriores, começa do 0
    }

    return data.revision_number || 0;
  }

  async updateOrderRevisionStatus(id: number, status: string): Promise<OrderRevision | undefined> {
    const { data, error } = await supabase
      .from('order_revisions')
      .update({ status })
      .eq('id', id)
      .select('*')
      .single();

    if (error || !data) {
      console.error('Erro ao atualizar status da revisão:', error);
      return undefined;
    }

    return {
      id: data.id,
      orderId: data.order_id,
      revisionNumber: data.revision_number,
      status: data.status,
      receivingMethod: data.receiving_method,
      receivingDate: data.receiving_date ? new Date(data.receiving_date) : null,
      receivingTime: data.receiving_time,
      deliveryAddress: data.delivery_address,
      paymentMethod: data.payment_method,
      subtotal: data.subtotal,
      deliveryFee: data.delivery_fee,
      discount: data.discount || 0,
      discountType: data.discount_type || 'fixed',
      originalPercentage: data.original_percentage || null,
      couponId: data.coupon_id || null,
      couponCode: data.coupon_code || null,
      couponType: data.coupon_type || null,
      total: data.total,
      notes: data.notes,
      createdBy: data.created_by,
      isCurrent: data.is_current,
      createdAt: new Date(data.created_at)
    };
  }

  async updateOrderRevision(id: number, revisionData: Partial<OrderRevision>): Promise<OrderRevision | undefined> {
    console.log('Atualizando revisão de pedido com dados:', revisionData);

    // Converter de camelCase para snake_case para o formato do Supabase
    const updateData: Record<string, any> = {};

    if ('status' in revisionData) updateData.status = revisionData.status;
    if ('receivingMethod' in revisionData) updateData.receiving_method = revisionData.receivingMethod;
    if ('receivingDate' in revisionData) updateData.receiving_date = revisionData.receivingDate;
    if ('receivingTime' in revisionData) updateData.receiving_time = revisionData.receivingTime;
    if ('deliveryAddress' in revisionData) updateData.delivery_address = revisionData.deliveryAddress;
    if ('paymentMethod' in revisionData) updateData.payment_method = revisionData.paymentMethod;
    if ('subtotal' in revisionData) updateData.subtotal = revisionData.subtotal;
    if ('deliveryFee' in revisionData) updateData.delivery_fee = revisionData.deliveryFee;
    if ('discount' in revisionData) updateData.discount = revisionData.discount;
    if ('discountType' in revisionData) updateData.discount_type = revisionData.discountType;
    if ('originalPercentage' in revisionData) updateData.original_percentage = revisionData.originalPercentage;
    if ('couponId' in revisionData) updateData.coupon_id = revisionData.couponId;
    if ('couponCode' in revisionData) updateData.coupon_code = revisionData.couponCode;
    if ('couponType' in revisionData) updateData.coupon_type = revisionData.couponType;
    if ('total' in revisionData) updateData.total = revisionData.total;
    if ('notes' in revisionData) updateData.notes = revisionData.notes;
    if ('isCurrent' in revisionData) updateData.is_current = revisionData.isCurrent;

    console.log('Dados formatados para atualização:', updateData);

    const { data, error } = await supabase
      .from('order_revisions')
      .update(updateData)
      .eq('id', id)
      .select('*')
      .single();

    if (error || !data) {
      console.error('Erro ao atualizar revisão de pedido:', error);
      return undefined;
    }

    console.log('Revisão atualizada com sucesso:', data);

    return {
      id: data.id,
      orderId: data.order_id,
      revisionNumber: data.revision_number,
      status: data.status,
      receivingMethod: data.receiving_method,
      receivingDate: data.receiving_date ? new Date(data.receiving_date) : null,
      receivingTime: data.receiving_time,
      deliveryAddress: data.delivery_address,
      paymentMethod: data.payment_method,
      subtotal: data.subtotal,
      deliveryFee: data.delivery_fee,
      discount: data.discount || 0,
      discountType: data.discount_type || 'fixed',
      originalPercentage: data.original_percentage || null,
      couponId: data.coupon_id || null,
      couponCode: data.coupon_code || null,
      couponType: data.coupon_type || null,
      total: data.total,
      notes: data.notes,
      createdBy: data.created_by,
      isCurrent: data.is_current,
      createdAt: new Date(data.created_at)
    };
  }

  async setCurrentRevision(revisionId: number, orderId: number): Promise<void> {
    // Primeiro, remove o flag de todas as revisões deste pedido
    await supabase
      .from('order_revisions')
      .update({ is_current: false })
      .eq('order_id', orderId);

    // Em seguida, define a revisão especificada como atual
    await supabase
      .from('order_revisions')
      .update({ is_current: true })
      .eq('id', revisionId);
  }

  async clearCurrentRevisions(orderId: number): Promise<void> {
    // Define todas as revisões deste pedido como não atuais
    await supabase
      .from('order_revisions')
      .update({ is_current: false })
      .eq('order_id', orderId);
  }

  async deleteOrderRevision(id: number): Promise<boolean> {
    try {
      // Primeiro, exclui os itens da revisão
      const { error: itemsError } = await supabase
        .from('order_revision_items')
        .delete()
        .eq('revision_id', id);

      if (itemsError) {
        console.error('Erro ao excluir itens da revisão:', itemsError);
        return false;
      }

      // Em seguida, exclui a revisão
      const { error } = await supabase
        .from('order_revisions')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Erro ao excluir revisão:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Erro ao excluir revisão:', error);
      return false;
    }
  }
}
