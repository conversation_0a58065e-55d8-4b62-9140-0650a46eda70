import type { Request, Response, NextFunction } from "express";
import { subscriptionService } from "./subscription-service";
import { storage } from "./storage";
import { type PlanLimits } from "@shared/schema";

// Middleware para verificar se uma funcionalidade está disponível no plano
export function requireFeature(feature: keyof PlanLimits) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const firebaseUid = (req as any).user?.uid;
      
      if (!firebaseUid) {
        return res.status(401).json({ message: "Usuário não autenticado" });
      }

      // Obter loja do usuário
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(404).json({ message: "Loja não encontrada" });
      }

      // Verificar se a funcionalidade está disponível
      const isAvailable = await subscriptionService.isFeatureAvailableForStore(store.id, feature);
      
      if (!isAvailable) {
        return res.status(403).json({ 
          message: "Funcionalidade não disponível no seu plano atual",
          feature,
          upgradeRequired: true
        });
      }

      // Adicionar informações da loja ao request para uso posterior
      (req as any).store = store;
      next();
    } catch (error) {
      console.error('Erro no middleware de verificação de funcionalidade:', error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  };
}

// Middleware para verificar limites numéricos (produtos, pedidos)
export function checkLimit(feature: 'maxProducts' | 'maxOrdersPerMonth') {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const firebaseUid = (req as any).user?.uid;
      
      if (!firebaseUid) {
        return res.status(401).json({ message: "Usuário não autenticado" });
      }

      // Obter loja do usuário
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (!store) {
        return res.status(404).json({ message: "Loja não encontrada" });
      }

      // Obter contagem atual
      let currentCount = 0;
      if (feature === 'maxProducts') {
        currentCount = await subscriptionService.getCurrentProductCount(store.id);
      } else if (feature === 'maxOrdersPerMonth') {
        currentCount = await subscriptionService.getCurrentMonthOrderCount(store.id);
      }

      // Verificar se o limite foi excedido
      const isExceeded = await subscriptionService.isLimitExceededForStore(store.id, feature, currentCount);
      
      if (isExceeded) {
        const usageInfo = await subscriptionService.getUsageInfo(store.id);
        const limit = feature === 'maxProducts' 
          ? usageInfo.usage.products.limit 
          : usageInfo.usage.orders.limit;

        return res.status(403).json({ 
          message: `Limite de ${feature === 'maxProducts' ? 'produtos' : 'pedidos'} excedido`,
          feature,
          currentCount,
          limit,
          upgradeRequired: true
        });
      }

      // Adicionar informações da loja ao request para uso posterior
      (req as any).store = store;
      next();
    } catch (error) {
      console.error('Erro no middleware de verificação de limite:', error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  };
}

// Middleware para verificar se pode gerar PDFs
export const requirePdfGeneration = requireFeature('allowPdfGeneration');

// Middleware para verificar se pode usar analytics
export const requireAnalytics = requireFeature('allowAnalytics');

// Middleware para verificar se pode usar integração WhatsApp
export const requireWhatsappIntegration = requireFeature('allowWhatsappIntegration');

// Middleware para verificar se pode usar cupons
export const requireCoupons = requireFeature('allowCoupons');

// Middleware para verificar se pode usar customização
export const requireCustomization = requireFeature('allowCustomization');

// Middleware para verificar limite de produtos
export const checkProductLimit = checkLimit('maxProducts');

// Middleware para verificar limite de pedidos
export const checkOrderLimit = checkLimit('maxOrdersPerMonth');

// Middleware para adicionar informações de plano ao response
export async function addPlanInfo(req: Request, res: Response, next: NextFunction) {
  try {
    const firebaseUid = (req as any).user?.uid;
    
    if (firebaseUid) {
      // Obter loja do usuário
      const store = await storage.getStoreByFirebaseUid(firebaseUid);
      if (store) {
        const usageInfo = await subscriptionService.getUsageInfo(store.id);
        (req as any).planInfo = usageInfo;
      }
    }

    next();
  } catch (error) {
    console.error('Erro ao adicionar informações de plano:', error);
    // Não bloquear a requisição, apenas continuar sem as informações de plano
    next();
  }
}

// Função helper para verificar funcionalidade em rotas específicas
export async function checkFeatureForStore(storeId: number, feature: keyof PlanLimits): Promise<boolean> {
  try {
    return await subscriptionService.isFeatureAvailableForStore(storeId, feature);
  } catch (error) {
    console.error('Erro ao verificar funcionalidade:', error);
    return false; // Em caso de erro, negar acesso
  }
}

// Função helper para verificar limite em rotas específicas
export async function checkLimitForStore(
  storeId: number, 
  feature: 'maxProducts' | 'maxOrdersPerMonth', 
  currentCount: number
): Promise<boolean> {
  try {
    return await subscriptionService.isLimitExceededForStore(storeId, feature, currentCount);
  } catch (error) {
    console.error('Erro ao verificar limite:', error);
    return true; // Em caso de erro, considerar limite excedido
  }
}

// Middleware para verificar se a loja tem assinatura ativa
export async function requireActiveSubscription(req: Request, res: Response, next: NextFunction) {
  try {
    const firebaseUid = (req as any).user?.uid;
    
    if (!firebaseUid) {
      return res.status(401).json({ message: "Usuário não autenticado" });
    }

    // Obter loja do usuário
    const store = await storage.getStoreByFirebaseUid(firebaseUid);
    if (!store) {
      return res.status(404).json({ message: "Loja não encontrada" });
    }

    // Verificar se tem assinatura ativa
    const subscription = await subscriptionService.getActiveSubscription(store.id);
    
    if (!subscription) {
      // Criar assinatura gratuita se não existir
      await subscriptionService.createFreeSubscription(store.id);
    } else if (subscription.status !== 'active') {
      return res.status(403).json({ 
        message: "Assinatura inativa",
        status: subscription.status,
        subscriptionRequired: true
      });
    }

    // Adicionar informações da loja ao request
    (req as any).store = store;
    next();
  } catch (error) {
    console.error('Erro no middleware de assinatura ativa:', error);
    res.status(500).json({ message: "Erro interno do servidor" });
  }
}
