import { and, desc, eq, isNotNull, lt, gte, lte } from 'drizzle-orm';
import { db } from './db';
import { 
  users, categories, products, customers, orders, orderItems, stores, storeVisits,
  productVariations, variationOptions,
  type User, type InsertUser, type UpsertUser, type Store, type InsertStore, type Category, type InsertCategory,
  type Product, type InsertProduct, type Customer, type InsertCustomer, type Order, type InsertOrder,
  type OrderItem, type InsertOrderItem, type StoreVisit, type InsertStoreVisit,
  type ProductVariation, type InsertProductVariation, type VariationOption, type InsertVariationOption 
} from '@shared/schema';

export interface IStorage {
  // User methods - updated for Replit Auth
  getUser(id: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  upsertUser(user: UpsertUser): Promise<User>;

  // Store methods
  getStore(id: number): Promise<Store | undefined>;
  getStoreBySlug(slug: string): Promise<Store | undefined>;
  getStoreByUserId(userId: number): Promise<Store | undefined>;
  createStore(store: InsertStore): Promise<Store>;
  updateStore(id: number, store: Partial<Store>): Promise<Store | undefined>;

  // Product methods
  getProduct(id: number): Promise<Product | undefined>;
  getProductsByStoreId(storeId: number): Promise<Product[]>;
  getProductsByCategoryId(categoryId: number): Promise<Product[]>;
  createProduct(product: InsertProduct): Promise<Product>;
  updateProduct(id: number, product: Partial<Product>): Promise<Product | undefined>;
  deleteProduct(id: number): Promise<boolean>;

  // Product Variation methods
  getProductVariationsByProductId(productId: number): Promise<ProductVariation[]>;
  createProductVariation(variation: InsertProductVariation): Promise<ProductVariation>;
  updateProductVariation(id: number, variation: Partial<ProductVariation>): Promise<ProductVariation | undefined>;
  deleteProductVariation(id: number): Promise<boolean>;
  
  // Variation Option methods
  getVariationOptionsByVariationId(variationId: number): Promise<VariationOption[]>;
  createVariationOption(option: InsertVariationOption): Promise<VariationOption>;
  updateVariationOption(id: number, option: Partial<VariationOption>): Promise<VariationOption | undefined>;
  deleteVariationOption(id: number): Promise<boolean>;

  // Category methods
  getCategory(id: number): Promise<Category | undefined>;
  getCategoriesByStoreId(storeId: number): Promise<Category[]>;
  createCategory(category: InsertCategory): Promise<Category>;
  updateCategory(id: number, category: Partial<Category>): Promise<Category | undefined>;
  deleteCategory(id: number): Promise<boolean>;

  // Customer methods
  getCustomer(id: number): Promise<Customer | undefined>;
  getCustomersByStoreId(storeId: number): Promise<Customer[]>;
  createCustomer(customer: InsertCustomer): Promise<Customer>;

  // Order methods
  getOrder(id: number): Promise<Order | undefined>;
  getOrdersByStoreId(storeId: number): Promise<Order[]>;
  getOrdersByCustomerId(customerId: number): Promise<Order[]>;
  createOrder(order: InsertOrder): Promise<Order>;
  updateOrderStatus(id: number, status: string): Promise<Order | undefined>;

  // Order item methods
  getOrderItemsByOrderId(orderId: number): Promise<OrderItem[]>;
  createOrderItem(orderItem: InsertOrderItem): Promise<OrderItem>;

  // Analytics methods
  recordStoreVisit(storeVisit: InsertStoreVisit): Promise<StoreVisit>;
  getStoreVisitsByStoreId(storeId: number, startDate?: Date, endDate?: Date): Promise<StoreVisit[]>;
  getMonthlyVisitCount(storeId: number): Promise<number>;
  getMonthlyOrderCount(storeId: number): Promise<number>;
}

export class MemStorage implements IStorage {
  private userMap: Map<number, User>;
  private storeMap: Map<number, Store>;
  private productMap: Map<number, Product>;
  private categoryMap: Map<number, Category>;
  private customerMap: Map<number, Customer>;
  private orderMap: Map<number, Order>;
  private orderItemMap: Map<number, OrderItem>;
  private storeVisitMap: Map<number, StoreVisit>;
  private profileMap: Map<string, Profile>;
  private productVariationMap: Map<number, ProductVariation>;
  private variationOptionMap: Map<number, VariationOption>;
  
  private userId: number;
  private storeId: number;
  private productId: number;
  private categoryId: number;
  private customerId: number;
  private orderId: number;
  private orderItemId: number;
  private storeVisitId: number;
  private productVariationId: number;
  private variationOptionId: number;
  
  constructor() {
    this.userMap = new Map();
    this.storeMap = new Map();
    this.productMap = new Map();
    this.categoryMap = new Map();
    this.customerMap = new Map();
    this.orderMap = new Map();
    this.orderItemMap = new Map();
    this.storeVisitMap = new Map();
    this.profileMap = new Map();
    this.productVariationMap = new Map();
    this.variationOptionMap = new Map();
    
    this.userId = 1;
    this.storeId = 1;
    this.productId = 1;
    this.categoryId = 1;
    this.customerId = 1;
    this.orderId = 1;
    this.orderItemId = 1;
    this.storeVisitId = 1;
    this.productVariationId = 1;
    this.variationOptionId = 1;
  }

  async getUser(id: number): Promise<User | undefined> {
    return this.userMap.get(id);
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    for (const user of this.userMap.values()) {
      if (user.email === email) {
        return user;
      }
    }
    return undefined;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.userId++;
    const user = { ...insertUser, id, createdAt: new Date() };
    this.userMap.set(id, user);
    return user;
  }

  async getStore(id: number): Promise<Store | undefined> {
    return this.storeMap.get(id);
  }

  async getStoreBySlug(slug: string): Promise<Store | undefined> {
    for (const store of this.storeMap.values()) {
      if (store.slug === slug) {
        return store;
      }
    }
    return undefined;
  }

  async getStoreByUserId(userId: number): Promise<Store | undefined> {
    for (const store of this.storeMap.values()) {
      if (store.userId === userId) {
        return store;
      }
    }
    return undefined;
  }

  async createStore(insertStore: InsertStore): Promise<Store> {
    const id = this.storeId++;
    const store = { ...insertStore, id, createdAt: new Date() };
    this.storeMap.set(id, store);
    return store;
  }

  async updateStore(id: number, storeUpdate: Partial<Store>): Promise<Store | undefined> {
    const store = this.storeMap.get(id);
    if (!store) return undefined;
    
    const updatedStore = { ...store, ...storeUpdate };
    this.storeMap.set(id, updatedStore);
    return updatedStore;
  }

  async getProduct(id: number): Promise<Product | undefined> {
    return this.productMap.get(id);
  }

  async getProductsByStoreId(storeId: number): Promise<Product[]> {
    const products: Product[] = [];
    for (const product of this.productMap.values()) {
      if (product.storeId === storeId) {
        products.push(product);
      }
    }
    return products;
  }

  async getProductsByCategoryId(categoryId: number): Promise<Product[]> {
    const products: Product[] = [];
    for (const product of this.productMap.values()) {
      if (product.categoryId === categoryId) {
        products.push(product);
      }
    }
    return products;
  }

  async createProduct(insertProduct: InsertProduct): Promise<Product> {
    const id = this.productId++;
    const product = { ...insertProduct, id, createdAt: new Date() };
    this.productMap.set(id, product);
    return product;
  }

  async updateProduct(id: number, productUpdate: Partial<Product>): Promise<Product | undefined> {
    const product = this.productMap.get(id);
    if (!product) return undefined;
    
    const updatedProduct = { ...product, ...productUpdate };
    this.productMap.set(id, updatedProduct);
    return updatedProduct;
  }

  async deleteProduct(id: number): Promise<boolean> {
    return this.productMap.delete(id);
  }

  async getCategory(id: number): Promise<Category | undefined> {
    return this.categoryMap.get(id);
  }

  async getCategoriesByStoreId(storeId: number): Promise<Category[]> {
    const categories: Category[] = [];
    for (const category of this.categoryMap.values()) {
      if (category.storeId === storeId) {
        categories.push(category);
      }
    }
    return categories;
  }

  async createCategory(insertCategory: InsertCategory): Promise<Category> {
    const id = this.categoryId++;
    const category = { ...insertCategory, id };
    this.categoryMap.set(id, category);
    return category;
  }

  async updateCategory(id: number, categoryUpdate: Partial<Category>): Promise<Category | undefined> {
    const category = this.categoryMap.get(id);
    if (!category) return undefined;
    
    const updatedCategory = { ...category, ...categoryUpdate };
    this.categoryMap.set(id, updatedCategory);
    return updatedCategory;
  }

  async deleteCategory(id: number): Promise<boolean> {
    return this.categoryMap.delete(id);
  }

  async getCustomer(id: number): Promise<Customer | undefined> {
    return this.customerMap.get(id);
  }

  async getCustomersByStoreId(storeId: number): Promise<Customer[]> {
    const customers: Customer[] = [];
    for (const customer of this.customerMap.values()) {
      if (customer.storeId === storeId) {
        customers.push(customer);
      }
    }
    return customers;
  }

  async createCustomer(insertCustomer: InsertCustomer): Promise<Customer> {
    const id = this.customerId++;
    const customer = { ...insertCustomer, id, createdAt: new Date() };
    this.customerMap.set(id, customer);
    return customer;
  }

  async getOrder(id: number): Promise<Order | undefined> {
    return this.orderMap.get(id);
  }

  async getOrdersByStoreId(storeId: number): Promise<Order[]> {
    const orders: Order[] = [];
    for (const order of this.orderMap.values()) {
      if (order.storeId === storeId) {
        orders.push(order);
      }
    }
    return orders;
  }

  async getOrdersByCustomerId(customerId: number): Promise<Order[]> {
    const orders: Order[] = [];
    for (const order of this.orderMap.values()) {
      if (order.customerId === customerId) {
        orders.push(order);
      }
    }
    return orders;
  }

  async createOrder(insertOrder: InsertOrder): Promise<Order> {
    const id = this.orderId++;
    const order = { ...insertOrder, id, createdAt: new Date() };
    this.orderMap.set(id, order);
    return order;
  }

  async updateOrderStatus(id: number, status: string): Promise<Order | undefined> {
    const order = this.orderMap.get(id);
    if (!order) return undefined;
    
    const updatedOrder = { ...order, status };
    this.orderMap.set(id, updatedOrder);
    return updatedOrder;
  }

  async getOrderItemsByOrderId(orderId: number): Promise<OrderItem[]> {
    const orderItems: OrderItem[] = [];
    for (const orderItem of this.orderItemMap.values()) {
      if (orderItem.orderId === orderId) {
        orderItems.push(orderItem);
      }
    }
    return orderItems;
  }

  async createOrderItem(insertOrderItem: InsertOrderItem): Promise<OrderItem> {
    const id = this.orderItemId++;
    const orderItem = { ...insertOrderItem, id };
    this.orderItemMap.set(id, orderItem);
    return orderItem;
  }

  async recordStoreVisit(insertStoreVisit: InsertStoreVisit): Promise<StoreVisit> {
    const id = this.storeVisitId++;
    const storeVisit = { ...insertStoreVisit, id, visitDate: new Date() };
    this.storeVisitMap.set(id, storeVisit);
    return storeVisit;
  }

  async getStoreVisitsByStoreId(storeId: number, startDate?: Date, endDate?: Date): Promise<StoreVisit[]> {
    const storeVisits: StoreVisit[] = [];
    for (const storeVisit of this.storeVisitMap.values()) {
      if (storeVisit.storeId === storeId) {
        if (startDate && storeVisit.visitDate < startDate) continue;
        if (endDate && storeVisit.visitDate > endDate) continue;
        storeVisits.push(storeVisit);
      }
    }
    return storeVisits;
  }

  async getMonthlyVisitCount(storeId: number): Promise<number> {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    
    const visits = await this.getStoreVisitsByStoreId(storeId, startOfMonth, endOfMonth);
    return visits.length;
  }

  async getMonthlyOrderCount(storeId: number): Promise<number> {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    
    let count = 0;
    for (const order of this.orderMap.values()) {
      if (order.storeId === storeId && order.createdAt >= startOfMonth && order.createdAt <= endOfMonth) {
        count++;
      }
    }
    return count;
  }

  async getProfile(id: string): Promise<Profile | undefined> {
    return this.profileMap.get(id);
  }

  async getProfileByEmail(email: string): Promise<Profile | undefined> {
    for (const profile of this.profileMap.values()) {
      if (profile.email === email) {
        return profile;
      }
    }
    return undefined;
  }

  async createProfile(insertProfile: InsertProfile): Promise<Profile> {
    const profile = { ...insertProfile, created_at: new Date() };
    this.profileMap.set(profile.id, profile);
    return profile;
  }

  // Product Variation methods
  async getProductVariationsByProductId(productId: number): Promise<ProductVariation[]> {
    const variations: ProductVariation[] = [];
    for (const variation of this.productVariationMap.values()) {
      if (variation.productId === productId) {
        variations.push(variation);
      }
    }
    return variations;
  }

  async createProductVariation(insertVariation: InsertProductVariation): Promise<ProductVariation> {
    const id = this.productVariationId++;
    const variation = { ...insertVariation, id };
    this.productVariationMap.set(id, variation);
    return variation;
  }

  async updateProductVariation(id: number, variationUpdate: Partial<ProductVariation>): Promise<ProductVariation | undefined> {
    const variation = this.productVariationMap.get(id);
    if (!variation) return undefined;
    
    const updatedVariation = { ...variation, ...variationUpdate };
    this.productVariationMap.set(id, updatedVariation);
    return updatedVariation;
  }

  async deleteProductVariation(id: number): Promise<boolean> {
    // Quando excluir uma variação, também exclui todas as opções dessa variação
    const optionsToDelete: number[] = [];
    
    // Encontra as opções a serem excluídas
    for (const option of this.variationOptionMap.values()) {
      if (option.variationId === id) {
        optionsToDelete.push(option.id);
      }
    }
    
    // Exclui as opções
    for (const optionId of optionsToDelete) {
      this.variationOptionMap.delete(optionId);
    }
    
    // Exclui a variação
    return this.productVariationMap.delete(id);
  }
  
  // Variation Option methods
  async getVariationOptionsByVariationId(variationId: number): Promise<VariationOption[]> {
    const options: VariationOption[] = [];
    for (const option of this.variationOptionMap.values()) {
      if (option.variationId === variationId) {
        options.push(option);
      }
    }
    return options;
  }

  async createVariationOption(insertOption: InsertVariationOption): Promise<VariationOption> {
    const id = this.variationOptionId++;
    const option = { ...insertOption, id };
    this.variationOptionMap.set(id, option);
    return option;
  }

  async updateVariationOption(id: number, optionUpdate: Partial<VariationOption>): Promise<VariationOption | undefined> {
    const option = this.variationOptionMap.get(id);
    if (!option) return undefined;
    
    const updatedOption = { ...option, ...optionUpdate };
    this.variationOptionMap.set(id, updatedOption);
    return updatedOption;
  }

  async deleteVariationOption(id: number): Promise<boolean> {
    return this.variationOptionMap.delete(id);
  }
}

export class DatabaseStorage implements IStorage {
  // User methods
  async getUser(id: number): Promise<User | undefined> {
    try {
      const { data: user, error } = await supabaseAdmin
        .from('users')
        .select('*')
        .eq('id', id)
        .maybeSingle();
        
      if (error) {
        console.error('Error fetching user by ID:', error);
        return undefined;
      }
      
      return user as User;
    } catch (error) {
      console.error('Unexpected error in getUser:', error);
      return undefined;
    }
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    try {
      console.log('Fetching user by email with admin client to bypass RLS...');
      
      const { data: user, error } = await supabaseAdmin
        .from('users')
        .select('*')
        .eq('email', email)
        .maybeSingle();
        
      if (error) {
        console.error('Error fetching user by email:', error);
        return undefined;
      }
      
      return user as User;
    } catch (error) {
      console.error('Unexpected error in getUserByEmail:', error);
      return undefined;
    }
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    try {
      const { data, error } = await supabaseAdmin
        .from('users')
        .insert([{
          email: insertUser.email,
          password: insertUser.password,
          full_name: insertUser.fullName
        }])
        .select()
        .single();
        
      if (error) {
        console.error('Error creating user:', error);
        throw new Error(`Failed to create user: ${error.message}`);
      }
      
      return {
        id: data.id,
        email: data.email,
        password: data.password,
        fullName: data.full_name,
        createdAt: new Date(data.created_at)
      };
    } catch (error) {
      console.error('Unexpected error in createUser:', error);
      throw error;
    }
  }

  // Store methods
  async getStore(id: number): Promise<Store | undefined> {
    try {
      const { data: store, error } = await supabaseAdmin
        .from('stores')
        .select('*')
        .eq('id', id)
        .maybeSingle();
        
      if (error) {
        console.error('Error fetching store by ID:', error);
        return undefined;
      }

      if (!store) return undefined;
      
      return {
        id: store.id,
        name: store.name,
        description: store.description,
        userId: store.user_id,
        slug: store.slug,
        logo: store.logo,
        colors: store.colors,
        paymentMethods: store.payment_methods,
        createdAt: new Date(store.created_at)
      };
    } catch (error) {
      console.error('Unexpected error in getStore:', error);
      return undefined;
    }
  }

  async getStoreBySlug(slug: string): Promise<Store | undefined> {
    try {
      const { data: store, error } = await supabaseAdmin
        .from('stores')
        .select('*')
        .eq('slug', slug)
        .maybeSingle();
        
      if (error) {
        console.error('Error fetching store by slug:', error);
        return undefined;
      }

      if (!store) return undefined;
      
      return {
        id: store.id,
        name: store.name,
        description: store.description,
        userId: store.user_id,
        slug: store.slug,
        logo: store.logo,
        colors: store.colors,
        paymentMethods: store.payment_methods,
        createdAt: new Date(store.created_at)
      };
    } catch (error) {
      console.error('Unexpected error in getStoreBySlug:', error);
      return undefined;
    }
  }

  async getStoreByUserId(userId: number): Promise<Store | undefined> {
    try {
      console.log('Fetching store by user ID with admin client to bypass RLS...');
      
      const { data: store, error } = await supabaseAdmin
        .from('stores')
        .select('*')
        .eq('user_id', userId)
        .maybeSingle();
        
      if (error) {
        console.error('Error fetching store by user ID:', error);
        return undefined;
      }

      if (!store) {
        console.log('Store not found for user ID:', userId);
        return undefined;
      }
      
      console.log('Store found for user ID:', userId);
      
      return {
        id: store.id,
        name: store.name,
        description: store.description,
        userId: store.user_id,
        slug: store.slug,
        logo: store.logo,
        colors: store.colors,
        paymentMethods: store.payment_methods,
        createdAt: new Date(store.created_at)
      };
    } catch (error) {
      console.error('Unexpected error in getStoreByUserId:', error);
      return undefined;
    }
  }

  async createStore(insertStore: InsertStore): Promise<Store> {
    try {
      // Converta de camelCase para snake_case para o banco
      const { data, error } = await supabaseAdmin
        .from('stores')
        .insert([{
          name: insertStore.name,
          description: insertStore.description || null,
          user_id: insertStore.userId,
          slug: insertStore.slug,
          logo: insertStore.logo || null,
          colors: insertStore.colors,
          payment_methods: insertStore.paymentMethods
        }])
        .select()
        .single();
        
      if (error) {
        console.error('Error creating store:', error);
        throw new Error(`Failed to create store: ${error.message}`);
      }
      
      return {
        id: data.id,
        name: data.name,
        description: data.description,
        userId: data.user_id,
        slug: data.slug,
        logo: data.logo,
        colors: data.colors,
        paymentMethods: data.payment_methods,
        createdAt: new Date(data.created_at)
      };
    } catch (error) {
      console.error('Unexpected error in createStore:', error);
      throw error;
    }
  }

  async updateStore(id: number, storeUpdate: Partial<Store>): Promise<Store | undefined> {
    try {
      const updateData: any = { ...storeUpdate };
      
      // Mapeie campos específicos de camelCase para snake_case
      if (storeUpdate.userId !== undefined) {
        updateData.user_id = storeUpdate.userId;
        delete updateData.userId;
      }
      
      if (storeUpdate.paymentMethods !== undefined) {
        updateData.payment_methods = storeUpdate.paymentMethods;
        delete updateData.paymentMethods;
      }
      
      // Remover createdAt se presente
      if (updateData.createdAt) {
        delete updateData.createdAt;
      }
      
      console.log('Updating store in Supabase with data:', updateData);
      
      const { data, error } = await supabaseAdmin
        .from('stores')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();
        
      if (error) {
        console.error('Error updating store:', error);
        console.error('Error code:', error.code, 'Error message:', error.message, 'Error details:', error.details);
        return undefined;
      }
      
      console.log('Store updated successfully in Supabase');
      
      return {
        id: data.id,
        name: data.name,
        description: data.description,
        userId: data.user_id,
        slug: data.slug,
        logo: data.logo,
        colors: data.colors,
        paymentMethods: data.payment_methods,
        createdAt: new Date(data.created_at)
      };
    } catch (error) {
      console.error('Unexpected error in updateStore:', error);
      return undefined;
    }
  }

  // Product methods
  async getProduct(id: number): Promise<Product | undefined> {
    try {
      console.log('Fetching product with ID:', id);
      
      const { data, error } = await supabaseAdmin
        .from('products')
        .select('*')
        .eq('id', id)
        .maybeSingle();
        
      if (error) {
        console.error('Error fetching product:', error);
        return undefined;
      }
      
      if (!data) {
        console.log('Product not found for ID:', id);
        return undefined;
      }
      
      console.log('Product found:', data);
      
      // Mapeie de snake_case para camelCase
      return {
        id: data.id,
        name: data.name,
        description: data.description,
        storeId: data.store_id,
        categoryId: data.category_id,
        price: data.price,
        images: data.images,
        inStock: data.in_stock,
        hasVariations: data.has_variations || false,
        createdAt: new Date(data.created_at)
      };
    } catch (error) {
      console.error('Unexpected error in getProduct:', error);
      return undefined;
    }
  }

  async getProductsByStoreId(storeId: number): Promise<Product[]> {
    try {
      console.log('Fetching products for store ID:', storeId);
      
      const { data, error } = await supabaseAdmin
        .from('products')
        .select('*')
        .eq('store_id', storeId);
        
      if (error) {
        console.error('Error fetching products for store:', error);
        return [];
      }
      
      console.log(`Found ${data.length} products for store ID: ${storeId}`);
      
      // Mapeie de snake_case para camelCase
      return data.map(product => ({
        id: product.id,
        name: product.name,
        description: product.description,
        storeId: product.store_id,
        categoryId: product.category_id,
        price: product.price,
        images: product.images,
        inStock: product.in_stock,
        hasVariations: product.has_variations || false,
        createdAt: new Date(product.created_at)
      }));
    } catch (error) {
      console.error('Unexpected error in getProductsByStoreId:', error);
      return [];
    }
  }

  async getProductsByCategoryId(categoryId: number): Promise<Product[]> {
    try {
      const { data, error } = await supabaseAdmin
        .from('products')
        .select('*')
        .eq('category_id', categoryId);
        
      if (error) {
        console.error('Error fetching products by category:', error);
        return [];
      }
      
      return data.map(product => ({
        id: product.id,
        name: product.name,
        description: product.description,
        storeId: product.store_id,
        categoryId: product.category_id,
        price: product.price,
        images: product.images,
        inStock: product.in_stock,
        hasVariations: product.has_variations || false,
        createdAt: new Date(product.created_at)
      }));
    } catch (error) {
      console.error('Unexpected error in getProductsByCategoryId:', error);
      return [];
    }
  }

  async createProduct(insertProduct: InsertProduct): Promise<Product> {
    try {
      console.log('Creating product in Supabase, original data:', insertProduct);
      
      // Preparar dados do produto, garantindo os campos corretos
      const productData = {
        name: insertProduct.name,
        price: insertProduct.price,
        store_id: insertProduct.storeId,
        description: insertProduct.description || null,
        category_id: insertProduct.categoryId || null,
        images: insertProduct.images || [],
        in_stock: insertProduct.inStock !== undefined ? insertProduct.inStock : true // Usar snake_case para o campo no banco
      };
      
      // Remover campos camelCase que já foram mapeados para snake_case
      delete (productData as any).storeId;
      delete (productData as any).categoryId;
      delete (productData as any).inStock;
      
      console.log('Mapped product data for Supabase:', productData);
      
      // Usar supabaseAdmin para contornar as restrições de RLS
      const { data, error } = await supabaseAdmin
        .from('products')
        .insert([productData])
        .select()
        .single();
        
      if (error) {
        console.error('Error creating product:', error);
        console.error('Error code:', error.code, 'Error message:', error.message, 'Error details:', error.details);
        throw new Error(`Failed to create product: ${error.message}`);
      }
      
      console.log('Product created in Supabase, response data:', data);
      
      // Mapear o produto de volta para o formato da aplicação
      const product = {
        id: data.id,
        name: data.name,
        description: data.description,
        storeId: data.store_id,
        categoryId: data.category_id,
        price: data.price,
        images: data.images,
        inStock: data.in_stock,
        hasVariations: data.has_variations || false,
        createdAt: new Date(data.created_at)
      };
      
      return product as Product;
    } catch (error) {
      console.error('Unexpected error in createProduct:', error);
      throw error;
    }
  }

  async updateProduct(id: number, productUpdate: Partial<Product>): Promise<Product | undefined> {
    try {
      console.log('Updating product in Supabase, ID:', id);
      console.log('Original update data:', productUpdate);
      
      // Preparar dados para atualização, convertendo de camelCase para snake_case
      const productData: any = { ...productUpdate };
      
      // Mapear campos específicos
      if (productUpdate.storeId !== undefined) {
        productData.store_id = productUpdate.storeId;
        delete productData.storeId;
      }
      
      if (productUpdate.categoryId !== undefined) {
        productData.category_id = productUpdate.categoryId;
        delete productData.categoryId;
      }
      
      if (productUpdate.inStock !== undefined) {
        productData.in_stock = productUpdate.inStock;
        delete productData.inStock;
      }
      
      // Remover createdAt se presente para evitar erros
      if (productData.createdAt) {
        delete productData.createdAt;
      }
      
      console.log('Mapped product update data for Supabase:', productData);
      
      // Usar supabaseAdmin para contornar as restrições de RLS
      const { data, error } = await supabaseAdmin
        .from('products')
        .update(productData)
        .eq('id', id)
        .select()
        .single();
        
      if (error) {
        console.error('Error updating product:', error);
        console.error('Error code:', error.code, 'Error message:', error.message, 'Error details:', error.details);
        return undefined;
      }
      
      console.log('Product updated successfully in Supabase, response data:', data);
      
      // Mapear o produto de volta para o formato da aplicação
      const product = {
        id: data.id,
        name: data.name,
        description: data.description,
        storeId: data.store_id,
        categoryId: data.category_id,
        price: data.price,
        images: data.images,
        inStock: data.in_stock,
        hasVariations: data.has_variations || false,
        createdAt: new Date(data.created_at)
      };
      
      return product as Product;
    } catch (error) {
      console.error('Unexpected error in updateProduct:', error);
      return undefined;
    }
  }

  async deleteProduct(id: number): Promise<boolean> {
    try {
      console.log('Deleting product in Supabase, ID:', id);
      
      // Usar supabaseAdmin para contornar as restrições de RLS
      const { error } = await supabaseAdmin
        .from('products')
        .delete()
        .eq('id', id);
      
      if (error) {
        console.error('Error deleting product:', error);
        console.error('Error code:', error.code, 'Error message:', error.message, 'Error details:', error.details);
        return false;
      }
      
      console.log('Product deleted successfully in Supabase');
      return true;
    } catch (error) {
      console.error('Unexpected error in deleteProduct:', error);
      return false;
    }
  }

  // Category methods
  async getCategory(id: number): Promise<Category | undefined> {
    try {
      const { data, error } = await supabaseAdmin
        .from('categories')
        .select('*')
        .eq('id', id)
        .maybeSingle();
        
      if (error) {
        console.error('Error fetching category:', error);
        return undefined;
      }
      
      if (!data) return undefined;
      
      return {
        id: data.id,
        name: data.name,
        description: data.description,
        storeId: data.store_id
      };
    } catch (error) {
      console.error('Unexpected error in getCategory:', error);
      return undefined;
    }
  }

  async getCategoriesByStoreId(storeId: number): Promise<Category[]> {
    try {
      const { data, error } = await supabaseAdmin
        .from('categories')
        .select('*')
        .eq('store_id', storeId);
        
      if (error) {
        console.error('Error fetching categories for store:', error);
        return [];
      }
      
      return data.map(category => ({
        id: category.id,
        name: category.name,
        description: category.description,
        storeId: category.store_id
      }));
    } catch (error) {
      console.error('Unexpected error in getCategoriesByStoreId:', error);
      return [];
    }
  }

  async createCategory(insertCategory: InsertCategory): Promise<Category> {
    try {
      const { data, error } = await supabaseAdmin
        .from('categories')
        .insert([{
          name: insertCategory.name,
          description: insertCategory.description || null,
          store_id: insertCategory.storeId
        }])
        .select()
        .single();
        
      if (error) {
        console.error('Error creating category:', error);
        throw new Error(`Failed to create category: ${error.message}`);
      }
      
      return {
        id: data.id,
        name: data.name,
        description: data.description,
        storeId: data.store_id
      };
    } catch (error) {
      console.error('Unexpected error in createCategory:', error);
      throw error;
    }
  }

  async updateCategory(id: number, categoryUpdate: Partial<Category>): Promise<Category | undefined> {
    try {
      const updateData: any = { ...categoryUpdate };
      
      // Map storeId to store_id
      if (categoryUpdate.storeId !== undefined) {
        updateData.store_id = categoryUpdate.storeId;
        delete updateData.storeId;
      }
      
      const { data, error } = await supabaseAdmin
        .from('categories')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();
        
      if (error) {
        console.error('Error updating category:', error);
        return undefined;
      }
      
      return {
        id: data.id,
        name: data.name,
        description: data.description,
        storeId: data.store_id
      };
    } catch (error) {
      console.error('Unexpected error in updateCategory:', error);
      return undefined;
    }
  }

  async deleteCategory(id: number): Promise<boolean> {
    try {
      const { error } = await supabaseAdmin
        .from('categories')
        .delete()
        .eq('id', id);
      
      if (error) {
        console.error('Error deleting category:', error);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Unexpected error in deleteCategory:', error);
      return false;
    }
  }

  // Customer methods
  async getCustomer(id: number): Promise<Customer | undefined> {
    try {
      const { data, error } = await supabaseAdmin
        .from('customers')
        .select('*')
        .eq('id', id)
        .maybeSingle();
        
      if (error) {
        console.error('Error fetching customer:', error);
        return undefined;
      }
      
      if (!data) return undefined;
      
      return {
        id: data.id,
        name: data.name,
        email: data.email,
        phone: data.phone,
        storeId: data.store_id,
        createdAt: new Date(data.created_at)
      };
    } catch (error) {
      console.error('Unexpected error in getCustomer:', error);
      return undefined;
    }
  }

  async getCustomersByStoreId(storeId: number): Promise<Customer[]> {
    try {
      const { data, error } = await supabaseAdmin
        .from('customers')
        .select('*')
        .eq('store_id', storeId);
        
      if (error) {
        console.error('Error fetching customers for store:', error);
        return [];
      }
      
      return data.map(customer => ({
        id: customer.id,
        name: customer.name,
        email: customer.email,
        phone: customer.phone,
        storeId: customer.store_id,
        createdAt: new Date(customer.created_at)
      }));
    } catch (error) {
      console.error('Unexpected error in getCustomersByStoreId:', error);
      return [];
    }
  }

  async createCustomer(insertCustomer: InsertCustomer): Promise<Customer> {
    try {
      // Primeiro verifica se já existe um cliente com o mesmo e-mail na mesma loja
      const { data: existingCustomer, error: queryError } = await supabaseAdmin
        .from('customers')
        .select('*')
        .eq('email', insertCustomer.email)
        .eq('store_id', insertCustomer.storeId)
        .maybeSingle();
        
      if (queryError) {
        console.error('Error checking for existing customer:', queryError);
        // Continua com a criação mesmo em caso de erro na consulta
      }
      
      // Se já existe um cliente, retorna-o
      if (existingCustomer) {
        console.log('Customer already exists, returning existing customer');
        return {
          id: existingCustomer.id,
          name: existingCustomer.name,
          email: existingCustomer.email,
          phone: existingCustomer.phone,
          storeId: existingCustomer.store_id,
          createdAt: new Date(existingCustomer.created_at)
        };
      }
      
      // Caso contrário, cria um novo cliente
      const { data, error } = await supabaseAdmin
        .from('customers')
        .insert([{
          name: insertCustomer.name,
          email: insertCustomer.email,
          phone: insertCustomer.phone || null,
          store_id: insertCustomer.storeId
        }])
        .select()
        .single();
        
      if (error) {
        console.error('Error creating customer:', error);
        throw new Error(`Failed to create customer: ${error.message}`);
      }
      
      return {
        id: data.id,
        name: data.name,
        email: data.email,
        phone: data.phone,
        storeId: data.store_id,
        createdAt: new Date(data.created_at)
      };
    } catch (error) {
      console.error('Unexpected error in createCustomer:', error);
      throw error;
    }
  }

  // Order methods
  async getOrder(id: number): Promise<Order | undefined> {
    try {
      const { data, error } = await supabaseAdmin
        .from('orders')
        .select('*')
        .eq('id', id)
        .maybeSingle();
        
      if (error) {
        console.error('Error fetching order:', error);
        return undefined;
      }
      
      if (!data) return undefined;
      
      return {
        id: data.id,
        customerId: data.customer_id,
        storeId: data.store_id,
        total: data.total,
        status: data.status,
        paymentMethod: data.payment_method,
        notes: data.notes,
        createdAt: new Date(data.created_at)
      };
    } catch (error) {
      console.error('Unexpected error in getOrder:', error);
      return undefined;
    }
  }

  async getOrdersByStoreId(storeId: number): Promise<Order[]> {
    try {
      const { data, error } = await supabaseAdmin
        .from('orders')
        .select('*')
        .eq('store_id', storeId)
        .order('created_at', { ascending: false });
        
      if (error) {
        console.error('Error fetching orders for store:', error);
        return [];
      }
      
      return data.map(order => ({
        id: order.id,
        customerId: order.customer_id,
        storeId: order.store_id,
        total: order.total,
        status: order.status,
        paymentMethod: order.payment_method,
        notes: order.notes,
        createdAt: new Date(order.created_at)
      }));
    } catch (error) {
      console.error('Unexpected error in getOrdersByStoreId:', error);
      return [];
    }
  }

  async getOrdersByCustomerId(customerId: number): Promise<Order[]> {
    try {
      const { data, error } = await supabaseAdmin
        .from('orders')
        .select('*')
        .eq('customer_id', customerId)
        .order('created_at', { ascending: false });
        
      if (error) {
        console.error('Error fetching orders for customer:', error);
        return [];
      }
      
      return data.map(order => ({
        id: order.id,
        customerId: order.customer_id,
        storeId: order.store_id,
        total: order.total,
        status: order.status,
        paymentMethod: order.payment_method,
        notes: order.notes,
        createdAt: new Date(order.created_at)
      }));
    } catch (error) {
      console.error('Unexpected error in getOrdersByCustomerId:', error);
      return [];
    }
  }

  async createOrder(insertOrder: InsertOrder): Promise<Order> {
    try {
      const { data, error } = await supabaseAdmin
        .from('orders')
        .insert([{
          customer_id: insertOrder.customerId,
          store_id: insertOrder.storeId,
          total: insertOrder.total,
          status: insertOrder.status || 'pending',
          payment_method: insertOrder.paymentMethod,
          notes: insertOrder.notes || null
        }])
        .select()
        .single();
        
      if (error) {
        console.error('Error creating order:', error);
        throw new Error(`Failed to create order: ${error.message}`);
      }
      
      return {
        id: data.id,
        customerId: data.customer_id,
        storeId: data.store_id,
        total: data.total,
        status: data.status,
        paymentMethod: data.payment_method,
        notes: data.notes,
        createdAt: new Date(data.created_at)
      };
    } catch (error) {
      console.error('Unexpected error in createOrder:', error);
      throw error;
    }
  }

  async updateOrderStatus(id: number, status: string): Promise<Order | undefined> {
    try {
      const { data, error } = await supabaseAdmin
        .from('orders')
        .update({ status })
        .eq('id', id)
        .select()
        .single();
        
      if (error) {
        console.error('Error updating order status:', error);
        return undefined;
      }
      
      return {
        id: data.id,
        customerId: data.customer_id,
        storeId: data.store_id,
        total: data.total,
        status: data.status,
        paymentMethod: data.payment_method,
        notes: data.notes,
        createdAt: new Date(data.created_at)
      };
    } catch (error) {
      console.error('Unexpected error in updateOrderStatus:', error);
      return undefined;
    }
  }

  // Order item methods
  async getOrderItemsByOrderId(orderId: number): Promise<OrderItem[]> {
    try {
      const { data, error } = await supabaseAdmin
        .from('order_items')
        .select('*')
        .eq('order_id', orderId);
        
      if (error) {
        console.error('Error fetching order items:', error);
        return [];
      }
      
      return data.map(item => ({
        id: item.id,
        orderId: item.order_id,
        productId: item.product_id,
        quantity: item.quantity,
        price: item.price
      }));
    } catch (error) {
      console.error('Unexpected error in getOrderItemsByOrderId:', error);
      return [];
    }
  }

  async createOrderItem(insertOrderItem: InsertOrderItem): Promise<OrderItem> {
    try {
      const { data, error } = await supabaseAdmin
        .from('order_items')
        .insert([{
          order_id: insertOrderItem.orderId,
          product_id: insertOrderItem.productId,
          quantity: insertOrderItem.quantity,
          price: insertOrderItem.price
        }])
        .select()
        .single();
        
      if (error) {
        console.error('Error creating order item:', error);
        throw new Error(`Failed to create order item: ${error.message}`);
      }
      
      return {
        id: data.id,
        orderId: data.order_id,
        productId: data.product_id,
        quantity: data.quantity,
        price: data.price
      };
    } catch (error) {
      console.error('Unexpected error in createOrderItem:', error);
      throw error;
    }
  }

  // Analytics methods
  async recordStoreVisit(insertStoreVisit: InsertStoreVisit): Promise<StoreVisit> {
    try {
      const { data, error } = await supabaseAdmin
        .from('store_visits')
        .insert([{
          store_id: insertStoreVisit.storeId,
          visitor_ip: insertStoreVisit.visitorIp || null
        }])
        .select()
        .single();
        
      if (error) {
        console.error('Error recording store visit:', error);
        throw new Error(`Failed to record store visit: ${error.message}`);
      }
      
      return {
        id: data.id,
        storeId: data.store_id,
        visitorIp: data.visitor_ip,
        visitDate: new Date(data.visit_date)
      };
    } catch (error) {
      console.error('Unexpected error in recordStoreVisit:', error);
      throw error;
    }
  }

  async getStoreVisitsByStoreId(storeId: number, startDate?: Date, endDate?: Date): Promise<StoreVisit[]> {
    try {
      let query = supabaseAdmin
        .from('store_visits')
        .select('*')
        .eq('store_id', storeId);
        
      if (startDate) {
        query = query.gte('visit_date', startDate.toISOString());
      }
      
      if (endDate) {
        query = query.lte('visit_date', endDate.toISOString());
      }
        
      const { data, error } = await query;
        
      if (error) {
        console.error('Error fetching store visits:', error);
        return [];
      }
      
      return data.map(visit => ({
        id: visit.id,
        storeId: visit.store_id,
        visitorIp: visit.visitor_ip,
        visitDate: new Date(visit.visit_date)
      }));
    } catch (error) {
      console.error('Unexpected error in getStoreVisitsByStoreId:', error);
      return [];
    }
  }

  async getMonthlyVisitCount(storeId: number): Promise<number> {
    try {
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
      
      const { count, error } = await supabaseAdmin
        .from('store_visits')
        .select('*', { count: 'exact', head: true })
        .eq('store_id', storeId)
        .gte('visit_date', startOfMonth.toISOString())
        .lte('visit_date', endOfMonth.toISOString());
        
      if (error) {
        console.error('Error counting monthly visits:', error);
        return 0;
      }
      
      return count || 0;
    } catch (error) {
      console.error('Unexpected error in getMonthlyVisitCount:', error);
      return 0;
    }
  }

  async getMonthlyOrderCount(storeId: number): Promise<number> {
    try {
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
      
      const { count, error } = await supabaseAdmin
        .from('orders')
        .select('*', { count: 'exact', head: true })
        .eq('store_id', storeId)
        .gte('created_at', startOfMonth.toISOString())
        .lte('created_at', endOfMonth.toISOString());
        
      if (error) {
        console.error('Error counting monthly orders:', error);
        return 0;
      }
      
      return count || 0;
    } catch (error) {
      console.error('Unexpected error in getMonthlyOrderCount:', error);
      return 0;
    }
  }

  // Profile methods
  async getProfile(id: string): Promise<Profile | undefined> {
    try {
      console.log('Fetching profile with admin client to bypass RLS...');
      
      const { data, error } = await supabaseAdmin
        .from('profiles')
        .select('*')
        .eq('id', id)
        .maybeSingle();
        
      if (error) {
        console.error('Error fetching profile:', error);
        return undefined;
      }
      
      if (!data) {
        console.log('Profile not found by ID, trying by email:', id);
        return undefined;
      }
      
      return {
        id: data.id,
        email: data.email,
        full_name: data.full_name,
        created_at: new Date(data.created_at)
      };
    } catch (error) {
      console.error('Unexpected error in getProfile:', error);
      return undefined;
    }
  }

  async getProfileByEmail(email: string): Promise<Profile | undefined> {
    try {
      console.log('Fetching profile by email with admin client to bypass RLS...');
      
      const { data, error } = await supabaseAdmin
        .from('profiles')
        .select('*')
        .eq('email', email)
        .maybeSingle();
        
      if (error) {
        console.error('Error fetching profile by email:', error);
        return undefined;
      }
      
      if (!data) {
        console.log('Profile not found for email:', email);
        return undefined;
      }
      
      console.log('Profile found in database:', email);
      
      return {
        id: data.id,
        email: data.email,
        full_name: data.full_name,
        created_at: new Date(data.created_at)
      };
    } catch (error) {
      console.error('Unexpected error in getProfileByEmail:', error);
      return undefined;
    }
  }

  async createProfile(insertProfile: InsertProfile): Promise<Profile> {
    try {
      // First check if profile already exists
      const { data: existingProfile } = await supabaseAdmin
        .from('profiles')
        .select('*')
        .eq('id', insertProfile.id)
        .maybeSingle();
        
      if (existingProfile) {
        console.log('Profile already exists, returning existing profile');
        return {
          id: existingProfile.id,
          email: existingProfile.email,
          full_name: existingProfile.full_name,
          created_at: new Date(existingProfile.created_at)
        };
      }
      
      // Create new profile
      const { data, error } = await supabaseAdmin
        .from('profiles')
        .insert([{
          id: insertProfile.id,
          email: insertProfile.email,
          full_name: insertProfile.full_name
        }])
        .select()
        .single();
        
      if (error) {
        console.error('Error creating profile:', error);
        throw new Error(`Failed to create profile: ${error.message}`);
      }
      
      return {
        id: data.id,
        email: data.email,
        full_name: data.full_name,
        created_at: new Date(data.created_at)
      };
    } catch (error) {
      console.error('Unexpected error in createProfile:', error);
      throw error;
    }
  }
}

export class SupabaseStorage implements IStorage {
  async getUser(id: number): Promise<User | undefined> {
    return new DatabaseStorage().getUser(id);
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    return new DatabaseStorage().getUserByEmail(email);
  }
  
  // Product Variation methods
  async getProductVariationsByProductId(productId: number): Promise<ProductVariation[]> {
    try {
      const { data, error } = await supabaseAdmin
        .from('product_variations')
        .select('*')
        .eq('product_id', productId);
        
      if (error) {
        console.error('Error fetching product variations:', error);
        return [];
      }
      
      return data.map(variation => ({
        id: variation.id,
        productId: variation.product_id,
        name: variation.name,
        description: variation.description,
        required: variation.required,
        maxSelections: variation.max_selections,
        minSelections: variation.min_selections
      }));
    } catch (error) {
      console.error('Unexpected error in getProductVariationsByProductId:', error);
      return [];
    }
  }

  async createProductVariation(insertVariation: InsertProductVariation): Promise<ProductVariation> {
    try {
      const { data, error } = await supabaseAdmin
        .from('product_variations')
        .insert([{
          product_id: insertVariation.productId,
          name: insertVariation.name,
          description: insertVariation.description,
          required: insertVariation.required,
          max_selections: insertVariation.maxSelections,
          min_selections: insertVariation.minSelections
        }])
        .select()
        .single();
        
      if (error) {
        console.error('Error creating product variation:', error);
        throw new Error(`Failed to create product variation: ${error.message}`);
      }
      
      return {
        id: data.id,
        productId: data.product_id,
        name: data.name,
        description: data.description,
        required: data.required,
        maxSelections: data.max_selections,
        minSelections: data.min_selections
      };
    } catch (error) {
      console.error('Unexpected error in createProductVariation:', error);
      throw error;
    }
  }

  async updateProductVariation(id: number, variationUpdate: Partial<ProductVariation>): Promise<ProductVariation | undefined> {
    try {
      const updateData: any = { ...variationUpdate };
      
      // Mapeie campos específicos de camelCase para snake_case
      if (variationUpdate.productId !== undefined) {
        updateData.product_id = variationUpdate.productId;
        delete updateData.productId;
      }
      
      if (variationUpdate.maxSelections !== undefined) {
        updateData.max_selections = variationUpdate.maxSelections;
        delete updateData.maxSelections;
      }
      
      if (variationUpdate.minSelections !== undefined) {
        updateData.min_selections = variationUpdate.minSelections;
        delete updateData.minSelections;
      }
      
      const { data, error } = await supabaseAdmin
        .from('product_variations')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();
        
      if (error) {
        console.error('Error updating product variation:', error);
        return undefined;
      }
      
      return {
        id: data.id,
        productId: data.product_id,
        name: data.name,
        description: data.description,
        required: data.required,
        maxSelections: data.max_selections,
        minSelections: data.min_selections
      };
    } catch (error) {
      console.error('Unexpected error in updateProductVariation:', error);
      return undefined;
    }
  }

  async deleteProductVariation(id: number): Promise<boolean> {
    try {
      // As opções serão excluídas automaticamente devido à restrição ON DELETE CASCADE
      const { error } = await supabaseAdmin
        .from('product_variations')
        .delete()
        .eq('id', id);
        
      if (error) {
        console.error('Error deleting product variation:', error);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Unexpected error in deleteProductVariation:', error);
      return false;
    }
  }
  
  // Variation Option methods
  async getVariationOptionsByVariationId(variationId: number): Promise<VariationOption[]> {
    try {
      const { data, error } = await supabaseAdmin
        .from('variation_options')
        .select('*')
        .eq('variation_id', variationId);
        
      if (error) {
        console.error('Error fetching variation options:', error);
        return [];
      }
      
      return data.map(option => ({
        id: option.id,
        variationId: option.variation_id,
        name: option.name,
        additionalPrice: option.additional_price
      }));
    } catch (error) {
      console.error('Unexpected error in getVariationOptionsByVariationId:', error);
      return [];
    }
  }

  async createVariationOption(insertOption: InsertVariationOption): Promise<VariationOption> {
    try {
      const { data, error } = await supabaseAdmin
        .from('variation_options')
        .insert([{
          variation_id: insertOption.variationId,
          name: insertOption.name,
          additional_price: insertOption.additionalPrice
        }])
        .select()
        .single();
        
      if (error) {
        console.error('Error creating variation option:', error);
        throw new Error(`Failed to create variation option: ${error.message}`);
      }
      
      return {
        id: data.id,
        variationId: data.variation_id,
        name: data.name,
        additionalPrice: data.additional_price
      };
    } catch (error) {
      console.error('Unexpected error in createVariationOption:', error);
      throw error;
    }
  }

  async updateVariationOption(id: number, optionUpdate: Partial<VariationOption>): Promise<VariationOption | undefined> {
    try {
      const updateData: any = { ...optionUpdate };
      
      // Mapeie campos específicos de camelCase para snake_case
      if (optionUpdate.variationId !== undefined) {
        updateData.variation_id = optionUpdate.variationId;
        delete updateData.variationId;
      }
      
      if (optionUpdate.additionalPrice !== undefined) {
        updateData.additional_price = optionUpdate.additionalPrice;
        delete updateData.additionalPrice;
      }
      
      const { data, error } = await supabaseAdmin
        .from('variation_options')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();
        
      if (error) {
        console.error('Error updating variation option:', error);
        return undefined;
      }
      
      return {
        id: data.id,
        variationId: data.variation_id,
        name: data.name,
        additionalPrice: data.additional_price
      };
    } catch (error) {
      console.error('Unexpected error in updateVariationOption:', error);
      return undefined;
    }
  }

  async deleteVariationOption(id: number): Promise<boolean> {
    try {
      const { error } = await supabaseAdmin
        .from('variation_options')
        .delete()
        .eq('id', id);
        
      if (error) {
        console.error('Error deleting variation option:', error);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Unexpected error in deleteVariationOption:', error);
      return false;
    }
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    return new DatabaseStorage().createUser(insertUser);
  }

  async getProfile(id: string): Promise<Profile | undefined> {
    return new DatabaseStorage().getProfile(id);
  }
  
  async getProfileByEmail(email: string): Promise<Profile | undefined> {
    return new DatabaseStorage().getProfileByEmail(email);
  }
  
  async createProfile(insertProfile: InsertProfile): Promise<Profile> {
    return new DatabaseStorage().createProfile(insertProfile);
  }

  async getStore(id: number): Promise<Store | undefined> {
    return new DatabaseStorage().getStore(id);
  }

  async getStoreBySlug(slug: string): Promise<Store | undefined> {
    return new DatabaseStorage().getStoreBySlug(slug);
  }

  async getStoreByUserId(userId: number): Promise<Store | undefined> {
    return new DatabaseStorage().getStoreByUserId(userId);
  }

  async createStore(insertStore: InsertStore): Promise<Store> {
    return new DatabaseStorage().createStore(insertStore);
  }

  async updateStore(id: number, storeUpdate: Partial<Store>): Promise<Store | undefined> {
    return new DatabaseStorage().updateStore(id, storeUpdate);
  }

  async getProduct(id: number): Promise<Product | undefined> {
    return new DatabaseStorage().getProduct(id);
  }

  async getProductsByStoreId(storeId: number): Promise<Product[]> {
    return new DatabaseStorage().getProductsByStoreId(storeId);
  }

  async getProductsByCategoryId(categoryId: number): Promise<Product[]> {
    return new DatabaseStorage().getProductsByCategoryId(categoryId);
  }

  async createProduct(insertProduct: InsertProduct): Promise<Product> {
    return new DatabaseStorage().createProduct(insertProduct);
  }

  async updateProduct(id: number, productUpdate: Partial<Product>): Promise<Product | undefined> {
    return new DatabaseStorage().updateProduct(id, productUpdate);
  }

  async deleteProduct(id: number): Promise<boolean> {
    return new DatabaseStorage().deleteProduct(id);
  }

  async getCategory(id: number): Promise<Category | undefined> {
    return new DatabaseStorage().getCategory(id);
  }

  async getCategoriesByStoreId(storeId: number): Promise<Category[]> {
    return new DatabaseStorage().getCategoriesByStoreId(storeId);
  }

  async createCategory(insertCategory: InsertCategory): Promise<Category> {
    return new DatabaseStorage().createCategory(insertCategory);
  }

  async updateCategory(id: number, categoryUpdate: Partial<Category>): Promise<Category | undefined> {
    return new DatabaseStorage().updateCategory(id, categoryUpdate);
  }

  async deleteCategory(id: number): Promise<boolean> {
    return new DatabaseStorage().deleteCategory(id);
  }

  async getCustomer(id: number): Promise<Customer | undefined> {
    return new DatabaseStorage().getCustomer(id);
  }

  async getCustomersByStoreId(storeId: number): Promise<Customer[]> {
    return new DatabaseStorage().getCustomersByStoreId(storeId);
  }

  async createCustomer(insertCustomer: InsertCustomer): Promise<Customer> {
    return new DatabaseStorage().createCustomer(insertCustomer);
  }

  async getOrder(id: number): Promise<Order | undefined> {
    return new DatabaseStorage().getOrder(id);
  }

  async getOrdersByStoreId(storeId: number): Promise<Order[]> {
    return new DatabaseStorage().getOrdersByStoreId(storeId);
  }

  async getOrdersByCustomerId(customerId: number): Promise<Order[]> {
    return new DatabaseStorage().getOrdersByCustomerId(customerId);
  }

  async createOrder(insertOrder: InsertOrder): Promise<Order> {
    return new DatabaseStorage().createOrder(insertOrder);
  }

  async updateOrderStatus(id: number, status: string): Promise<Order | undefined> {
    return new DatabaseStorage().updateOrderStatus(id, status);
  }

  async getOrderItemsByOrderId(orderId: number): Promise<OrderItem[]> {
    return new DatabaseStorage().getOrderItemsByOrderId(orderId);
  }

  async createOrderItem(insertOrderItem: InsertOrderItem): Promise<OrderItem> {
    return new DatabaseStorage().createOrderItem(insertOrderItem);
  }

  async recordStoreVisit(insertStoreVisit: InsertStoreVisit): Promise<StoreVisit> {
    return new DatabaseStorage().recordStoreVisit(insertStoreVisit);
  }

  async getStoreVisitsByStoreId(storeId: number, startDate?: Date, endDate?: Date): Promise<StoreVisit[]> {
    return new DatabaseStorage().getStoreVisitsByStoreId(storeId, startDate, endDate);
  }

  async getMonthlyVisitCount(storeId: number): Promise<number> {
    return new DatabaseStorage().getMonthlyVisitCount(storeId);
  }

  async getMonthlyOrderCount(storeId: number): Promise<number> {
    return new DatabaseStorage().getMonthlyOrderCount(storeId);
  }

  // Product Variation methods
  async getProductVariationsByProductId(productId: number): Promise<ProductVariation[]> {
    try {
      const { data, error } = await supabaseAdmin
        .from('product_variations')
        .select('*')
        .eq('product_id', productId);
        
      if (error) {
        console.error('Error fetching product variations:', error);
        return [];
      }
      
      return data.map(variation => ({
        id: variation.id,
        productId: variation.product_id,
        name: variation.name,
        description: variation.description,
        required: variation.required,
        maxSelections: variation.max_selections,
        minSelections: variation.min_selections
      }));
    } catch (error) {
      console.error('Unexpected error in getProductVariationsByProductId:', error);
      return [];
    }
  }

  async createProductVariation(insertVariation: InsertProductVariation): Promise<ProductVariation> {
    try {
      const { data, error } = await supabaseAdmin
        .from('product_variations')
        .insert([{
          product_id: insertVariation.productId,
          name: insertVariation.name,
          description: insertVariation.description,
          required: insertVariation.required,
          max_selections: insertVariation.maxSelections,
          min_selections: insertVariation.minSelections
        }])
        .select()
        .single();
        
      if (error) {
        console.error('Error creating product variation:', error);
        throw new Error(`Failed to create product variation: ${error.message}`);
      }
      
      return {
        id: data.id,
        productId: data.product_id,
        name: data.name,
        description: data.description,
        required: data.required,
        maxSelections: data.max_selections,
        minSelections: data.min_selections
      };
    } catch (error) {
      console.error('Unexpected error in createProductVariation:', error);
      throw error;
    }
  }

  async updateProductVariation(id: number, variationUpdate: Partial<ProductVariation>): Promise<ProductVariation | undefined> {
    try {
      const updateData: any = { ...variationUpdate };
      
      // Mapeie campos específicos de camelCase para snake_case
      if (variationUpdate.productId !== undefined) {
        updateData.product_id = variationUpdate.productId;
        delete updateData.productId;
      }
      
      if (variationUpdate.maxSelections !== undefined) {
        updateData.max_selections = variationUpdate.maxSelections;
        delete updateData.maxSelections;
      }
      
      if (variationUpdate.minSelections !== undefined) {
        updateData.min_selections = variationUpdate.minSelections;
        delete updateData.minSelections;
      }
      
      const { data, error } = await supabaseAdmin
        .from('product_variations')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();
        
      if (error) {
        console.error('Error updating product variation:', error);
        return undefined;
      }
      
      return {
        id: data.id,
        productId: data.product_id,
        name: data.name,
        description: data.description,
        required: data.required,
        maxSelections: data.max_selections,
        minSelections: data.min_selections
      };
    } catch (error) {
      console.error('Unexpected error in updateProductVariation:', error);
      return undefined;
    }
  }

  async deleteProductVariation(id: number): Promise<boolean> {
    try {
      // As opções serão excluídas automaticamente devido à restrição ON DELETE CASCADE
      const { error } = await supabaseAdmin
        .from('product_variations')
        .delete()
        .eq('id', id);
        
      if (error) {
        console.error('Error deleting product variation:', error);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Unexpected error in deleteProductVariation:', error);
      return false;
    }
  }
  
  // Variation Option methods
  async getVariationOptionsByVariationId(variationId: number): Promise<VariationOption[]> {
    try {
      const { data, error } = await supabaseAdmin
        .from('variation_options')
        .select('*')
        .eq('variation_id', variationId);
        
      if (error) {
        console.error('Error fetching variation options:', error);
        return [];
      }
      
      return data.map(option => ({
        id: option.id,
        variationId: option.variation_id,
        name: option.name,
        additionalPrice: option.additional_price
      }));
    } catch (error) {
      console.error('Unexpected error in getVariationOptionsByVariationId:', error);
      return [];
    }
  }

  async createVariationOption(insertOption: InsertVariationOption): Promise<VariationOption> {
    try {
      const { data, error } = await supabaseAdmin
        .from('variation_options')
        .insert([{
          variation_id: insertOption.variationId,
          name: insertOption.name,
          additional_price: insertOption.additionalPrice
        }])
        .select()
        .single();
        
      if (error) {
        console.error('Error creating variation option:', error);
        throw new Error(`Failed to create variation option: ${error.message}`);
      }
      
      return {
        id: data.id,
        variationId: data.variation_id,
        name: data.name,
        additionalPrice: data.additional_price
      };
    } catch (error) {
      console.error('Unexpected error in createVariationOption:', error);
      throw error;
    }
  }

  async updateVariationOption(id: number, optionUpdate: Partial<VariationOption>): Promise<VariationOption | undefined> {
    try {
      const updateData: any = { ...optionUpdate };
      
      // Mapeie campos específicos de camelCase para snake_case
      if (optionUpdate.variationId !== undefined) {
        updateData.variation_id = optionUpdate.variationId;
        delete updateData.variationId;
      }
      
      if (optionUpdate.additionalPrice !== undefined) {
        updateData.additional_price = optionUpdate.additionalPrice;
        delete updateData.additionalPrice;
      }
      
      const { data, error } = await supabaseAdmin
        .from('variation_options')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();
        
      if (error) {
        console.error('Error updating variation option:', error);
        return undefined;
      }
      
      return {
        id: data.id,
        variationId: data.variation_id,
        name: data.name,
        additionalPrice: data.additional_price
      };
    } catch (error) {
      console.error('Unexpected error in updateVariationOption:', error);
      return undefined;
    }
  }

  async deleteVariationOption(id: number): Promise<boolean> {
    try {
      const { error } = await supabaseAdmin
        .from('variation_options')
        .delete()
        .eq('id', id);
        
      if (error) {
        console.error('Error deleting variation option:', error);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Unexpected error in deleteVariationOption:', error);
      return false;
    }
  }
}

export const storage = new SupabaseStorage();
