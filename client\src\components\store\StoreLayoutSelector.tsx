import React from 'react';
import { Category, Product, Store } from '@shared/schema';
import HorizontalCategoriesLayout from './layouts/HorizontalCategoriesLayout';
import BottomNavLayout from './layouts/BottomNavLayout';

interface StoreLayoutSelectorProps {
  store: Store;
  categories: Category[];
  products: Product[];
  onProductClick: (product: Product) => void;
  onAddToCart: (product: Product) => void;
}

/**
 * Componente que seleciona o layout adequado com base nas configurações da loja
 * Layout 1: Grid view - Exibição em grade de produtos
 * Layout 2: List view - Exibição em lista de produtos
 */
const StoreLayoutSelector: React.FC<StoreLayoutSelectorProps> = ({
  store,
  categories,
  products,
  onProductClick,
  onAddToCart
}) => {
  // Determinar qual layout usar com base nas configurações da loja
  // Normalizar para os layouts suportados: 1 = Grid (padrão), 2 = Lista
  let layoutType = store.layout || 1;
  if (layoutType > 2) {
    // Caso seja um layout legado (3 ou 4), converter para grid (1)
    layoutType = 1;
  }
  
  // Log para debug
  console.log('Loja:', store);
  console.log('Layout selecionado:', layoutType);

  // Propriedades comuns para todos os layouts
  const layoutProps = {
    store,
    categories,
    products,
    onProductClick,
    onAddToCart
  };

  // Renderizar o layout apropriado (apenas grid ou lista)
  switch (layoutType) {
    case 1:
      // Grid view - usando HorizontalCategoriesLayout que já implementa grade
      return <HorizontalCategoriesLayout {...layoutProps} />;
    case 2:
      // List view - usando BottomNavLayout que já implementa lista
      return <BottomNavLayout {...layoutProps} />;
    default:
      // Fallback para o layout padrão (grid)
      return <HorizontalCategoriesLayout {...layoutProps} />;
  }
};

export default StoreLayoutSelector;