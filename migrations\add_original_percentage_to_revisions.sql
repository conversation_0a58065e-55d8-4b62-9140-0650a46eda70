-- Adicionar campo para armazenar o valor original do percentual de desconto
-- Execute este script no console SQL do Supabase

-- Adicionar a coluna original_percentage à tabela order_revisions
ALTER TABLE order_revisions 
ADD COLUMN IF NOT EXISTS original_percentage REAL DEFAULT NULL;

-- Adicionar coment<PERSON>rio para documentação
COMMENT ON COLUMN order_revisions.original_percentage IS 'Valor original do percentual quando o discount_type é percentage';

-- Adicionar a coluna na tabela orders também
ALTER TABLE orders
ADD COLUMN IF NOT EXISTS original_percentage REAL DEFAULT NULL;

-- Adicionar coment<PERSON>rio para documentação
COMMENT ON COLUMN orders.original_percentage IS 'Valor original do percentual quando o discount_type é percentage';