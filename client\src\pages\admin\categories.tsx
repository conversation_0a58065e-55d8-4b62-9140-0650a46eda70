
import { useEffect } from 'react';
import { useLocation } from 'wouter';
import AdminLayout from '@/components/admin/AdminLayout';
import { Categories } from '@/components/admin/Categories';
import { useTranslation } from '@/hooks/useTranslation';
import { useStore } from '@/context/StoreContext';

export default function CategoriesPage() {
  const { t } = useTranslation();
  const [, setLocation] = useLocation();
  const { store, isLoading } = useStore();

  // Redirect to settings if no store exists
  useEffect(() => {
    if (!isLoading && !store) {
      setLocation('/admin/settings');
    }
  }, [store, isLoading, setLocation]);

  return (
    <AdminLayout title={t('admin.categories')}>
      <Categories />
    </AdminLayout>
  );
}
