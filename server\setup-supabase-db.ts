import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import { fileURLToPath } from 'url';
import path from 'path';
import fetch from 'node-fetch';

// Get the current file path for ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Initialize Supabase client with service role key for admin privileges
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.VITE_SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Erro: VITE_SUPABASE_URL ou VITE_SUPABASE_SERVICE_KEY não definidos.');
  console.error('Por favor, defina essas variáveis no arquivo .env');
  process.exit(1);
}

// Initialize the Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// List of tables to create
const tableQueries = [
  // Profiles table for Supabase auth
  `CREATE TABLE IF NOT EXISTS profiles (
    id UUID PRIMARY KEY,
    email TEXT NOT NULL,
    full_name TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
  );`,
  
  // Users table
  `CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    email TEXT NOT NULL UNIQUE,
    password TEXT NOT NULL,
    full_name TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
  );`,
  
  // Stores table
  `CREATE TABLE IF NOT EXISTS stores (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    name TEXT NOT NULL,
    slug TEXT NOT NULL UNIQUE,
    description TEXT,
    logo TEXT,
    colors JSONB NOT NULL,
    payment_methods JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
  );`
];

// Function to execute SQL directly via the SQL API
async function executeSql(query) {
  try {
    const response = await fetch(`${supabaseUrl}/rest/v1/sql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabaseServiceKey}`,
        'apikey': supabaseServiceKey,
        'Prefer': 'params=single-object'
      },
      body: JSON.stringify({ query: query })
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`SQL API Error (${response.status}):`, errorText);
      return false;
    }
    
    const result = await response.json();
    console.log('SQL executed successfully!');
    return true;
  } catch (error) {
    console.error('Error executing SQL:', error);
    return false;
  }
}

async function setupDatabase() {
  console.log('Starting Supabase database setup using SQL API...');
  console.log(`Using Supabase URL: ${supabaseUrl}`);
  
  try {
    // Try to create each table
    for (const query of tableQueries) {
      console.log(`Executing: ${query.substring(0, 60)}...`);
      await executeSql(query);
    }
    
    // Check if table was created successfully by querying it
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .limit(1);
        
      if (error) {
        console.error('Error querying profiles table:', error);
      } else {
        console.log('Profiles table exists and is accessible!');
      }
    } catch (err) {
      console.error('Error verifying table creation:', err);
    }
    
    console.log('Database setup completed!');
  } catch (error) {
    console.error('Error in database setup:', error);
  }
}

// Run the setup function
setupDatabase();