# Dashboard Administrativo Global - Doce Menu

Este guia explica como configurar e usar o sistema de dashboard administrativo global para super-administradores da plataforma Doce Menu.

## 📋 Visão Geral

O Dashboard Administrativo Global permite que super-administradores:

- **Visualizem métricas globais** da plataforma (lojas, receita, pedidos)
- **<PERSON><PERSON><PERSON><PERSON><PERSON> todas as lojas** (ativar/desativar, visualizar detalhes)
- **Controlem assinaturas** e planos de pagamento
- **Gerenciem usuários** e permissões de admin global
- **Analisem performance** com rankings e analytics

## 🚀 Configuração Inicial

### 1. Adicionar Campo na Tabela Users

Primeiro, execute o script para adicionar o campo `is_global_admin` na tabela users:

```bash
npm run setup:global-admin
```

### 2. Promover Usuário a Super-Admin

Liste os usuários existentes:

```bash
npm run list:users
```

Promova um usuário específico:

```bash
npm run promote:global-admin <user_id>
```

Exemplo:
```bash
npm run promote:global-admin 1
```

Para remover permissões:

```bash
node scripts/promote-global-admin.js <user_id> demote
```

## 🎯 Funcionalidades

### Dashboard Principal (`/admin/global`)

- **Métricas Globais**: Total de lojas, lojas ativas, lojas premium, receita total
- **Crescimento**: Comparação com mês anterior (receita e pedidos)
- **Top 10 Lojas**: Rankings por número de pedidos e receita
- **Distribuição de Assinaturas**: Visualização de planos (Free, Premium, Canceladas, Em Atraso)

### Gerenciamento de Lojas (`/admin/global/stores`)

- **Lista Paginada**: Todas as lojas com filtros avançados
- **Filtros**: Status (ativa/inativa), plano (free/premium), busca por nome/email
- **Ordenação**: Por nome, data de criação, última atividade
- **Ações**: Visualizar detalhes, ativar/desativar lojas
- **Cards iOS-nativos**: Design responsivo com informações essenciais

### Detalhes da Loja (`/admin/global/stores/:id`)

- **Informações Completas**: Dados da loja, proprietário, assinatura
- **Métricas Específicas**: Produtos, pedidos, clientes, receita recente
- **Histórico**: Atividade e performance da loja

## 🔐 Segurança e Permissões

### Backend

- **Middleware `requireGlobalAdmin`**: Valida permissões em todas as rotas
- **Verificação de Token**: Integração com Firebase Auth
- **Proteção de Rotas**: Todas as APIs globais são protegidas

### Frontend

- **GlobalAdminGuard**: Componente de proteção para rotas
- **Hook `useGlobalAdminGuard`**: Verificação de permissões
- **Redirecionamento**: Usuários sem permissão são redirecionados

## 🛠️ Estrutura Técnica

### Backend (`/server`)

```
routes/global-admin.ts          # Rotas da API global
middleware/global-admin.ts      # Middleware de autenticação
storage.ts                      # Interface com métodos globais
storage.supabase.ts            # Implementação Supabase
```

### Frontend (`/client/src`)

```
pages/admin/global/            # Páginas do dashboard global
├── index.tsx                  # Dashboard principal
├── stores.tsx                 # Gerenciamento de lojas
└── store-details.tsx          # Detalhes específicos

components/global-admin/       # Componentes específicos
├── GlobalAdminLayout.tsx      # Layout principal
└── GlobalAdminGuard.tsx       # Proteção de rotas

hooks/useGlobalAdmin.ts        # Hooks personalizados
```

### APIs Disponíveis

```
GET  /api/admin/global/analytics              # Métricas globais
GET  /api/admin/global/stores                 # Lista de lojas
GET  /api/admin/global/stores/:id             # Detalhes da loja
PATCH /api/admin/global/stores/:id/toggle-status  # Ativar/desativar
PATCH /api/admin/global/users/:id/toggle-admin    # Gerenciar admins
```

## 📱 Design iOS Nativo

O dashboard segue as diretrizes de design iOS:

- **Componentes**: Cards com bordas arredondadas e sombras suaves
- **Cores**: Paleta consistente com gradientes rosa-amarelo da marca
- **Ícones**: Lucide React com estilo minimalista
- **Animações**: Transições suaves e responsivas
- **Layout**: Mobile-first com navegação adaptativa

## 🔧 Comandos Úteis

```bash
# Configuração inicial
npm run setup:global-admin

# Gerenciamento de usuários
npm run list:users
npm run promote:global-admin <user_id>

# Desenvolvimento
npm run dev                    # Iniciar servidor de desenvolvimento
npm run build                 # Build para produção

# Banco de dados
npm run db:push               # Aplicar mudanças no schema
```

## 🚨 Troubleshooting

### Erro: "Campo is_global_admin não existe"

Execute o script de configuração:
```bash
npm run setup:global-admin
```

### Erro: "Acesso negado"

Verifique se o usuário foi promovido corretamente:
```bash
npm run list:users
```

### Erro: "Usuário não encontrado"

Certifique-se de que o usuário está logado e sincronizado:
1. Faça login no sistema
2. Verifique se o usuário aparece na lista
3. Promova o usuário correto

### Problemas de Permissão

1. Verifique as variáveis de ambiente do Supabase
2. Confirme que o `VITE_SUPABASE_SERVICE_KEY` está configurado
3. Teste a conexão com o banco de dados

## 📊 Monitoramento

### Métricas Importantes

- **Taxa de Conversão**: Free → Premium
- **Churn Rate**: Cancelamentos de assinatura
- **Crescimento**: Novas lojas por mês
- **Engagement**: Lojas ativas vs inativas

### Alertas Recomendados

- Lojas com muitos pedidos mas plano gratuito
- Assinaturas próximas do vencimento
- Lojas inativas há mais de 30 dias
- Picos de crescimento ou declínio

## 🔄 Atualizações Futuras

### Funcionalidades Planejadas

- **Relatórios Avançados**: Exportação em PDF/Excel
- **Notificações**: Sistema de alertas em tempo real
- **Análise Geográfica**: Distribuição de lojas por região
- **Suporte**: Sistema de tickets integrado
- **Auditoria**: Log de ações administrativas

### Melhorias de UX

- **Dashboard Customizável**: Widgets arrastar e soltar
- **Filtros Salvos**: Configurações personalizadas
- **Modo Escuro**: Tema alternativo
- **Atalhos**: Navegação por teclado

## 📞 Suporte

Para dúvidas ou problemas:

1. Consulte este guia primeiro
2. Verifique os logs do servidor
3. Teste em ambiente de desenvolvimento
4. Documente o erro com detalhes

---

**Nota**: Este sistema é destinado apenas para super-administradores da plataforma. Use com responsabilidade e mantenha as credenciais seguras.
