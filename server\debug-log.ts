/**
 * Arquivo para debugar as operações de variações de produtos
 */

export function logVariationData(variation: any, options: any[]) {
  console.log('=== VARIAÇÃO DETALHES DE DEBUG ===');
  console.log('ID da Variação:', variation.id);
  console.log('Nome da Variação:', variation.name);
  console.log('Número de Opções:', options.length);
  
  options.forEach((option, index) => {
    console.log(`\nOpção #${index + 1}:`);
    console.log('  ID (se existente):', option.id || 'Novo');
    console.log('  Nome:', option.name);
    console.log('  Preço:', option.price);
    console.log('  Tipo do Preço:', typeof option.price);
    
    // Verificar se additionalPrice também está presente
    if ('additionalPrice' in option) {
      console.log('  additionalPrice:', option.additionalPrice);
      console.log('  Tipo do additionalPrice:', typeof option.additionalPrice);
    }
  });
  
  console.log('\n=== FIM DEBUG ===');
}