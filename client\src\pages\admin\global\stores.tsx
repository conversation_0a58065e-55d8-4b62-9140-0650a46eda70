import { useState, useEffect } from 'react';
import { useLocation } from 'wouter';
import { useQueryClient } from '@tanstack/react-query';
import GlobalAdminLayout from '@/components/global-admin/GlobalAdminLayout';
import { useGlobalAdminGuard, useGlobalStores, toggleStoreStatus } from '@/hooks/useGlobalAdmin';
import { useTranslation } from '@/hooks/useTranslation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { useToast } from '@/hooks/use-toast';
import {
  Store,
  Search,
  Filter,
  Eye,
  Power,
  PowerOff,
  Crown,
  Calendar,
  User,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';

export default function GlobalStoresPage() {
  const [, setLocation] = useLocation();
  const { t } = useTranslation();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Estados para filtros e paginação
  const [page, setPage] = useState(1);
  const [search, setSearch] = useState('');
  const [status, setStatus] = useState<'active' | 'inactive' | 'all'>('all');
  const [planType, setPlanType] = useState<'free' | 'premium' | 'all'>('all');
  const [sortBy, setSortBy] = useState<'name' | 'createdAt' | 'lastActivity'>('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // Estados para ações
  const [selectedStore, setSelectedStore] = useState<any>(null);
  const [showToggleDialog, setShowToggleDialog] = useState(false);
  const [isToggling, setIsToggling] = useState(false);

  const { data: adminUser, isLoading: isLoadingUser } = useGlobalAdminGuard();
  const { data: storesData, isLoading: isLoadingStores, error } = useGlobalStores({
    page,
    limit: 12,
    status: status === 'all' ? undefined : status,
    planType: planType === 'all' ? undefined : planType,
    search: search || undefined,
    sortBy,
    sortOrder
  });

  // Redirecionar se não for admin global
  useEffect(() => {
    if (!isLoadingUser && (!adminUser || !adminUser.isGlobalAdmin)) {
      setLocation('/admin');
    }
  }, [adminUser, isLoadingUser, setLocation]);

  // Mostrar loading enquanto verifica permissões
  if (isLoadingUser) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Crown className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
          <p className="text-gray-600">{t('common.loading')}</p>
        </div>
      </div>
    );
  }

  // Mostrar erro se não conseguir verificar usuário
  if (!adminUser?.isGlobalAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Alert className="max-w-md">
          <AlertDescription>
            Acesso negado. Permissões de super-administrador necessárias.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const handleToggleStore = async () => {
    if (!selectedStore) return;

    setIsToggling(true);
    try {
      await toggleStoreStatus(selectedStore.id, !selectedStore.isActive);
      
      // Invalidar queries para atualizar dados
      queryClient.invalidateQueries({ queryKey: ['/api/admin/global/stores'] });
      queryClient.invalidateQueries({ queryKey: ['/api/admin/global/analytics'] });

      toast({
        title: t('common.success'),
        description: selectedStore.isActive 
          ? t('globalAdmin.stores.actions.deactivated')
          : t('globalAdmin.stores.actions.activated'),
      });
    } catch (error) {
      toast({
        title: t('common.error'),
        description: 'Erro ao alterar status da loja',
        variant: 'destructive',
      });
    } finally {
      setIsToggling(false);
      setShowToggleDialog(false);
      setSelectedStore(null);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  const getPlanBadgeColor = (planType: string) => {
    return planType === 'premium' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800';
  };

  const getStatusBadgeColor = (isActive: boolean) => {
    return isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
  };

  return (
    <GlobalAdminLayout
      title={t('globalAdmin.stores.title')}
      description={t('globalAdmin.stores.subtitle')}
    >
      {/* Filtros */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Filter className="h-5 w-5" />
            <span>Filtros</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Busca */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder={t('globalAdmin.stores.searchPlaceholder')}
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Status */}
            <Select value={status} onValueChange={setStatus}>
              <SelectTrigger>
                <SelectValue placeholder={t('globalAdmin.stores.filters.status')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t('globalAdmin.stores.filters.all')}</SelectItem>
                <SelectItem value="active">{t('globalAdmin.stores.filters.active')}</SelectItem>
                <SelectItem value="inactive">{t('globalAdmin.stores.filters.inactive')}</SelectItem>
              </SelectContent>
            </Select>

            {/* Plano */}
            <Select value={planType} onValueChange={setPlanType}>
              <SelectTrigger>
                <SelectValue placeholder={t('globalAdmin.stores.filters.planType')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t('globalAdmin.stores.filters.all')}</SelectItem>
                <SelectItem value="free">{t('globalAdmin.stores.filters.free')}</SelectItem>
                <SelectItem value="premium">{t('globalAdmin.stores.filters.premium')}</SelectItem>
              </SelectContent>
            </Select>

            {/* Ordenação */}
            <Select value={`${sortBy}-${sortOrder}`} onValueChange={(value) => {
              const [newSortBy, newSortOrder] = value.split('-') as [typeof sortBy, typeof sortOrder];
              setSortBy(newSortBy);
              setSortOrder(newSortOrder);
            }}>
              <SelectTrigger>
                <SelectValue placeholder="Ordenar por" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="name-asc">{t('globalAdmin.stores.sort.name')} (A-Z)</SelectItem>
                <SelectItem value="name-desc">{t('globalAdmin.stores.sort.name')} (Z-A)</SelectItem>
                <SelectItem value="createdAt-desc">{t('globalAdmin.stores.sort.createdAt')} (Mais recente)</SelectItem>
                <SelectItem value="createdAt-asc">{t('globalAdmin.stores.sort.createdAt')} (Mais antigo)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Lista de lojas */}
      {isLoadingStores ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-2/3" />
                  <div className="flex space-x-2">
                    <Skeleton className="h-6 w-16" />
                    <Skeleton className="h-6 w-16" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : error ? (
        <Alert>
          <AlertDescription>
            Erro ao carregar lojas: {error.message}
          </AlertDescription>
        </Alert>
      ) : (
        <>
          {/* Grid de lojas */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
            {storesData?.stores.map((store) => (
              <Card key={store.id} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-lg">{store.name}</CardTitle>
                      <p className="text-sm text-muted-foreground">/{store.slug}</p>
                    </div>
                    {store.logo && (
                      <img 
                        src={store.logo} 
                        alt={store.name}
                        className="w-12 h-12 rounded-lg object-cover"
                      />
                    )}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {/* Informações do proprietário */}
                    {store.user && (
                      <div className="flex items-center space-x-2 text-sm">
                        <User className="h-4 w-4 text-muted-foreground" />
                        <span>{store.user.fullName || store.user.email}</span>
                      </div>
                    )}

                    {/* Data de criação */}
                    <div className="flex items-center space-x-2 text-sm">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span>{t('globalAdmin.stores.card.createdAt')} {formatDate(store.createdAt)}</span>
                    </div>

                    {/* Badges de status e plano */}
                    <div className="flex space-x-2">
                      <Badge className={getStatusBadgeColor(store.isActive)}>
                        {store.isActive ? t('globalAdmin.stores.filters.active') : t('globalAdmin.stores.filters.inactive')}
                      </Badge>
                      {store.subscription && (
                        <Badge className={getPlanBadgeColor(store.subscription.planType)}>
                          {store.subscription.planType === 'premium' ? (
                            <Crown className="h-3 w-3 mr-1" />
                          ) : null}
                          {store.subscription.planType === 'premium' ? 'Premium' : 'Gratuito'}
                        </Badge>
                      )}
                    </div>

                    {/* Ações */}
                    <div className="flex space-x-2 pt-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setLocation(`/admin/global/stores/${store.id}`)}
                        className="flex-1"
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        {t('globalAdmin.stores.card.viewDetails')}
                      </Button>
                      <Button
                        variant={store.isActive ? "destructive" : "default"}
                        size="sm"
                        onClick={() => {
                          setSelectedStore(store);
                          setShowToggleDialog(true);
                        }}
                      >
                        {store.isActive ? (
                          <PowerOff className="h-4 w-4" />
                        ) : (
                          <Power className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Paginação */}
          {storesData && storesData.pagination.totalPages > 1 && (
            <div className="flex items-center justify-between">
              <p className="text-sm text-muted-foreground">
                Página {storesData.pagination.currentPage} de {storesData.pagination.totalPages} 
                ({storesData.pagination.totalStores} lojas)
              </p>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(page - 1)}
                  disabled={!storesData.pagination.hasPrevPage}
                >
                  <ChevronLeft className="h-4 w-4" />
                  Anterior
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(page + 1)}
                  disabled={!storesData.pagination.hasNextPage}
                >
                  Próximo
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </>
      )}

      {/* Dialog de confirmação para toggle de status */}
      <AlertDialog open={showToggleDialog} onOpenChange={setShowToggleDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {selectedStore?.isActive 
                ? t('globalAdmin.stores.actions.deactivate')
                : t('globalAdmin.stores.actions.activate')
              }
            </AlertDialogTitle>
            <AlertDialogDescription>
              {selectedStore?.isActive 
                ? t('globalAdmin.stores.actions.confirmDeactivate')
                : t('globalAdmin.stores.actions.confirmActivate')
              }
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t('common.cancel')}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleToggleStore}
              disabled={isToggling}
            >
              {isToggling ? t('common.loading') : t('common.yes')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </GlobalAdminLayout>
  );
}
