import { Pool, neonConfig } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-serverless';
import { createClient } from '@supabase/supabase-js';
import ws from "ws";
import * as schema from "@shared/schema";
import 'dotenv/config';

// Configure neonConfig para usar websockets
neonConfig.webSocketConstructor = ws;

// Obtém as variáveis de ambiente
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_KEY;
const databaseUrl = process.env.DATABASE_URL;
const useSupabasePool = process.env.USE_SUPABASE_POOL === 'true';

// Verificar variáveis de ambiente
if (!supabaseUrl || !supabaseKey) {
  console.error('Erro: VITE_SUPABASE_URL ou VITE_SUPABASE_SERVICE_KEY não definidos.');
  console.error('Por favor, defina essas variáveis no arquivo .env');
  process.exit(1);
}

if (!databaseUrl && !useSupabasePool) {
  console.error('Erro: DATABASE_URL não definida.');
  console.error('Por favor, provisione um banco de dados PostgreSQL.');
  process.exit(1);
}

// Inicializar cliente Supabase com a service key (permissões administrativas)
export const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Configurar conexão com o banco de dados
let pool: Pool;

if (useSupabasePool) {
  console.log('Usando pool de conexões do Supabase');
  // Criar um adaptador para o Supabase que implementa a interface do Pool
  const supabaseAdapter = {
    query: async (text: string, params?: any[]) => {
      try {
        console.log('Executando query via Supabase:', text);

        // Usar o cliente Supabase diretamente para operações CRUD
        if (text.trim().toUpperCase().startsWith('SELECT')) {
          // Para consultas SELECT, usar o método .from() do Supabase
          const tableName = extractTableName(text);
          if (tableName) {
            const { data, error } = await supabase
              .from(tableName)
              .select('*');

            if (error) throw error;
            return { rows: data || [] };
          }
        } else if (text.trim().toUpperCase().startsWith('UPDATE')) {
          // Para operações UPDATE, usar o método .update() do Supabase
          const { tableName, conditions } = extractUpdateInfo(text, params);
          if (tableName && conditions) {
            const { data, error } = await supabase
              .from(tableName)
              .update(conditions.updateData)
              .eq(conditions.column, conditions.value)
              .select('*');

            if (error) throw error;
            return { rows: data || [] };
          }
        }

        // Fallback para SQL direto via RPC (requer função no Supabase)
        console.log('Fallback para SQL direto via RPC');
        const { data, error } = await supabase.rpc('execute_sql', {
          query_text: text,
          query_params: params || []
        });

        if (error) {
          console.error('Erro ao executar SQL via Supabase:', error);
          throw error;
        }

        return { rows: data || [] };
      } catch (error) {
        console.error('Erro ao executar query via Supabase:', error);
        throw error;
      }
    },
    // Implementar outros métodos necessários do Pool
    connect: async () => {
      return {
        query: async (text: string, params?: any[]) => {
          const result = await supabaseAdapter.query(text, params);
          return result;
        },
        release: () => {}
      };
    },
    end: async () => {}
  } as unknown as Pool;

  pool = supabaseAdapter;
} else {
  console.log('Usando conexão direta com Neon');
  // Configurar conexão direta com o banco PostgreSQL usando Neon ServerLess
  pool = new Pool({ connectionString: databaseUrl });
}

// Funções auxiliares para extrair informações das queries SQL
function extractTableName(query: string): string | null {
  // Expressão regular simples para extrair o nome da tabela de uma consulta SELECT
  const match = query.match(/FROM\s+([^\s,;]+)/i);
  return match ? match[1] : null;
}

function extractUpdateInfo(query: string, params?: any[]): { tableName: string | null, conditions: any | null } {
  // Expressão regular para extrair o nome da tabela de uma consulta UPDATE
  const tableMatch = query.match(/UPDATE\s+([^\s,;]+)/i);
  const tableName = tableMatch ? tableMatch[1] : null;

  // Expressão regular para extrair a condição WHERE
  const whereMatch = query.match(/WHERE\s+([^\s=]+)\s*=\s*\$(\d+)/i);

  if (whereMatch && params && params.length >= parseInt(whereMatch[2])) {
    const column = whereMatch[1];
    const paramIndex = parseInt(whereMatch[2]) - 1;
    const value = params[paramIndex];

    // Extrair os dados a serem atualizados
    const setMatch = query.match(/SET\s+([^WHERE]+)/i);
    const updateData: Record<string, any> = {};

    if (setMatch) {
      const setParts = setMatch[1].split(',');
      setParts.forEach((part, index) => {
        const keyValueMatch = part.trim().match(/([^\s=]+)\s*=\s*\$(\d+)/);
        if (keyValueMatch && params) {
          const key = keyValueMatch[1];
          const valueIndex = parseInt(keyValueMatch[2]) - 1;
          if (params.length > valueIndex) {
            updateData[key] = params[valueIndex];
          }
        }
      });
    }

    return {
      tableName,
      conditions: {
        column,
        value,
        updateData
      }
    };
  }

  return { tableName, conditions: null };
}

export { pool };

// Usar o pool diretamente com o Neon
export const db = drizzle(pool as any, { schema });
