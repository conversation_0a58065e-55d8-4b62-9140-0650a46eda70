import { useQuery } from '@tanstack/react-query';
import { Link } from 'wouter';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useStore } from '@/context/StoreContext';
import { useTranslation } from '@/hooks/useTranslation';
import { Eye, ShoppingBag, CreditCard } from 'lucide-react';
import { queryClient } from '@/lib/queryClient';
import { formatCurrency, formatDate } from '@/lib/utils';

// Dashboard stats and recent orders component
export function Dashboard() {
  const { t } = useTranslation();
  const { store } = useStore();

  // Fetch analytics data
  const { data: analytics, isLoading: isLoadingAnalytics } = useQuery({
    queryKey: ['/api/analytics/dashboard'],
    enabled: !!store
  });

  // Fetch recent orders
  const { data: orders, isLoading: isLoadingOrders } = useQuery({
    queryKey: ['/api/orders'],
    enabled: !!store,
    select: (data) => data.slice(0, 5) // Get only 5 most recent orders
  });

  // Status badge component
  const OrderStatusBadge = ({ status }: { status: string }) => {
    let bgColor = 'bg-primary';
    let textColor = 'text-primary';

    switch (status) {
      case 'completed':
        bgColor = 'bg-success bg-opacity-10';
        textColor = 'text-success';
        break;
      case 'processing':
        bgColor = 'bg-warning bg-opacity-10';
        textColor = 'text-warning';
        break;
      case 'pending':
        bgColor = 'bg-warning bg-opacity-10';
        textColor = 'text-warning';
        break;
      case 'shipped':
        bgColor = 'bg-primary bg-opacity-10';
        textColor = 'text-primary';
        break;
      case 'cancelled':
        bgColor = 'bg-error bg-opacity-10';
        textColor = 'text-error';
        break;
    }

    return (
      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${bgColor} ${textColor}`}>
        {t(`dashboard.${status}`)}
      </span>
    );
  };

  return (
    <div>
      {/* Stats cards */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
        {/* Visits */}
        <Card>
          <CardContent className="p-0">
            <div className="px-4 py-5 sm:p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0 bg-accent bg-opacity-20 rounded-md p-3">
                  <Eye className="h-6 w-6 text-accent" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-neutral-dark truncate">{t('dashboard.visitsThisMonth')}</dt>
                    <dd>
                      {isLoadingAnalytics ? (
                        <Skeleton className="h-7 w-16 mt-1" />
                      ) : (
                        <div className="text-lg font-bold text-neutral-dark">{analytics?.visitCount || 0}</div>
                      )}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
            <div className="bg-neutral-light px-4 py-4 sm:px-6">
              <div className="text-sm">
                <Link href="#" className="font-medium text-primary hover:text-primary-dark">
                  {t('dashboard.viewDetails')}
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Orders */}
        <Card>
          <CardContent className="p-0">
            <div className="px-4 py-5 sm:p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0 bg-secondary bg-opacity-20 rounded-md p-3">
                  <ShoppingBag className="h-6 w-6 text-secondary" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-neutral-dark truncate">{t('dashboard.ordersThisMonth')}</dt>
                    <dd>
                      {isLoadingAnalytics ? (
                        <Skeleton className="h-7 w-12 mt-1" />
                      ) : (
                        <div className="text-lg font-bold text-neutral-dark">{analytics?.orderCount || 0}</div>
                      )}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
            <div className="bg-neutral-light px-4 py-4 sm:px-6">
              <div className="text-sm">
                <Link href="/admin/orders" className="font-medium text-primary hover:text-primary-dark">
                  {t('dashboard.viewAllOrders')}
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Revenue */}
        <Card>
          <CardContent className="p-0">
            <div className="px-4 py-5 sm:p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0 bg-primary bg-opacity-20 rounded-md p-3">
                  <CreditCard className="h-6 w-6 text-primary" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-neutral-dark truncate">{t('dashboard.revenueThisMonth')}</dt>
                    <dd>
                      {isLoadingAnalytics ? (
                        <Skeleton className="h-7 w-24 mt-1" />
                      ) : (
                        <div className="text-lg font-bold text-neutral-dark">
                          {formatCurrency(analytics?.revenue || 0)}
                        </div>
                      )}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
            <div className="bg-neutral-light px-4 py-4 sm:px-6">
              <div className="text-sm">
                <Link href="#" className="font-medium text-primary hover:text-primary-dark">
                  {t('dashboard.viewFinancialReport')}
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Orders */}
      <div className="mt-8">
        <h2 className="text-lg font-medium text-neutral-dark font-heading">{t('dashboard.recentOrders')}</h2>
        <div className="mt-4 overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
          <table className="min-w-full divide-y divide-gray-300">
            <thead className="bg-neutral-light">
              <tr>
                <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-neutral-dark sm:pl-6">{t('dashboard.orderID')}</th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-neutral-dark">{t('dashboard.customer')}</th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-neutral-dark">{t('dashboard.orderDate')}</th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-neutral-dark">{t('dashboard.orderValue')}</th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-neutral-dark">{t('dashboard.orderStatus')}</th>
                <th scope="col" className="relative py-3.5 pl-3 pr-4 sm:pr-6">
                  <span className="sr-only">{t('common.actions')}</span>
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white">
              {isLoadingOrders ? (
                Array(3).fill(0).map((_, index) => (
                  <tr key={index}>
                    <td className="whitespace-nowrap py-4 pl-4 pr-3 sm:pl-6">
                      <Skeleton className="h-5 w-16" />
                    </td>
                    <td className="whitespace-nowrap px-3 py-4">
                      <Skeleton className="h-5 w-24" />
                    </td>
                    <td className="whitespace-nowrap px-3 py-4">
                      <Skeleton className="h-5 w-20" />
                    </td>
                    <td className="whitespace-nowrap px-3 py-4">
                      <Skeleton className="h-5 w-16" />
                    </td>
                    <td className="whitespace-nowrap px-3 py-4">
                      <Skeleton className="h-5 w-20" />
                    </td>
                    <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right sm:pr-6">
                      <Skeleton className="h-5 w-16 ml-auto" />
                    </td>
                  </tr>
                ))
              ) : orders && orders.length > 0 ? (
                orders.map((order: any) => (
                  <tr key={order.id}>
                    <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-neutral-dark sm:pl-6">#{order.id}</td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-neutral-dark">{order.customer?.name || 'Unknown'}</td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-neutral-dark">{formatDate(order.createdAt)}</td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-neutral-dark">{formatCurrency(order.total)}</td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm">
                      <OrderStatusBadge status={order.status} />
                    </td>
                    <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                      <Link href={`/admin/orders/${order.id}`} className="text-primary hover:text-primary-dark">
                        {t('orders.viewDetails')}
                      </Link>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={6} className="px-6 py-8 text-center text-sm text-neutral-dark">
                    {t('orders.noOrders')}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

export default Dashboard;
