import { useEffect, ReactNode } from 'react';
import { useLocation } from 'wouter';
import { useGlobalAdminGuard } from '@/hooks/useGlobalAdmin';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Crown } from 'lucide-react';

interface GlobalAdminGuardProps {
  children: ReactNode;
}

/**
 * Componente de proteção para rotas que requerem permissões de super-administrador global
 */
export default function GlobalAdminGuard({ children }: GlobalAdminGuardProps) {
  const [, setLocation] = useLocation();
  const { data: adminUser, isLoading, error } = useGlobalAdminGuard();

  useEffect(() => {
    // Redirecionar para admin normal se não for admin global
    if (!isLoading && (!adminUser || !adminUser.isGlobalAdmin)) {
      setLocation('/admin');
    }
  }, [adminUser, isLoading, setLocation]);

  // Mostrar loading enquanto verifica permissões
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <Crown className="h-12 w-12 text-yellow-500 mx-auto mb-4 animate-pulse" />
          <p className="text-gray-600">Verificando permissões...</p>
        </div>
      </div>
    );
  }

  // Mostrar erro se não conseguir verificar usuário
  if (error || !adminUser?.isGlobalAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Alert className="max-w-md">
          <Crown className="h-4 w-4" />
          <AlertDescription>
            Acesso negado. Permissões de super-administrador necessárias.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Renderizar conteúdo se for admin global
  return <>{children}</>;
}
