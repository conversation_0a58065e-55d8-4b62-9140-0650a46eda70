/**
 * Utilitários para trabalhar com localização e mapas
 */

export interface StoreAddress {
  addressStreet?: string;
  addressNumber?: string;
  addressComplement?: string;
  addressNeighborhood?: string;
  addressCity?: string;
  addressState?: string;
}

/**
 * Gera uma string de endereço formatada a partir dos dados da loja
 */
export function formatStoreAddress(address: StoreAddress): string {
  const parts: string[] = [];

  // Rua e número
  if (address.addressStreet) {
    let streetPart = address.addressStreet;
    if (address.addressNumber) {
      streetPart += `, ${address.addressNumber}`;
    }
    parts.push(streetPart);
  }

  // Complemento
  if (address.addressComplement) {
    parts.push(address.addressComplement);
  }

  // Bairro
  if (address.addressNeighborhood) {
    parts.push(address.addressNeighborhood);
  }

  // Cidade e estado
  if (address.addressCity || address.addressState) {
    let cityStatePart = '';
    if (address.addressCity) {
      cityStatePart = address.addressCity;
    }
    if (address.addressState) {
      cityStatePart += cityStatePart ? ` - ${address.addressState}` : address.addressState;
    }
    if (cityStatePart) {
      parts.push(cityStatePart);
    }
  }

  return parts.join(', ');
}

/**
 * Gera um link do Google Maps para o endereço da loja
 */
export function generateGoogleMapsLink(address: StoreAddress): string {
  const formattedAddress = formatStoreAddress(address);

  if (!formattedAddress.trim()) {
    return '';
  }

  // Codificar o endereço para URL
  const encodedAddress = encodeURIComponent(formattedAddress);

  // Retornar link do Google Maps
  return `https://www.google.com/maps/search/?api=1&query=${encodedAddress}`;
}

/**
 * Abre o endereço da loja no Google Maps
 */
export function openStoreLocation(address: StoreAddress | undefined | null): void {
  if (!address) return;

  const mapsLink = generateGoogleMapsLink(address);

  if (mapsLink) {
    window.open(mapsLink, '_blank', 'noopener,noreferrer');
  }
}

/**
 * Verifica se a loja tem endereço suficiente para gerar um link de localização
 */
export function hasValidAddress(address: StoreAddress | undefined | null): boolean {
  if (!address) return false;
  return !!(address.addressStreet || address.addressCity || address.addressNeighborhood);
}
