import { useState, ReactNode } from 'react';
import { Link, useLocation } from 'wouter';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/context/FirebaseAuthContext';
import { useStore } from '@/context/StoreContext';
import { useTranslation } from '@/hooks/useTranslation';
import { useIsGlobalAdmin } from '@/hooks/useGlobalAdmin';
import {
  LayoutDashboard,
  ShoppingBag,
  ShoppingCart,
  Users,
  Settings,
  Store,
  Menu,
  User,
  LogOut,
  FolderTree, // Added import for FolderTree icon
  Ticket, // Import for coupon icon
  Crown // Import for global admin icon
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface AdminLayoutProps {
  children: ReactNode;
  title: string;
  description?: string;
}

export function AdminLayout({ children, title, description }: AdminLayoutProps) {
  const [location] = useLocation();
  const { t } = useTranslation();
  const { user, signOut } = useAuth();
  const { store } = useStore();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const isGlobalAdmin = useIsGlobalAdmin();

  const navItems = [
    { path: '/admin', label: t('common.dashboard'), icon: <LayoutDashboard className="h-5 w-5" /> },
    { path: '/admin/products', label: t('common.products'), icon: <ShoppingBag className="h-5 w-5" /> },
    { path: '/admin/orders', label: t('common.orders'), icon: <ShoppingCart className="h-5 w-5" /> },
    { path: '/admin/customers', label: t('common.customers'), icon: <Users className="h-5 w-5" /> },
    { path: '/admin/coupons', label: t('coupons.title'), icon: <Ticket className="h-5 w-5" /> },
    { path: '/admin/categories', label: t('common.categories'), icon: <FolderTree className="h-5 w-5" /> },
    { path: '/admin/settings', label: t('common.settings'), icon: <Settings className="h-5 w-5" /> }
  ];

  const isActive = (path: string) => {
    return location === path;
  };

  return (
    <div className="min-h-screen flex flex-col">
      {/* Navbar */}
      <nav className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex">
              <div className="flex-shrink-0 flex items-center">
                <span className="text-primary text-xl font-bold font-heading">Doce Menu</span>
              </div>
              <div className="hidden sm:ml-6 sm:flex sm:items-center">
                <div className="flex space-x-4">
                  {navItems.map((item) => (
                    <Link
                      key={item.path}
                      href={item.path}
                      className={`flex items-center px-3 py-2 text-sm font-medium ${isActive(item.path)
                        ? 'text-primary border-b-2 border-primary'
                        : 'text-neutral-dark hover:text-primary'}`}
                    >
                      {item.icon} {item.label} {/*Added icon to the navigation*/}
                    </Link>
                  ))}
                </div>
              </div>
            </div>
            <div className="flex items-center">
              {/* Link para Dashboard Global (apenas para super-admins) */}
              {isGlobalAdmin && (
                <div className="hidden md:flex items-center mr-4">
                  <Link
                    href="/admin/global"
                    className="text-yellow-600 hover:text-yellow-700 flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors"
                  >
                    <Crown className="h-4 w-4 mr-2" />
                    <span>Dashboard Global</span>
                  </Link>
                </div>
              )}

              {store && (
                <div className="hidden md:flex items-center">
                  <Link
                    href={`/${store.slug}`}
                    className="text-neutral-dark hover:text-primary mr-4 flex items-center"
                  >
                    <Store className="h-5 w-5 mr-1" />
                    <span className="ml-1 text-sm">{t('common.viewStore')}</span>
                  </Link>
                </div>
              )}
              <div className="ml-3 relative">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="flex items-center text-sm focus:outline-none">
                      <span className="hidden md:block mr-2 text-neutral-dark">{user?.displayName || 'Minha Conta'}</span>
                      <div className="h-8 w-8 rounded-full bg-secondary text-white flex items-center justify-center">
                        <span className="text-sm font-medium">
                          {user?.displayName?.split(' ').map((n: string) => n[0]).join('') || 'U'}
                        </span>
                      </div>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem className="cursor-pointer flex items-center">
                      <User className="mr-2 h-4 w-4" />
                      <span>{t('common.settings')}</span>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem className="cursor-pointer flex items-center" onClick={() => signOut()}>
                      <LogOut className="mr-2 h-4 w-4" />
                      <span>{t('common.logout')}</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
            <div className="flex items-center sm:hidden">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              >
                <Menu className="h-6 w-6" />
              </Button>
            </div>
          </div>
        </div>

        {/* Mobile menu */}
        {mobileMenuOpen && (
          <div className="sm:hidden">
            <div className="pt-2 pb-3 space-y-1">
              {navItems.map((item) => (
                <Link
                  key={item.path}
                  href={item.path}
                  className={`flex items-center px-3 py-2 text-base font-medium ${isActive(item.path)
                    ? 'text-primary bg-neutral-light'
                    : 'text-neutral-dark hover:bg-neutral-light hover:text-primary'}`}
                  onClick={() => setMobileMenuOpen(false)}
                >
                  {item.icon}
                  <span className="ml-2">{item.label}</span>
                </Link>
              ))}

              {/* Link para Dashboard Global no menu mobile */}
              {isGlobalAdmin && (
                <Link
                  href="/admin/global"
                  className="flex items-center px-3 py-2 text-base font-medium text-yellow-600 hover:bg-yellow-50 hover:text-yellow-700"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  <Crown className="h-5 w-5" />
                  <span className="ml-2">Dashboard Global</span>
                </Link>
              )}

              {store && (
                <Link
                  href={`/${store.slug}`}
                  className="flex items-center px-3 py-2 text-base font-medium text-neutral-dark hover:bg-neutral-light hover:text-primary"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  <Store className="h-5 w-5" />
                  <span className="ml-2">{t('common.viewStore')}</span>
                </Link>
              )}
            </div>
          </div>
        )}
      </nav>

      {/* Main content */}
      <main className="flex-grow bg-neutral-light">
        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-5 sm:px-6">
            <div className="mb-8">
              <h1 className="text-2xl font-bold text-neutral-dark font-heading">{title}</h1>
              {description && (
                <p className="mt-1 text-neutral-dark">{description}</p>
              )}
            </div>
            {children}
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white">
        <div className="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
          <p className="text-center text-neutral-dark text-sm">
            &copy; {new Date().getFullYear()} {t('storefront.poweredBy')}.
          </p>
        </div>
      </footer>
    </div>
  );
}

export default AdminLayout;