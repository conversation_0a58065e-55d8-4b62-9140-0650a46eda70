-- Migration: Update order status from processing/sent to confirmed
-- Date: 2025-01-29
-- Description: Updates existing order statuses to match new workflow: pending -> confirmed -> delivered -> cancelled

-- Update orders table
UPDATE orders 
SET status = 'confirmed' 
WHERE status IN ('processing', 'sent');

-- Update order_revisions table
UPDATE order_revisions 
SET status = 'confirmed' 
WHERE status IN ('processing', 'sent');

-- Add comment to document the status values
COMMENT ON COLUMN orders.status IS 'Status do pedido: pending, confirmed, delivered, cancelled';
COMMENT ON COLUMN order_revisions.status IS 'Status do pedido: pending, confirmed, delivered, cancelled';