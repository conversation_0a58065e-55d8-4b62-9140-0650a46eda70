import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useLocation } from "wouter";
import { useQuery } from "@tanstack/react-query";
import { ChevronLeft, ChevronRight, ShoppingCart, ArrowLeft, Plus, Minus, Info } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/hooks/use-toast";
import { useCart } from "@/context/CartContext";
import { useAdminOrder } from "@/context/AdminOrderContext";
import { formatCurrency, formatWithCurrencySymbol } from "@/lib/utils";
import { useTranslation } from "react-i18next";
import { Badge } from "@/components/ui/badge";
import { Toolt<PERSON>, TooltipContent, Too<PERSON><PERSON><PERSON>rovider, TooltipTrigger } from "@/components/ui/tooltip";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useActiveStore } from "@/hooks/useActiveStore";

// Função para ajustar o brilho de uma cor hexadecimal
function adjustColorBrightness(hexColor: string, percent: number): string {
  // Verifica se o hexColor está no formato correto
  if (!hexColor || hexColor.indexOf('#') !== 0) {
    return hexColor;
  }

  // Remove o # e converte para o formato longo se for curto
  let hex = hexColor.slice(1);
  if (hex.length === 3) {
    hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
  }

  // Converte hex para RGB
  const r = parseInt(hex.slice(0, 2), 16);
  const g = parseInt(hex.slice(2, 4), 16);
  const b = parseInt(hex.slice(4, 6), 16);

  // Ajusta o brilho
  const adjustBrightness = (color: number) => {
    return Math.max(0, Math.min(255, Math.round(color * (1 + percent / 100))));
  };

  const newR = adjustBrightness(r);
  const newG = adjustBrightness(g);
  const newB = adjustBrightness(b);

  // Converte RGB de volta para hex
  const toHex = (c: number) => {
    const hex = c.toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  };

  return `#${toHex(newR)}${toHex(newG)}${toHex(newB)}`;
}

type SelectedOption = {
  options: string[];
  required: boolean;
  multipleChoice: boolean;
  quantities: { [optionId: string]: number };
};

type SelectedOptions = {
  [variationId: string]: SelectedOption;
};

export default function ProductDetailPage() {
  const { id } = useParams();
  const [, navigate] = useLocation();
  const productId = parseInt(id);
  const { t } = useTranslation();
  const { toast } = useToast();
  const { addItem: addToCart } = useCart();
  const { addItem: addToAdminOrder } = useAdminOrder();
  const { store } = useActiveStore();

  // Verificar se estamos adicionando um produto a um pedido administrativo
  const [isAdminOrderCreation, setIsAdminOrderCreation] = useState(false);

  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [selectedOptions, setSelectedOptions] = useState<SelectedOptions>({});
  const [productPrice, setProductPrice] = useState(0);
  const [canAddToCart, setCanAddToCart] = useState(false);
  const [observation, setObservation] = useState('');
  const [quantity, setQuantity] = useState(1);

  // Get the store's currency symbol and colors
  const currencySymbol = store?.currency || 'R$';

  // Apply store colors
  const storeColors = store?.colors || { primary: "#FF6B6B", secondary: "#4ECDC4", accent: "#FFD166" };

  // CSS variables for applying direct color values
  const cssColorVariables = {
    "--primary-color": storeColors.primary,
    "--secondary-color": storeColors.secondary,
    "--accent-color": storeColors.accent,
    "--background-color": "#FFFFFF",
    "--foreground-color": "#111"
  } as React.CSSProperties;

  // Store theme class
  const storeThemeClass = `store-theme`;

  // Fetching product data from the public API
  const { data: product, error, isLoading } = useQuery({
    queryKey: [`/api/public/products/${productId}`],
    enabled: !isNaN(productId)
  });

  // Função utilitária para navegação de volta para a página da loja
  const navigateToStorePage = () => {
    // Se temos o objeto da loja no contexto, use-o
    if (store && store.slug) {
      navigate(`/${store.slug}`);
      return;
    }

    // Verificar formatos de URL possíveis
    const pathParts = window.location.pathname.split('/');

    // Formato novo: /padaria/product/1
    // [0]='', [1]='padaria', [2]='product', [3]='1'
    if (pathParts.length >= 2 && pathParts[1] !== 'store' && pathParts[1] !== '') {
      navigate(`/${pathParts[1]}`);
      return;
    }

    // Formato antigo: /store/padaria/product/1
    // [0]='', [1]='store', [2]='padaria', [3]='product', [4]='1'
    if (pathParts.length >= 3 && pathParts[1] === 'store') {
      navigate(`/${pathParts[2]}`);
      return;
    }

    // Fallback para a página inicial
    navigate('/');
  };

  // Verificar se estamos adicionando um produto a um pedido administrativo
  useEffect(() => {
    const adminOrderCreation = localStorage.getItem('adminOrderCreationProduct');
    if (adminOrderCreation === 'true') {
      setIsAdminOrderCreation(true);
    }
  }, []);

  // Initialize selected options when product changes
  useEffect(() => {
    if (product) {
      setProductPrice(product.price);

      // Initialize selected options
      const initialOptions: SelectedOptions = {};
      if (product.variations) {
        product.variations.forEach(variation => {
          // Identificador seguro para a variação
          const variationId = variation.id || variation.name || '';

          const quantities: { [optionId: string]: number } = {};

          // Initialize quantities for all options to 0
          const optionsList = variation.opcoes || variation.options;
          if (optionsList) {
            optionsList.forEach(option => {
              quantities[option.id || option.name] = 0;
            });
          }

          initialOptions[variationId] = {
            options: [],
            required: variation.required || false,
            multipleChoice: variation.multipleChoice || false,
            quantities
          };
        });
      }
      setSelectedOptions(initialOptions);

      // Reset the image index
      setCurrentImageIndex(0);
    }
  }, [product]);

  // Check if we can add to cart (all required variations have sufficient quantities)
  useEffect(() => {
    if (!product) {
      setCanAddToCart(false);
      return;
    }

    // If no variations, can add to cart
    if (!product.variations || product.variations.length === 0) {
      setCanAddToCart(true);
      return;
    }

    // Check if all required variations have sufficient quantities selected
    const allRequirementsMet = product.variations.every(variation => {
      // Get safe variation ID
      const variationId = variation.id || variation.name || '';

      // Get the current selected options for this variation
      const currentSelection = selectedOptions[variationId];

      // If not required, it's always valid
      if (!variation.required) {
        return true;
      }

      // If required but no selection object exists yet, not valid
      if (!currentSelection) {
        return false;
      }

      // Calculate total quantity selected across all options for this variation
      const totalQuantity = Object.values(currentSelection.quantities).reduce((sum, qty) => sum + qty, 0);

      // Check if minimum requirement met
      const minRequired = variation.minSelections || 1; // If required, at least 1 selection is needed by default

      return totalQuantity >= minRequired;
    });

    setCanAddToCart(allRequirementsMet);
  }, [product, selectedOptions]);

  // Calculate total price based on selected options and quantities
  useEffect(() => {
    if (!product) return;

    let totalPrice = product.price;

    // Add prices of selected options based on quantities
    if (product.variations) {
      product.variations.forEach(variation => {
        // Safe identifier for variation
        const variationId = variation.id || variation.name || '';

        if (selectedOptions[variationId]) {
          const selectedIds = selectedOptions[variationId].options;
          const quantities = selectedOptions[variationId].quantities;

          // Usar opcoes ou options, dependendo de qual estiver disponível
          const optionsList = variation.opcoes || variation.options;
          if (optionsList) {
            optionsList.forEach(option => {
              const optionIdentifier = option.id || option.name;
              if (selectedIds.includes(optionIdentifier)) {
                const quantity = quantities[optionIdentifier] || 0;
                // Usar price ou precoAdicional, dependendo de qual estiver disponível
                const additionalPrice = option.precoAdicional || option.price || 0;
                totalPrice += additionalPrice * quantity;
              }
            });
          }
        }
      });
    }

    setProductPrice(totalPrice);
  }, [product, selectedOptions]);

  // Function to navigate to next image
  const nextImage = () => {
    if (!product?.images) return;
    setCurrentImageIndex((prev) => (prev + 1) % product.images!.length);
  };

  // Function to navigate to previous image
  const prevImage = () => {
    if (!product?.images) return;
    setCurrentImageIndex((prev) => (prev - 1 + product.images!.length) % product.images!.length);
  };

  // Function to handle option selection
  const handleOptionSelect = (variationId: string, optionId: string) => {
    setSelectedOptions(prev => {
      const variation = { ...prev[variationId] };

      // If multiple choice, add or remove from list
      if (variation.multipleChoice) {
        if (variation.options.includes(optionId)) {
          variation.options = variation.options.filter(id => id !== optionId);

          // Reset quantity to 0 when deselected
          variation.quantities = { ...variation.quantities, [optionId]: 0 };
        } else {
          variation.options = [...variation.options, optionId];

          // Set initial quantity to 1 when selected
          variation.quantities = { ...variation.quantities, [optionId]: 1 };
        }
      } else {
        // If not multiple choice, replace current selection
        variation.options = [optionId];

        // Reset all quantities to 0
        const resetQuantities = { ...variation.quantities };
        Object.keys(resetQuantities).forEach(key => {
          resetQuantities[key] = 0;
        });

        // Set selected option quantity to 1
        variation.quantities = { ...resetQuantities, [optionId]: 1 };
      }

      return { ...prev, [variationId]: variation };
    });
  };

  // Function to increase quantity for a variation option
  const increaseQuantity = (variationId: string, optionId: string) => {
    setSelectedOptions(prev => {
      const variation = { ...prev[variationId] };
      const currentProduct = product!;

      // Find the variation object to check max selection limit
      const variationObj = currentProduct.variations?.find(v => {
        const vId = v.id || v.name || '';
        return vId.toString() === variationId;
      });

      if (!variationObj) return prev;

      const maxSelections = variationObj.maxSelections || 1;

      // Calculate total current selected count for this variation
      const currentTotal = Object.values(variation.quantities).reduce((sum, qty) => sum + qty, 0);

      // Only increment if we haven't reached the max selection limit yet
      if (currentTotal < maxSelections) {
        const newQuantities = { ...variation.quantities };
        newQuantities[optionId] = (newQuantities[optionId] || 0) + 1;

        // Add to options list if not already there
        if (!variation.options.includes(optionId)) {
          variation.options = [...variation.options, optionId];
        }

        variation.quantities = newQuantities;
      } else {
        // Show toast notification when max limit reached
        toast({
          title: t('common.error'),
          description: t('storefront.maxSelectionsReached', { max: maxSelections }),
          variant: "destructive",
        });
      }

      return { ...prev, [variationId]: variation };
    });
  };

  // Function to decrease quantity for a variation option
  const decreaseQuantity = (variationId: string, optionId: string) => {
    setSelectedOptions(prev => {
      const variation = { ...prev[variationId] };
      const currentProduct = product!;

      // Find the variation object to check min selection limit
      const variationObj = currentProduct.variations?.find(v => {
        const vId = v.id || v.name || '';
        return vId.toString() === variationId;
      });

      if (!variationObj) return prev;

      const minSelections = variationObj.minSelections || 0;
      const currentQuantity = variation.quantities[optionId] || 0;

      if (currentQuantity > 0) {
        const newQuantities = { ...variation.quantities };
        newQuantities[optionId] = currentQuantity - 1;

        // If quantity reached 0, remove from options list
        if (newQuantities[optionId] === 0 && variation.options.includes(optionId)) {
          variation.options = variation.options.filter(id => id !== optionId);
        }

        // Check if we have enough selections to meet minimum requirements
        const newTotal = Object.values(newQuantities).reduce((sum, qty) => sum + qty, 0);

        if (variationObj.required && newTotal < minSelections) {
          // Show toast notification when below minimum limit
          toast({
            title: t('common.error'),
            description: t('storefront.minSelectionsRequired', { min: minSelections }),
            variant: "destructive",
          });
          return prev; // Don't update if it would violate minimum requirements
        }

        variation.quantities = newQuantities;
      }

      return { ...prev, [variationId]: variation };
    });
  };

  // State to track if item was added to cart
  const [itemAddedToCart, setItemAddedToCart] = useState(false);

  // Effect to show toast and navigate when item is added to cart
  useEffect(() => {
    if (itemAddedToCart) {
      // Show success message
      toast({
        title: isAdminOrderCreation
          ? t('storefront.addedToCart')
          : t('storefront.addedToCart'),
        description: isAdminOrderCreation
          ? t('storefront.productAddedToCart')
          : t('storefront.productAddedToCart'),
        duration: 3000,
      });

      // Reset state
      setItemAddedToCart(false);

      // Limpar flag de criação de pedido administrativo
      if (isAdminOrderCreation) {
        localStorage.removeItem('adminOrderCreationProduct');
        localStorage.removeItem('currentProductId');

        console.log('Produto com variações adicionado ao pedido administrativo, redirecionando para /admin/orders/new');

        // Navegar de volta para a página de novo pedido
        navigate('/admin/orders/new');
      } else {
        try {
          // Usar função utilitária para navegação
          navigateToStorePage();
        } catch (error) {
          // Fallback mais seguro em caso de erro
          console.error("Erro ao navegar:", error);
          navigate('/');
        }
      }
    }
  }, [itemAddedToCart, navigate, toast, t, product, store, isAdminOrderCreation]);

  // Function to add to cart
  const handleAddToCart = () => {
    if (!product || !canAddToCart) return;

    // Prepare selected options to send to cart
    const selectedVariations: any[] = [];

    if (product.variations) {
      product.variations.forEach(variation => {
        // Safe identifier for the variation
        const variationId = variation.id || variation.name || '';

        if (selectedOptions[variationId]?.options.length > 0) {
          // For each selected option of this variation
          selectedOptions[variationId].options.forEach(optionId => {
            // Use opcoes or options, depending on which is available
            const optionsList = variation.opcoes || variation.options;
            if (!optionsList) return;

            // Find the option by ID or name
            const option = optionsList.find(opt => (opt.id || opt.name) === optionId);
            if (option) {
              // Get the quantity for this option
              const quantity = selectedOptions[variationId].quantities[optionId] || 0;

              // Only add options with quantity > 0
              if (quantity > 0) {
                selectedVariations.push({
                  variationId: variation.id,
                  variationName: variation.nomeGrupo || variation.name,
                  optionId: option.id || option.name,
                  optionName: option.name,
                  price: parseFloat(option.precoAdicional || option.price || 0),
                  quantity: quantity
                });
              }
            }
          });
        }
      });
    }

    // Preparar o item
    const item = {
      productId: product.id,
      name: product.name,
      price: product.price, // Usar apenas o preço base do produto
      image: product.images && product.images.length > 0 ? product.images[0] : undefined,
      selectedVariations,
      observation: observation.trim() || undefined
    };

    // Adicionar ao carrinho ou ao pedido administrativo
    if (isAdminOrderCreation) {
      addToAdminOrder({
        ...item,
        quantity: quantity
      });
    } else {
      addToCart(item, quantity);
    }

    // Set flag to trigger navigation and toast in useEffect
    setItemAddedToCart(true);
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex flex-col min-h-screen p-4">
        <div className="flex items-center mb-4">
          <Button
            variant="ghost"
            onClick={navigateToStorePage}
            className="mr-2"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t('common.back')}
          </Button>
        </div>
        <div className="flex-1 flex justify-center items-center">
          <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full" aria-label="Loading"/>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !product) {
    return (
      <div className="flex flex-col min-h-screen p-4">
        <div className="flex items-center mb-4">
          <Button
            variant="ghost"
            onClick={navigateToStorePage}
            className="mr-2"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t('common.back')}
          </Button>
        </div>
        <div className="flex-1 flex flex-col justify-center items-center">
          <h2 className="text-xl font-bold mb-2">{t('common.error')}</h2>
          <p className="text-muted-foreground mb-4">{t('storefront.productNotFound')}</p>
          <Button onClick={navigateToStorePage}>
            {t('common.goBack')}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex flex-col min-h-screen p-4 ${storeThemeClass}`} style={cssColorVariables}>
      {/* Back button */}
      <div className="flex items-center mb-4">
        <Button
          variant="ghost"
          onClick={navigateToStorePage}
          className="mr-2 text-primary hover:text-primary/90 hover:bg-primary/10"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          {t('common.back')}
        </Button>
      </div>

      {/* Product details */}
      <div className="max-w-3xl mx-auto w-full">
        <h1 className="text-2xl font-bold mb-2">{product.name}</h1>
        {product.description && (
          <p className="text-muted-foreground mb-4">{product.description}</p>
        )}

        {/* Image carousel */}
        {product.images && product.images.length > 0 && (
          <div className="relative w-full h-64 bg-gray-100 rounded overflow-hidden mb-6">
            <img
              src={product.images[currentImageIndex]}
              alt={product.name}
              className="w-full h-full object-cover"
            />

            {product.images.length > 1 && (
              <>
                <Button
                  variant="secondary"
                  size="icon"
                  className="absolute left-2 top-1/2 transform -translate-y-1/2 rounded-full opacity-70 hover:opacity-100"
                  onClick={prevImage}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <Button
                  variant="secondary"
                  size="icon"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 rounded-full opacity-70 hover:opacity-100"
                  onClick={nextImage}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>

                {/* Image indicators */}
                <div className="absolute bottom-2 left-0 right-0 flex justify-center space-x-1">
                  {product.images.map((_, index) => (
                    <div
                      key={index}
                      className={`w-2 h-2 rounded-full cursor-pointer ${index === currentImageIndex ? 'bg-primary' : 'bg-gray-300'}`}
                      onClick={() => setCurrentImageIndex(index)}
                      style={index === currentImageIndex ? {backgroundColor: storeColors.primary} : {}}
                    />
                  ))}
                </div>
              </>
            )}
          </div>
        )}

        {/* Quantity controls */}
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <Label htmlFor="product-quantity" className="font-medium">
              {t('storefront.quantity')}
            </Label>
            <div className="flex items-center">
              <Button
                type="button"
                size="icon"
                variant="outline"
                className="h-8 w-8"
                onClick={() => setQuantity(prev => Math.max(1, prev - 1))}
                disabled={quantity <= 1}
              >
                <Minus className="h-3 w-3" />
              </Button>
              <span className="w-10 text-center font-medium">{quantity}</span>
              <Button
                type="button"
                size="icon"
                variant="outline"
                className="h-8 w-8"
                onClick={() => setQuantity(prev => prev + 1)}
              >
                <Plus className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>

        {/* Product variations */}
        {product.variations && product.variations.length > 0 && (
          <div className="space-y-6 mb-6">
            {product.variations.map((variation) => (
              <div key={variation.id || variation.name} className="border rounded-md p-4">
                <div className="flex justify-between mb-3">
                  <Label className="text-base font-medium">
                    {variation.nomeGrupo || variation.name}
                    {variation.obrigatorio && <span className="text-red-500 ml-1">*</span>}
                  </Label>
                  {variation.maxSelecionados > 1 && (
                    <span className="text-xs text-muted-foreground">
                      {t('storefront.selectMultiple')}
                    </span>
                  )}
                </div>

                {/* Variation requirements info banner */}
                <div className="mb-4">
                  <div className="bg-muted/40 p-3 rounded-md flex items-center text-sm">
                    <Info className="h-4 w-4 mr-2 flex-shrink-0" />
                    <div>
                      {variation.required ?
                        (t('storefront.requiredVariation')) :
                        (t('storefront.optionalVariation'))}

                      {' • '}

                      {variation.multipleChoice ?
                        (t('storefront.selectUpTo', { count: variation.maxSelections })) :
                        (t('storefront.selectOne'))}

                      {variation.minSelections > 0 && variation.multipleChoice &&
                        (` • ${t('storefront.selectAtLeast', { count: variation.minSelections })}`)}
                    </div>
                  </div>
                </div>

                {/* Variation options as list with quantity buttons */}
                <div className="space-y-2">
                  {(variation.opcoes || variation.options) && (variation.opcoes || variation.options).map((option) => {
                    // Safe identifier for variation
                    const variationId = variation.id || variation.name || '';
                    // Safe identifier for option
                    const optionId = option.id || option.name;

                    const isSelected = selectedOptions[variationId]?.options.includes(optionId);
                    const quantity = selectedOptions[variationId]?.quantities[optionId] || 0;

                    return (
                      <div
                        key={option.id || option.name}
                        className={`border ${isSelected ? 'border-primary' : 'border-border'} rounded-md p-3 flex justify-between items-center`}
                      >
                        <div className="flex-1">
                          <div className="font-medium">
                            {option.name}
                            {(option.precoAdicional > 0 || option.price > 0) && (
                              <span className="text-sm font-normal text-muted-foreground ml-2">
                                + {formatWithCurrencySymbol(option.precoAdicional || option.price || 0, currencySymbol)}
                              </span>
                            )}
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <Button
                            type="button"
                            size="icon"
                            variant="outline"
                            className="h-8 w-8"
                            onClick={() => decreaseQuantity(
                              (variation.id || variation.name || '').toString(),
                              option.id || option.name
                            )}
                            disabled={quantity === 0}
                          >
                            <Minus className="h-3 w-3" />
                          </Button>

                          <span className="w-6 text-center">{quantity}</span>

                          <Button
                            type="button"
                            size="icon"
                            variant="outline"
                            className="h-8 w-8"
                            onClick={() => increaseQuantity(
                              (variation.id || variation.name || '').toString(),
                              option.id || option.name
                            )}
                          >
                            <Plus className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Price summary - shows base price and additions */}
        <div className="bg-gray-50 p-4 rounded-lg mb-4">
          <h3 className="font-medium text-base mb-2">{t('storefront.priceSummary')}</h3>
          <div className="space-y-1 text-sm">
            {/* Base price */}
            <div className="flex justify-between">
              <span>{t('storefront.basePrice')} x {quantity}</span>
              <span>{formatWithCurrencySymbol(product.price * quantity, currencySymbol)}</span>
            </div>

            {/* Selected options */}
            {product.variations && product.variations.map(variation => {
              // Safe identifier for the variation
              const variationId = variation.id || variation.name || '';

              // Only include variations with selected options
              const selectedIds = selectedOptions[variationId]?.options || [];
              if (!selectedIds.length) return null;

              const optionsList = variation.opcoes || variation.options;
              if (!optionsList) return null;

              return optionsList.map(option => {
                const optionId = option.id || option.name;
                if (!selectedIds.includes(optionId)) return null;
                const optionQuantity = selectedOptions[variationId]?.quantities[optionId] || 0;
                // Usar price ou precoAdicional, dependendo de qual estiver disponível
                const additionalPrice = option.precoAdicional || option.price || 0;
                if (optionQuantity <= 0 || additionalPrice <= 0) return null;

                // Multiplicar pelo número de produtos
                const totalOptionPrice = additionalPrice * optionQuantity * quantity;

                return (
                  <div key={option.id || option.name} className="flex justify-between text-muted-foreground">
                    <span>
                      {option.name} x {optionQuantity} {quantity > 1 ? `(x${quantity})` : ''}
                    </span>
                    <span>
                      + {formatWithCurrencySymbol(totalOptionPrice, currencySymbol)}
                    </span>
                  </div>
                );
              });
            })}

            {/* Total line with divider */}
            <div className="border-t border-gray-200 pt-1 mt-2 font-medium flex justify-between">
              <span>{t('storefront.totalPrice')}</span>
              <span>{formatWithCurrencySymbol(productPrice * quantity, currencySymbol)}</span>
            </div>
          </div>
        </div>

        {/* Observations field */}
        <div className="mb-4">
          <Label htmlFor="observation" className="mb-2 block">
            {t('storefront.observation')}
          </Label>
          <textarea
            id="observation"
            className="w-full border rounded-md p-3 min-h-[80px] text-sm"
            placeholder={t('storefront.observationPlaceholder')}
            value={observation}
            onChange={(e) => setObservation(e.target.value)}
            maxLength={500}
          />
          <div className="flex justify-end mt-1">
            <span className="text-xs text-gray-500">
              {observation.length}/500
            </span>
          </div>
        </div>

        {/* Price and add to cart */}
        <div className="sticky bottom-0 bg-white p-4 border-t mt-auto">
          <div className="flex justify-between items-center">
            <div>
              <span className="text-xl font-bold">{formatWithCurrencySymbol(productPrice * quantity, currencySymbol)}</span>
              {!canAddToCart && product.variations && (
                <p className="text-xs text-red-500 mt-1">
                  {t('storefront.selectRequiredOptions')}
                </p>
              )}
              {!product.inStock && (
                <p className="text-xs text-red-500 mt-1">
                  {t('storefront.outOfStock')}
                </p>
              )}
            </div>
            <Button
              onClick={handleAddToCart}
              disabled={!canAddToCart || !product.inStock}
              size="lg"
              className="gap-2 text-white rounded-full"
              style={{
                backgroundColor: storeColors.primary,
                transform: 'scale(1)',
                transition: 'all 0.2s ease-in-out'
              }}
              onMouseOver={(e) => {
                e.currentTarget.style.transform = 'scale(1.05)';
                e.currentTarget.style.boxShadow = '0 3px 8px rgba(0, 0, 0, 0.12)';
                // Escurecer a cor primária no hover
                const primaryColor = storeColors.primary;
                e.currentTarget.style.backgroundColor = adjustColorBrightness(primaryColor, -10);
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'scale(1)';
                e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.08)';
                e.currentTarget.style.backgroundColor = storeColors.primary;
              }}
            >
              <ShoppingCart className="h-4 w-4" />
              {isAdminOrderCreation
                ? t('storefront.addToOrder')
                : t('storefront.addToCart')}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}