import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import { AlertCircle, ArrowLeft, Home } from "lucide-react";
import { Link } from "wouter";
import { useTranslation } from "@/hooks/useTranslation";

export default function NotFound() {
  const { t } = useTranslation();
  
  return (
    <div className="min-h-screen w-full flex flex-col items-center justify-center bg-gradient-to-b from-pink-50 to-white">
      <Card className="w-full max-w-md mx-4 shadow-lg border-0">
        <CardContent className="pt-6">
          <div className="flex flex-col items-center text-center mb-6">
            <div className="rounded-full bg-pink-100 p-3 mb-4">
              <AlertCircle className="h-8 w-8 text-pink-500" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900">404</h1>
            <p className="text-xl font-medium text-gray-700 mt-2">
              {t('common.pageNotFound')}
            </p>
            <p className="mt-4 text-gray-600 max-w-sm">
              {t('common.pageNotFoundDescription')}
            </p>
          </div>
        </CardContent>
        <CardFooter className="flex flex-col sm:flex-row gap-3 justify-center">
          <Button 
            variant="outline" 
            onClick={() => window.history.back()}
            className="w-full sm:w-auto"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('common.back')}
          </Button>
          <Button 
            asChild
            className="w-full sm:w-auto"
          >
            <Link href="/">
              <Home className="mr-2 h-4 w-4" />
              {t('common.home')}
            </Link>
          </Button>
        </CardFooter>
      </Card>
      <div className="mt-8 text-sm text-gray-500">
        <Link href="/">
          <span className="hover:underline cursor-pointer">Doce Menu</span>
        </Link>
      </div>
    </div>
  );
}
