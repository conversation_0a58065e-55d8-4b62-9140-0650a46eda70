@tailwind base;
@tailwind components;
@tailwind utilities;

/* Personalizações para tabelas na página de pedidos */
@media (min-width: 768px) {
  .md\:block table {
    width: 100%;
    max-width: 100%;
  }
  
  .md\:block table tfoot tr td {
    min-width: 100px;
  }
  
  .md\:block table tfoot tr td:last-child {
    width: 160px;
  }
}

/* Estilos customizados para o tema do carrinho */
.ios-style-calendar .rdp-button.rdp-day_selected,
.ios-style-calendar .rdp-button.rdp-day_selected:focus, 
.ios-style-calendar .rdp-button.rdp-day_selected:hover {
  background-color: var(--store-primary, #6082e6) !important;
  color: white !important;
}

.ios-style-calendar .rdp-button:focus-visible {
  background-color: var(--store-primary-light, rgba(96, 130, 230, 0.2)) !important;
}

.ios-style-calendar .rdp-button:hover:not([disabled]):not(.rdp-day_selected) {
  background-color: var(--store-primary-light, rgba(96, 130, 230, 0.1)) !important;
}

/* Estilo dos rádios e elementos de seleção */
[data-radix-popper-content-wrapper] [data-highlighted] {
  background-color: var(--store-primary-light, rgba(96, 130, 230, 0.1)) !important;
}

/* Radio buttons com a cor da loja */
[data-state="checked"].radio-store-color {
  border-color: var(--store-primary, #6082e6) !important;
  color: var(--store-primary, #6082e6) !important;
}

input[type="radio"]:checked {
  background-color: var(--store-primary, #6082e6) !important;
  border-color: var(--store-primary, #6082e6) !important;
}

:root {
  --background: 0 0% 100%;
  --foreground: 20 14.3% 4.1%;
  --muted: 60 4.8% 95.9%;
  --muted-foreground: 25 5.3% 44.7%;
  --popover: 0 0% 100%;
  --popover-foreground: 20 14.3% 4.1%;
  --card: 0 0% 100%;
  --card-foreground: 20 14.3% 4.1%;
  --border: 20 5.9% 90%;
  --input: 20 5.9% 90%;
  --primary: 354 100% 70%; /* FF6B6B */
  --primary-foreground: 211 100% 99%;
  --secondary: 175 64% 55%; /* 4ECDC4 */
  --secondary-foreground: 24 9.8% 10%;
  --accent: 42 100% 70%; /* FFD166 */
  --accent-foreground: 24 9.8% 10%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 60 9.1% 97.8%;
  --success: 142 69% 49%; /* 2ECC71 */
  --warning: 37 95% 51%; /* F39C12 */
  --error: 6 78% 57%; /* E74C3C */
  --ring: 354 100% 70%; /* FF6B6B */
  --radius: 0.5rem;
}

.dark {
  --background: 240 10% 3.9%;
  --foreground: 0 0% 98%;
  --muted: 240 3.7% 15.9%;
  --muted-foreground: 240 5% 64.9%;
  --popover: 240 10% 3.9%;
  --popover-foreground: 0 0% 98%;
  --card: 240 10% 3.9%;
  --card-foreground: 0 0% 98%;
  --border: 240 3.7% 15.9%;
  --input: 240 3.7% 15.9%;
  --primary: 354 100% 70%; /* FF6B6B */
  --primary-foreground: 0 0% 98%;
  --secondary: 175 64% 55%; /* 4ECDC4 */
  --secondary-foreground: 0 0% 98%;
  --accent: 42 100% 70%; /* FFD166 */
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --success: 142 69% 49%; /* 2ECC71 */
  --warning: 37 95% 51%; /* F39C12 */
  --error: 6 78% 57%; /* E74C3C */
  --ring: 354 100% 64%;
  --radius: 0.5rem;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-family: 'Nunito', sans-serif;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Poppins', sans-serif;

/* Melhorias para visualização de radio buttons em dispositivos móveis */
@media (max-width: 768px) {
  .radix-radio-group-item {
    appearance: none;
    -webkit-appearance: none;
    display: flex;
    width: 1.25rem;
    height: 1.25rem;
    border-radius: 50%;
    border: 2px solid var(--store-primary, #6082e6);
    position: relative;
    margin-right: 0.75rem;
  }

  .radix-radio-group-item[data-state="checked"] {
    background-color: var(--store-primary, #6082e6);
  }

  .radix-radio-group-item[data-state="checked"]::after {
    content: '';
    display: block;
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    background-color: white;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

  }
}

/* Define accent color CSS variables */
.store-theme {
  --background: 0 0% 100%;
  --foreground: 20 14.3% 4.1%;
  
  /* Custom theme variables */
  /* Converte cores hexadecimais em variáveis hsl */
  --primary: var(--primary-color, #FF6B6B);
  --secondary: var(--secondary-color, #4ECDC4);
  --accent: var(--accent-color, #FFD166);
  
  /* Adiciona regras para elementos com cores personalizadas */
  .bg-primary { background-color: var(--primary-color, #FF6B6B); }
  .bg-secondary { background-color: var(--secondary-color, #4ECDC4); }
  .bg-accent { background-color: var(--accent-color, #FFD166); }
  
  .text-primary { color: var(--primary-color, #FF6B6B); }
  .text-secondary { color: var(--secondary-color, #4ECDC4); }
  .text-accent { color: var(--accent-color, #FFD166); }
  
  .border-primary { border-color: var(--primary-color, #FF6B6B); }
  .border-secondary { border-color: var(--secondary-color, #4ECDC4); }
  .border-accent { border-color: var(--accent-color, #FFD166); }
  
  .hover\:bg-primary:hover { background-color: var(--primary-color, #FF6B6B); }
  .hover\:bg-secondary:hover { background-color: var(--secondary-color, #4ECDC4); }
  .hover\:bg-accent:hover { background-color: var(--accent-color, #FFD166); }
  
  .hover\:text-primary:hover { color: var(--primary-color, #FF6B6B); }
  .hover\:text-secondary:hover { color: var(--secondary-color, #4ECDC4); }
  .hover\:text-accent:hover { color: var(--accent-color, #FFD166); }
}

.store-theme.accent-red {
  --primary: 354 100% 70%; /* FF6B6B */
  --ring: 354 100% 70%;
}

.store-theme.accent-blue {
  --primary: 210 100% 56%;
  --ring: 210 100% 56%;
}

.store-theme.accent-green {
  --primary: 142 69% 49%;
  --ring: 142 69% 49%;
}

.store-theme.accent-purple {
  --primary: 280 100% 70%;
  --ring: 280 100% 70%;
}

.store-theme.accent-orange {
  --primary: 27 96% 61%;
  --ring: 27 96% 61%;
}

/* Gradient text */
.gradient-text {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary;
}

/* Transition for all buttons */
button, a {
  @apply transition-all duration-200;
}

/* Primary button with hover state fix */
.primary-button {
  background-color: hsl(var(--primary));
  color: white;
}

.primary-button:hover {
  background-color: hsl(var(--primary) / 0.9);
  color: white;
}
