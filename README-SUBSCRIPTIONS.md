# Sistema de Planos de Assinatura - Doce Menu

Este documento descreve como configurar e usar o sistema de planos de assinatura do Doce Menu.

## 🚀 Configuração Inicial

### 1. Configurar Variáveis de Ambiente

Adicione as seguintes variáveis ao seu arquivo `.env`:

```env
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
STRIPE_PREMIUM_MONTHLY_PRICE_ID=price_your_premium_monthly_price_id
STRIPE_PREMIUM_YEARLY_PRICE_ID=price_your_premium_yearly_price_id

# URLs de redirecionamento
STRIPE_SUCCESS_URL=http://localhost:5000/admin/settings?subscription=success
STRIPE_CANCEL_URL=http://localhost:5000/admin/settings?subscription=canceled
```

### 2. Executar Migração do Banco de Dados

```bash
npm run setup:subscriptions
```

Este comando irá:
- Criar a tabela `subscriptions`
- Configurar índices e triggers
- Criar assinaturas gratuitas para lojas existentes
- Verificar a configuração do Stripe

### 3. Configurar Produtos no Stripe

1. Acesse o [Stripe Dashboard](https://dashboard.stripe.com)
2. Vá para **Produtos** → **Criar produto**
3. Configure o produto "Doce Menu Premium":
   - Nome: "Doce Menu Premium"

4. **Adicionar Preço Mensal**:
   - Preço: R$ 29,90 (recorrente mensal)
   - Trial: 7 dias
   - Nickname: "Premium Monthly"

5. **Adicionar Preço Anual**:
   - Preço: R$ 299,00 (recorrente anual)
   - Trial: 0 dias
   - Nickname: "Premium Yearly"

6. Copie ambos os `Price IDs` e adicione às variáveis:
   - `STRIPE_PREMIUM_MONTHLY_PRICE_ID`
   - `STRIPE_PREMIUM_YEARLY_PRICE_ID`

### 4. Configurar Webhooks

1. No Stripe Dashboard, vá para **Desenvolvedores** → **Webhooks**
2. Clique em **Adicionar endpoint**
3. URL: `https://seu-dominio.com/api/subscriptions/webhook`
4. Eventos para escutar:
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`
5. Copie o **Signing secret** e adicione à variável `STRIPE_WEBHOOK_SECRET`

## 📋 Planos Disponíveis

### Plano Gratuito (Free)
- **Preço**: Gratuito
- **Produtos**: Até 10 produtos
- **Pedidos**: Máximo 5 por mês
- **Limitações**:
  - ❌ Sem geração de PDFs
  - ❌ Sem relatórios/analytics
  - ❌ Sem integração WhatsApp
  - ❌ Sem sistema de cupons
  - ❌ Sem personalização avançada

### Plano Premium
#### Mensal (R$ 29,90/mês)
- **Preço**: R$ 29,90 por mês
- **Trial**: 7 dias grátis
- **Produtos**: Até 50 produtos
- **Pedidos**: Ilimitados

#### Anual (R$ 299,00/ano)
- **Preço**: R$ 299,00 por ano (R$ 24,92/mês)
- **Economia**: R$ 59,80 por ano (16.6% de desconto)
- **Trial**: Sem período de teste
- **Produtos**: Até 50 produtos
- **Pedidos**: Ilimitados

**Funcionalidades Premium (ambos os planos)**:
- ✅ Geração de PDFs
- ✅ Relatórios e analytics completos
- ✅ Integração WhatsApp
- ✅ Sistema de cupons de desconto
- ✅ Personalização visual avançada

## 🛠️ Comandos Úteis

### Verificar Status do Sistema
```bash
npm run subscriptions:status
```

### Reconfigurar Sistema
```bash
npm run setup:subscriptions
```

### Logs do Servidor
```bash
# Verificar logs de webhook
tail -f logs/stripe-webhooks.log

# Verificar logs de assinatura
tail -f logs/subscriptions.log
```

## 🔧 Desenvolvimento

### Testando Localmente

1. **Usar ngrok para webhooks**:
```bash
ngrok http 5000
```

2. **Configurar webhook URL no Stripe**:
```
https://your-ngrok-url.ngrok.io/api/subscriptions/webhook
```

3. **Cartões de teste**:
- Sucesso: `4242 4242 4242 4242`
- Falha: `4000 0000 0000 0002`
- 3D Secure: `4000 0025 0000 3155`

### Verificações de Limite no Frontend

```typescript
import { useProductLimitGuard } from '@/hooks/useSubscriptionGuard';

function AddProductButton() {
  const { canAdd, withCheck } = useProductLimitGuard();
  
  return (
    <Button onClick={withCheck(() => navigate('/products/new'))}>
      Adicionar Produto
    </Button>
  );
}
```

### Verificações de Funcionalidade

```typescript
import { usePremiumFeatureGuard } from '@/hooks/useSubscriptionGuard';

function PDFButton() {
  const { canUse, withCheck } = usePremiumFeatureGuard('allowPdfGeneration');
  
  return (
    <Button onClick={withCheck(() => generatePDF())}>
      Gerar PDF
    </Button>
  );
}
```

## 🚨 Troubleshooting

### Problema: Webhook não está sendo recebido
**Solução**:
1. Verificar URL do webhook no Stripe
2. Confirmar que `STRIPE_WEBHOOK_SECRET` está correto
3. Verificar logs do servidor

### Problema: Assinatura não sincronizada
**Solução**:
```bash
# Forçar sincronização manual
curl -X POST -H "Authorization: Bearer $TOKEN" \
  http://localhost:5000/api/subscriptions/sync/$SUBSCRIPTION_ID
```

### Problema: Limite não sendo aplicado
**Solução**:
1. Verificar se middleware está aplicado nas rotas
2. Confirmar dados de assinatura no banco
3. Verificar contexto de assinatura no frontend

### Problema: Erro de pagamento
**Solução**:
1. Verificar configuração do Stripe
2. Confirmar que `STRIPE_PREMIUM_PRICE_ID` está correto
3. Testar com cartões de teste

## 📊 Monitoramento

### Métricas Importantes
- Taxa de conversão Free → Premium
- Churn rate (cancelamentos)
- Receita recorrente mensal (MRR)
- Uso médio por plano

### Logs para Monitorar
- Criação de assinaturas
- Upgrades e downgrades
- Falhas de pagamento
- Sincronização de webhooks

## 🔒 Segurança

### Validações Implementadas
- ✅ Verificação de assinatura ativa
- ✅ Validação de limites por plano
- ✅ Verificação de funcionalidades premium
- ✅ Proteção contra bypass de limitações

### Boas Práticas
- Sempre validar no backend
- Usar middleware de verificação
- Implementar fallbacks graceful
- Monitorar tentativas de bypass

## 📞 Suporte

Para problemas relacionados ao sistema de assinaturas:

1. Verificar logs do servidor
2. Executar `npm run subscriptions:status`
3. Consultar documentação do Stripe
4. Verificar configuração de webhooks

## 🔄 Atualizações

### Versão 1.0.0
- ✅ Sistema básico de planos Free/Premium
- ✅ Integração com Stripe
- ✅ Verificações de limite
- ✅ Interface de upgrade

### Próximas Versões
- 🔄 Planos anuais com desconto
- 🔄 Planos empresariais
- 🔄 Métricas avançadas
- 🔄 API para parceiros
