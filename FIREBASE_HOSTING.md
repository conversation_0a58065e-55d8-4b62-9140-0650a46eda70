# Firebase Hosting Guide for Doce Menu

This document provides a comprehensive guide for deploying the Doce Menu application to Firebase Hosting.

## Prerequisites

Before deploying, ensure you have:

1. A Google account with access to Firebase
2. A Firebase project created at https://console.firebase.google.com/
3. The Firebase CLI installed in this project (already done)

## Step 1: Login to Firebase

Run the following command to authenticate with Firebase:

```bash
npx firebase login
```

This will open a browser window to complete the authentication process.

## Step 2: Initialize Firebase Project

Create a Firebase project in the Firebase Console with the name "docemenu" if you haven't already.

Then run the initialization script:

```bash
./firebase-init.sh
```

This will guide you through connecting your local project to the Firebase project.

## Step 3: Deploy to Firebase Hosting

To deploy your application to Firebase Hosting, use the deployment script:

```bash
./deploy.sh
```

This script will:
1. Build the application
2. Optimize the build output for Firebase hosting
3. Deploy the application to Firebase

## Step 4: Set Up Custom Domain

After deployment, you'll need to set up your custom domain (app.docemenu.com.br):

1. Go to the Firebase Console: https://console.firebase.google.com/
2. Navigate to your project
3. Go to Hosting in the left sidebar
4. Click "Add custom domain"
5. Enter "app.docemenu.com.br" and follow the verification process
6. Add the required DNS records to your domain registrar's settings

**Important DNS Records**:
- You'll need to add a TXT record for domain verification
- Then you'll need to add an A record or CNAME record to point to Firebase hosting

## Step 5: Verify Deployment

After deployment and domain setup:

1. Visit your site at https://app.docemenu.com.br
2. Test all main functionality including:
   - User registration and login
   - Store creation
   - Product management
   - Customer order processes

## Troubleshooting Common Issues

### Issue: Firebase Deploy Command Not Found
Solution: Run `npx firebase` instead of just `firebase`

### Issue: Build Fails
Solution: Check the build logs and resolve any errors before deploying

### Issue: Custom Domain Not Working
Solution: 
1. Verify DNS records are correctly set up
2. Check if domain verification is complete in Firebase Console
3. DNS propagation may take up to 48 hours

### Issue: Authentication Not Working in Production
Solution: 
1. Ensure all environment variables are properly set
2. Check Supabase configuration for production environment

## Maintaining Your Deployment

For each update to your application:

1. Make and test your changes locally
2. Commit your changes
3. Run the deploy script again: `./deploy.sh`

## Contact Information

If you need help with your Firebase deployment:
- Firebase Support: https://firebase.google.com/support
- Google Cloud Platform Support: https://cloud.google.com/support

## Additional Resources

- [Firebase Hosting Documentation](https://firebase.google.com/docs/hosting)
- [Managing Custom Domains in Firebase](https://firebase.google.com/docs/hosting/custom-domain)
- [Firebase CLI Reference](https://firebase.google.com/docs/cli)