import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { CreditCard, Plus, RefreshCw } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';
import { useTranslation } from '@/hooks/useTranslation';
import {
  calculateTotalReceived,
  calculatePendingAmount,
  getPaymentStatusColor,
  getPaymentStatusText,
  type OrderPayment
} from '@/hooks/useOrderPayments';

interface PaymentStatusCardProps {
  orderTotal: number;
  paymentStatus: string;
  payments: OrderPayment[];
  currency?: string;
  onRegisterPayment: () => void;
  onRecalculateStatus: () => void;
  isRecalculating?: boolean;
}

export default function PaymentStatusCard({
  orderTotal,
  paymentStatus,
  payments,
  currency = 'R$',
  onRegisterPayment,
  onRecalculateStatus,
  isRecalculating = false
}: PaymentStatusCardProps) {
  const { t } = useTranslation();

  const totalReceived = calculateTotalReceived(payments);
  const pendingAmount = calculatePendingAmount(orderTotal, payments);
  const statusColor = getPaymentStatusColor(paymentStatus);
  const statusText = getPaymentStatusText(paymentStatus, t);

  // Log para debugging do status de pagamento
  console.log('💳 PaymentStatusCard - Status recebido:', {
    paymentStatus,
    statusText,
    statusColor,
    orderTotal,
    totalReceived,
    pendingAmount,
    paymentsCount: payments.length
  });

  return (
    <Card key={`payment-status-${paymentStatus}-${totalReceived}-${orderTotal}`} className="overflow-hidden shadow-sm">
      <CardHeader className="bg-gradient-to-r from-green-50 to-green-100/50 px-6 py-4 border-b">
        <CardTitle className="text-lg flex items-center justify-between">
          <div className="flex items-center">
            <CreditCard className="h-5 w-5 mr-2 text-green-600" />
            {t('payments.paymentControl') || 'Controle de Recebimentos'}
          </div>
          <div className="flex items-center gap-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      // Emitir evento para preservar estado da revisão antes do recálculo
                      const currentUrl = window.location.pathname;
                      const revisionMatch = currentUrl.match(/\/admin\/orders\/(\d+)/);
                      if (revisionMatch) {
                        const preserveRevisionEvent = new CustomEvent('preserve-revision-state', {
                          detail: { orderId: revisionMatch[1] }
                        });
                        window.dispatchEvent(preserveRevisionEvent);
                      }
                      onRecalculateStatus();
                    }}
                    disabled={isRecalculating}
                    className="h-8 px-2"
                  >
                    <RefreshCw className={`h-4 w-4 ${isRecalculating ? 'animate-spin' : ''}`} />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{t('payments.recalculateTooltip') || 'Recalcular status de pagamento baseado nos recebimentos registrados'}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            {paymentStatus !== 'recebido' && (
              <Button
                onClick={onRegisterPayment}
                size="sm"
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                <Plus className="h-4 w-4 mr-1" />
                {t('payments.registerPayment') || 'Registrar Pagamento'}
              </Button>
            )}
          </div>
        </CardTitle>
      </CardHeader>

      <CardContent className="p-6">
        {/* Status Badge */}
        <div className="mb-6">
          <Badge
            variant="outline"
            className={`${statusColor} px-3 py-1 text-sm font-medium border`}
          >
            {statusText}
          </Badge>
        </div>

        {/* Payment Summary */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Total do Pedido */}
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <div className="text-sm text-blue-600 font-medium mb-1">
              {t('payments.orderTotal') || 'Total do Pedido'}
            </div>
            <div className="text-2xl font-bold text-blue-700">
              {formatCurrency(orderTotal, currency)}
            </div>
          </div>

          {/* Total Recebido */}
          <div className="bg-green-50 p-4 rounded-lg border border-green-200">
            <div className="text-sm text-green-600 font-medium mb-1">
              {t('payments.totalReceived') || 'Total Recebido'}
            </div>
            <div className="text-2xl font-bold text-green-700">
              {formatCurrency(totalReceived, currency)}
            </div>
            {payments.length > 0 && (
              <div className="text-xs text-green-600 mt-1">
                {t('payments.paymentsCount', { count: payments.length }) || `${payments.length} recebimento(s)`}
              </div>
            )}
          </div>

          {/* Valor Pendente */}
          <div className={`p-4 rounded-lg border ${
            pendingAmount > 0
              ? 'bg-yellow-50 border-yellow-200'
              : 'bg-gray-50 border-gray-200'
          }`}>
            <div className={`text-sm font-medium mb-1 ${
              pendingAmount > 0 ? 'text-yellow-600' : 'text-gray-600'
            }`}>
              {t('payments.pendingAmount') || 'Valor Pendente'}
            </div>
            <div className={`text-2xl font-bold ${
              pendingAmount > 0 ? 'text-yellow-700' : 'text-gray-700'
            }`}>
              {formatCurrency(pendingAmount, currency)}
            </div>
            {pendingAmount === 0 && (
              <div className="text-xs text-gray-600 mt-1">
                {t('payments.fullyPaid') || 'Totalmente pago'}
              </div>
            )}
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mt-6">
          <div className="flex justify-between text-sm text-muted-foreground mb-2">
            <span>{t('payments.paymentProgress') || 'Progresso do Pagamento'}</span>
            <span>
              {orderTotal > 0 ? Math.round((totalReceived / orderTotal) * 100) : 0}%
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-300 ${
                paymentStatus === 'recebido'
                  ? 'bg-green-500'
                  : paymentStatus === 'parcialmente_recebido'
                  ? 'bg-yellow-500'
                  : 'bg-blue-500'
              }`}
              style={{
                width: `${orderTotal > 0 ? Math.min((totalReceived / orderTotal) * 100, 100) : 0}%`
              }}
            />
          </div>
        </div>

        {/* Additional Info */}
        {paymentStatus === 'em_disputa' && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <div className="text-sm text-red-700 font-medium">
              ⚠️ {t('payments.disputeWarning') || 'Pagamento em disputa'}
            </div>
            <div className="text-xs text-red-600 mt-1">
              {t('payments.disputeDescription') || 'Verifique os recebimentos marcados como disputa'}
            </div>
          </div>
        )}

        {paymentStatus === 'estornado' && (
          <div className="mt-4 p-3 bg-red-100 border border-red-300 rounded-lg">
            <div className="text-sm text-red-800 font-medium">
              🔄 {t('payments.refundWarning') || 'Pagamento estornado'}
            </div>
            <div className="text-xs text-red-700 mt-1">
              {t('payments.refundDescription') || 'O valor foi estornado ao cliente'}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}