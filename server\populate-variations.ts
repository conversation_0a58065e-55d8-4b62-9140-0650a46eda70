import { supabase } from './db';
import { storage } from './storage';

const productId = 1; // ID do produto "Bolo de Chocolate"

async function populateVariations() {
  try {
    console.log('Iniciando população de variações...');
    
    // Buscar as variações existentes
    const variations = await storage.getProductVariationsByProductId(productId);
    
    if (!variations || variations.length === 0) {
      console.log('Nenhuma variação encontrada para o produto:', productId);
      return;
    }
    
    console.log(`Encontrado ${variations.length} variações para o produto ${productId}`);
    
    // Dados das opções a serem inseridas
    const variationOptionsData = [
      // Opções para "Tamanho" (variation_id = 1)
      { variationId: 1, name: 'pequeno', price: 0 },
      { variationId: 1, name: 'medio', price: 0 },
      { variationId: 1, name: 'grande', price: 0 },
      
      // Opções para "Recheio" (variation_id = 2)
      { variationId: 2, name: 'chocolate', price: 0 },
      { variationId: 2, name: 'morango', price: 0 },
    ];
    
    console.log('Verificando se já existem opções...');
    
    // Verificar cada variação e suas opções
    for (const variation of variations) {
      console.log(`Verificando opções para variação ID ${variation.id} (${variation.name})`);
      
      const options = await storage.getVariationOptionsByVariationId(variation.id);
      
      if (options && options.length > 0) {
        console.log(`Variação ${variation.id} já possui ${options.length} opções. Pulando...`);
        continue;
      }
      
      // Filtrar opções para esta variação
      const optionsToInsert = variationOptionsData.filter(opt => opt.variationId === variation.id);
      
      console.log(`Inserindo ${optionsToInsert.length} opções para variação ${variation.id}`);
      
      // Inserir cada opção
      for (const option of optionsToInsert) {
        await storage.createVariationOption(option);
        console.log(`- Opção "${option.name}" inserida com sucesso para variação ${variation.id}`);
      }
    }
    
    console.log('Processo de população de variações concluído com sucesso!');
  } catch (error) {
    console.error('Erro ao popular variações:', error);
  }
}

// Executar a função
populateVariations().then(() => {
  console.log('Script finalizado.');
});
