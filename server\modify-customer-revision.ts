import { db } from './db';
import { sql } from 'drizzle-orm';

/**
 * Script para adicionar a coluna customer_id à tabela order_revisions
 * Este script usa a configuração de banco de dados já existente no projeto
 */
async function runMigration() {
  try {
    console.log('Iniciando processo de migração para adicionar customer_id na tabela order_revisions...');
    
    console.log('Verificando se a coluna já existe...');
    // Verificando se a coluna customer_id existe na tabela order_revisions
    const columnCheck = await db.execute(sql`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'order_revisions' AND column_name = 'customer_id'
    `);
    
    if (columnCheck.length === 0) {
      console.log('Coluna customer_id não encontrada. Adicionando coluna...');
      
      // Adicionar a coluna customer_id à tabela order_revisions
      await db.execute(sql`
        ALTER TABLE order_revisions 
        ADD COLUMN customer_id INTEGER REFERENCES customers(id)
      `);
      
      console.log('Coluna customer_id adicionada com sucesso!');
      
      // Atualizar os registros existentes com os IDs de cliente dos pedidos originais
      await db.execute(sql`
        UPDATE order_revisions r
        SET customer_id = o.customer_id
        FROM orders o
        WHERE r.order_id = o.id AND r.customer_id IS NULL
      `);
      
      console.log('Dados de cliente copiados com sucesso dos pedidos originais!');
    } else {
      console.log('A coluna customer_id já existe na tabela order_revisions.');
    }
    
    console.log('Migração concluída com sucesso!');
    process.exit(0);
  } catch (error) {
    console.error('Erro durante o processo de migração:', error);
    process.exit(1);
  }
}

runMigration();