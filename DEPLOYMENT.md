# Deployment Guide for Doce Menu

This guide explains how to deploy the Doce Menu application to Firebase Hosting.

## Prerequisites

1. A Firebase account
2. Firebase project named "docemenu" created in the Firebase console
3. Firebase CLI installed (included in this project's dependencies)

## Initial Setup

1. Run the Firebase initialization script:

```bash
./firebase-init.sh
```

This script will guide you through:
- Logging into Firebase
- Initializing the Firebase project
- Setting up Firebase Hosting

## Deployment

To deploy the application to Firebase Hosting:

```bash
./deploy.sh
```

This script will:
1. Build the application
2. Deploy the built files to Firebase Hosting

## Custom Domain Setup

To set up custom domain for your Firebase hosting:

1. Go to the Firebase Console: https://console.firebase.google.com/
2. Select your "docemenu" project
3. Go to Hosting in the left menu
4. Click "Add custom domain"
5. Follow the instructions to add "app.docemenu.com.br"
6. Add the required DNS records to your domain registrar

## Configuration Files

- `firebase.json`: Main Firebase configuration
- `firebase.hosting.json`: Configuration specific to hosting
- `.firebaserc`: Project configuration

## Environment Variables

For production deployment, ensure all required environment variables are set in the Firebase project:

1. Go to the Firebase Console
2. Select your project
3. Go to Project Settings
4. Navigate to the Service Accounts tab
5. Set the required environment variables

## Troubleshooting

If you encounter issues during deployment:

1. Check the Firebase deployment logs for errors
2. Verify all environment variables are correctly set
3. Ensure your Firebase project has Hosting enabled
4. Verify your build is generating the correct files in the `dist/public` directory