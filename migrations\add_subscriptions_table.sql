-- Criar tabela de assinaturas para sistema de planos
CREATE TABLE IF NOT EXISTS subscriptions (
  id SERIAL PRIMARY KEY,
  store_id INTEGER NOT NULL REFERENCES stores(id) ON DELETE CASCADE,
  stripe_customer_id VARCHAR(255),
  stripe_subscription_id VARCHAR(255),
  plan_type VARCHAR(50) NOT NULL DEFAULT 'free',
  status VARCHAR(50) NOT NULL DEFAULT 'active',
  current_period_start TIMESTAMP WITH TIME ZONE,
  current_period_end TIMESTAMP WITH TIME ZONE,
  trial_end TIMESTAMP WITH TIME ZONE,
  cancel_at_period_end BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Criar índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_subscriptions_store_id ON subscriptions(store_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_stripe_customer_id ON subscriptions(stripe_customer_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_stripe_subscription_id ON subscriptions(stripe_subscription_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_plan_type ON subscriptions(plan_type);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON subscriptions(status);

-- Adicionar constraint para garantir que cada loja tenha apenas uma assinatura ativa
CREATE UNIQUE INDEX IF NOT EXISTS idx_subscriptions_unique_active_store 
ON subscriptions(store_id) 
WHERE status IN ('active', 'past_due', 'unpaid');

-- Criar função para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_subscriptions_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Criar trigger para atualizar updated_at
DROP TRIGGER IF EXISTS trigger_update_subscriptions_updated_at ON subscriptions;
CREATE TRIGGER trigger_update_subscriptions_updated_at
  BEFORE UPDATE ON subscriptions
  FOR EACH ROW
  EXECUTE FUNCTION update_subscriptions_updated_at();

-- Inserir assinatura gratuita padrão para todas as lojas existentes
INSERT INTO subscriptions (store_id, plan_type, status, created_at, updated_at)
SELECT 
  id as store_id,
  'free' as plan_type,
  'active' as status,
  NOW() as created_at,
  NOW() as updated_at
FROM stores 
WHERE id NOT IN (SELECT store_id FROM subscriptions WHERE store_id IS NOT NULL)
ON CONFLICT DO NOTHING;

-- Comentários para documentação
COMMENT ON TABLE subscriptions IS 'Tabela para gerenciar assinaturas dos planos de cada loja';
COMMENT ON COLUMN subscriptions.store_id IS 'ID da loja associada à assinatura';
COMMENT ON COLUMN subscriptions.stripe_customer_id IS 'ID do cliente no Stripe';
COMMENT ON COLUMN subscriptions.stripe_subscription_id IS 'ID da assinatura no Stripe';
COMMENT ON COLUMN subscriptions.plan_type IS 'Tipo do plano: free ou premium';
COMMENT ON COLUMN subscriptions.status IS 'Status da assinatura: active, past_due, canceled, unpaid, incomplete';
COMMENT ON COLUMN subscriptions.current_period_start IS 'Início do período atual de cobrança';
COMMENT ON COLUMN subscriptions.current_period_end IS 'Fim do período atual de cobrança';
COMMENT ON COLUMN subscriptions.trial_end IS 'Data de fim do período de trial';
COMMENT ON COLUMN subscriptions.cancel_at_period_end IS 'Se a assinatura deve ser cancelada no fim do período';
