import { useEffect } from 'react';
import { useLocation } from 'wouter';
import AuthForm from '@/components/auth/AuthForm';
import { useAuth } from '@/context/FirebaseAuthContext';

export default function Login() {
  const [, setLocation] = useLocation();
  const { isAuthenticated } = useAuth();

  // Redirect to admin dashboard if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      setLocation('/admin');
    }
  }, [isAuthenticated, setLocation]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-neutral-light p-4">
      <AuthForm mode="login" />
    </div>
  );
}
