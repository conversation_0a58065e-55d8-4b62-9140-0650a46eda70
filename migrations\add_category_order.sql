-- Adiciona coluna de ordem de exibição na tabela de categorias
ALTER TABLE categories ADD COLUMN IF NOT EXISTS display_order INTEGER NOT NULL DEFAULT 0;

-- Atualiza as categorias existentes com ordem sequencial
UPDATE categories SET display_order = subquery.row_num
FROM (
  SELECT id, ROW_NUMBER() OVER (PARTITION BY store_id ORDER BY id) - 1 as row_num
  FROM categories
) AS subquery
WHERE categories.id = subquery.id;