#!/bin/bash

echo "=== Doce Menu Firebase Deployment ==="
echo "Deploying to app.docemenu.com.br"
echo ""

# Copy Firebase environment to production
echo "1. Setting up production environment..."
cp .env.firebase .env.production

# Build the application with production environment
echo "2. Building the application..."
NODE_ENV=production npm run build

# Run build optimization script
echo ""
echo "3. Optimizing build for Firebase hosting..."
node build-optimize.js

# Deploy to Firebase hosting
echo ""
echo "4. Deploying to Firebase hosting..."
npx firebase deploy --only hosting

echo ""
echo "=== Deployment Complete ==="
echo "Your Doce Menu application should now be live at:"
echo "https://app.docemenu.com.br"
echo ""
echo "Note: If this is your first deployment or if you've changed your"
echo "      domain settings, it may take up to 48 hours for DNS changes"
echo "      to propagate fully."