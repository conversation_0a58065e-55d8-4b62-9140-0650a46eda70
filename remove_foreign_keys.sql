-- Remover restrições de chave estrangeira das tabelas que referenciam produtos
-- Tabela order_items
ALTER TABLE "order_items" DROP CONSTRAINT IF EXISTS "order_items_product_id_fkey";

-- Tabela order_revision_items
ALTER TABLE "order_revision_items" DROP CONSTRAINT IF EXISTS "order_revision_items_product_id_fkey";

-- Tabela cart_items
ALTER TABLE "cart_items" DROP CONSTRAINT IF EXISTS "cart_items_product_id_fkey";

-- Atualizar a função que cria revisões de pedidos para não depender da existência do produto
CREATE OR REPLACE FUNCTION create_order_revision(order_id_param INTEGER)
RETURNS INTEGER AS $$
DECLARE
  new_revision_id INTEGER;
  new_revision_number INTEGER;
  order_record RECORD;
BEGIN
  -- Get the next revision number for this order
  SELECT COALESCE(MAX(revision_number), 0) + 1 INTO new_revision_number
  FROM order_revisions
  WHERE order_id = order_id_param;
  
  -- Get the order data
  SELECT * INTO order_record
  FROM orders
  WHERE id = order_id_param;
  
  -- Create the new revision
  INSERT INTO order_revisions (
    order_id,
    revision_number,
    status,
    receiving_method,
    receiving_date,
    receiving_time,
    delivery_address,
    payment_method,
    subtotal,
    delivery_fee,
    total,
    notes,
    is_current
  ) VALUES (
    order_id_param,
    new_revision_number,
    order_record.status,
    order_record.receiving_method,
    order_record.receiving_date,
    order_record.receiving_time,
    order_record.delivery_address,
    order_record.payment_method,
    order_record.subtotal,
    order_record.delivery_fee,
    order_record.total,
    order_record.notes,
    FALSE -- New revision is not current by default
  ) RETURNING id INTO new_revision_id;
  
  -- Copy order items to the new revision with complete product information
  -- Use LEFT JOIN para garantir que mesmo se o produto não existir mais, os itens do pedido sejam copiados
  INSERT INTO order_revision_items (
    revision_id,
    product_id,
    product_name,
    product_description,
    product_image,
    quantity,
    unit_price,
    subtotal,
    selected_variations,
    observation
  )
  SELECT
    new_revision_id,
    oi.product_id,
    COALESCE(p.name, 'Produto Indisponível'), -- Usar nome padrão se o produto não existir
    p.description,
    (CASE WHEN p.images IS NOT NULL AND jsonb_array_length(p.images) > 0 
          THEN jsonb_extract_path_text(p.images, '0')
          ELSE NULL
     END),
    oi.quantity,
    oi.price,
    (oi.quantity * oi.price),
    oi.selected_options,
    oi.observation
  FROM order_items oi
  LEFT JOIN products p ON oi.product_id = p.id
  WHERE oi.order_id = order_id_param;
  
  RETURN new_revision_id;
END;
$$ LANGUAGE plpgsql;