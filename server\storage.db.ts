import {
  type User, type InsertUser, type UpsertUser, type Store, type InsertStore, type Category, type InsertCategory,
  type Product, type InsertProduct, type Customer, type InsertCustomer, type Order, type InsertOrder,
  type OrderItem, type InsertOrderItem, type StoreVisit, type InsertStoreVisit,
  type ProductVariation, type InsertProductVariation, type VariationOption as VariationOptionType,
  type InsertVariationOption, type CartItem, type InsertCartItem,
  type OrderRevision, type InsertOrderRevision, type OrderRevisionItem, type InsertOrderRevisionItem,
  type Coupon, type InsertCoupon, type CouponUsage, type InsertCouponUsage,
  users, stores, categories, products, productVariations, variationOptions,
  customers, orders, orderItems, storeVisits, cartItems, orderRevisions, orderRevisionItems,
  coupons, couponUsage
} from '@shared/schema';
import { IStorage } from './storage';
import { db } from './db';
import { eq, and, desc, gte, lte, isNull, sql } from 'drizzle-orm';

export class DatabaseStorage implements IStorage {
  // User methods
  async getUser(id: number): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user;
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.email, email));
    return user;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user;
  }

  async getUserByFirebaseUid(firebaseUid: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.firebaseUid, firebaseUid));
    return user;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const [user] = await db.insert(users).values(insertUser).returning();
    return user;
  }

  async createUserWithFirebaseUid(userData: Partial<User> & { fullName?: string }): Promise<User> {
    // Adaptar campos se necessário
    const { fullName, ...restData } = userData;
    const insertData: InsertUser = {
      ...restData as InsertUser,
      // Se fullName estiver presente, usá-lo como username se não houver username
      username: userData.username || fullName || '',
      fullName: fullName,
    };

    const [user] = await db.insert(users).values(insertData).returning();
    return user;
  }

  async upsertUser(userData: UpsertUser): Promise<User> {
    // Verificar se o usuário já existe
    let user: User | undefined;

    if (userData.firebaseUid) {
      user = await this.getUserByFirebaseUid(userData.firebaseUid);
    } else if (userData.email) {
      user = await this.getUserByEmail(userData.email);
    } else if (userData.username) {
      user = await this.getUserByUsername(userData.username);
    }

    if (user) {
      // Atualizar usuário existente
      const updatedUser = await this.updateUserByFirebaseUid(userData.firebaseUid!, userData);
      return updatedUser!;
    } else {
      // Criar novo usuário
      return this.createUser(userData as InsertUser);
    }
  }

  async updateUserByFirebaseUid(firebaseUid: string, userData: Partial<User>): Promise<User | undefined> {
    const [updatedUser] = await db
      .update(users)
      .set({
        ...userData,
        updatedAt: new Date()
      })
      .where(eq(users.firebaseUid, firebaseUid))
      .returning();
    return updatedUser;
  }

  // Método privado para mapear os dados da loja do banco para o formato da aplicação
  private mapStoreData(store: any): Store {
    if (!store) return store;

    // Parse deliverySettings como objeto JSON tipado
    let deliverySettings: any = undefined;
    if (store.deliverySettings) {
      try {
        deliverySettings = typeof store.deliverySettings === 'string'
          ? JSON.parse(store.deliverySettings)
          : store.deliverySettings;
      } catch (e) {
        console.error('Erro ao converter deliverySettings:', e);
      }
    }

    return {
      ...store,
      deliverySettings: deliverySettings as any,
    };
  }

  // Store methods
  async getStore(id: number): Promise<Store | undefined> {
    const [store] = await db.select().from(stores).where(eq(stores.id, id));
    return this.mapStoreData(store);
  }

  async getStoreBySlug(slug: string): Promise<Store | undefined> {
    const [store] = await db.select().from(stores).where(eq(stores.slug, slug));
    return this.mapStoreData(store);
  }

  async getStoreByUserId(userId: number): Promise<Store | undefined> {
    const [store] = await db.select().from(stores).where(eq(stores.userId, userId));
    return this.mapStoreData(store);
  }

  async getStoreByFirebaseUid(firebaseUid: string): Promise<Store | undefined> {
    // Encontrar o usuário pelo Firebase UID
    const user = await this.getUserByFirebaseUid(firebaseUid);
    if (!user) return undefined;

    // Encontrar a loja pelo ID do usuário
    return this.getStoreByUserId(user.id);
  }

  async createStore(insertStore: InsertStore): Promise<Store> {
    const [store] = await db.insert(stores).values(insertStore).returning();
    return this.mapStoreData(store);
  }

  async updateStore(id: number, storeUpdate: Partial<Store>): Promise<Store | undefined> {
    // Converter deliverySettings para JSON se for um objeto
    if (storeUpdate.deliverySettings && typeof storeUpdate.deliverySettings === 'object') {
      const dbUpdate = { ...storeUpdate };
      dbUpdate.deliverySettings = dbUpdate.deliverySettings as any;

      const [updatedStore] = await db
        .update(stores)
        .set(dbUpdate)
        .where(eq(stores.id, id))
        .returning();

      return this.mapStoreData(updatedStore);
    }

    const [updatedStore] = await db
      .update(stores)
      .set(storeUpdate)
      .where(eq(stores.id, id))
      .returning();

    return this.mapStoreData(updatedStore);
  }

  // Product methods
  async getProduct(id: number): Promise<Product | undefined> {
    const [product] = await db.select().from(products).where(eq(products.id, id));
    return product;
  }

  async getProductsByStoreId(storeId: number): Promise<Product[]> {
    return db.select().from(products).where(eq(products.storeId, storeId));
  }

  async getProductsByCategoryId(categoryId: number): Promise<Product[]> {
    return db.select().from(products).where(eq(products.categoryId, categoryId));
  }

  async createProduct(insertProduct: InsertProduct): Promise<Product> {
    const [product] = await db.insert(products).values(insertProduct).returning();
    return product;
  }

  async updateProduct(id: number, productUpdate: Partial<Product>): Promise<Product | undefined> {
    const [updatedProduct] = await db
      .update(products)
      .set(productUpdate)
      .where(eq(products.id, id))
      .returning();
    return updatedProduct;
  }

  async deleteProduct(id: number): Promise<boolean> {
    const result = await db.delete(products).where(eq(products.id, id));
    return true; // O Drizzle não retorna o número de linhas afetadas de forma direta
  }

  // Product Variation methods
  async getProductVariationsByProductId(productId: number): Promise<ProductVariation[]> {
    return db.select().from(productVariations).where(eq(productVariations.productId, productId));
  }

  async createProductVariation(insertVariation: InsertProductVariation): Promise<ProductVariation> {
    const [variation] = await db.insert(productVariations).values(insertVariation).returning();
    return variation;
  }

  async updateProductVariation(id: number, variationUpdate: Partial<ProductVariation>): Promise<ProductVariation | undefined> {
    const [updatedVariation] = await db
      .update(productVariations)
      .set(variationUpdate)
      .where(eq(productVariations.id, id))
      .returning();
    return updatedVariation;
  }

  async deleteProductVariation(id: number): Promise<boolean> {
    await db.delete(productVariations).where(eq(productVariations.id, id));
    return true;
  }

  // Variation Option methods
  async getVariationOptionsByVariationId(variationId: number): Promise<VariationOptionType[]> {
    const options = await db.select().from(variationOptions).where(eq(variationOptions.variationId, variationId));

    // Mapear para o formato esperado pela aplicação (compatibilidade)
    return options.map(option => ({
      id: String(option.id),
      name: option.name,
      precoAdicional: option.price
    }));
  }

  async createVariationOption(insertOption: InsertVariationOption): Promise<VariationOptionType> {
    const [option] = await db.insert(variationOptions).values(insertOption).returning();

    // Mapear para o formato esperado pela aplicação (compatibilidade)
    return {
      id: String(option.id),
      name: option.name,
      precoAdicional: option.price
    };
  }

  async updateVariationOption(id: number, optionUpdate: Partial<VariationOptionType>): Promise<VariationOptionType | undefined> {
    // Converter para o formato do banco de dados
    const dbUpdate: any = {};
    if (optionUpdate.name !== undefined) dbUpdate.name = optionUpdate.name;
    if (optionUpdate.precoAdicional !== undefined) dbUpdate.price = optionUpdate.precoAdicional;

    const [updatedOption] = await db
      .update(variationOptions)
      .set(dbUpdate)
      .where(eq(variationOptions.id, id))
      .returning();

    if (!updatedOption) return undefined;

    // Mapear para o formato esperado pela aplicação (compatibilidade)
    return {
      id: String(updatedOption.id),
      name: updatedOption.name,
      precoAdicional: updatedOption.price
    };
  }

  async deleteVariationOption(id: number): Promise<boolean> {
    await db.delete(variationOptions).where(eq(variationOptions.id, id));
    return true;
  }

  // Category methods
  async getCategory(id: number): Promise<Category | undefined> {
    const [category] = await db.select().from(categories).where(eq(categories.id, id));
    return category;
  }

  async getCategoriesByStoreId(storeId: number): Promise<Category[]> {
    return db.select()
      .from(categories)
      .where(eq(categories.storeId, storeId))
      .orderBy(categories.displayOrder);
  }

  async createCategory(insertCategory: InsertCategory): Promise<Category> {
    const [category] = await db.insert(categories).values(insertCategory).returning();
    return category;
  }

  async updateCategory(id: number, categoryUpdate: Partial<Category>): Promise<Category | undefined> {
    const [updatedCategory] = await db
      .update(categories)
      .set(categoryUpdate)
      .where(eq(categories.id, id))
      .returning();
    return updatedCategory;
  }

  async deleteCategory(id: number): Promise<boolean> {
    await db.delete(categories).where(eq(categories.id, id));
    return true;
  }

  // Customer methods
  async getCustomer(id: number): Promise<Customer | undefined> {
    const [customer] = await db.select().from(customers).where(eq(customers.id, id));
    return customer;
  }

  async getCustomersByStoreId(storeId: number): Promise<Customer[]> {
    return db.select().from(customers).where(eq(customers.storeId, storeId));
  }

  async getCustomerByEmail(storeId: number, email: string): Promise<Customer | undefined> {
    const [customer] = await db.select()
      .from(customers)
      .where(and(
        eq(customers.storeId, storeId),
        eq(customers.email, email)
      ));
    return customer;
  }

  async getCustomerByPhone(storeId: number, phone: string): Promise<Customer | undefined> {
    const [customer] = await db.select()
      .from(customers)
      .where(and(
        eq(customers.storeId, storeId),
        eq(customers.phone, phone)
      ));
    return customer;
  }

  async createCustomer(insertCustomer: InsertCustomer): Promise<Customer> {
    const [customer] = await db.insert(customers).values(insertCustomer).returning();
    return customer;
  }

  async updateCustomer(id: number, customerUpdate: Partial<Customer>): Promise<Customer | undefined> {
    const [updatedCustomer] = await db
      .update(customers)
      .set(customerUpdate)
      .where(eq(customers.id, id))
      .returning();
    return updatedCustomer;
  }

  // Order methods
  async getOrder(id: number): Promise<Order | undefined> {
    const [order] = await db.select().from(orders).where(eq(orders.id, id));
    return order;
  }

  async getOrdersByStoreId(storeId: number): Promise<Order[]> {
    return db.select()
      .from(orders)
      .where(eq(orders.storeId, storeId))
      .orderBy(desc(orders.createdAt));
  }

  async getOrdersByCustomerId(customerId: number): Promise<Order[]> {
    return db.select()
      .from(orders)
      .where(eq(orders.customerId, customerId))
      .orderBy(desc(orders.createdAt));
  }

  async createOrder(insertOrder: InsertOrder): Promise<Order> {
    const [order] = await db.insert(orders).values(insertOrder).returning();
    return order;
  }

  async updateOrderStatus(id: number, status: string): Promise<Order | undefined> {
    const [updatedOrder] = await db
      .update(orders)
      .set({
        status,
        updatedAt: new Date()
      })
      .where(eq(orders.id, id))
      .returning();
    return updatedOrder;
  }

  // Order item methods
  async getOrderItemsByOrderId(orderId: number): Promise<OrderItem[]> {
    return db.select().from(orderItems).where(eq(orderItems.orderId, orderId));
  }

  async createOrderItem(insertOrderItem: InsertOrderItem): Promise<OrderItem> {
    const [orderItem] = await db.insert(orderItems).values(insertOrderItem).returning();
    return orderItem;
  }

  // Analytics methods
  async recordStoreVisit(insertStoreVisit: InsertStoreVisit): Promise<StoreVisit> {
    const [storeVisit] = await db.insert(storeVisits).values(insertStoreVisit).returning();
    return storeVisit;
  }

  async getStoreVisitsByStoreId(storeId: number, startDate?: Date, endDate?: Date): Promise<StoreVisit[]> {
    let conditions = [eq(storeVisits.storeId, storeId)];

    // Adicionar filtros de data se fornecidos
    if (startDate) {
      conditions.push(gte(storeVisits.createdAt, startDate));
    }

    if (endDate) {
      conditions.push(lte(storeVisits.createdAt, endDate));
    }

    return db.select()
      .from(storeVisits)
      .where(and(...conditions))
      .orderBy(desc(storeVisits.createdAt));
  }

  async getMonthlyVisitCount(storeId: number): Promise<number> {
    // Pegar o primeiro dia do mês atual
    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    const result = await db.select({ count: sql<number>`count(*)` })
      .from(storeVisits)
      .where(and(
        eq(storeVisits.storeId, storeId),
        gte(storeVisits.createdAt, firstDayOfMonth)
      ));

    return result[0]?.count || 0;
  }

  async getMonthlyOrderCount(storeId: number): Promise<number> {
    // Pegar o primeiro dia do mês atual
    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    const result = await db.select({ count: sql<number>`count(*)` })
      .from(orders)
      .where(and(
        eq(orders.storeId, storeId),
        gte(orders.createdAt, firstDayOfMonth)
      ));

    return result[0]?.count || 0;
  }

  // Cart item methods
  async getCartItemsBySessionId(storeId: number, sessionId: string): Promise<CartItem[]> {
    return db.select()
      .from(cartItems)
      .where(and(
        eq(cartItems.storeId, storeId),
        eq(cartItems.sessionId, sessionId)
      ));
  }

  async getCartItemsByUserId(storeId: number, userId: string): Promise<CartItem[]> {
    return db.select()
      .from(cartItems)
      .where(and(
        eq(cartItems.storeId, storeId),
        eq(cartItems.userId, userId)
      ));
  }

  async createCartItem(insertCartItem: InsertCartItem): Promise<CartItem> {
    const [cartItem] = await db.insert(cartItems).values(insertCartItem).returning();
    return cartItem;
  }

  async updateCartItem(id: number, cartItemUpdate: Partial<CartItem>): Promise<CartItem | undefined> {
    const [updatedCartItem] = await db
      .update(cartItems)
      .set({
        ...cartItemUpdate,
        updatedAt: new Date()
      })
      .where(eq(cartItems.id, id))
      .returning();
    return updatedCartItem;
  }

  async deleteCartItem(id: number): Promise<boolean> {
    await db.delete(cartItems).where(eq(cartItems.id, id));
    return true;
  }

  async clearCartItems(storeId: number, sessionId: string): Promise<boolean> {
    await db.delete(cartItems).where(and(
      eq(cartItems.storeId, storeId),
      eq(cartItems.sessionId, sessionId)
    ));
    return true;
  }

  // Order Revision methods
  async getOrderRevision(id: number): Promise<OrderRevision | undefined> {
    const [revision] = await db.select().from(orderRevisions).where(eq(orderRevisions.id, id));
    return revision;
  }

  async getOrderRevisionsByOrderId(orderId: number): Promise<OrderRevision[]> {
    return db.select()
      .from(orderRevisions)
      .where(eq(orderRevisions.orderId, orderId))
      .orderBy(desc(orderRevisions.revisionNumber));
  }

  async getOrderRevisionItems(revisionId: number): Promise<OrderRevisionItem[]> {
    return db.select()
      .from(orderRevisionItems)
      .where(eq(orderRevisionItems.revisionId, revisionId));
  }

  async createOrderRevision(insertRevision: InsertOrderRevision): Promise<OrderRevision> {
    const [revision] = await db.insert(orderRevisions).values(insertRevision).returning();
    return revision;
  }

  async createOrderRevisionItem(insertItem: InsertOrderRevisionItem): Promise<OrderRevisionItem> {
    const [item] = await db.insert(orderRevisionItems).values(insertItem).returning();
    return item;
  }

  async getLatestRevisionNumber(orderId: number): Promise<number> {
    const [result] = await db
      .select({ maxRevision: sql<number>`max(${orderRevisions.revisionNumber})` })
      .from(orderRevisions)
      .where(eq(orderRevisions.orderId, orderId));

    return result?.maxRevision || 0;
  }

  async updateOrderRevisionStatus(id: number, status: string): Promise<OrderRevision | undefined> {
    const [updatedRevision] = await db
      .update(orderRevisions)
      .set({ status })
      .where(eq(orderRevisions.id, id))
      .returning();
    return updatedRevision;
  }

  async updateOrderRevision(id: number, data: Partial<OrderRevision>): Promise<OrderRevision | undefined> {
    console.log('Updating order revision with data:', data);
    const [updatedRevision] = await db
      .update(orderRevisions)
      .set(data)
      .where(eq(orderRevisions.id, id))
      .returning();
    console.log('Updated revision result:', updatedRevision);
    return updatedRevision;
  }

  async setCurrentRevision(revisionId: number, orderId: number): Promise<void> {
    // Primeiro, remove o flag de todas as revisões deste pedido
    await db
      .update(orderRevisions)
      .set({ isCurrent: false })
      .where(eq(orderRevisions.orderId, orderId));

    // Em seguida, define a revisão especificada como atual
    await db
      .update(orderRevisions)
      .set({ isCurrent: true })
      .where(eq(orderRevisions.id, revisionId));
  }

  async clearCurrentRevisions(orderId: number): Promise<void> {
    // Define todas as revisões deste pedido como não atuais
    await db
      .update(orderRevisions)
      .set({ isCurrent: false })
      .where(eq(orderRevisions.orderId, orderId));
  }

  async deleteOrderRevision(id: number): Promise<boolean> {
    try {
      // Primeiro, exclui os itens da revisão
      await db
        .delete(orderRevisionItems)
        .where(eq(orderRevisionItems.revisionId, id));

      // Em seguida, exclui a revisão
      const result = await db
        .delete(orderRevisions)
        .where(eq(orderRevisions.id, id));

      return true;
    } catch (error) {
      console.error('Erro ao excluir revisão:', error);
      return false;
    }
  }

  // Coupon methods
  async getCoupon(id: number): Promise<Coupon | undefined> {
    const [coupon] = await db.select().from(coupons).where(eq(coupons.id, id));
    return coupon;
  }

  async getCouponsByStoreId(storeId: number): Promise<Coupon[]> {
    return db.select()
      .from(coupons)
      .where(eq(coupons.storeId, storeId))
      .orderBy(desc(coupons.createdAt));
  }

  async getCouponByCode(storeId: number, code: string): Promise<Coupon | undefined> {
    const [coupon] = await db.select()
      .from(coupons)
      .where(and(
        eq(coupons.storeId, storeId),
        eq(coupons.code, code)
      ));
    return coupon;
  }

  async createCoupon(insertCoupon: InsertCoupon): Promise<Coupon> {
    console.log('Recebendo dados para criar cupom:', insertCoupon);

    // Mapear os campos do frontend para o formato esperado pelo banco de dados
    const mappedData: Record<string, any> = {
      store_id: insertCoupon.storeId,
      code: insertCoupon.code,
      type: insertCoupon.tipo,
      value: insertCoupon.valor,
      min_purchase: insertCoupon.minimoCompra,
      expiration_date: insertCoupon.dataValidade,
      single_use: insertCoupon.usoUnico,
      active: insertCoupon.ativo
    };

    console.log('Dados mapeados para inserção no banco:', mappedData);

    try {
      const [coupon] = await db.insert(coupons).values(mappedData).returning();
      console.log('Cupom criado com sucesso:', coupon);
      return coupon;
    } catch (error) {
      console.error('Erro ao inserir cupom no banco de dados:', error);
      throw error;
    }
  }

  async updateCoupon(id: number, couponUpdate: Partial<Coupon>): Promise<Coupon | undefined> {
    console.log('Recebendo dados para atualizar cupom:', couponUpdate);

    // Mapear os campos do frontend para o formato esperado pelo banco de dados
    const mappedData: Record<string, any> = {};

    if (couponUpdate.code !== undefined) mappedData.code = couponUpdate.code;
    if (couponUpdate.tipo !== undefined) mappedData.type = couponUpdate.tipo;
    if (couponUpdate.valor !== undefined) mappedData.value = couponUpdate.valor;
    if (couponUpdate.minimoCompra !== undefined) mappedData.min_purchase = couponUpdate.minimoCompra;
    if (couponUpdate.dataValidade !== undefined) mappedData.expiration_date = couponUpdate.dataValidade;
    if (couponUpdate.usoUnico !== undefined) mappedData.single_use = couponUpdate.usoUnico;
    if (couponUpdate.ativo !== undefined) mappedData.active = couponUpdate.ativo;

    console.log('Dados mapeados para atualização no banco:', mappedData);

    try {
      const [updatedCoupon] = await db
        .update(coupons)
        .set(mappedData)
        .where(eq(coupons.id, id))
        .returning();
      console.log('Cupom atualizado com sucesso:', updatedCoupon);
      return updatedCoupon;
    } catch (error) {
      console.error('Erro ao atualizar cupom no banco de dados:', error);
      throw error;
    }
  }

  async updateCouponStatus(id: number, active: boolean): Promise<Coupon | undefined> {
    const [updatedCoupon] = await db
      .update(coupons)
      .set({ ativo: active })
      .where(eq(coupons.id, id))
      .returning();
    return updatedCoupon;
  }

  // Coupon Usage methods
  async createCouponUsage(insertUsage: InsertCouponUsage): Promise<CouponUsage> {
    const [usage] = await db.insert(couponUsage).values(insertUsage).returning();
    return usage;
  }

  async getCouponUsagesByCouponId(couponId: number): Promise<CouponUsage[]> {
    return db.select()
      .from(couponUsage)
      .where(eq(couponUsage.couponId, couponId))
      .orderBy(desc(couponUsage.usedAt));
  }

  async getCouponUsagesByCustomerId(customerId: number): Promise<CouponUsage[]> {
    return db.select()
      .from(couponUsage)
      .where(eq(couponUsage.customerId, customerId))
      .orderBy(desc(couponUsage.usedAt));
  }
}
