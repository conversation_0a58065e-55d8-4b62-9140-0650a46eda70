import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { useTranslation } from '@/hooks/useTranslation';

// Função utilitária para forçar atualização completa dos dados preservando estado da revisão
const forceRefreshOrderData = async (queryClient: any, orderId: number) => {
  console.log('🔄 Iniciando refresh forçado dos dados do pedido (preservando estado da revisão)...');

  // 1. Invalidar queries sem remover do cache para preservar estado
  await queryClient.invalidateQueries({ queryKey: [`/api/orders/${orderId}`] });
  await queryClient.invalidateQueries({ queryKey: [`/api/orders/${orderId}/revisions`] });
  await queryClient.invalidateQueries({ queryKey: ['orderPayments', orderId] });

  // 2. Invalidar queries específicas de revisões individuais
  await queryClient.invalidateQueries({
    predicate: (query) => {
      const queryKey = query.queryKey;
      return Array.isArray(queryKey) &&
             queryKey.length >= 2 &&
             queryKey[0] === '/api/orders/revisions';
    }
  });

  // 3. Aguardar um pouco para garantir que as invalidações foram processadas
  await new Promise(resolve => setTimeout(resolve, 100));

  // 4. Forçar refetch das queries principais
  await queryClient.refetchQueries({
    queryKey: [`/api/orders/${orderId}`],
    type: 'active'
  });
  await queryClient.refetchQueries({
    queryKey: [`/api/orders/${orderId}/revisions`],
    type: 'active'
  });
  await queryClient.refetchQueries({
    queryKey: ['orderPayments', orderId],
    type: 'active'
  });

  // 5. Refetch queries específicas de revisões individuais
  await queryClient.refetchQueries({
    predicate: (query) => {
      const queryKey = query.queryKey;
      return Array.isArray(queryKey) &&
             queryKey.length >= 2 &&
             queryKey[0] === '/api/orders/revisions';
    },
    type: 'active'
  });

  console.log('✅ Refresh forçado concluído (estado da revisão preservado)');
};

export interface OrderPayment {
  id: number;
  orderId: number;
  valor: number;
  data: string;
  metodo: 'Pix' | 'Dinheiro' | 'Cartão' | 'Transferência' | 'Outro';
  observacao?: string;
  createdAt: string;
  createdBy?: number;
}

export interface CreatePaymentData {
  valor: number;
  data: string;
  metodo: 'Pix' | 'Dinheiro' | 'Cartão' | 'Transferência' | 'Outro';
  observacao?: string;
}

export interface UpdatePaymentData {
  valor: number;
  data: string;
  metodo: 'Pix' | 'Dinheiro' | 'Cartão' | 'Transferência' | 'Outro';
  observacao?: string;
}

// Hook para buscar recebimentos de um pedido
export function useOrderPayments(orderId: number) {
  return useQuery({
    queryKey: ['orderPayments', orderId],
    queryFn: async () => {
      const response = await apiRequest('GET', `/api/orders/${orderId}/payments`);
      return response.json();
    },
    enabled: !!orderId,
    staleTime: 0, // Sempre busque dados novos quando for invalidado
    refetchOnWindowFocus: false,
  });
}

// Hook para criar um novo recebimento
export function useCreatePayment(orderId: number) {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: async (paymentData: CreatePaymentData) => {
      const response = await apiRequest('POST', `/api/orders/${orderId}/payments`, paymentData);
      return response.json();
    },
    onSuccess: async () => {
      console.log('🎯 Criação bem-sucedida, forçando refresh completo...');

      // Usar função utilitária para forçar refresh completo
      await forceRefreshOrderData(queryClient, orderId);

      // Invalidar lista de pedidos
      await queryClient.invalidateQueries({ queryKey: ['/api/orders'] });

      toast({
        title: t('payments.success') || 'Sucesso',
        description: t('payments.paymentCreated') || 'Recebimento registrado com sucesso',
      });
    },
    onError: (error: any) => {
      console.error('Erro ao criar recebimento:', error);

      // Verificar se é erro de valor excedendo o pendente
      let errorMessage = error.message || t('payments.paymentError') || 'Erro ao registrar recebimento';

      if (error?.message?.includes('excede o valor pendente')) {
        errorMessage = t('payments.valueExceedsPendingMessage') || 'O valor informado é maior que o valor pendente do pedido.';
      }

      toast({
        title: t('payments.error') || 'Erro',
        description: errorMessage,
        variant: 'destructive',
      });
    },
  });
}

// Hook para atualizar um recebimento
export function useUpdatePayment(orderId: number) {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: async ({ paymentId, paymentData }: { paymentId: number; paymentData: UpdatePaymentData }) => {
      console.log('🔄 Atualizando recebimento:', { paymentId, paymentData });
      const response = await apiRequest('PATCH', `/api/orders/${orderId}/payments/${paymentId}`, paymentData);
      const result = await response.json();
      console.log('✅ Resposta da atualização:', result);
      return result;
    },
    onSuccess: async () => {
      console.log('🎯 Atualização bem-sucedida, forçando refresh completo...');

      // Usar função utilitária para forçar refresh completo
      await forceRefreshOrderData(queryClient, orderId);

      // Invalidar lista de pedidos
      await queryClient.invalidateQueries({ queryKey: ['/api/orders'] });

      toast({
        title: t('payments.success') || 'Sucesso',
        description: t('payments.paymentUpdated') || 'Recebimento atualizado com sucesso',
      });
    },
    onError: (error: any) => {
      console.error('Erro ao atualizar recebimento:', error);

      // Verificar se é erro de valor excedendo o pendente
      let errorMessage = error.message || t('payments.paymentError') || 'Erro ao atualizar recebimento';

      if (error?.message?.includes('excede o valor pendente')) {
        errorMessage = t('payments.valueExceedsPendingMessage') || 'O valor informado é maior que o valor pendente do pedido.';
      }

      toast({
        title: t('payments.error') || 'Erro',
        description: errorMessage,
        variant: 'destructive',
      });
    },
  });
}

// Hook para excluir um recebimento
export function useDeletePayment(orderId: number) {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: async (paymentId: number) => {
      const response = await apiRequest('DELETE', `/api/orders/${orderId}/payments/${paymentId}`);
      return response.json();
    },
    onSuccess: async () => {
      console.log('🎯 Exclusão bem-sucedida, forçando refresh completo...');

      // Usar função utilitária para forçar refresh completo
      await forceRefreshOrderData(queryClient, orderId);

      // Invalidar lista de pedidos
      await queryClient.invalidateQueries({ queryKey: ['/api/orders'] });

      toast({
        title: t('payments.success') || 'Sucesso',
        description: t('payments.paymentDeleted') || 'Recebimento excluído com sucesso',
      });
    },
    onError: (error: any) => {
      console.error('Erro ao excluir recebimento:', error);
      toast({
        title: t('payments.error') || 'Erro',
        description: error.message || t('payments.deleteError') || 'Erro ao excluir recebimento',
        variant: 'destructive',
      });
    },
  });
}

// Hook para recalcular status de pagamento
export function useRecalculatePaymentStatus(orderId: number) {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: async () => {
      console.log('🔄 Iniciando recálculo manual do status de pagamento para pedido:', orderId);
      const response = await apiRequest('POST', `/api/orders/${orderId}/payments/recalculate`);
      const result = await response.json();
      console.log('✅ Resposta do recálculo:', result);
      return result;
    },
    onSuccess: async (data) => {
      console.log('🎯 Recálculo bem-sucedido, forçando refresh completo...');

      // Invalidar apenas as queries específicas de pagamentos e status
      // sem remover completamente do cache para evitar perda de estado
      await Promise.all([
        queryClient.invalidateQueries({ 
          queryKey: [`/api/orders/${orderId}`],
          refetchType: 'active' // Apenas refetch se a query estiver ativa
        }),
        queryClient.invalidateQueries({ 
          queryKey: [`/api/orders/${orderId}/revisions`],
          refetchType: 'active'
        }),
        queryClient.invalidateQueries({ 
          queryKey: ['orderPayments', orderId],
          refetchType: 'active'
        }),
        // Não invalidar a lista de pedidos para evitar refresh desnecessário
      ]);

      console.log('📊 Novo status de pagamento:', {
        orderStatus: data.paymentStatus,
        revisionStatus: data.revisionPaymentStatus
      });

      toast({
        title: t('payments.success') || 'Sucesso',
        description: t('payments.statusRecalculated') || 'Status de pagamento recalculado',
      });
    },
    onError: (error: any) => {
      console.error('Erro ao recalcular status:', error);
      toast({
        title: t('payments.error') || 'Erro',
        description: error.message || t('payments.recalculateError') || 'Erro ao recalcular status',
        variant: 'destructive',
      });
    },
  });
}

// Função utilitária para calcular total recebido
export function calculateTotalReceived(payments: OrderPayment[]): number {
  return payments.reduce((total, payment) => total + payment.valor, 0);
}

// Função utilitária para calcular valor pendente
export function calculatePendingAmount(orderTotal: number, payments: OrderPayment[]): number {
  const totalReceived = calculateTotalReceived(payments);
  return Math.max(0, orderTotal - totalReceived);
}

// Função utilitária para obter cor do status de pagamento
export function getPaymentStatusColor(status: string): string {
  switch (status) {
    case 'recebido':
      return 'text-green-600 bg-green-50 border-green-200';
    case 'parcialmente_recebido':
      return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    case 'em_disputa':
      return 'text-red-600 bg-red-50 border-red-200';
    case 'estornado':
      return 'text-red-800 bg-red-100 border-red-300';
    case 'cancelado':
      return 'text-gray-600 bg-gray-50 border-gray-200';
    case 'pendente':
    default:
      return 'text-blue-600 bg-blue-50 border-blue-200';
  }
}

// Função utilitária para obter texto do status de pagamento
export function getPaymentStatusText(status: string, t: any): string {
  switch (status) {
    case 'recebido':
      return t('payments.status.received') || 'Recebido';
    case 'parcialmente_recebido':
      return t('payments.status.partiallyReceived') || 'Parcialmente Recebido';
    case 'em_disputa':
      return t('payments.status.disputed') || 'Em Disputa';
    case 'estornado':
      return t('payments.status.refunded') || 'Estornado';
    case 'cancelado':
      return t('payments.status.cancelled') || 'Cancelado';
    case 'pendente':
    default:
      return t('payments.status.pending') || 'Pendente';
  }
}
