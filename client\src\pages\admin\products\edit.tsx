import { useEffect, useState, useRef } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useTranslation } from "@/hooks/useTranslation";
import { useStore } from "@/context/StoreContext";
import { uploadProductImage } from "@/lib/uploadUtils";
import { apiRequest } from "@/lib/queryClient";
import { useLocation } from "wouter";
import AdminLayout from "@/components/admin/AdminLayout";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { X, Plus, Trash2, ArrowLeft, ImagePlus, Image as ImageIcon } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

// Variation option schema
const variationOptionSchema = z.object({
  id: z.number().optional(),
  name: z.string().min(1, { message: "Option name is required" }),
  price: z.coerce.number().default(0),
});

// Variation schema
const variationSchema = z.object({
  id: z.number().optional(),
  name: z.string().min(1, { message: "Variation name is required" }),
  required: z.boolean().default(false),
  multipleChoice: z.boolean().default(false),
  minSelections: z.coerce.number().min(0).optional(),
  maxSelections: z.coerce.number().min(1).optional(),
  options: z.array(variationOptionSchema).default([]),
});

// Product form schema
const productSchema = z.object({
  name: z.string().min(1, { message: "Product name is required" }),
  description: z.string().optional(),
  price: z.coerce.number().positive({ message: "Price must be positive" }),
  images: z.array(z.string()).default([]),
  categoryId: z.coerce.number().optional(),
  inStock: z.boolean().default(true),
  hasVariations: z.boolean().default(false),
  variations: z.array(variationSchema).default([]),
});

type ProductFormValues = z.infer<typeof productSchema>;

// Componente de Variação para o formulário de edição que evita o problema do React Hook chamado em um loop
function VariationFieldset({
  variationIndex,
  control,
  onRemove,
  t,
  watch,
  store
}: {
  variationIndex: number;
  control: any;
  onRemove: () => void;
  t: (key: string) => string | undefined;
  watch: any;
  store: any;
}) {
  // Criar field array separado para opções desta variação
  const optionsField = useFieldArray({
    control,
    name: `variations.${variationIndex}.options`
  });

  // Observar se é multiple choice
  const isMultipleChoice = watch(`variations.${variationIndex}.multipleChoice`);

  return (
    <div className="border rounded-lg p-3 space-y-3">
      <div className="flex justify-between items-center">
        <h4 className="font-medium">{t('products.variation') || "Variação"} {variationIndex + 1}</h4>
        <Button
          type="button"
          variant="ghost"
          size="icon"
          onClick={onRemove}
        >
          <Trash2 className="h-4 w-4 text-error" />
        </Button>
      </div>

      {/* Variation Name */}
      <div className="grid grid-cols-1 gap-3">
        <FormField
          control={control}
          name={`variations.${variationIndex}.name`}
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('products.variationName') || "Nome da variação"}</FormLabel>
              <FormControl>
                <Input {...field} placeholder={t('products.variationNamePlaceholder') || "Ex: Tamanho, Sabor, Cor"} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Variation Required */}
        <FormField
          control={control}
          name={`variations.${variationIndex}.required`}
          render={({ field }) => (
            <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-3">
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
              <div className="space-y-1 leading-none">
                <FormLabel>
                  {t('products.required') || "Obrigatório"}
                </FormLabel>
                <FormDescription>
                  {t('products.requiredDescription') || "O cliente precisa selecionar uma opção desta variação."}
                </FormDescription>
              </div>
            </FormItem>
          )}
        />

        {/* Multiple Choice */}
        <FormField
          control={control}
          name={`variations.${variationIndex}.multipleChoice`}
          render={({ field }) => (
            <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-3">
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
              <div className="space-y-1 leading-none">
                <FormLabel>
                  {t('products.multipleChoice') || "Múltipla escolha"}
                </FormLabel>
                <FormDescription>
                  {t('products.multipleChoiceDescription') || "O cliente pode escolher mais de uma opção."}
                </FormDescription>
              </div>
            </FormItem>
          )}
        />

        {/* Min/Max Selections */}
        {/* Only show min/max if multipleChoice is enabled */}
        {isMultipleChoice && (
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={control}
              name={`variations.${variationIndex}.minSelections`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('products.minSelections') || "Mínimo de escolhas"}</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min={0}
                      {...field}
                      onChange={(e) => field.onChange(e.target.valueAsNumber)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={control}
              name={`variations.${variationIndex}.maxSelections`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('products.maxSelections') || "Máximo de escolhas"}</FormLabel>
                  <div className="flex gap-2">
                    <FormControl>
                      <Input
                        type="number"
                        min={1}
                        {...field}
                        onChange={(e) => {
                          // Garantir que seja um número
                          const value = e.target.valueAsNumber;
                          console.log(`Valor max seleções alterado: ${value} (type: ${typeof value})`);
                          field.onChange(value);
                        }}
                        onBlur={() => {
                          // Log do valor ao perder o foco
                          console.log(`Valor max seleções ao perder foco: ${field.value} (type: ${typeof field.value})`);
                        }}
                      />
                    </FormControl>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        // Forçar número explícito
                        const valor = Number(field.value || 1);
                        console.log(`Setando explicitamente o valor: ${valor} (${typeof valor})`);
                        field.onChange(valor);
                      }}
                    >
                      Fixar
                    </Button>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )}
      </div>

      {/* Options Section */}
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <h5 className="text-sm font-medium">{t('products.options') || "Opções"}</h5>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => optionsField.append({ name: '', price: 0 })}
          >
            <Plus className="h-3 w-3 mr-1" />
            {t('products.addOption') || "Adicionar opção"}
          </Button>
        </div>

        {optionsField.fields.length === 0 && (
          <div className="text-center py-3 text-xs text-muted-foreground border rounded-md">
            {t('products.noOptions') || "Adicione opções para esta variação."}
          </div>
        )}

        <div className="space-y-2">
          {optionsField.fields.map((option, optionIndex) => (
            <div key={option.id} className="flex items-center space-x-2">
              <FormField
                control={control}
                name={`variations.${variationIndex}.options.${optionIndex}.name`}
                render={({ field }) => (
                  <FormItem className="flex-1">
                    <FormControl>
                      <Input {...field} placeholder={t('products.optionNamePlaceholder') || "Ex: Pequeno, Médio, Grande"} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={control}
                name={`variations.${variationIndex}.options.${optionIndex}.price`}
                render={({ field }) => {
                  // Imprimir valor atual para debug
                  console.log(`Campo price da opção ${optionIndex}:`, field.value);
                  return (
                    <FormItem className="w-32">
                      <FormControl>
                        <div className="relative">
                          <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm">
                            +{store?.currency || 'R$'}
                          </span>
                          <Input
                            type="number"
                            step="0.01"
                            min="0"
                            placeholder="0.00"
                            className="pl-12"
                            // Importante: Remover o spread do field para evitar conflito entre value e defaultValue
                            name={field.name}
                            ref={field.ref}
                            onBlur={field.onBlur}
                            // Usar value diretamente em vez de field.value para garantir que seja um número
                            value={field.value !== undefined && field.value !== null ? field.value : 0}
                            onChange={(e) => {
                              const value = e.target.value ? parseFloat(e.target.value) : 0;
                              field.onChange(value);
                              console.log(`Opção ${optionIndex} preço atualizado:`, value);
                            }}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )
                }}
              />
              <Button
                type="button"
                variant="ghost"
                size="icon"
                onClick={() => optionsField.remove(optionIndex)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

interface EditProductPageProps {
  id: number;
}

export default function EditProductPage({ id }: EditProductPageProps) {
  const { t } = useTranslation();
  const { store } = useStore();
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const imageRef1 = useRef<HTMLInputElement>(null);
  const imageRef2 = useRef<HTMLInputElement>(null);
  const imageRef3 = useRef<HTMLInputElement>(null);

  // Fetch product data
  const { data: product, isLoading: productLoading, error: productError } = useQuery({
    queryKey: ['/api/products', id],
    enabled: !!id && !isNaN(id),
  });

  // Log para depuração
  useEffect(() => {
    console.log('Product ID:', id);
    console.log('Product loading:', productLoading);
    console.log('Product error:', productError);
    console.log('Product data:', product);
  }, [id, product, productLoading, productError]);

  // Fetch categories
  const { data: categories, isLoading: categoriesLoading } = useQuery({
    queryKey: ['/api/categories'],
    enabled: !!store,
  });

  // Fetch variations for this product
  const { data: variations, isLoading: variationsLoading } = useQuery({
    queryKey: [`/api/products/${id}/variations`],
    queryFn: async () => {
      console.log(`API Request: GET /api/products/${id}/variations`);
      const response = await apiRequest('GET', `/api/products/${id}/variations`);
      // Verifica se response é um objeto Response ou já é o array de variações
      if (response instanceof Response) {
        return await response.json();
      }
      return response; // Se já for o array, retorna diretamente
    },
    enabled: !!id && !isNaN(id),
  });

  // Form setup
  const form = useForm<ProductFormValues>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      name: '',
      description: '',
      price: 0,
      images: [],
      categoryId: undefined,
      inStock: true,
      hasVariations: false,
      variations: [],
    },
  });

  // Field array for variations
  const variationsArray = useFieldArray({
    control: form.control,
    name: "variations"
  });

  // Update form when product data is loaded
  useEffect(() => {
    if (product && variations) {
      // Debug das variações antes de popular o formulário
      console.log('Variações recebidas:', variations);

      // Se temos variações, vamos garantir que os preços sejam números
      const processedVariations = variations.map(variation => ({
        ...variation,
        options: variation.options.map(option => ({
          ...option,
          price: typeof option.price === 'number' ? option.price :
            (typeof option.precoAdicional === 'number' ? option.precoAdicional : 0)
        }))
      }));

      console.log('Variações processadas:', processedVariations);

      form.reset({
        name: product.name,
        description: product.description || '',
        price: product.price,
        images: product.images || [],
        categoryId: product.categoryId,
        inStock: product.inStock,
        hasVariations: !!processedVariations && processedVariations.length > 0,
        variations: processedVariations || [],
      });
    }
  }, [product, variations, form]);

  // Update mutation
  const updateMutation = useMutation({
    mutationFn: async (data: ProductFormValues) => {
      return apiRequest('PATCH', `/api/products/${id}`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/products'] });
      queryClient.invalidateQueries({ queryKey: ['/api/products', id] });
      // Importante: Invalidar também a query de variações
      queryClient.invalidateQueries({ queryKey: [`/api/products/${id}/variations`] });
      console.log('Invalidated queries, including variations');

      toast({
        title: t('products.updateSuccess') || "Produto atualizado",
        description: t('products.updateSuccessDescription') || "O produto foi atualizado com sucesso.",
      });

      // Recarregar a página após o sucesso para garantir que todas as variações sejam carregadas
      setTimeout(() => {
        queryClient.refetchQueries({ queryKey: [`/api/products/${id}/variations`] });
        console.log('Refetching variations');
      }, 500);
    },
    onError: (error) => {
      toast({
        title: t('products.updateError') || "Erro ao atualizar produto",
        description: (error as Error).message || t('products.updateErrorDescription') || "Ocorreu um erro ao atualizar o produto. Tente novamente.",
        variant: "destructive",
      });
    },
  });

  // Handle form submission
  const onSubmit = async (values: ProductFormValues) => {
    // If hasVariations is false, clear the variations array
    const dataToSubmit = {
      ...values,
      price: Number(values.price), // Garantir que o preço seja um número
      variations: values.hasVariations ? values.variations : [],
    };

    // Garantir que categoryId seja null se for 0 ou undefined
    if (dataToSubmit.categoryId === 0 || dataToSubmit.categoryId === undefined) {
      dataToSubmit.categoryId = null;
    }
    console.log('CategoryId final:', dataToSubmit.categoryId);

    // Log detalhado dos dados antes da submissão
    console.log('Formulário a enviar:', JSON.stringify(dataToSubmit));
    console.log('Imagens a enviar:', JSON.stringify(dataToSubmit.images));

    // Log detalhado das variações e tipos
    if (dataToSubmit.variations && dataToSubmit.variations.length > 0) {
      dataToSubmit.variations.forEach((variation, index) => {
        console.log(`Variação ${index} - ${variation.name}:`);
        console.log(` - required: ${variation.required} (${typeof variation.required})`);
        console.log(` - multipleChoice: ${variation.multipleChoice} (${typeof variation.multipleChoice})`);
        console.log(` - minSelections: ${variation.minSelections} (${typeof variation.minSelections})`);
        console.log(` - maxSelections: ${variation.maxSelections} (${typeof variation.maxSelections})`);

        // Forçar conversão de maxSelections para número
        if (variation.maxSelections !== undefined) {
          variation.maxSelections = Number(variation.maxSelections);
          console.log(` - maxSelections após conversão: ${variation.maxSelections} (${typeof variation.maxSelections})`);
        }

        // Processar preços de cada opção
        if (variation.options && Array.isArray(variation.options)) {
          variation.options.forEach((option, optIdx) => {
            // Garantir que o preço seja um número
            const priceValue = option.price !== undefined ? Number(option.price) :
              (option.precoAdicional !== undefined ? Number(option.precoAdicional) : 0);

            // Atualizar ambos os campos price e precoAdicional para compatibilidade
            option.price = priceValue;
            option.precoAdicional = priceValue;

            console.log(`   - Opção ${optIdx} (${option.name}): preço ${priceValue} (${typeof priceValue})`);
          });
        }
      });
    }

    // Submit to API
    await updateMutation.mutateAsync(dataToSubmit);
  };

  // Handle image upload
  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const file = event.target.files?.[0];
    if (!file || !store) return;

    try {
      const imageUrl = await uploadProductImage(file, store.id);

      // Get current images array
      const currentImages = form.getValues('images') || [];

      // Create a new array with the new image at the specified index
      const newImages = [...currentImages];
      newImages[index] = imageUrl;

      console.log('Imagem carregada com sucesso no índice', index, 'URL:', imageUrl);
      console.log('Array de imagens atualizado:', newImages);

      // Update the form
      form.setValue('images', newImages, { shouldDirty: true, shouldTouch: true });

      // Force form update to ensure the images array is properly recognized
      form.trigger('images');
    } catch (error) {
      console.error('Image upload error:', error);
      toast({
        title: t('products.imageUploadError') || "Erro ao enviar imagem",
        description: (error as Error).message || t('products.imageUploadErrorDescription') || "Ocorreu um erro ao enviar a imagem. Tente novamente.",
        variant: "destructive",
      });
    }
  };

  // Render image input
  const renderImageInput = (index: number, inputRef: React.RefObject<HTMLInputElement>) => {
    const images = form.watch('images') || [];
    const hasImage = images[index];

    return (
      <div className="relative">
        <input
          type="file"
          accept="image/*"
          id={`image-${index}`}
          className="hidden"
          ref={inputRef}
          onChange={(e) => handleImageUpload(e, index)}
        />
        <Button
          type="button"
          variant={hasImage ? "outline" : "secondary"}
          className="w-full"
          onClick={() => inputRef.current?.click()}
        >
          {hasImage ? (
            <>
              <ImageIcon className="h-4 w-4 mr-2" />
              {t('products.changeImage') || "Alterar imagem"}
            </>
          ) : (
            <>
              <ImagePlus className="h-4 w-4 mr-2" />
              {index === 0
                ? (t('products.addMainImage') || "Adicionar imagem principal")
                : (t('products.addExtraImage') || `Adicionar imagem ${index + 1}`)}
            </>
          )}
        </Button>
      </div>
    );
  };

  // Render image previews
  const renderImagePreviews = (images: string[]) => {
    if (!images || images.length === 0) return null;

    return (
      <div className="grid grid-cols-3 gap-2 mt-3">
        {images.map((url, idx) => {
          if (!url) return null;
          return (
            <div key={idx} className="relative aspect-square rounded-md overflow-hidden border">
              <img src={url} alt={`Preview ${idx + 1}`} className="w-full h-full object-cover" />
              <button
                type="button"
                className="absolute top-1 right-1 bg-error text-white p-1 rounded-full"
                onClick={() => {
                  const newImages = [...images];
                  newImages[idx] = '';
                  console.log('Removendo imagem do índice', idx);
                  console.log('Novo array de imagens após remoção:', newImages);
                  form.setValue('images', newImages, { shouldDirty: true, shouldTouch: true });
                  form.trigger('images');
                }}
              >
                <X className="h-3 w-3" />
              </button>
            </div>
          );
        })}
      </div>
    );
  };

  if (productLoading || variationsLoading) {
    return (
      <AdminLayout>
        <div className="space-y-4">
          <Skeleton className="h-12 w-2/3" />
          <Skeleton className="h-24 w-full" />
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-12 w-full" />
          <Skeleton className="h-32 w-full" />
        </div>
      </AdminLayout>
    );
  }

  if (productError) {
    return (
      <AdminLayout>
        <div className="p-4 rounded-md bg-error/10 text-error">
          <h2 className="font-semibold text-lg">{t('products.loadingError') || "Erro ao carregar produto"}</h2>
          <p>{t('products.tryAgain') || "Tente novamente mais tarde."}</p>
          <Button variant="outline" className="mt-4" onClick={() => navigate('/admin/products')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t('common.back') || "Voltar"}
          </Button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title={t('products.editProduct') || "Editar produto"}>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <Breadcrumb className="mb-2">
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/admin">{t('common.dashboard') || "Dashboard"}</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbLink href="/admin/products">{t('common.products') || "Produtos"}</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbLink>{t('products.editProduct') || "Editar produto"}</BreadcrumbLink>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
          <Button variant="outline" onClick={() => navigate('/admin/products')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            {t('common.back') || "Voltar"}
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>{t('products.productDetails') || "Detalhes do produto"}</CardTitle>
            <CardDescription>
              {t('products.productDetailsDescription') || "Preencha os detalhes do produto."}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={(event) => {
                console.log('Form submit event triggered');
                event.preventDefault(); // Prevenir o envio automático do formulário
                // Não iremos fazer o handleSubmit aqui, pois o botão tem o comportamento manual
              }} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('products.productName') || "Nome do produto"}</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="price"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('products.productPrice') || "Preço"}</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                              {store?.currency || 'R$'}
                            </span>
                            <Input {...field} type="number" step="0.01" min="0" className="pl-10" />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t('products.productDescription') || "Descrição"}</FormLabel>
                      <FormControl>
                        <Textarea {...field} rows={4} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Category Selection */}
                {categories && Array.isArray(categories) && categories.length > 0 && (
                  <FormField
                    control={form.control}
                    name="categoryId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t('products.productCategory') || "Categoria"}</FormLabel>
                        <Select
                          onValueChange={(value) => {
                            console.log('Category value selected in edit:', value);
                            // Se o valor for "null" ou "0", definir como null
                            field.onChange(value === "null" || value === "0" ? null : parseInt(value));
                          }}
                          value={field.value?.toString() || ""}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder={t('products.selectCategory') || "Selecione uma categoria"} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="null">{t('products.noCategory') || "Sem categoria"}</SelectItem>
                            {categories.map((category: any) => (
                              <SelectItem key={category.id} value={category.id.toString()}>
                                {category.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                {/* Images Section */}
                <div>
                  <FormLabel>{t('products.productImages') || "Imagens do produto"}</FormLabel>
                  <FormDescription>
                    {t('products.imagesHelperText') || "Adicione até 3 imagens para o produto. A primeira será a imagem principal."}
                  </FormDescription>
                  <div className="space-y-2 mt-2">
                    {renderImageInput(0, imageRef1)}
                    {renderImageInput(1, imageRef2)}
                    {renderImageInput(2, imageRef3)}
                  </div>
                  {renderImagePreviews(form.watch('images') || [])}
                </div>

                <FormField
                  control={form.control}
                  name="hasVariations"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                      <div className="space-y-0.5">
                        <FormLabel>{t('products.hasVariations') || "Tem variações"}</FormLabel>
                        <FormDescription>
                          {t('products.hasVariationsDescription') || "Produto tem opções como tamanho, sabor, etc."}
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="inStock"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                      <div className="space-y-0.5">
                        <FormLabel>{t('products.inStock') || "Disponível em estoque"}</FormLabel>
                        <FormDescription>
                          {field.value ? t('products.available') || "Produto disponível para venda" : t('products.unavailable') || "Produto indisponível para venda"}
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                {/* Product Variations Section */}
                {form.watch("hasVariations") && (
                  <div className="space-y-4 border rounded-lg p-4">
                    <div className="flex justify-between items-center">
                      <h3 className="text-lg font-medium">{t('products.variations') || "Variações"}</h3>
                      <Button
                        type="button"
                        onClick={() => {
                          variationsArray.append({
                            name: "",
                            required: true,
                            multipleChoice: false,
                            options: [{ name: "", price: 0 }]
                          });
                        }}
                        variant="outline"
                        size="sm"
                      >
                        <Plus className="mr-1 h-4 w-4" />
                        {t('products.addVariation') || "Adicionar variação"}
                      </Button>
                    </div>

                    {variationsArray.fields.length === 0 && (
                      <div className="text-center py-4 text-muted-foreground">
                        {t('products.noVariations') || "Sem variações cadastradas. Clique em 'Adicionar variação' para começar."}
                      </div>
                    )}

                    {variationsArray.fields.map((variation, variationIndex) => (
                      <VariationFieldset
                        key={variation.id}
                        variationIndex={variationIndex}
                        control={form.control}
                        onRemove={() => variationsArray.remove(variationIndex)}
                        t={t}
                        watch={form.watch}
                        store={store}
                      />
                    ))}
                  </div>
                )}

                <CardFooter className="flex justify-between px-0">
                  <Button type="button" variant="outline" onClick={() => navigate('/admin/products')}>
                    {t('common.cancel') || "Cancelar"}
                  </Button>
                  <Button
                    type="button"
                    disabled={updateMutation.isPending}
                    onClick={(e) => {
                      e.preventDefault();
                      console.log('Submit button clicked manually in edit page');
                      // Chamar manualmente o onSubmit com os valores atuais do form
                      const values = form.getValues();
                      onSubmit(values);
                    }}
                  >
                    {updateMutation.isPending ? t('common.saving') || "Salvando..." : t('common.save') || "Salvar"}
                  </Button>
                </CardFooter>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}