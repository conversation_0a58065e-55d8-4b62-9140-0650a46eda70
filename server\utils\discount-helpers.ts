/**
 * Utilitários para cálculo de desconto
 * Este arquivo contém funções para calcular descontos com precisão matemática
 */

/**
 * Calcula o valor de um desconto com base no tipo e valor
 * @param subtotal Valor do subtotal
 * @param discount Valor do desconto (valor fixo ou percentual)
 * @param discountType Tipo de desconto: 'fixed' ou 'percentage'
 * @returns Objeto com valor calculado do desconto e o percentual original (se aplicável)
 */
export function calculateDiscount(
  subtotal: number,
  discount: number,
  discountType: 'fixed' | 'percentage'
): { finalDiscount: number, originalPercentage: number | null } {
  // Garantir que o subtotal seja um número válido
  if (isNaN(subtotal) || subtotal < 0) {
    throw new Error('Subtotal inválido para cálculo de desconto');
  }

  // Garantir que o desconto seja um número válido
  if (isNaN(discount)) {
    throw new Error('Valor de desconto inválido');
  }

  // Para desconto percentual, calcular o valor real com base no subtotal
  if (discountType === 'percentage') {
    // Limitar percentual a 100%
    const percentValue = Math.min(discount, 100);

    // Calcular o valor do desconto
    const discountValue = (percentValue / 100) * subtotal;

    // Arredondar para 2 casas decimais com precisão matemática
    const finalDiscount = parseFloat(discountValue.toFixed(2));

    console.log('Cálculo de desconto percentual:', {
      percentValue,
      subtotal,
      calculoExplicito: `${percentValue}% de ${subtotal} = ${discountValue}`,
      finalDiscount
    });

    // Garantir que o desconto não seja maior que o subtotal
    return {
      finalDiscount: Math.min(finalDiscount, subtotal),
      originalPercentage: percentValue
    };
  } else {
    // Para desconto de valor fixo, usar o valor diretamente
    // Garantir que o desconto não seja maior que o subtotal
    console.log('Cálculo de desconto fixo:', {
      valorFixo: discount,
      subtotal,
      finalDiscount: Math.min(discount, subtotal)
    });

    return {
      finalDiscount: Math.min(discount, subtotal),
      originalPercentage: null
    };
  }
}

/**
 * Calcula o total com base no subtotal, desconto e taxa de entrega
 * @param subtotal Valor do subtotal
 * @param discount Valor do desconto
 * @param deliveryFee Taxa de entrega
 * @returns Total calculado
 */
export function calculateTotal(subtotal: number, discount: number, deliveryFee: number): number {
  // Garantir valores válidos
  subtotal = isNaN(subtotal) ? 0 : subtotal;
  discount = isNaN(discount) ? 0 : discount;
  deliveryFee = isNaN(deliveryFee) ? 0 : deliveryFee;

  // Calcular o total (subtotal - desconto + taxa de entrega)
  const total = Math.max(0, subtotal - discount + deliveryFee);

  // Arredondar para 2 casas decimais com precisão matemática
  return parseFloat(total.toFixed(2));
}

/**
 * Calcula o subtotal a partir dos itens
 * @param items Itens com preço e quantidade
 * @returns Subtotal calculado
 */
export function calculateSubtotalFromItems(items: Array<{ subtotal: number } | { price: number, quantity: number }>): number {
  if (!items || items.length === 0) {
    return 0;
  }

  // Calcular o subtotal somando todos os itens
  const subtotal = items.reduce((sum, item) => {
    // Verificar se o item tem o campo subtotal
    if ('subtotal' in item) {
      return sum + (isNaN(item.subtotal) ? 0 : item.subtotal);
    }

    // Caso contrário, calcular com base no preço e quantidade
    if ('price' in item && 'quantity' in item) {
      return sum + (isNaN(item.price) || isNaN(item.quantity) ? 0 : item.price * item.quantity);
    }

    return sum;
  }, 0);

  // Arredondar para 2 casas decimais com precisão matemática
  return parseFloat(subtotal.toFixed(2));
}