-- Script para adicionar campos de endereço da loja e email de contato à tabela stores
-- Este script deve ser executado no Supabase SQL Editor

-- Verificar se a tabela stores existe
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'stores') THEN
        -- Adicionar campos de endereço
        ALTER TABLE stores 
        ADD COLUMN IF NOT EXISTS address_street VARCHAR(255),
        ADD COLUMN IF NOT EXISTS address_number VARCHAR(50),
        ADD COLUMN IF NOT EXISTS address_complement VARCHAR(255),
        ADD COLUMN IF NOT EXISTS address_neighborhood VARCHAR(255),
        ADD COLUMN IF NOT EXISTS address_city VARCHAR(255),
        ADD COLUMN IF NOT EXISTS address_state VARCHAR(100),
        ADD COLUMN IF NOT EXISTS contact_email VARCHAR(255);
        
        RAISE NOTICE 'Campos de endereço e email de contato adicionados à tabela stores';
    ELSE
        RAISE EXCEPTION 'A tabela stores não existe no banco de dados';
    END IF;
END
$$;
