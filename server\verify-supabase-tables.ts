import { createClient } from '@supabase/supabase-js';
import 'dotenv/config';

// Obter variáveis do Supabase
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Supabase URL ou Service Key não encontrados nas variáveis de ambiente!');
  process.exit(1);
}

console.log(`Conectando ao Supabase: ${supabaseUrl}`);

// Inicializar cliente Supabase com service key
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Lista das tabelas que devem existir
const requiredTables = [
  'users',
  'stores',
  'categories',
  'products',
  'product_variations',
  'variation_options',
  'customers',
  'orders',
  'order_items',
  'store_visits',
  'cart_items'
];

// Função para verificar se uma tabela existe
async function checkTableExists(tableName: string): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from(tableName)
      .select('count')
      .limit(1);
      
    if (error && error.code === 'PGRST116') {
      // Erro de relação não encontrada
      return false;
    }
    
    // Se chegou aqui, a tabela existe
    return true;
  } catch (error) {
    console.error(`Erro ao verificar tabela ${tableName}:`, error);
    return false;
  }
}

// Função principal para verificar tabelas
async function verifyTables() {
  console.log('Verificando tabelas no Supabase...');
  let allTablesExist = true;
  
  for (const tableName of requiredTables) {
    const exists = await checkTableExists(tableName);
    
    if (exists) {
      console.log(`✓ Tabela "${tableName}" existe.`);
    } else {
      console.error(`❌ Tabela "${tableName}" NÃO existe!`);
      allTablesExist = false;
    }
  }
  
  if (allTablesExist) {
    console.log('✅ Todas as tabelas necessárias foram criadas com sucesso!');
  } else {
    console.error('❌ Atenção: Algumas tabelas não foram encontradas!');
    console.log(`\nPara criar as tabelas no Supabase:\n`);
    console.log(`1. Acesse o painel do Supabase: ${supabaseUrl}`);
    console.log(`2. Vá para "SQL Editor" no menu lateral`);
    console.log(`3. Clique em "New Query"`);
    console.log(`4. Copie e cole o conteúdo do arquivo 'migrations/execute_me_in_supabase.sql'`);
    console.log(`5. Clique em "Run" para executar o script SQL`);
  }
}

// Executar a função principal
verifyTables();
