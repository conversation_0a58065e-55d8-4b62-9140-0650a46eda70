
# PLANO DE IMPLEMENTAÇÃO - DASHBOARD ADMINISTRATIVO DOCE MENU

## 📋 RESUMO EXECUTIVO

### Objetivo
Dashboard administrativo **minimalista e funcional** para gestão operacional do Doce Menu, implementado em **4-5 dias úteis** com foco em resolver problemas críticos do dia a dia.

### Abordagem MVP
- **Funcionalidade > Estética**: Interface limpa mas altamente funcional
- **Dados Híbridos**: Considera revisões ativas para valores financeiros precisos
- **Zero Dependências Novas**: Máximo aproveitamento da base existente
- **Implementação Rápida**: Valor entregue em menos de 1 semana

---

## 🏗️ ESTRUTURA DO DASHBOARD

### Layout Responsivo (Mobile-First)

```
┌─────────────────────────────────────────┐
│ 📊 RESUMO RÁPIDO (4 cards)              │
├─────────────────────────────────────────┤
│ 📋 LISTA DE ENCOMENDAS PENDENTES        │
├─────────────────────────────────────────┤
│ 📈 ATIVIDADE NO SITE (4 cards)          │
├─────────────────────────────────────────┤
│ 🏆 TOP 3 PRODUTOS MAIS VENDIDOS         │
├─────────────────────────────────────────┤
│ 👥 CLIENTES RECENTES                    │
└─────────────────────────────────────────┘
```

### Seções Detalhadas

#### 1. **Resumo Rápido (Cards Principais)**
- 🔢 **Encomendas Pendentes**: Contagem total (status: pending, preparing, ready)
- 💰 **Valor Recebido no Mês**: Total financeiro já recebido (payment_status: 'recebido')
- ⏰ **Próxima Entrega**: Cliente + data/hora da próxima entrega
- ⚠️ **Alertas Importantes**: pagamentos pendentes

#### 2. **Lista de Encomendas Pendentes**
- **Colunas**: Cliente | Produtos | Total | Status | Entrega
- **Ações Rápidas**:
  - "Ver Detalhes" → Navega para página do pedido
- **Filtros Simples**: Por status e data de entrega

#### 3. **Atividade no Site (Grid 2x2)**
- 📊 **Visitas no Mês**: Total de visitas únicas (store_visits)
- 📅 **Visitas Hoje**: Visitas do dia atual
- 📈 **Taxa de Conversão**: Pedidos / Visitas únicas
- 🔄 **Clientes Recorrentes**: Clientes com mais de 1 pedido

#### 4. **Top 3 Produtos Mais Vendidos**
- **Dados**: Nome do produto + quantidade vendida no mês
- **Consideração**: Usar dados híbridos (order_revisions quando ativo)

#### 5. **Clientes Recentes**
- **Lista**: Últimos 5 clientes que fizeram pedidos
- **Ações**: "Ver Histórico", "Enviar WhatsApp"

---

## 🔄 DADOS HÍBRIDOS SIMPLIFICADOS

### Estratégia
**Regra de Ouro**: Status operacional do pedido original + valores financeiros da revisão ativa

### View SQL Essencial

```sql
CREATE VIEW effective_orders AS
SELECT
  o.id,
  o.status,                                    -- Status operacional (sempre original)
  o.created_at,
  o.customer_id,
  c.name as customer_name,
  c.whatsapp as customer_phone,
  -- Valores financeiros (híbridos)
  COALESCE(r.total, o.total) as total,
  COALESCE(r.subtotal, o.subtotal) as subtotal,
  COALESCE(r.payment_status, o.payment_status) as payment_status,
  -- Dados logísticos (híbridos)
  COALESCE(r.receiving_date, o.receiving_date) as receiving_date,
  COALESCE(r.receiving_time, o.receiving_time) as receiving_time,
  COALESCE(r.receiving_method, o.receiving_method) as receiving_method,
  -- Metadados
  (r.id IS NOT NULL) as has_revision,
  -- Verificação de atraso
  CASE
    WHEN COALESCE(r.receiving_date, o.receiving_date) < CURRENT_DATE
    THEN true
    ELSE false
  END as is_late
FROM orders o
LEFT JOIN order_revisions r ON o.id = r.order_id AND r.is_current = true
LEFT JOIN customers c ON o.customer_id = c.id
WHERE o.store_id = $1;
```

---

## � ESPECIFICAÇÕES TÉCNICAS

### Interface de Dados

```typescript
interface DashboardData {
  // Resumo rápido
  summary: {
    pendingOrders: number;
    monthlyRevenue: number;
    nextDelivery: {
      customerName: string;
      deliveryTime: string;
    } | null;
    alertsCount: number;
  };

  // Lista de pedidos pendentes
  pendingOrders: {
    id: number;
    customerName: string;
    customerPhone: string;
    products: string; // Lista concatenada de produtos
    total: number;
    status: string;
    deliveryTime: string;
    isLate: boolean;
  }[];

  // Atividade do site
  siteActivity: {
    monthlyVisits: number;
    todayVisits: number;
    conversionRate: number;
    returningCustomers: number;
  };

  // Top produtos
  topProducts: {
    name: string;
    totalSold: number;
    revenue: number;
  }[];

  // Clientes recentes
  recentCustomers: {
    id: number;
    name: string;
    phone: string;
    lastOrderDate: string;
    totalOrders: number;
  }[];
}
```

### Componentes Principais

#### MetricCard Reutilizável
```typescript
interface MetricCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  color?: 'blue' | 'green' | 'orange' | 'red';
  subtitle?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
}

export function MetricCard({ title, value, icon, color = 'blue', subtitle, action }: MetricCardProps) {
  return (
    <Card className="p-4 hover:shadow-md transition-shadow">
      <div className="flex items-start justify-between">
        <div className="flex items-center space-x-3">
          <div className={`p-2 rounded-lg bg-${color}-100`}>
            <div className={`text-${color}-600 w-5 h-5`}>{icon}</div>
          </div>
          <div>
            <p className="text-sm text-gray-600">{title}</p>
            <p className="text-2xl font-bold">{value}</p>
            {subtitle && <p className="text-xs text-gray-500">{subtitle}</p>}
          </div>
        </div>
        {action && (
          <Button variant="ghost" size="sm" onClick={action.onClick}>
            {action.label}
          </Button>
        )}
      </div>
    </Card>
  );
}
```

#### Hook Principal
```typescript
export function useDashboardData() {
  return useQuery({
    queryKey: ['dashboard'],
    queryFn: () => apiRequest('/api/dashboard'),
    refetchInterval: 60000, // 1 minuto
    staleTime: 30000, // 30 segundos
  });
}
```

---

## � CRONOGRAMA DE IMPLEMENTAÇÃO (4-5 dias)

### **DIA 1: Fundação (8h)**

#### Manhã (4h)
- [ ] **Criar view `effective_orders`** no banco de dados
- [ ] **Implementar rota `/api/dashboard`** no backend
- [ ] **Criar interfaces TypeScript** para dados do dashboard
- [ ] **Setup da estrutura de pastas** do dashboard

#### Tarde (4h)
- [ ] **Implementar hook `useDashboardData`**
- [ ] **Criar componente `MetricCard`** reutilizável
- [ ] **Estrutura básica do `DashboardMVP.tsx`**
- [ ] **Implementar seção de resumo rápido** (4 cards)

### **DIA 2: Lista de Pedidos (8h)**

#### Manhã (4h)
- [ ] **Componente `PendingOrdersList`**
- [ ] **Lógica de dados híbridos** para pedidos pendentes
- [ ] **Estados de loading e erro**
- [ ] **Responsividade mobile** da lista

#### Tarde (4h)
- [ ] **Ações rápidas**: "Ver Detalhes" e "Confirmar Recebimento"
- [ ] **Filtros básicos** por status e data
- [ ] **Indicadores visuais** para pedidos atrasados
- [ ] **Integração com navegação** existente

### **DIA 3: Atividade do Site (8h)**

#### Manhã (4h)
- [ ] **Queries para métricas de visitação** (store_visits)
- [ ] **Cálculo de taxa de conversão**
- [ ] **Identificação de clientes recorrentes**
- [ ] **Componente `AtividadeSiteGrid`**

#### Tarde (4h)
- [ ] **Top 3 produtos mais vendidos** (considerando revisões)
- [ ] **Lista de clientes recentes**
- [ ] **Ações para clientes**: Ver histórico, WhatsApp
- [ ] **Integração completa** de todas as seções

### **DIA 4: Polimento e Testes (8h)**

#### Manhã (4h)
- [ ] **Otimizações de performance**
- [ ] **Tratamento de casos extremos** (sem dados, erros)
- [ ] **Melhorias de UX/UI**
- [ ] **Testes manuais completos**

#### Tarde (4h)
- [ ] **Responsividade final** em diferentes dispositivos
- [ ] **Acessibilidade básica**
- [ ] **Documentação** do código
- [ ] **Preparação para deploy**

### **DIA 5: Buffer e Melhorias (4-8h)**
- [ ] **Correções de bugs** identificados
- [ ] **Melhorias baseadas em feedback**
- [ ] **Pequenos ajustes de UX**
- [ ] **Testes finais**

---

## ✅ CRITÉRIOS DE SUCESSO

### Performance
- [ ] Dashboard carrega em **< 2 segundos**
- [ ] Dados atualizados automaticamente a cada **1 minuto**
- [ ] Interface responsiva em **mobile e desktop**

### Funcionalidade
- [ ] **Dados financeiros corretos** (considerando revisões ativas)
- [ ] **Alertas funcionais** para pedidos atrasados
- [ ] **Ações rápidas** operacionais (Ver Detalhes, Confirmar Recebimento)

### Usabilidade
- [ ] **Interface intuitiva** sem necessidade de treinamento
- [ ] **Navegação fluida** entre dashboard e outras páginas
- [ ] **Estados de loading** claros e informativos

---

## 🛠️ IMPLEMENTAÇÃO TÉCNICA

### Estrutura de Arquivos

```
client/src/components/admin/dashboard/
├── DashboardMVP.tsx              # Componente principal
├── MetricCard.tsx                # Card reutilizável
├── PendingOrdersList.tsx         # Lista de pedidos pendentes
├── AtividadeSiteGrid.tsx         # Grid de atividade do site
├── TopProductsList.tsx           # Top 3 produtos
├── RecentCustomersList.tsx       # Clientes recentes
└── hooks/
    └── useDashboardData.ts       # Hook para dados

server/routes/
└── dashboard.ts                  # Rota única para dashboard
```

### API Endpoint

```typescript
// GET /api/dashboard
router.get('/dashboard', async (req, res) => {
  try {
    const storeId = req.user.storeId;

    // Buscar todos os dados em paralelo
    const [
      summaryData,
      pendingOrders,
      siteActivity,
      topProducts,
      recentCustomers
    ] = await Promise.all([
      getSummaryData(storeId),
      getPendingOrders(storeId),
      getSiteActivity(storeId),
      getTopProducts(storeId),
      getRecentCustomers(storeId)
    ]);

    res.json({
      summary: summaryData,
      pendingOrders,
      siteActivity,
      topProducts,
      recentCustomers
    });
  } catch (error) {
    res.status(500).json({ error: 'Erro ao carregar dashboard' });
  }
});
```

### Otimizações

```sql
-- Índices essenciais para performance
CREATE INDEX idx_orders_store_status ON orders(store_id, status);
CREATE INDEX idx_order_revisions_current ON order_revisions(order_id, is_current);
CREATE INDEX idx_store_visits_store_date ON store_visits(store_id, created_at);
CREATE INDEX idx_customers_store ON customers(store_id);
```

---

## 🎯 PRÓXIMOS PASSOS

### Implementação Imediata
1. **Executar script SQL** para criar view `effective_orders`
2. **Implementar rota `/api/dashboard`** no backend
3. **Criar componente `DashboardMVP.tsx`** no frontend

### Validação Contínua
- **Teste diário** com dados reais
- **Feedback direto** do usuário administrador
- **Ajustes imediatos** baseados no uso

### Evolução Futura
- **Filtros avançados** por período
- **Gráficos simples** de tendência
- **Notificações push** para alertas críticos
- **Exportação básica** de relatórios

---

**Este plano garante um dashboard funcional e útil, implementado rapidamente sem comprometer a qualidade ou a experiência do usuário.**
