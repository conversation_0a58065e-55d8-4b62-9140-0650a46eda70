import { Request, Response, NextFunction } from 'express';
import { storage } from '../storage';

/**
 * Middleware para verificar se o usuário é um super-administrador global
 * Deve ser usado após o middleware de autenticação padrão (isAuthenticated)
 */
export const requireGlobalAdmin = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    console.log('=== MIDDLEWARE GLOBAL ADMIN ===');
    console.log('Verificando permissões de super-administrador...');

    // Obter o UID do Firebase do request (definido pelo middleware isAuthenticated)
    console.log('DEBUG - req.user:', (req as any).user);
    console.log('DEBUG - req.body.uid:', req.body.uid);
    console.log('DEBUG - req.query.uid:', req.query.uid);

    const firebaseUid = (req as any).user?.uid || req.body.uid || req.query.uid;
    console.log('DEBUG - firebaseUid final:', firebaseUid);

    if (!firebaseUid) {
      console.log('Firebase UID não encontrado no request');
      res.status(401).json({
        message: 'Usuário não autenticado',
        code: 'UNAUTHENTICATED'
      });
      return;
    }

    // Buscar o usuário no banco de dados
    console.log('DEBUG - Buscando usuário com Firebase UID:', firebaseUid);
    const user = await storage.getUserByFirebaseUid(firebaseUid);

    console.log('DEBUG - Usuário retornado do storage:', user);

    if (!user) {
      console.log('Usuário não encontrado no banco de dados:', firebaseUid);
      res.status(404).json({
        message: 'Usuário não encontrado',
        code: 'USER_NOT_FOUND'
      });
      return;
    }

    // Verificar se o usuário é um super-administrador global
    // Suporte para ambos os formatos: camelCase e snake_case
    const isGlobalAdmin = user.isGlobalAdmin || (user as any).is_global_admin || false;

    console.log('DEBUG - Objeto user completo:', JSON.stringify(user, null, 2));
    console.log('DEBUG - isGlobalAdmin final:', isGlobalAdmin);

    if (!isGlobalAdmin) {
      console.log('Usuário não tem permissões de super-administrador:', user.email);
      res.status(403).json({
        message: 'Acesso negado. Permissões de super-administrador necessárias.',
        code: 'INSUFFICIENT_PERMISSIONS'
      });
      return;
    }

    console.log('Super-administrador verificado com sucesso:', user.email);
    
    // Adicionar informações do usuário ao request para uso posterior
    req.user = user;
    
    next();
  } catch (error) {
    console.error('Erro no middleware de super-administrador:', error);
    res.status(500).json({ 
      message: 'Erro interno do servidor',
      code: 'INTERNAL_ERROR'
    });
  }
};

/**
 * Middleware mais flexível que verifica se o usuário é admin global
 * mas não bloqueia o acesso, apenas adiciona a informação ao request
 */
export const checkGlobalAdmin = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const firebaseUid = (req as any).user?.uid || req.body.uid || req.query.uid;

    if (firebaseUid) {
      const user = await storage.getUserByFirebaseUid(firebaseUid);
      if (user) {
        req.user = user;
        req.isGlobalAdmin = user.isGlobalAdmin || false;
      }
    }

    next();
  } catch (error) {
    console.error('Erro ao verificar status de admin global:', error);
    // Não bloqueia o request, apenas continua sem as informações
    next();
  }
};

// Estender a interface Request para incluir as novas propriedades
declare global {
  namespace Express {
    interface Request {
      user?: any;
      isGlobalAdmin?: boolean;
    }
  }
}
