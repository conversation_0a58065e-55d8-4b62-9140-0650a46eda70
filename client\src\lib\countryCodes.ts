// Lista de códigos de países para uso no WhatsApp

export interface CountryCode {
  id: string;    // ID único para cada país
  code: string;  // Código telefônico
  name: string;
  flag?: string;
}

export const countryCodes: CountryCode[] = [
  { id: "br", code: "+55", name: "Brasil", flag: "🇧🇷" },
  { id: "us", code: "+1 (EUA)", name: "Estados Unidos", flag: "🇺🇸" },
  { id: "ca", code: "+1 (CA)", name: "Canad<PERSON>", flag: "🇨🇦" },
  { id: "gb", code: "+44", name: "Reino Unido", flag: "🇬🇧" },
  { id: "pt", code: "+351", name: "Portugal", flag: "🇵🇹" },
  { id: "es", code: "+34", name: "Espan<PERSON>", flag: "🇪🇸" },
  { id: "fr", code: "+33", name: "Fr<PERSON><PERSON>", flag: "🇫🇷" },
  { id: "de", code: "+49", name: "<PERSON><PERSON><PERSON>", flag: "🇩🇪" },
  { id: "it", code: "+39", name: "<PERSON><PERSON><PERSON>", flag: "🇮🇹" },
  { id: "jp", code: "+81", name: "<PERSON><PERSON><PERSON>", flag: "🇯🇵" },
  { id: "cn", code: "+86", name: "China", flag: "🇨🇳" },
  { id: "ru", code: "+7 (RU)", name: "Rússia", flag: "🇷🇺" },
  { id: "au", code: "+61", name: "Austrália", flag: "🇦🇺" },
  { id: "ar", code: "+54", name: "Argentina", flag: "🇦🇷" },
  { id: "cl", code: "+56", name: "Chile", flag: "🇨🇱" },
  { id: "co", code: "+57", name: "Colômbia", flag: "🇨🇴" },
  { id: "mx", code: "+52", name: "México", flag: "🇲🇽" },
  { id: "pe", code: "+51", name: "Peru", flag: "🇵🇪" },
  { id: "ve", code: "+58", name: "Venezuela", flag: "🇻🇪" },
  { id: "ec", code: "+593", name: "Equador", flag: "🇪🇨" },
  { id: "uy", code: "+598", name: "Uruguai", flag: "🇺🇾" },
  { id: "py", code: "+595", name: "Paraguai", flag: "🇵🇾" },
  { id: "bo", code: "+591", name: "Bolívia", flag: "🇧🇴" },
  { id: "kr", code: "+82", name: "Coreia do Sul", flag: "🇰🇷" },
  { id: "kp", code: "+850", name: "Coreia do Norte", flag: "🇰🇵" },
  { id: "ae", code: "+971", name: "Emirados Árabes", flag: "🇦🇪" },
  { id: "sa", code: "+966", name: "Arábia Saudita", flag: "🇸🇦" },
  { id: "eg", code: "+20", name: "Egito", flag: "🇪🇬" },
  { id: "za", code: "+27", name: "África do Sul", flag: "🇿🇦" },
  { id: "ng", code: "+234", name: "Nigéria", flag: "🇳🇬" },
  { id: "in", code: "+91", name: "Índia", flag: "🇮🇳" },
  { id: "pk", code: "+92", name: "Paquistão", flag: "🇵🇰" },
  { id: "vn", code: "+84", name: "Vietnã", flag: "🇻🇳" },
  { id: "id", code: "+62", name: "Indonésia", flag: "🇮🇩" },
  { id: "my", code: "+60", name: "Malásia", flag: "🇲🇾" },
  { id: "sg", code: "+65", name: "Singapura", flag: "🇸🇬" },
  { id: "th", code: "+66", name: "Tailândia", flag: "🇹🇭" },
  { id: "ph", code: "+63", name: "Filipinas", flag: "🇵🇭" },
  { id: "nz", code: "+64", name: "Nova Zelândia", flag: "🇳🇿" },
  { id: "nl", code: "+31", name: "Holanda", flag: "🇳🇱" },
  { id: "be", code: "+32", name: "Bélgica", flag: "🇧🇪" },
  { id: "ch", code: "+41", name: "Suíça", flag: "🇨🇭" },
  { id: "at", code: "+43", name: "Áustria", flag: "🇦🇹" },
  { id: "no", code: "+47", name: "Noruega", flag: "🇳🇴" },
  { id: "se", code: "+46", name: "Suécia", flag: "🇸🇪" },
  { id: "dk", code: "+45", name: "Dinamarca", flag: "🇩🇰" },
  { id: "fi", code: "+358", name: "Finlândia", flag: "🇫🇮" },
  { id: "gr", code: "+30", name: "Grécia", flag: "🇬🇷" },
  { id: "pl", code: "+48", name: "Polônia", flag: "🇵🇱" },
  { id: "hu", code: "+36", name: "Hungria", flag: "🇭🇺" },
];

export const getCountryByCode = (code: string): CountryCode | undefined => {
  if (!code) return undefined;

  // Garante que encontremos o país correto removendo qualquer sufixo entre parênteses
  const cleanInputCode = code.replace(/\s*\([^)]*\)\s*/g, '').split(' ')[0];

  // Primeiro, tenta encontrar países cujo código limpo corresponda exatamente ao código de entrada limpo
  const matchingCountries = countryCodes.filter(country => {
    const cleanCountryCode = country.code.replace(/\s*\([^)]*\)\s*/g, '').split(' ')[0];
    return cleanCountryCode === cleanInputCode;
  });

  if (matchingCountries.length === 0) {
    return undefined;
  }

  // Se há mais de um país com o mesmo código base, tente encontrar correspondência exata
  if (matchingCountries.length > 1) {
    const exactMatch = matchingCountries.find(country => country.code === code);
    if (exactMatch) {
      return exactMatch;
    }
  }

  // Retorna o primeiro país correspondente
  return matchingCountries[0];
};

export const getCountryFlag = (code: string): string => {
  const country = getCountryByCode(code);
  return country?.flag || "";
};
