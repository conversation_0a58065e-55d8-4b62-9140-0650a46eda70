# Guia de Configuração do Stripe - Doce Menu

Este guia detalha como configurar o Stripe para o sistema de assinaturas do Doce Menu.

## 🚀 Configuração Inicial

### 1. Criar Conta no Stripe

1. Acesse [stripe.com](https://stripe.com)
2. Clique em "Start now" e crie sua conta
3. Complete o processo de verificação
4. Acesse o Dashboard

### 2. Configurar Modo de Teste

Durante o desenvolvimento, use sempre o **modo de teste**:

1. No Dashboard, certifique-se que está em "Test mode" (canto superior direito)
2. <PERSON><PERSON> as chaves devem começar com `sk_test_` e `pk_test_`

## 📦 Criar Produtos e Preços

### 1. Criar Produto Premium

1. No Dashboard, vá para **Products** → **Add product**
2. Configure:
   - **Name**: "Doce Menu Premium"
   - **Description**: "Plano premium com todas as funcionalidades"
   - **Image**: Upload do logo do Doce Menu (opcional)

### 2. Configurar Preços Recorrentes

#### Preço Mensal
1. Na seção **Pricing**, clique em **Add another price**:
   - **Price**: R$ 29,90
   - **Currency**: BRL (Real brasileiro)
   - **Billing period**: Monthly
   - **Usage type**: Licensed (quantidade fixa)

2. **Advanced options**:
   - **Trial period**: 7 days
   - **Nickname**: "Premium Monthly" (para identificação)

#### Preço Anual
1. Adicione outro preço para o mesmo produto:
   - **Price**: R$ 299,00 (equivale a R$ 24,92/mês)
   - **Currency**: BRL (Real brasileiro)
   - **Billing period**: Yearly
   - **Usage type**: Licensed (quantidade fixa)

2. **Advanced options**:
   - **Trial period**: 0 days (sem trial para anual)
   - **Nickname**: "Premium Yearly" (para identificação)

3. Clique em **Save product**

### 3. Copiar Price IDs

1. Após criar o produto, clique nele
2. Na seção **Pricing**, copie ambos os **Price IDs**:
   - Price ID mensal (formato: `price_xxxxxxxxxxxxx`)
   - Price ID anual (formato: `price_xxxxxxxxxxxxx`)
3. Adicione ao `.env`:
   ```env
   STRIPE_PREMIUM_MONTHLY_PRICE_ID=price_monthly_xxxxxxxxxxxxx
   STRIPE_PREMIUM_YEARLY_PRICE_ID=price_yearly_xxxxxxxxxxxxx
   ```

## 🔗 Configurar Webhooks

### 1. Criar Endpoint de Webhook

1. No Dashboard, vá para **Developers** → **Webhooks**
2. Clique em **Add endpoint**
3. Configure:
   - **Endpoint URL**: `https://seu-dominio.com/api/subscriptions/webhook`
   - **Description**: "Doce Menu Subscription Webhooks"

### 2. Selecionar Eventos

Marque os seguintes eventos:

#### Customer Events
- `customer.subscription.created`
- `customer.subscription.updated` 
- `customer.subscription.deleted`

#### Invoice Events
- `invoice.payment_succeeded`
- `invoice.payment_failed`
- `invoice.payment_action_required`

### 3. Configurar Webhook Secret

1. Após criar o webhook, clique nele
2. Na seção **Signing secret**, clique em **Reveal**
3. Copie o secret (formato: `whsec_xxxxxxxxxxxxx`)
4. Adicione ao `.env` como `STRIPE_WEBHOOK_SECRET`

## 🔑 Obter Chaves da API

### 1. Chaves de Teste

1. No Dashboard, vá para **Developers** → **API keys**
2. Copie as chaves:
   - **Publishable key** (pk_test_): Para o frontend
   - **Secret key** (sk_test_): Para o backend

### 2. Adicionar ao .env

```env
# Stripe Test Keys
STRIPE_SECRET_KEY=sk_test_xxxxxxxxxxxxx
STRIPE_PUBLISHABLE_KEY=pk_test_xxxxxxxxxxxxx
STRIPE_WEBHOOK_SECRET=whsec_xxxxxxxxxxxxx
STRIPE_PREMIUM_PRICE_ID=price_xxxxxxxxxxxxx
```

## 🧪 Testar Configuração

### 1. Cartões de Teste

Use estes cartões para testar:

```
# Pagamento bem-sucedido
4242 4242 4242 4242

# Pagamento negado
4000 0000 0000 0002

# Requer autenticação 3D Secure
4000 0025 0000 3155

# Cartão expirado
4000 0000 0000 0069
```

**Dados adicionais para teste**:
- **Expiry**: Qualquer data futura (ex: 12/25)
- **CVC**: Qualquer 3 dígitos (ex: 123)
- **ZIP**: Qualquer CEP (ex: 12345-678)

### 2. Testar Webhook Localmente

Para desenvolvimento local, use ngrok:

```bash
# Instalar ngrok
npm install -g ngrok

# Expor porta local
ngrok http 5000

# Usar URL do ngrok no webhook
https://abc123.ngrok.io/api/subscriptions/webhook
```

### 3. Executar Testes

```bash
# Testar sistema completo
npm run test:subscriptions

# Verificar status
npm run subscriptions:status
```

## 🔄 Fluxo de Pagamento

### 1. Processo de Upgrade

1. **Cliente clica em "Upgrade"**
   - Frontend chama `/api/subscriptions/create-checkout`
   - Backend cria sessão no Stripe
   - Cliente é redirecionado para Stripe Checkout

2. **Cliente completa pagamento**
   - Stripe processa pagamento
   - Cliente é redirecionado para success_url
   - Webhook é enviado para o backend

3. **Webhook processa evento**
   - Backend recebe `customer.subscription.created`
   - Dados são sincronizados no banco local
   - Assinatura é ativada

### 2. Gerenciamento de Assinatura

1. **Customer Portal**
   - Cliente acessa "Gerenciar Assinatura"
   - Backend cria sessão do Customer Portal
   - Cliente pode cancelar, atualizar cartão, etc.

## 🚨 Troubleshooting

### Problema: Webhook não funciona

**Soluções**:
1. Verificar URL do webhook
2. Confirmar que endpoint está acessível
3. Verificar logs do Stripe Dashboard
4. Testar com ngrok em desenvolvimento

### Problema: Price ID inválido

**Soluções**:
1. Verificar se copiou o ID correto
2. Confirmar que está no modo correto (test/live)
3. Verificar se produto está ativo

### Problema: Pagamento negado

**Soluções**:
1. Usar cartões de teste válidos
2. Verificar configuração da conta Stripe
3. Confirmar que não há restrições de país

## 🔒 Segurança

### Boas Práticas

1. **Nunca expor chaves secretas**:
   - Chaves `sk_` apenas no backend
   - Usar variáveis de ambiente
   - Não commitar chaves no Git

2. **Validar webhooks**:
   - Sempre verificar assinatura
   - Usar HTTPS em produção
   - Implementar idempotência

3. **Logs e monitoramento**:
   - Monitorar eventos no Stripe Dashboard
   - Implementar logs de auditoria
   - Alertas para falhas de pagamento

## 🌐 Produção

### Migrar para Modo Live

1. **Ativar conta**:
   - Complete verificação de identidade
   - Configure informações bancárias
   - Ative modo live no Dashboard

2. **Atualizar chaves**:
   - Substitua chaves `sk_test_` por `sk_live_`
   - Atualize webhook URLs para produção
   - Reconfigure Price IDs para modo live

3. **Testar em produção**:
   - Fazer teste com valor baixo
   - Verificar webhooks funcionando
   - Confirmar fluxo completo

### Monitoramento

1. **Métricas importantes**:
   - Taxa de conversão
   - Churn rate
   - Receita recorrente (MRR)
   - Falhas de pagamento

2. **Alertas**:
   - Configurar alertas para falhas
   - Monitorar webhooks perdidos
   - Acompanhar disputas

## 📞 Suporte

### Recursos Úteis

- [Documentação Stripe](https://stripe.com/docs)
- [Dashboard Stripe](https://dashboard.stripe.com)
- [Status Stripe](https://status.stripe.com)
- [Comunidade Stripe](https://support.stripe.com)

### Logs para Debug

```bash
# Logs do webhook
tail -f logs/stripe-webhooks.log

# Logs de assinatura
tail -f logs/subscriptions.log

# Testar endpoint
curl -X POST https://seu-dominio.com/api/subscriptions/webhook \
  -H "Content-Type: application/json" \
  -d '{"test": true}'
```
