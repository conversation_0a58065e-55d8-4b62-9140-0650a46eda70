-- Migration: Add payment status and order payments table
-- Date: 2024-01-XX
-- Description: Adds payment_status field to orders and order_revisions tables, and creates order_payments table for tracking payment receipts

-- Add payment_status column to orders table
ALTER TABLE orders 
ADD COLUMN payment_status VARCHAR(50) NOT NULL DEFAULT 'pendente';

-- Add payment_status column to order_revisions table
ALTER TABLE order_revisions 
ADD COLUMN payment_status VARCHAR(50) NOT NULL DEFAULT 'pendente';

-- Create order_payments table for tracking payment receipts
CREATE TABLE order_payments (
  id SERIAL PRIMARY KEY,
  order_id INTEGER NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
  value REAL NOT NULL,
  date TIMESTAMP NOT NULL,
  method VARCHAR(50) NOT NULL,
  observation TEXT,
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  created_by INTEGER
);

-- Add comment to explain payment_status values
COMMENT ON COLUMN orders.payment_status IS 'Status do pagamento: pendente, parcialmente_recebido, recebido, em_disputa, estornado, cancelado';
COMMENT ON COLUMN order_revisions.payment_status IS 'Status do pagamento: pendente, parcialmente_recebido, recebido, em_disputa, estornado, cancelado';

-- Add comment to explain order_payments table
COMMENT ON TABLE order_payments IS 'Registra os recebimentos de pagamento dos pedidos';
COMMENT ON COLUMN order_payments.method IS 'Método de pagamento: Pix, Dinheiro, Cartão, Transferência, Outro';

-- Create index for better performance on order_id lookups
CREATE INDEX idx_order_payments_order_id ON order_payments(order_id);
CREATE INDEX idx_orders_payment_status ON orders(payment_status);
CREATE INDEX idx_order_revisions_payment_status ON order_revisions(payment_status);
