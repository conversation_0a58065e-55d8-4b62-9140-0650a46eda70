#!/usr/bin/env node

/**
 * Script para testar o sistema de assinaturas
 * Verifica se todas as funcionalidades estão funcionando corretamente
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

// Configuração do Supabase
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Erro: SUPABASE_URL e SUPABASE_SERVICE_ROLE_KEY são obrigatórios');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Cores para output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(color, message) {
  console.log(`${color}${message}${colors.reset}`);
}

async function testDatabaseStructure() {
  log(colors.blue, '\n🔍 Testando estrutura do banco de dados...');

  try {
    // Verificar se a tabela subscriptions existe
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .eq('table_name', 'subscriptions');

    if (tablesError) {
      log(colors.red, '❌ Erro ao verificar tabela subscriptions');
      return false;
    }

    if (!tables || tables.length === 0) {
      log(colors.red, '❌ Tabela subscriptions não encontrada');
      return false;
    }

    log(colors.green, '✅ Tabela subscriptions encontrada');

    // Verificar colunas da tabela
    const { data: columns, error: columnsError } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type')
      .eq('table_schema', 'public')
      .eq('table_name', 'subscriptions');

    if (columnsError) {
      log(colors.red, '❌ Erro ao verificar colunas da tabela');
      return false;
    }

    const requiredColumns = [
      'id', 'store_id', 'stripe_customer_id', 'stripe_subscription_id',
      'plan_type', 'status', 'current_period_start', 'current_period_end',
      'trial_end', 'cancel_at_period_end', 'created_at', 'updated_at'
    ];

    const existingColumns = columns.map(col => col.column_name);
    const missingColumns = requiredColumns.filter(col => !existingColumns.includes(col));

    if (missingColumns.length > 0) {
      log(colors.red, `❌ Colunas faltando: ${missingColumns.join(', ')}`);
      return false;
    }

    log(colors.green, '✅ Todas as colunas necessárias estão presentes');
    return true;

  } catch (error) {
    log(colors.red, `❌ Erro inesperado: ${error.message}`);
    return false;
  }
}

async function testSubscriptionData() {
  log(colors.blue, '\n🔍 Testando dados de assinatura...');

  try {
    // Verificar se existem assinaturas
    const { data: subscriptions, error: subscriptionsError } = await supabase
      .from('subscriptions')
      .select('*')
      .limit(5);

    if (subscriptionsError) {
      log(colors.red, '❌ Erro ao buscar assinaturas');
      return false;
    }

    if (!subscriptions || subscriptions.length === 0) {
      log(colors.yellow, '⚠️ Nenhuma assinatura encontrada');
      return true;
    }

    log(colors.green, `✅ ${subscriptions.length} assinatura(s) encontrada(s)`);

    // Verificar se todas as lojas têm assinatura
    const { data: stores, error: storesError } = await supabase
      .from('stores')
      .select('id, name');

    if (storesError) {
      log(colors.red, '❌ Erro ao buscar lojas');
      return false;
    }

    if (stores && stores.length > 0) {
      const storeIds = stores.map(store => store.id);
      const subscriptionStoreIds = subscriptions.map(sub => sub.store_id);
      const storesWithoutSubscription = storeIds.filter(id => !subscriptionStoreIds.includes(id));

      if (storesWithoutSubscription.length > 0) {
        log(colors.yellow, `⚠️ ${storesWithoutSubscription.length} loja(s) sem assinatura`);
        log(colors.blue, 'Execute: npm run setup:subscriptions para corrigir');
      } else {
        log(colors.green, '✅ Todas as lojas têm assinatura');
      }
    }

    return true;

  } catch (error) {
    log(colors.red, `❌ Erro inesperado: ${error.message}`);
    return false;
  }
}

async function testStripeConfiguration() {
  log(colors.blue, '\n🔍 Testando configuração do Stripe...');

  const requiredVars = [
    'STRIPE_SECRET_KEY',
    'STRIPE_WEBHOOK_SECRET',
    'STRIPE_PREMIUM_MONTHLY_PRICE_ID',
    'STRIPE_PREMIUM_YEARLY_PRICE_ID'
  ];

  let allConfigured = true;

  requiredVars.forEach(varName => {
    if (process.env[varName]) {
      log(colors.green, `✅ ${varName} configurada`);
    } else {
      log(colors.red, `❌ ${varName} não configurada`);
      allConfigured = false;
    }
  });

  // Testar conexão com Stripe (se configurado)
  if (process.env.STRIPE_SECRET_KEY) {
    try {
      const { default: Stripe } = await import('stripe');
      const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
        apiVersion: '2024-12-18.acacia',
      });
      
      // Tentar listar produtos (teste básico de conectividade)
      const products = await stripe.products.list({ limit: 1 });
      log(colors.green, '✅ Conexão com Stripe funcionando');
      
      // Verificar se os price IDs existem
      const priceIds = [
        { name: 'Monthly', id: process.env.STRIPE_PREMIUM_MONTHLY_PRICE_ID },
        { name: 'Yearly', id: process.env.STRIPE_PREMIUM_YEARLY_PRICE_ID }
      ];

      for (const priceInfo of priceIds) {
        if (priceInfo.id) {
          try {
            const price = await stripe.prices.retrieve(priceInfo.id);
            log(colors.green, `✅ ${priceInfo.name} Price ID válido: ${price.nickname || price.id}`);
          } catch (error) {
            log(colors.red, `❌ ${priceInfo.name} Price ID inválido ou não encontrado`);
            allConfigured = false;
          }
        }
      }
      
    } catch (error) {
      log(colors.red, `❌ Erro ao conectar com Stripe: ${error.message}`);
      allConfigured = false;
    }
  }

  return allConfigured;
}

async function testAPIEndpoints() {
  log(colors.blue, '\n🔍 Testando endpoints da API...');

  const baseUrl = process.env.BASE_URL || 'http://localhost:5000';
  
  const endpoints = [
    '/api/subscriptions/usage',
    '/api/subscriptions/status',
    '/api/subscriptions/webhook'
  ];

  // Nota: Este teste apenas verifica se os endpoints existem
  // Para testes completos, seria necessário autenticação
  log(colors.yellow, '⚠️ Teste de endpoints requer autenticação');
  log(colors.blue, `ℹ️ Endpoints configurados para: ${baseUrl}`);
  
  endpoints.forEach(endpoint => {
    log(colors.blue, `📍 ${baseUrl}${endpoint}`);
  });

  return true;
}

async function testPlanConfiguration() {
  log(colors.blue, '\n🔍 Testando configuração de planos...');

  try {
    // Simular importação da configuração de planos
    const planConfigs = {
      free: {
        id: 'free',
        name: 'Plano Gratuito',
        price: 0,
        limits: {
          maxProducts: 10,
          maxOrdersPerMonth: 5,
          allowPdfGeneration: false,
          allowAnalytics: false,
          allowWhatsappIntegration: false,
          allowCoupons: false,
          allowCustomization: false,
        }
      },
      premium: {
        id: 'premium',
        name: 'Plano Premium',
        price: 29.90,
        limits: {
          maxProducts: 50,
          maxOrdersPerMonth: -1,
          allowPdfGeneration: true,
          allowAnalytics: true,
          allowWhatsappIntegration: true,
          allowCoupons: true,
          allowCustomization: true,
        }
      }
    };

    log(colors.green, '✅ Configuração de planos válida');
    log(colors.blue, `📋 Plano Free: ${planConfigs.free.limits.maxProducts} produtos, ${planConfigs.free.limits.maxOrdersPerMonth} pedidos/mês`);
    log(colors.blue, `📋 Plano Premium: ${planConfigs.premium.limits.maxProducts} produtos, pedidos ilimitados`);

    return true;

  } catch (error) {
    log(colors.red, `❌ Erro na configuração de planos: ${error.message}`);
    return false;
  }
}

async function runAllTests() {
  log(colors.bold, '🧪 INICIANDO TESTES DO SISTEMA DE ASSINATURAS\n');

  const tests = [
    { name: 'Estrutura do Banco', fn: testDatabaseStructure },
    { name: 'Dados de Assinatura', fn: testSubscriptionData },
    { name: 'Configuração do Stripe', fn: testStripeConfiguration },
    { name: 'Endpoints da API', fn: testAPIEndpoints },
    { name: 'Configuração de Planos', fn: testPlanConfiguration }
  ];

  let passedTests = 0;
  let totalTests = tests.length;

  for (const test of tests) {
    const result = await test.fn();
    if (result) {
      passedTests++;
    }
  }

  log(colors.bold, `\n📊 RESULTADO DOS TESTES: ${passedTests}/${totalTests} passaram`);

  if (passedTests === totalTests) {
    log(colors.green, '🎉 Todos os testes passaram! Sistema de assinaturas está funcionando corretamente.');
  } else {
    log(colors.yellow, '⚠️ Alguns testes falharam. Verifique a configuração.');
    
    if (passedTests < totalTests / 2) {
      log(colors.red, '❌ Muitos testes falharam. Execute: npm run setup:subscriptions');
    }
  }

  log(colors.blue, '\n📖 Para mais informações, consulte: README-SUBSCRIPTIONS.md');
}

// Executar testes
runAllTests().catch(error => {
  log(colors.red, `❌ Erro fatal: ${error.message}`);
  process.exit(1);
});
