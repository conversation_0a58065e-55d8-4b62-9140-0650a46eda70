import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Crown, ExternalLink, Calendar, CreditCard, AlertCircle, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useSubscription } from '@/context/SubscriptionContext';
import { PlanSelector, CurrentPlanBadge, UsageProgress } from './PlanSelector';
import { UpgradePrompt } from './UpgradePrompt';
import { PLAN_CONFIGS } from '@shared/schema';
import { cn } from '@/lib/utils';

export function SubscriptionSettings() {
  const { t } = useTranslation();
  const { 
    subscription, 
    usageInfo, 
    planConfig, 
    isLoading, 
    getCustomerPortalUrl 
  } = useSubscription();
  const [isLoadingPortal, setIsLoadingPortal] = useState(false);

  const handleManageSubscription = async () => {
    setIsLoadingPortal(true);
    try {
      const portalUrl = await getCustomerPortalUrl();
      if (portalUrl) {
        window.open(portalUrl, '_blank');
      }
    } catch (error) {
      console.error('Erro ao abrir Customer Portal:', error);
    } finally {
      setIsLoadingPortal(false);
    }
  };

  const formatDate = (date: Date | null) => {
    if (!date) return t('subscription.not_specified');
    return new Intl.DateTimeFormat('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    }).format(date);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'past_due':
        return 'bg-yellow-100 text-yellow-800';
      case 'canceled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="w-4 h-4" />;
      case 'past_due':
      case 'unpaid':
        return <AlertCircle className="w-4 h-4" />;
      default:
        return <AlertCircle className="w-4 h-4" />;
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-32 bg-gray-200 rounded mb-4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  const currentPlan = planConfig?.id || 'free';
  const isPremium = currentPlan === 'premium';

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            {t('subscription.title')}
          </h2>
          <p className="text-gray-600 mt-1">
            Gerencie sua assinatura e veja o uso atual
          </p>
        </div>
        <CurrentPlanBadge />
      </div>

      {/* Status da Assinatura */}
      {subscription && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="w-5 h-5" />
              Status da Assinatura
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {getStatusIcon(subscription.status)}
                <span className="font-medium">Status:</span>
              </div>
              <Badge className={cn("capitalize", getStatusColor(subscription.status))}>
                {subscription.status}
              </Badge>
            </div>

            {subscription.currentPeriodEnd && (
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4" />
                  <span className="font-medium">Próxima cobrança:</span>
                </div>
                <span className="text-gray-600">
                  {formatDate(subscription.currentPeriodEnd)}
                </span>
              </div>
            )}

            {subscription.trialEnd && new Date(subscription.trialEnd) > new Date() && (
              <Alert className="border-blue-200 bg-blue-50">
                <AlertCircle className="h-4 w-4 text-blue-600" />
                <AlertDescription className="text-blue-800">
                  Período de teste ativo até {formatDate(subscription.trialEnd)}
                </AlertDescription>
              </Alert>
            )}

            {isPremium && subscription.stripeCustomerId && (
              <Button
                onClick={handleManageSubscription}
                disabled={isLoadingPortal}
                variant="outline"
                className="w-full"
              >
                {isLoadingPortal ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                    Carregando...
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <ExternalLink className="w-4 h-4" />
                    Gerenciar Assinatura
                  </div>
                )}
              </Button>
            )}
          </CardContent>
        </Card>
      )}

      {/* Uso Atual */}
      {usageInfo && (
        <Card>
          <CardHeader>
            <CardTitle>Uso Atual</CardTitle>
            <CardDescription>
              Acompanhe o uso dos recursos do seu plano
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <UsageProgress 
              feature="maxProducts" 
              label={t('subscription.usage.products')} 
            />
            <UsageProgress 
              feature="maxOrdersPerMonth" 
              label={t('subscription.usage.orders')} 
            />

            {/* Recursos disponíveis */}
            <div className="pt-4 border-t">
              <h4 className="font-medium mb-3">Recursos Disponíveis</h4>
              <div className="grid grid-cols-2 gap-3">
                <div className="flex items-center gap-2">
                  <div className={cn(
                    "w-2 h-2 rounded-full",
                    usageInfo.features.allowPdfGeneration ? "bg-green-500" : "bg-gray-300"
                  )} />
                  <span className="text-sm">Geração de PDF</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className={cn(
                    "w-2 h-2 rounded-full",
                    usageInfo.features.allowAnalytics ? "bg-green-500" : "bg-gray-300"
                  )} />
                  <span className="text-sm">Analytics</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className={cn(
                    "w-2 h-2 rounded-full",
                    usageInfo.features.allowWhatsappIntegration ? "bg-green-500" : "bg-gray-300"
                  )} />
                  <span className="text-sm">WhatsApp</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className={cn(
                    "w-2 h-2 rounded-full",
                    usageInfo.features.allowCoupons ? "bg-green-500" : "bg-gray-300"
                  )} />
                  <span className="text-sm">Cupons</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Seletor de Planos */}
      {currentPlan === 'free' && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Fazer Upgrade</h3>
          <UpgradePrompt 
            title="Desbloqueie todo o potencial da sua loja"
            description="Upgrade para o plano Premium e tenha acesso a todas as funcionalidades"
            showFeatures={true}
          />
        </div>
      )}

      {/* Comparação de Planos */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Planos Disponíveis</h3>
        <PlanSelector 
          currentPlan={currentPlan}
          showCurrentPlan={true}
        />
      </div>
    </div>
  );
}
