import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "@/hooks/useTranslation";
import { useCart } from "@/context/CartContext";
import { formatCurrency, formatStoreCurrency } from "@/lib/utils";
import { useStore } from "@/context/StoreContext";
import { Plus } from "lucide-react";

export interface VariationOption {
  id: number;
  name: string;
  price: number;
}

export interface ProductVariation {
  id: number;
  name: string;
  required: boolean;
  multipleChoice: boolean;
  options: VariationOption[];
}

export interface Product {
  id: number;
  name: string;
  description?: string;
  price: number;
  images?: string[];
  inStock: boolean;
  hasVariations: boolean;
  variations?: ProductVariation[];
  categoryId?: number;
  storeId?: number;
}

interface ProductCardProps {
  product: Product;
  onClick?: (product: Product) => void;
  compact?: boolean; // Para o layout com categorias horizontais
  currency?: string; // Moeda da loja
}

export function ProductCard({ product, onClick, compact = false, currency }: ProductCardProps) {
  const { t } = useTranslation();
  const { addItem } = useCart();
  const { store } = useStore();

  // Use a moeda fornecida ou a do contexto como fallback
  const currencySymbol = currency || store?.currency || 'R$';

  const handleAddToCart = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!product.inStock) return;

    // Se o produto tem variações, não adicionamos diretamente ao carrinho
    if (product.hasVariations) {
      if (onClick) onClick(product);
      return;
    }

    addItem({
      productId: product.id,
      name: product.name,
      price: product.price,
      image: product.images && product.images.length > 0 ? product.images[0] : undefined
    });
  };

  const handleClick = () => {
    if (onClick) onClick(product);
  };

  // Layout compacto para o modo de categorias horizontais
  if (compact) {
    return (
      <div
        className="bg-white rounded-lg shadow overflow-hidden cursor-pointer transition-transform hover:scale-[1.02] h-full flex flex-col"
        onClick={handleClick}
      >
        <div className="h-28 w-full relative">
          {product.images && product.images.length > 0 ? (
            <img
              src={product.images[0]}
              alt={product.name}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full bg-muted flex items-center justify-center text-muted-foreground">
              <span className="text-xs">No image</span>
            </div>
          )}

          {/* Badge para indicar se o produto tem variações - menor no modo compacto */}
          {product.hasVariations && (
            <div className="absolute top-1 left-1 bg-secondary text-white text-[10px] px-1.5 py-0.5 rounded-full">
              {t('storefront.hasOptions')}
            </div>
          )}
        </div>
        <div className="p-2 flex-1 flex flex-col justify-between">
          <h3 className="text-sm font-medium text-neutral-dark line-clamp-1">{product.name}</h3>
          <div className="mt-1 flex justify-between items-center">
            <p className="font-bold text-primary text-sm">{formatStoreCurrency(product.price, currencySymbol)}</p>
            {product.inStock ? (
              <Button
                variant="secondary"
                size="icon"
                className="rounded-full h-6 w-6 min-w-6 bg-primary text-white hover:bg-primary/90 hover:text-white"
                onClick={handleAddToCart}
              >
                <Plus className="h-3 w-3" />
              </Button>
            ) : (
              <span className="text-error text-[10px]">{t('storefront.outOfStock')}</span>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Layout padrão
  return (
    <div
      className="bg-white rounded-lg shadow overflow-hidden cursor-pointer transition-transform hover:scale-[1.02]"
      onClick={handleClick}
    >
      <div className="h-40 w-full relative">
        {product.images && product.images.length > 0 ? (
          <img
            src={product.images[0]}
            alt={product.name}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full bg-muted flex items-center justify-center text-muted-foreground">
            {t('storefront.noImage')}
          </div>
        )}

        {/* Badge para indicar se o produto tem variações */}
        {product.hasVariations && (
          <div className="absolute top-2 left-2 bg-secondary text-white text-xs px-2 py-1 rounded-full">
            {t('storefront.hasOptions')}
          </div>
        )}
      </div>
      <div className="p-4">
        <h3 className="text-md font-medium text-neutral-dark line-clamp-1">{product.name}</h3>
        {product.description && (
          <p className="text-xs text-muted-foreground mt-1 line-clamp-2">{product.description}</p>
        )}
        <div className="mt-2 flex justify-between items-center">
          <p className="font-bold text-primary">{formatStoreCurrency(product.price, currencySymbol)}</p>
          {product.inStock ? (
            <Button
              variant="secondary"
              size="icon"
              className="rounded-full bg-primary text-white hover:bg-primary/90 hover:text-white"
              onClick={handleAddToCart}
            >
              <Plus className="h-4 w-4" />
            </Button>
          ) : (
            <span className="text-error text-xs">{t('storefront.outOfStock')}</span>
          )}
        </div>
      </div>
    </div>
  );
}

export default ProductCard;
