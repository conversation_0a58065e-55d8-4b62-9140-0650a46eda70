// Novo código para o endpoint de atualização da taxa de entrega
// Este código deve substituir o endpoint existente em server/routes.ts

app.patch("/api/orders/revisions/:revisionId/delivery-fee", requireAuth, async (req, res) => {
  try {
    const revisionId = parseInt(req.params.revisionId);
    if (isNaN(revisionId)) {
      return res.status(400).json({ message: "ID de revisão inválido" });
    }

    // Extrair a taxa de entrega do corpo da requisição
    const { deliveryFee } = req.body;
    
    if (deliveryFee === undefined) {
      return res.status(400).json({ message: "Taxa de entrega é obrigatória" });
    }

    console.log('Atualizando taxa de entrega para revisão ID:', revisionId);
    console.log('Dados da requisição:', req.body);
    console.log('Taxa de entrega extraída:', deliveryFee, 'Tipo:', typeof deliveryFee);

    // Converter para número
    const numericDeliveryFee = typeof deliveryFee === 'string'
      ? parseFloat(deliveryFee.replace(',', '.'))
      : parseFloat(String(deliveryFee));

    if (isNaN(numericDeliveryFee)) {
      return res.status(400).json({ message: "Taxa de entrega inválida" });
    }

    console.log('Taxa de entrega convertida para número:', numericDeliveryFee);

    // Obter a loja atual para verificar autorização
    const store = await getStoreFromReq(req);
    if (!store) {
      return res.status(403).json({ message: "Não autorizado" });
    }

    // Obter os detalhes da revisão atual
    const revision = await storage.getOrderRevision(revisionId);
    if (!revision) {
      return res.status(404).json({ message: "Revisão não encontrada" });
    }

    // Verificar se a revisão pertence a um pedido desta loja
    const order = await storage.getOrder(revision.orderId);
    if (!order || order.storeId !== store.id) {
      return res.status(403).json({ message: "Não autorizado" });
    }

    // Recalcular o subtotal com base nos itens
    const revisionItems = await storage.getOrderRevisionItems(revisionId);
    const calculatedSubtotal = revisionItems.reduce((sum, item) => sum + item.subtotal, 0);
    
    console.log('Subtotal recalculado com base nos itens:', calculatedSubtotal);

    // Obter o desconto atual
    const discount = revision.discount || 0;
    
    // Calcular o novo total
    const numericTotal = Math.max(0, calculatedSubtotal - discount + numericDeliveryFee);
    
    console.log('Novo total calculado:', numericTotal);
    console.log('(Subtotal:', calculatedSubtotal, '- Desconto:', discount, '+ Taxa de entrega:', numericDeliveryFee, ')');

    // Atualizar a revisão com a nova taxa de entrega e total
    try {
      // Método simplificado usando o storage diretamente
      const updatedRevision = await storage.updateOrderRevision(revisionId, {
        deliveryFee: numericDeliveryFee,
        total: numericTotal
      });

      if (!updatedRevision) {
        throw new Error('Falha ao atualizar revisão');
      }

      console.log('Revisão atualizada com sucesso');
      return res.status(200).json(updatedRevision);
    } catch (updateError) {
      console.error('Erro ao atualizar revisão:', updateError);
      return res.status(500).json({
        message: "Falha ao atualizar taxa de entrega",
        error: updateError instanceof Error ? updateError.message : String(updateError)
      });
    }
  } catch (error) {
    console.error('Erro ao processar requisição:', error);
    return res.status(500).json({
      message: "Falha ao atualizar taxa de entrega",
      error: error instanceof Error ? error.message : String(error)
    });
  }
});