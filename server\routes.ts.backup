import type { Express, Request, Response } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { supabaseAdmin } from "./db";
import bcrypt from "bcryptjs";
import session from "express-session";
import MemoryStore from "memorystore";
import { zodResolver } from "@hookform/resolvers/zod";
import multer from "multer";
import { v4 as uuidv4 } from "uuid";
import {
  insertUserSchema,
  insertStoreSchema,
  insertProductSchema,
  insertCategorySchema,
  insertCustomerSchema,
  insertOrderSchema,
  insertOrderItemSchema,
  insertStoreVisitSchema,
  insertProfileSchema,
  insertProductVariationSchema,
  insertVariationOptionSchema,
  User,
  Profile
} from "@shared/schema";
import { z } from "zod";

const SessionStore = MemoryStore(session);

// Configuração do Multer para salvar uploads na memória
const multerStorage = multer.memoryStorage();
const upload = multer({
  storage: multerStorage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB max file size
  },
  fileFilter: (_req, file, cb) => {
    // Permitir apenas imagens
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Apenas imagens são permitidas.') as any);
    }
  }
});

// Função para fazer upload de arquivo para o Supabase Storage
async function uploadToSupabaseStorage(file: Express.Multer.File, storeId: number): Promise<string> {
  try {
    console.log('Uploading file to Supabase Storage');
    const fileExt = file.originalname.split('.').pop(); // Pegar a extensão do arquivo
    const fileName = `${storeId}_${uuidv4()}.${fileExt}`;
    const filePath = `store-logos/${fileName}`;
    
    console.log(`File path: ${filePath}, Size: ${file.size} bytes, Type: ${file.mimetype}`);
    
    const { data, error } = await supabaseAdmin
      .storage
      .from('doce-menu')
      .upload(filePath, file.buffer, {
        contentType: file.mimetype,
        upsert: true
      });
    
    if (error) {
      console.error('Supabase Storage upload error:', error);
      throw new Error(`Failed to upload file: ${error.message}`);
    }
    
    console.log('Upload success, getting public URL');
    
    // Pegar a URL pública
    const { data: { publicUrl } } = supabaseAdmin
      .storage
      .from('doce-menu')
      .getPublicUrl(filePath);
    
    console.log('Public URL:', publicUrl);
    return publicUrl;
  } catch (error) {
    console.error('Error in uploadToSupabaseStorage:', error);
    throw error;
  }
}

export async function registerRoutes(app: Express): Promise<Server> {
  // Configure sessions
  app.use(session({
    secret: process.env.SESSION_SECRET || 'doce-menu-secret-key',
    resave: false,
    saveUninitialized: false,
    cookie: { secure: process.env.NODE_ENV === 'production', maxAge: 24 * 60 * 60 * 1000 }, // 24 hours
    store: new SessionStore({
      checkPeriod: 86400000 // prune expired entries every 24h
    })
  }));

  // Auth middleware
  const requireAuth = async (req: Request, res: Response, next: any) => {
    console.log('Auth headers:', req.headers.authorization);
    
    // First check if session-based auth is active
    if (req.session.userId) {
      console.log('Session auth valid, userId:', req.session.userId);
      return next();
    }
    
    // If no session, check for Supabase token auth
    const authHeader = req.headers.authorization;
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      console.log('Bearer token found, length:', token.length);
      
      try {
        // We're using Supabase for auth, we trust the Bearer token
        // since it was validated on the client side by Supabase
        
        // Extract the profile ID from the request header
        const profileId = req.headers['x-profile-id'] as string;
        if (!profileId) {
          console.log('No profile ID found in headers');
          return res.status(401).json({ message: "Unauthorized: Missing profile ID" });
        }
        
        console.log('Profile ID found in headers:', profileId);
        
        // Check if this profile exists in our database, first by ID
        let profile = await storage.getProfile(profileId);
        
        if (!profile) {
          // If not found by ID, try by email from token claims
          // We need to extract the email from the token claims
          // For simplicity, we'll get it from the request header that client should pass
          const userEmail = req.headers['x-user-email'] as string;
          
          if (userEmail) {
            console.log('Profile not found by ID, trying by email:', userEmail);
            profile = await storage.getProfileByEmail(userEmail);
          }
          
          if (!profile) {
            console.log('Profile not found by ID or email, creating a new profile');
            
            // The profile doesn't exist in our database yet
            // We need to create it automatically using the token data
            
            // Since we already have the user's email, attempt to create the profile
            if (userEmail) {
              try {
                profile = await storage.createProfile({
                  id: profileId,
                  email: userEmail,
                  full_name: (req.headers['x-user-name'] as string) || null
                });
                console.log('Created new profile automatically:', profile.id);
              } catch (err) {
                console.error('Failed to create profile automatically:', err);
              }
            }
            
            // If still no profile, ask client to create one
            if (!profile) {
              return res.status(401).json({ 
                message: "Profile not found. Please create a profile first.",
                code: "PROFILE_NOT_FOUND"
              });
            }
          }
        }
        
        console.log('Profile found in database:', profile.email);
        
        // Check if there's a corresponding user in our system
        let user = await storage.getUserByEmail(profile.email);
        if (!user) {
          // No matching user, but we have a valid profile
          // Create a new user for this profile with a temporary password
          // In a real app, you might want to handle this differently
          console.log('Creating new user for profile');
          
          // Generate a secure random password since this user will only
          // authenticate via Supabase token
          const tempPassword = Math.random().toString(36).slice(-10);
          const hashedPassword = await bcrypt.hash(tempPassword, 10);
          
          user = await storage.createUser({
            email: profile.email,
            password: hashedPassword,
            fullName: profile.full_name || profile.email.split('@')[0]
          });
          
          console.log('Created new user with ID:', user.id);
        }
        
        // Set the user ID in the session so future requests will be authorized
        req.session.userId = user.id;
        console.log('Set session userId to:', user.id);
        return next();
      } catch (error) {
        console.error('Token verification error:', error);
        return res.status(401).json({ message: "Invalid token" });
      }
    }
    
    // No valid authentication found
    console.log('No valid auth found');
    return res.status(401).json({ message: "Unauthorized" });
  };

  // Auth routes
  app.post("/api/auth/register", async (req, res) => {
    try {
      const userData = insertUserSchema.parse(req.body);
      
      // Check if user exists
      const existingUser = await storage.getUserByEmail(userData.email);
      if (existingUser) {
        return res.status(400).json({ message: "User already exists" });
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(userData.password, 10);
      
      // Create user
      const user = await storage.createUser({
        ...userData,
        password: hashedPassword
      });

      // Set session
      req.session.userId = user.id;

      return res.status(201).json({ 
        id: user.id,
        email: user.email,
        fullName: user.fullName 
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid data", errors: error.errors });
      }
      return res.status(500).json({ message: "Registration failed" });
    }
  });

  app.post("/api/auth/login", async (req, res) => {
    try {
      const { email, password } = req.body;
      
      if (!email || !password) {
        return res.status(400).json({ message: "Email and password are required" });
      }

      const user = await storage.getUserByEmail(email);
      
      if (!user) {
        return res.status(401).json({ message: "Invalid credentials" });
      }

      const isValidPassword = await bcrypt.compare(password, user.password);
      
      if (!isValidPassword) {
        return res.status(401).json({ message: "Invalid credentials" });
      }

      // Set session
      req.session.userId = user.id;

      return res.status(200).json({ 
        id: user.id,
        email: user.email,
        fullName: user.fullName 
      });
    } catch (error) {
      return res.status(500).json({ message: "Login failed" });
    }
  });

  app.post("/api/auth/logout", (req, res) => {
    req.session.destroy((err) => {
      if (err) {
        return res.status(500).json({ message: "Logout failed" });
      }
      res.clearCookie("connect.sid");
      return res.status(200).json({ message: "Logged out successfully" });
    });
  });

  app.get("/api/auth/me", async (req, res) => {
    if (!req.session.userId) {
      return res.status(401).json({ message: "Not authenticated" });
    }

    try {
      const user = await storage.getUser(req.session.userId);
      
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      return res.status(200).json({
        id: user.id,
        email: user.email,
        fullName: user.fullName
      });
    } catch (error) {
      return res.status(500).json({ message: "Failed to get user information" });
    }
  });
  
  // Supabase profile creation/sync route
  app.post("/api/auth/supabase-profile", async (req, res) => {
    try {
      // Verify that this request has a valid token
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({ message: "Unauthorized" });
      }
      
      // Parse profile data
      const profileData = insertProfileSchema.parse(req.body);
      
      // Check if profile already exists
      let profile = await storage.getProfileByEmail(profileData.email);
      if (profile) {
        console.log('Profile already exists, returning existing profile');
        
        // Check for a corresponding user, creating one if needed
        await ensureUserForProfile(profile);
        
        return res.status(200).json(profile); // Profile already exists
      }
      
      // Create the profile
      profile = await storage.createProfile(profileData);
      console.log('Created new profile:', profile.email);
      
      // Create a corresponding user record
      const user = await ensureUserForProfile(profile);
      console.log('Ensured user exists for profile:', user.id);
      
      return res.status(201).json(profile);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid data", errors: error.errors });
      }
      console.error('Error creating profile:', error);
      return res.status(500).json({ message: "Failed to create profile" });
    }
  });
  
  // Helper function to ensure a user exists for a profile
  async function ensureUserForProfile(profile: Profile): Promise<User> {
    // Check if we already have a user for this profile
    let user = await storage.getUserByEmail(profile.email);
    
    if (!user) {
      // Create a new user for this profile with a secure random password
      // In a real app with sensitive operations, you might want a more robust approach
      console.log('Creating new user for profile:', profile.email);
      
      // Generate a secure random password since this user will only
      // authenticate via Supabase token
      const tempPassword = Math.random().toString(36).slice(-10);
      const hashedPassword = await bcrypt.hash(tempPassword, 10);
      
      user = await storage.createUser({
        email: profile.email,
        password: hashedPassword,
        fullName: profile.full_name || profile.email.split('@')[0]
      });
      
      console.log('Created new user with ID:', user.id);
    }
    
    return user;
  }

  // Store routes
  app.post("/api/stores", requireAuth, async (req, res) => {
    try {
      const userId = req.session.userId!;
      
      // Check if user already has a store
      const existingStore = await storage.getStoreByUserId(userId);
      if (existingStore) {
        return res.status(400).json({ message: "User already has a store" });
      }

      // Validate store data
      const storeData = insertStoreSchema.parse({
        ...req.body,
        userId
      });

      // Check if slug is already taken
      const existingSlugStore = await storage.getStoreBySlug(storeData.slug);
      if (existingSlugStore) {
        return res.status(400).json({ message: "Store URL slug already in use" });
      }

      // Create store
      const store = await storage.createStore(storeData);

      return res.status(201).json(store);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid data", errors: error.errors });
      }
      return res.status(500).json({ message: "Failed to create store" });
    }
  });

  app.get("/api/stores/me", requireAuth, async (req, res) => {
    try {
      const userId = req.session.userId!;
      console.log('Looking for store with userId:', userId);
      
      const store = await storage.getStoreByUserId(userId);
      
      if (!store) {
        // If no store found, return a 404
        return res.status(404).json({ message: "Store not found" });
      }

      // Transform snake_case to camelCase for consistency
      const transformedStore = { ...store };
      
      // Check if payment_methods exists (snake_case from Supabase)
      if (store.payment_methods) {
        // Convert to camelCase
        transformedStore.paymentMethods = { 
          ...store.payment_methods,
          // Ensure customMethods exists
          customMethods: store.payment_methods.customMethods || []
        };
        // Remove the snake_case version
        delete transformedStore.payment_methods;
      } else if (store.paymentMethods) {
        // If already in camelCase, ensure customMethods exists
        if (!store.paymentMethods.customMethods) {
          transformedStore.paymentMethods.customMethods = [];
        }
      } else {
        // If neither exists, initialize with empty object
        transformedStore.paymentMethods = { 
          cash: true,
          creditCard: false,
          debitCard: false,
          pix: false,
          bankTransfer: false,
          customMethods: []
        };
      }

      console.log('Sending transformed store data:', transformedStore);
      
      return res.status(200).json(transformedStore);
    } catch (error) {
      console.error('Error getting store:', error);
      return res.status(500).json({ message: "Failed to get store information" });
    }
  });

  app.put("/api/stores/me", requireAuth, async (req, res) => {
    try {
      const userId = req.session.userId!;
      console.log('Attempting to update store for userId:', userId);
      
      // Obter a loja atual do usuário
      let store = await storage.getStoreByUserId(userId);
      
      // Validar os dados de atualização
      const updateData = { ...req.body };
      delete updateData.userId; // Evitar mudanças de propriedade
      
      console.log('Update data:', updateData);
      
      // Se a loja não existir, precisamos criá-la primeiro
      if (!store) {
        console.log('Store not found, creating a new store for user ID:', userId);
        
        try {
          // Criar uma nova loja para o usuário
          store = await storage.createStore({
            name: updateData.name || "Minha Loja",
            slug: updateData.slug || `loja-${userId}`,
            description: updateData.description || "Minha loja no Doce Menu",
            userId: userId,
            logo: updateData.logo || null,
            colors: updateData.colors || {
              primary: "#FF5722",
              secondary: "#2196F3",
              accent: "#FFEB3B"
            },
            paymentMethods: updateData.paymentMethods || {
              cash: true,
              creditCard: true,
              debitCard: true,
              pix: true,
              bankTransfer: false
            }
          });
          
          console.log('Store created successfully with ID:', store.id);
          return res.status(201).json(store);
        } catch (createError) {
          console.error('Error creating store:', createError);
          return res.status(500).json({ message: "Failed to create store" });
        }
      }
      
      console.log('Updating existing store with ID:', store.id);
      
      // If slug is changing, check if it's already taken
      if (updateData.slug && updateData.slug !== store.slug) {
        const existingSlugStore = await storage.getStoreBySlug(updateData.slug);
        if (existingSlugStore && existingSlugStore.id !== store.id) {
          return res.status(400).json({ message: "Store URL slug already in use" });
        }
      }
      
      // Verificar e converter propriedades snake_case para camelCase
      if (store.payment_methods && updateData.paymentMethods) {
        // Preservar os métodos de pagamento personalizados existentes se não forem fornecidos
        if (!updateData.paymentMethods.customMethods && store.payment_methods.customMethods) {
          updateData.paymentMethods.customMethods = store.payment_methods.customMethods;
        } else {
          // Garantir que customMethods existe
          updateData.paymentMethods.customMethods = updateData.paymentMethods.customMethods || [];
        }
      } else if (updateData.paymentMethods) {
        // Garantir que customMethods existe
        updateData.paymentMethods.customMethods = updateData.paymentMethods.customMethods || [];
      }
      
      console.log('Sending update data:', updateData);
      
      // Atualizar a loja existente
      const updatedStore = await storage.updateStore(store.id, updateData);
      
      if (!updatedStore) {
        console.error('Failed to update store, unknown error');
        return res.status(500).json({ message: "Failed to update store" });
      }
      
      console.log('Store updated successfully:', updatedStore.id);
      return res.status(200).json(updatedStore);
    } catch (error) {
      console.error('Error in PUT /api/stores/me:', error);
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid data", errors: error.errors });
      }
      return res.status(500).json({ message: "Failed to update store" });
    }
  });
  
  // Rota para upload da logo
  app.post("/api/stores/me/logo", requireAuth, upload.single('logo'), async (req, res) => {
    try {
      const userId = req.session.userId!;
      console.log('Attempting to upload logo for userId:', userId);
      
      // Find the store for this user
      const store = await storage.getStoreByUserId(userId);
      
      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }
      
      // Verificar se foi feito upload de um arquivo
      if (!req.file) {
        return res.status(400).json({ message: "No file uploaded" });
      }
      
      // Fazer upload do arquivo para o Supabase Storage
      const logoUrl = await uploadToSupabaseStorage(req.file, store.id);
      
      // Atualizar a store com a URL da logo
      const updatedStore = await storage.updateStore(store.id, {
        logo: logoUrl
      });
      
      if (!updatedStore) {
        return res.status(500).json({ message: "Failed to update store with logo URL" });
      }
      
      console.log('Store logo updated successfully:', store.id);
      return res.status(200).json({ 
        message: "Logo uploaded successfully",
        logo: logoUrl,
        store: updatedStore
      });
    } catch (error) {
      console.error('Error uploading logo:', error);
      return res.status(500).json({ 
        message: "Failed to upload logo",
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  app.get("/api/public/stores/:slug", async (req, res) => {
    try {
      const { slug } = req.params;
      console.log('Fetching store by slug:', slug);
      
      const store = await storage.getStoreBySlug(slug);
      
      if (!store) {
        console.log('Store not found with slug:', slug);
        return res.status(404).json({ message: "Store not found" });
      }
      
      console.log('Store found:', store);

      try {
        // Record store visit - usando try/catch para evitar que falhas nesta operação
        // interrompam o fluxo principal de retornar os dados da loja
        await storage.recordStoreVisit({
          storeId: store.id,
          visitorIp: req.ip
        });
        console.log('Successfully recorded store visit for store ID:', store.id);
      } catch (visitError) {
        // Log o erro mas continue com a resposta da API
        console.error('Failed to record store visit, but continuing with store data response:', visitError);
      }

      // Retornar apenas os dados necessários para exibição pública
      const publicStoreData = {
        id: store.id,
        name: store.name,
        description: store.description,
        logo: store.logo,
        colors: store.colors,
        slug: store.slug
      };
      
      console.log('Returning public store data:', publicStoreData);
      return res.status(200).json(publicStoreData);
    } catch (error) {
      console.error('Error in /api/public/stores/:slug endpoint:', error);
      return res.status(500).json({ message: "Failed to get store information" });
    }
  });

  // Product routes
  
  // Product image upload route (usando admin client para contornar RLS)
  app.post("/api/products/upload-image", requireAuth, upload.single('image'), async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ error: 'No file uploaded' });
      }
      
      const userId = req.session.userId!;
      console.log('Uploading product image for userId:', userId);
      
      const store = await storage.getStoreByUserId(userId);
      
      if (!store) {
        console.log('Store not found for userId:', userId);
        return res.status(404).json({ message: "Store not found" });
      }
      
      const storeId = store.id;
      const file = req.file;
      const fileExt = file.originalname.split('.').pop();
      const fileName = `${storeId}_${uuidv4()}.${fileExt}`;
      const filePath = `product-images/${fileName}`;
      
      console.log(`Uploading product image: ${filePath}, Size: ${file.size} bytes, Type: ${file.mimetype}`);
      
      // Upload do arquivo para o Supabase storage usando admin client
      const { data, error } = await supabaseAdmin.storage
        .from('doce-menu')
        .upload(filePath, file.buffer, {
          contentType: file.mimetype,
          cacheControl: '3600',
          upsert: false
        });
        
      if (error) {
        console.error('Error uploading to Supabase storage:', error);
        return res.status(500).json({ error: error.message });
      }
      
      // Obter URL pública
      const { data: { publicUrl } } = supabaseAdmin.storage
        .from('doce-menu')
        .getPublicUrl(filePath);
        
      console.log('Product image uploaded, public URL:', publicUrl);
      
      res.json({ url: publicUrl });
    } catch (error) {
      console.error('Error in product image upload:', error);
      res.status(500).json({ error: error instanceof Error ? error.message : String(error) });
    }
  });
  
  app.post("/api/products", requireAuth, async (req, res) => {
    try {
      const userId = req.session.userId!;
      console.log('Creating product for userId:', userId);
      
      const store = await storage.getStoreByUserId(userId);
      
      if (!store) {
        console.log('Store not found for userId:', userId);
        return res.status(404).json({ message: "Store not found" });
      }

      console.log('Store found with ID:', store.id);

      // Prepare product data with default values
      const productData = {
        ...req.body,
        storeId: store.id,
        // Garantir que inStock existe
        inStock: req.body.inStock !== undefined ? req.body.inStock : true
      };
      
      console.log('Product data before validation:', productData);

      // Validate product data
      const validatedData = insertProductSchema.parse(productData);
      
      console.log('Validated product data:', validatedData);

      // Create product
      const product = await storage.createProduct(validatedData);
      
      console.log('Product created successfully with ID:', product.id);
      return res.status(201).json(product);
    } catch (error) {
      console.error('Error creating product:', error);
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid data", errors: error.errors });
      }
      return res.status(500).json({ 
        message: "Failed to create product",
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  app.get("/api/products", requireAuth, async (req, res) => {
    try {
      const userId = req.session.userId!;
      console.log('Looking for products with userId:', userId);
      
      const store = await storage.getStoreByUserId(userId);
      
      if (!store) {
        console.log('Store not found for userId:', userId);
        return res.status(404).json({ message: "Store not found" });
      }

      console.log('Getting products for store:', store.id);
      const products = await storage.getProductsByStoreId(store.id);
      
      // Se não houver produtos, retorna uma lista vazia
      if (!products || products.length === 0) {
        console.log('No products found for store:', store.id);
        return res.status(200).json([]);
      }
      
      console.log(`Found ${products.length} products for store ${store.id}`);
      return res.status(200).json(products);
    } catch (error) {
      console.error('Error getting products:', error);
      return res.status(500).json({ message: "Failed to get products" });
    }
  });
  
  // Rota para obter um produto específico por ID
  app.get("/api/products/:id", requireAuth, async (req, res) => {
    try {
      const userId = req.session.userId!;
      console.log('Fetching product by ID:', req.params.id, 'for user:', userId);
      
      const store = await storage.getStoreByUserId(userId);
      
      if (!store) {
        console.log('Store not found for userId:', userId);
        return res.status(404).json({ message: "Store not found" });
      }
      
      const productId = parseInt(req.params.id);
      const product = await storage.getProduct(productId);
      
      if (!product) {
        console.log('Product not found with ID:', productId);
        return res.status(404).json({ message: "Product not found" });
      }
      
      // Verificar se o produto pertence à loja do usuário
      if (product.storeId !== store.id) {
        console.log('Product does not belong to user store. Product storeId:', product.storeId, 'User store ID:', store.id);
        return res.status(403).json({ message: "Not authorized to access this product" });
      }
      
      console.log('Product found and returning:', product);
      return res.status(200).json(product);
    } catch (error) {
      console.error('Error fetching product by ID:', error);
      return res.status(500).json({ message: "Failed to get product details" });
    }
  });

  app.get("/api/public/stores/:slug/products", async (req, res) => {
    try {
      const { slug } = req.params;
      const store = await storage.getStoreBySlug(slug);
      
      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      const products = await storage.getProductsByStoreId(store.id);
      return res.status(200).json(products);
    } catch (error) {
      return res.status(500).json({ message: "Failed to get products" });
    }
  });

  app.put("/api/products/:id", requireAuth, async (req, res) => {
    try {
      const userId = req.session.userId!;
      console.log('Updating product for userId:', userId);
      console.log('Product ID:', req.params.id);
      console.log('Update data:', req.body);
      
      // Obter a loja do usuário usando supabaseAdmin
      const store = await storage.getStoreByUserId(userId);
      
      if (!store) {
        console.log('Store not found for userId:', userId);
        return res.status(404).json({ message: "Store not found" });
      }

      console.log('Store found with ID:', store.id);
      const productId = parseInt(req.params.id);
      
      // Buscar o produto para verificar a propriedade
      const product = await storage.getProduct(productId);
      
      if (!product) {
        console.log('Product not found with ID:', productId);
        return res.status(404).json({ message: "Product not found" });
      }

      console.log('Product found:', product);
      console.log('Verifying ownership - Product storeId:', product.storeId, 'User store ID:', store.id);
      
      // Verificar a propriedade do produto
      if (product.storeId !== store.id) {
        console.log('Ownership verification failed');
        return res.status(403).json({ message: "Not authorized to modify this product" });
      }

      console.log('Ownership verified successfully');
      
      // Preparar dados de atualização (sem permitir alteração de storeId)
      const updateData = { ...req.body };
      delete updateData.storeId;
      
      // Extrair variações para processá-las separadamente
      const variations = updateData.variations || [];
      delete updateData.variations;
      
      console.log('Prepared update data:', updateData);
      console.log('Variations to process:', variations);
      
      // Atualizar o produto usando a função updateProduct melhorada
      const updatedProduct = await storage.updateProduct(productId, updateData);
      
      if (!updatedProduct) {
        console.log('Failed to update product');
        return res.status(500).json({ message: "Failed to update product" });
      }
      
      // Processar variações se o produto tiver variações ativadas
      if (updateData.hasVariations) {
        console.log('Processing variations for product:', productId);
        
        try {
          // Obter variações existentes primeiro
          const existingVariations = await storage.getProductVariationsByProductId(productId);
          console.log('Existing variations:', existingVariations);
          
          // Excluir variações removidas
          for (const existingVar of existingVariations) {
            const stillExists = variations.some(v => v.id === existingVar.id);
            if (!stillExists) {
              await storage.deleteProductVariation(existingVar.id);
            }
          }
          
          // Processar cada variação recebida
          for (const variation of variations) {
            const variationData = {
              name: variation.name,
              required: variation.required || false,
              multipleChoice: variation.multipleChoice || false,
              productId: productId,
              description: variation.description || '',
              maxSelections: variation.maxSelections || 1,
              minSelections: variation.minSelections || 0,
            };

            let currentVariation;
            if (variation.id) {
              // É uma variação existente, atualizar
              console.log('Updating existing variation:', variation.id);
              currentVariation = await storage.updateProductVariation(variation.id, variationData);
            } else {
              // É uma nova variação, criar
              console.log('Creating new variation for product:', productId);
              currentVariation = await storage.createProductVariation(variationData);
            }
            
            // Processar opções da variação
            if (variation.options && Array.isArray(variation.options)) {
              // Obter opções existentes
              const existingOptions = await storage.getVariationOptionsByVariationId(currentVariation.id);
              
              // Excluir opções removidas
              for (const existingOpt of existingOptions) {
                const stillExists = variation.options.some(o => o.id === existingOpt.id);
                if (!stillExists) {
                  await storage.deleteVariationOption(existingOpt.id);
                }
              }
              
              // Criar/atualizar opções
              for (const option of variation.options) {
                const optionData = {
                  name: option.name,
                  price: option.price || 0,
                  variationId: currentVariation.id,
                };

                if (option.id) {
                  // Atualizar opção existente
                  await storage.updateVariationOption(option.id, optionData);
                } else {
                  // Criar nova opção
                  await storage.createVariationOption(optionData);
                }
              }
            }
          }
          
          // Verificar se há variações que foram removidas
          const currentVariationIds = variations
            .filter(v => v.id)
            .map(v => v.id);
            
          for (const existingVar of existingVariations) {
            if (!currentVariationIds.includes(existingVar.id)) {
              // Esta variação foi removida, excluir
              console.log('Deleting removed variation:', existingVar.id);
              await storage.deleteProductVariation(existingVar.id);
            }
          }
          
        } catch (variationError) {
          console.error('Error processing variations:', variationError);
          // Não falhar toda a operação se houver erro nas variações
        }
      } else if (!updateData.hasVariations && variations.length === 0) {
        // Se as variações foram desativadas, excluir todas as variações existentes
        const existingVariations = await storage.getProductVariationsByProductId(productId);
        
        for (const variation of existingVariations) {
          await storage.deleteProductVariation(variation.id);
        }
      }
      
      console.log('Product updated successfully:', updatedProduct);
      return res.status(200).json(updatedProduct);
    } catch (error) {
      console.error('Error updating product:', error);
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid data", errors: error.errors });
      }
      return res.status(500).json({ 
        message: "Failed to update product",
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  app.delete("/api/products/:id", requireAuth, async (req, res) => {
    try {
      const userId = req.session.userId!;
      const store = await storage.getStoreByUserId(userId);
      
      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      const productId = parseInt(req.params.id);
      const product = await storage.getProduct(productId);
      
      if (!product) {
        return res.status(404).json({ message: "Product not found" });
      }

      // Verify ownership
      if (product.storeId !== store.id) {
        return res.status(403).json({ message: "Not authorized to delete this product" });
      }

      // Delete product
      await storage.deleteProduct(productId);

      return res.status(200).json({ message: "Product deleted successfully" });
    } catch (error) {
      return res.status(500).json({ message: "Failed to delete product" });
    }
  });

  // Category routes
  app.post("/api/categories", requireAuth, async (req, res) => {
    try {
      const userId = req.session.userId!;
      const store = await storage.getStoreByUserId(userId);
      
      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      // Validate category data
      const categoryData = insertCategorySchema.parse({
        ...req.body,
        storeId: store.id
      });

      // Create category
      const category = await storage.createCategory(categoryData);

      return res.status(201).json(category);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid data", errors: error.errors });
      }
      return res.status(500).json({ message: "Failed to create category" });
    }
  });

  app.get("/api/categories", requireAuth, async (req, res) => {
    try {
      const userId = req.session.userId!;
      const store = await storage.getStoreByUserId(userId);
      
      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      const categories = await storage.getCategoriesByStoreId(store.id);
      return res.status(200).json(categories);
    } catch (error) {
      return res.status(500).json({ message: "Failed to get categories" });
    }
  });

  app.get("/api/public/stores/:slug/categories", async (req, res) => {
    try {
      const { slug } = req.params;
      const store = await storage.getStoreBySlug(slug);
      
      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      const categories = await storage.getCategoriesByStoreId(store.id);
      return res.status(200).json(categories);
    } catch (error) {
      return res.status(500).json({ message: "Failed to get categories" });
    }
  });

  // Order routes
  app.post("/api/public/stores/:slug/orders", async (req, res) => {
    try {
      const { slug } = req.params;
      const store = await storage.getStoreBySlug(slug);
      
      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      // Extract and validate customer data
      const { customer, items, notes, paymentMethod } = req.body;
      
      if (!customer || !items || !items.length || !paymentMethod) {
        return res.status(400).json({ message: "Missing required order data" });
      }

      // Create or find existing customer
      const customerData = insertCustomerSchema.parse({
        ...customer,
        storeId: store.id
      });
      
      const newCustomer = await storage.createCustomer(customerData);

      // Calculate total
      let total = 0;
      for (const item of items) {
        const product = await storage.getProduct(item.productId);
        if (!product) {
          return res.status(404).json({ message: `Product with ID ${item.productId} not found` });
        }
        
        // Verify product belongs to this store
        if (product.storeId !== store.id) {
          return res.status(400).json({ message: `Product with ID ${item.productId} does not belong to this store` });
        }
        
        total += product.price * item.quantity;
      }

      // Create order
      const orderData = insertOrderSchema.parse({
        storeId: store.id,
        customerId: newCustomer.id,
        status: "pending",
        total,
        paymentMethod,
        notes
      });
      
      const order = await storage.createOrder(orderData);

      // Create order items
      for (const item of items) {
        const orderItemData = insertOrderItemSchema.parse({
          orderId: order.id,
          productId: item.productId,
          quantity: item.quantity,
          price: (await storage.getProduct(item.productId))!.price
        });
        
        await storage.createOrderItem(orderItemData);
      }

      return res.status(201).json({
        orderId: order.id,
        status: order.status,
        total: order.total
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid data", errors: error.errors });
      }
      return res.status(500).json({ message: "Failed to create order" });
    }
  });

  app.get("/api/orders", requireAuth, async (req, res) => {
    try {
      const userId = req.session.userId!;
      const store = await storage.getStoreByUserId(userId);
      
      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      const orders = await storage.getOrdersByStoreId(store.id);
      
      // Get customer info for each order
      const ordersWithCustomer = await Promise.all(orders.map(async (order) => {
        const customer = await storage.getCustomer(order.customerId);
        return {
          ...order,
          customer: customer ? {
            id: customer.id,
            name: customer.name,
            email: customer.email,
            phone: customer.phone
          } : null
        };
      }));
      
      return res.status(200).json(ordersWithCustomer);
    } catch (error) {
      return res.status(500).json({ message: "Failed to get orders" });
    }
  });

  app.get("/api/orders/:id", requireAuth, async (req, res) => {
    try {
      console.log('Fetching order details for order ID:', req.params.id);
      const userId = req.session.userId!;
      console.log('User ID from session:', userId);
      
      const store = await storage.getStoreByUserId(userId);
      console.log('Store retrieved:', store ? `ID: ${store.id}, Name: ${store.name}` : 'No store found');
      
      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      const orderId = parseInt(req.params.id);
      console.log('Order ID (parsed):', orderId);
      
      const order = await storage.getOrder(orderId);
      console.log('Order retrieved:', order ? `ID: ${order.id}, StoreID: ${order.storeId}` : 'Order not found');
      
      if (!order) {
        return res.status(404).json({ message: "Order not found" });
      }

      // Verify ownership
      console.log('Order store ID:', order.storeId, 'Current store ID:', store.id);
      if (order.storeId !== store.id) {
        console.log('Order store ID does not match current store ID - access denied');
        return res.status(403).json({ message: "Not authorized to view this order" });
      }

      // Get customer
      console.log('Fetching customer details for customer ID:', order.customerId);
      const customer = await storage.getCustomer(order.customerId);
      console.log('Customer retrieved:', customer ? `ID: ${customer.id}, Name: ${customer.name}` : 'No customer found');
      
      // Get order items
      console.log('Fetching order items for order ID:', order.id);
      const orderItems = await storage.getOrderItemsByOrderId(order.id);
      console.log(`Found ${orderItems.length} order items`);
      
      // Get product details for each item
      console.log('Fetching product details for each order item');
      const itemsWithProducts = await Promise.all(orderItems.map(async (item) => {
        const product = await storage.getProduct(item.productId);
        return {
          ...item,
          product: product ? {
            id: product.id,
            name: product.name,
            price: product.price,
            images: product.images
          } : null
        };
      }));

      const response = {
        ...order,
        customer: customer ? {
          id: customer.id,
          name: customer.name,
          email: customer.email,
          phone: customer.phone
        } : null,
        items: itemsWithProducts
      };
      
      console.log('Sending order details response:', response);
      return res.status(200).json(response);
    } catch (error) {
      console.error('Error fetching order details:', error);
      return res.status(500).json({ message: "Failed to get order details" });
    }
  });

  app.patch("/api/orders/:id/status", requireAuth, async (req, res) => {
    try {
      console.log('Updating order status for order ID:', req.params.id);
      const userId = req.session.userId!;
      console.log('User ID from session:', userId);
      
      const store = await storage.getStoreByUserId(userId);
      console.log('Store retrieved:', store ? `ID: ${store.id}, Name: ${store.name}` : 'No store found');
      
      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      const orderId = parseInt(req.params.id);
      console.log('Order ID (parsed):', orderId);
      
      const order = await storage.getOrder(orderId);
      console.log('Order retrieved:', order ? `ID: ${order.id}, StoreID: ${order.storeId}` : 'Order not found');
      
      if (!order) {
        return res.status(404).json({ message: "Order not found" });
      }

      // Verify ownership
      console.log('Order store ID:', order.storeId, 'Current store ID:', store.id);
      if (order.storeId !== store.id) {
        console.log('Order store ID does not match current store ID - access denied');
        return res.status(403).json({ message: "Not authorized to update this order" });
      }

      const { status } = req.body;
      console.log('New status from request body:', status);
      if (!status) {
        console.log('Status is missing in request body');
        return res.status(400).json({ message: "Status is required" });
      }

      // Valid statuses
      const validStatuses = ["pending", "processing", "completed", "cancelled"];
      console.log('Checking if status is valid:', validStatuses.includes(status));
      if (!validStatuses.includes(status)) {
        return res.status(400).json({ message: "Invalid status" });
      }

      // Update order status
      console.log(`Updating order ${orderId} status to ${status}`);
      const updatedOrder = await storage.updateOrderStatus(orderId, status);
      console.log('Order status updated, result:', updatedOrder);

      return res.status(200).json(updatedOrder);
    } catch (error) {
      console.error('Error updating order status:', error);
      return res.status(500).json({ message: "Failed to update order status" });
    }
  });

  // Customer routes
  app.get("/api/customers", requireAuth, async (req, res) => {
    try {
      const userId = req.session.userId!;
      const store = await storage.getStoreByUserId(userId);
      
      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      const customers = await storage.getCustomersByStoreId(store.id);
      return res.status(200).json(customers);
    } catch (error) {
      return res.status(500).json({ message: "Failed to get customers" });
    }
  });
  
  // Get orders by customer ID
  app.get("/api/customers/:id/orders", requireAuth, async (req, res) => {
    try {
      const userId = req.session.userId!;
      const customerId = parseInt(req.params.id);
      
      if (isNaN(customerId)) {
        return res.status(400).json({ message: "Invalid customer ID" });
      }
      
      console.log('Fetching orders for customer ID:', customerId);
      
      const store = await storage.getStoreByUserId(userId);
      
      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }
      
      // Verify the customer belongs to this store
      const customer = await storage.getCustomer(customerId);
      
      if (!customer || customer.storeId !== store.id) {
        return res.status(404).json({ message: "Customer not found" });
      }
      
      // Get orders for this customer
      const orders = await storage.getOrdersByCustomerId(customerId);
      
      // Add customer info to each order
      const ordersWithDetails = await Promise.all(orders.map(async (order) => {
        const items = await storage.getOrderItemsByOrderId(order.id);
        
        // For each item, get the product details
        const itemsWithProducts = await Promise.all(items.map(async (item) => {
          const product = await storage.getProduct(item.productId);
          return {
            ...item,
            product
          };
        }));
        
        return {
          ...order,
          items: itemsWithProducts
        };
      }));
      
      return res.status(200).json(ordersWithDetails);
    } catch (error) {
      console.error('Error getting customer orders:', error);
      return res.status(500).json({ message: "Failed to get customer orders" });
    }
  });

  // Analytics routes
  app.get("/api/analytics/dashboard", requireAuth, async (req, res) => {
    try {
      const userId = req.session.userId!;
      console.log('Getting analytics dashboard for userId:', userId);
      
      const store = await storage.getStoreByUserId(userId);
      
      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      // Get monthly visit count
      const visitCount = await storage.getMonthlyVisitCount(store.id);
      
      // Get monthly order count
      const orderCount = await storage.getMonthlyOrderCount(store.id);
      
      // Calculate monthly revenue
      const orders = await storage.getOrdersByStoreId(store.id);
      const now = new Date();
      const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      
      const monthlyOrders = orders.filter(order => new Date(order.createdAt) >= firstDayOfMonth);
      const revenue = monthlyOrders.reduce((sum, order) => sum + order.total, 0);

      return res.status(200).json({
        visitCount,
        orderCount,
        revenue
      });
    } catch (error) {
      console.error('Error getting analytics dashboard:', error);
      return res.status(500).json({ message: "Failed to get analytics data" });
    }
  });

  // ===== Rotas para Variações de Produto =====
  
  // Obter todas as variações de um produto
  app.get("/api/products/:productId/variations", requireAuth, async (req, res) => {
    try {
      if (!req.session.userId) {
        return res.status(401).json({ error: "Não autenticado" });
      }
      
      const productId = Number(req.params.productId);
      
      // Verificar acesso ao produto
      const store = await storage.getStoreByUserId(req.session.userId);
      if (!store) {
        return res.status(403).json({ error: "Loja não encontrada" });
      }
      
      const product = await storage.getProduct(productId);
      if (!product || product.storeId !== store.id) {
        return res.status(403).json({ error: "Acesso negado a este produto" });
      }
      
      const variations = await storage.getProductVariationsByProductId(productId);
      
      // Para cada variação, obter suas opções
      const variationsWithOptions = await Promise.all(
        variations.map(async (variation) => {
          const options = await storage.getVariationOptionsByVariationId(variation.id);
          return {
            ...variation,
            options
          };
        })
      );
      
      return res.json(variationsWithOptions);
    } catch (error) {
      console.error("Erro ao obter variações de produto:", error);
      return res.status(500).json({ error: "Erro interno do servidor" });
    }
  });
  
  // Criar uma nova variação para um produto
  app.post("/api/products/:productId/variations", requireAuth, async (req, res) => {
    try {
      if (!req.session.userId) {
        return res.status(401).json({ error: "Não autenticado" });
      }
      
      const productId = Number(req.params.productId);
      const { name, description, required, maxSelections, minSelections } = req.body;
      
      // Verificar acesso ao produto
      const store = await storage.getStoreByUserId(req.session.userId);
      if (!store) {
        return res.status(403).json({ error: "Loja não encontrada" });
      }
      
      const product = await storage.getProduct(productId);
      if (!product || product.storeId !== store.id) {
        return res.status(403).json({ error: "Acesso negado a este produto" });
      }
      
      // Criar a variação
      const variation = await storage.createProductVariation({
        productId,
        name,
        description,
        required: required || false,
        maxSelections: maxSelections || 1,
        minSelections: minSelections || 0
      });
      
      // Atualizar o produto para indicar que tem variações
      if (!product.hasVariations) {
        await storage.updateProduct(productId, { hasVariations: true });
      }
      
      return res.status(201).json(variation);
    } catch (error) {
      console.error("Erro ao criar variação de produto:", error);
      return res.status(500).json({ error: "Erro interno do servidor" });
    }
  });
  
  // Atualizar uma variação existente
  app.put("/api/variations/:variationId", requireAuth, async (req, res) => {
    try {
      if (!req.session.userId) {
        return res.status(401).json({ error: "Não autenticado" });
      }
      
      const variationId = Number(req.params.variationId);
      const { name, description, required, maxSelections, minSelections, productId } = req.body;
      
      // Verificar acesso ao produto
      const store = await storage.getStoreByUserId(req.session.userId);
      if (!store) {
        return res.status(403).json({ error: "Loja não encontrada" });
      }
      
      const product = await storage.getProduct(Number(productId));
      if (!product || product.storeId !== store.id) {
        return res.status(403).json({ error: "Acesso negado a este produto" });
      }
      
      // Obter a variação para verificar o produto
      const variations = await storage.getProductVariationsByProductId(product.id);
      const variation = variations.find(v => v.id === variationId);
      
      if (!variation) {
        return res.status(404).json({ error: "Variação não encontrada" });
      }
      
      // Atualizar a variação
      const updatedVariation = await storage.updateProductVariation(variationId, {
        name,
        description,
        required,
        maxSelections,
        minSelections
      });
      
      if (!updatedVariation) {
        return res.status(404).json({ error: "Erro ao atualizar variação" });
      }
      
      return res.json(updatedVariation);
    } catch (error) {
      console.error("Erro ao atualizar variação de produto:", error);
      return res.status(500).json({ error: "Erro interno do servidor" });
    }
  });
  
  // Excluir uma variação
  app.delete("/api/variations/:variationId", requireAuth, async (req, res) => {
    try {
      if (!req.session.userId) {
        return res.status(401).json({ error: "Não autenticado" });
      }
      
      const variationId = Number(req.params.variationId);
      const productId = Number(req.body.productId);
      
      // Verificar acesso ao produto
      const store = await storage.getStoreByUserId(req.session.userId);
      if (!store) {
        return res.status(403).json({ error: "Loja não encontrada" });
      }
      
      const product = await storage.getProduct(productId);
      if (!product || product.storeId !== store.id) {
        return res.status(403).json({ error: "Acesso negado a este produto" });
      }
      
      // Obter a variação para verificar o produto
      const variations = await storage.getProductVariationsByProductId(product.id);
      const variation = variations.find(v => v.id === variationId);
      
      if (!variation) {
        return res.status(404).json({ error: "Variação não encontrada" });
      }
      
      // Excluir a variação (as opções serão excluídas automaticamente devido à restrição ON DELETE CASCADE)
      const deleted = await storage.deleteProductVariation(variationId);
      
      if (!deleted) {
        return res.status(500).json({ error: "Erro ao excluir variação" });
      }
      
      // Verificar se o produto ainda tem outras variações
      const remainingVariations = await storage.getProductVariationsByProductId(product.id);
      if (remainingVariations.length === 0) {
        // Se não houver mais variações, atualizar o produto
        await storage.updateProduct(product.id, { hasVariations: false });
      }
      
      return res.json({ success: true });
    } catch (error) {
      console.error("Erro ao excluir variação de produto:", error);
      return res.status(500).json({ error: "Erro interno do servidor" });
    }
  });
  
  // ===== Rotas para Opções de Variação =====
  
  // Obter todas as opções de uma variação
  app.get("/api/variations/:variationId/options", requireAuth, async (req, res) => {
    try {
      if (!req.session.userId) {
        return res.status(401).json({ error: "Não autenticado" });
      }
      
      const variationId = Number(req.params.variationId);
      const productId = Number(req.query.productId);
      
      // Verificar acesso ao produto
      const store = await storage.getStoreByUserId(req.session.userId);
      if (!store) {
        return res.status(403).json({ error: "Loja não encontrada" });
      }
      
      const product = await storage.getProduct(productId);
      if (!product || product.storeId !== store.id) {
        return res.status(403).json({ error: "Acesso negado a este produto" });
      }
      
      // Obter a variação para verificar o produto
      const variations = await storage.getProductVariationsByProductId(product.id);
      const variation = variations.find(v => v.id === variationId);
      
      if (!variation) {
        return res.status(404).json({ error: "Variação não encontrada" });
      }
      
      const options = await storage.getVariationOptionsByVariationId(variationId);
      return res.json(options);
    } catch (error) {
      console.error("Erro ao obter opções de variação:", error);
      return res.status(500).json({ error: "Erro interno do servidor" });
    }
  });
  
  // Criar uma nova opção para uma variação
  app.post("/api/variations/:variationId/options", requireAuth, async (req, res) => {
    try {
      if (!req.session.userId) {
        return res.status(401).json({ error: "Não autenticado" });
      }
      
      const variationId = Number(req.params.variationId);
      const { name, price, productId } = req.body;
      
      // Verificar acesso ao produto
      const store = await storage.getStoreByUserId(req.session.userId);
      if (!store) {
        return res.status(403).json({ error: "Loja não encontrada" });
      }
      
      const product = await storage.getProduct(Number(productId));
      if (!product || product.storeId !== store.id) {
        return res.status(403).json({ error: "Acesso negado a este produto" });
      }
      
      // Obter a variação para verificar o produto
      const variations = await storage.getProductVariationsByProductId(product.id);
      const variation = variations.find(v => v.id === variationId);
      
      if (!variation) {
        return res.status(404).json({ error: "Variação não encontrada" });
      }
      
      // Criar a opção
      const option = await storage.createVariationOption({
        variationId,
        name,
        price: price || 0
      });
      
      return res.status(201).json(option);
    } catch (error) {
      console.error("Erro ao criar opção de variação:", error);
      return res.status(500).json({ error: "Erro interno do servidor" });
    }
  });
  
  // Atualizar uma opção existente
  app.put("/api/options/:optionId", requireAuth, async (req, res) => {
    try {
      if (!req.session.userId) {
        return res.status(401).json({ error: "Não autenticado" });
      }
      
      const optionId = Number(req.params.optionId);
      const { name, price, variationId, productId } = req.body;
      
      // Verificar acesso ao produto
      const store = await storage.getStoreByUserId(req.session.userId);
      if (!store) {
        return res.status(403).json({ error: "Loja não encontrada" });
      }
      
      const product = await storage.getProduct(Number(productId));
      if (!product || product.storeId !== store.id) {
        return res.status(403).json({ error: "Acesso negado a este produto" });
      }
      
      // Obter a variação para verificar o produto
      const variations = await storage.getProductVariationsByProductId(product.id);
      const variation = variations.find(v => v.id === Number(variationId));
      
      if (!variation) {
        return res.status(404).json({ error: "Variação não encontrada" });
      }
      
      // Atualizar a opção
      const updatedOption = await storage.updateVariationOption(optionId, {
        name,
        price
      });
      
      if (!updatedOption) {
        return res.status(404).json({ error: "Erro ao atualizar opção" });
      }
      
      return res.json(updatedOption);
    } catch (error) {
      console.error("Erro ao atualizar opção de variação:", error);
      return res.status(500).json({ error: "Erro interno do servidor" });
    }
  });
  
  // Excluir uma opção
  app.delete("/api/options/:optionId", requireAuth, async (req, res) => {
    try {
      if (!req.session.userId) {
        return res.status(401).json({ error: "Não autenticado" });
      }
      
      const optionId = Number(req.params.optionId);
      const variationId = Number(req.body.variationId);
      const productId = Number(req.body.productId);
      
      // Verificar acesso ao produto
      const store = await storage.getStoreByUserId(req.session.userId);
      if (!store) {
        return res.status(403).json({ error: "Loja não encontrada" });
      }
      
      const product = await storage.getProduct(productId);
      if (!product || product.storeId !== store.id) {
        return res.status(403).json({ error: "Acesso negado a este produto" });
      }
      
      // Obter a variação para verificar o produto
      const variations = await storage.getProductVariationsByProductId(product.id);
      const variation = variations.find(v => v.id === variationId);
      
      if (!variation) {
        return res.status(404).json({ error: "Variação não encontrada" });
      }
      
      // Excluir a opção
      const deleted = await storage.deleteVariationOption(optionId);
      
      if (!deleted) {
        return res.status(500).json({ error: "Erro ao excluir opção" });
      }
      
      return res.json({ success: true });
    } catch (error) {
      console.error("Erro ao excluir opção de variação:", error);
      return res.status(500).json({ error: "Erro interno do servidor" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
