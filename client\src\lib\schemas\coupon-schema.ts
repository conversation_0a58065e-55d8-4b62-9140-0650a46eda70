import * as z from "zod";

// Schema para validação do formulário de cupom
export const couponFormSchema = z.object({
  code: z.string()
    .min(3, { message: "O código deve ter pelo menos 3 caracteres" })
    .max(50, { message: "O código deve ter no máximo 50 caracteres" })
    .regex(/^[A-Za-z0-9\-]+$/, { message: "Código inválido. Use apenas letras, números e traços." }),
  tipo: z.enum(["valor_fixo", "percentual"], {
    required_error: "Selecione o tipo de desconto",
  }),
  valor: z.coerce.number()
    .positive({ message: "O valor deve ser maior que zero" }),
  minimoCompra: z.coerce.number()
    .min(0, { message: "O valor mínimo deve ser maior ou igual a zero" })
    .optional(),
  dataValidade: z.string()
    .refine((date) => new Date(date) > new Date(), {
      message: "A data de validade deve ser futura",
    }),
  usoUnico: z.boolean().default(false),
  ativo: z.boolean().default(true),
});

export type CouponFormValues = z.infer<typeof couponFormSchema>;
