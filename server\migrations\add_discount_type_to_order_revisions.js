/**
 * Migration para adicionar o campo discount_type na tabela order_revisions
 */

import { pool } from '../db';

async function up() {
  try {
    console.log('Iniciando migração: Adicionando campo discount_type à tabela order_revisions');

    // Verificar se a coluna já existe
    const checkColumnQuery = `
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'order_revisions' AND column_name = 'discount_type'
    `;

    const checkResult = await pool.query(checkColumnQuery);

    if (checkResult.rows.length === 0) {
      // A coluna não existe, então vamos criá-la
      const addColumnQuery = `
        ALTER TABLE order_revisions
        ADD COLUMN discount_type VARCHAR(10) DEFAULT 'fixed'
      `;

      await pool.query(addColumnQuery);
      console.log('Campo discount_type adicionado com sucesso à tabela order_revisions');
    } else {
      console.log('Campo discount_type já existe na tabela order_revisions');
    }

    return true;
  } catch (error) {
    console.error('Erro ao adicionar campo discount_type:', error);
    throw error;
  }
}

async function down() {
  try {
    console.log('Revertendo migração: Removendo campo discount_type da tabela order_revisions');

    // Verificar se a coluna existe
    const checkColumnQuery = `
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'order_revisions' AND column_name = 'discount_type'
    `;

    const checkResult = await pool.query(checkColumnQuery);

    if (checkResult.rows.length > 0) {
      // A coluna existe, então vamos removê-la
      const dropColumnQuery = `
        ALTER TABLE order_revisions
        DROP COLUMN discount_type
      `;

      await pool.query(dropColumnQuery);
      console.log('Campo discount_type removido com sucesso da tabela order_revisions');
    } else {
      console.log('Campo discount_type não existe na tabela order_revisions');
    }

    return true;
  } catch (error) {
    console.error('Erro ao remover campo discount_type:', error);
    throw error;
  }
}

export const addDiscountTypeToOrderRevisions = {
  up,
  down
};
