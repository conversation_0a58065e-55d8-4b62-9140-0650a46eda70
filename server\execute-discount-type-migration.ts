import dotenv from 'dotenv';
dotenv.config();

import fs from 'fs';
import path from 'path';
import { pool } from './db';
import { supabase } from './supabase-config';

/**
 * Script para executar a migração SQL que adiciona o campo discount_type
 * às tabelas order_revisions e orders
 */
async function executeMigration() {
  try {
    console.log('Iniciando migração para adicionar campo discount_type...');
    
    // Caminho para o arquivo SQL
    const sqlFilePath = path.join(process.cwd(), 'migrations', 'add_discount_type_to_order_revisions.sql');
    
    // Verificar se o arquivo existe
    if (!fs.existsSync(sqlFilePath)) {
      throw new Error(`Arquivo SQL não encontrado: ${sqlFilePath}`);
    }
    
    // Ler o conteúdo do arquivo SQL
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Dividir o conteúdo em declarações SQL individuais
    const statements = sqlContent.split(';').filter(stmt => stmt.trim().length > 0);
    
    console.log(`Encontradas ${statements.length} declarações SQL para executar.`);
    
    // Executar cada declaração SQL
    for (let i = 0; i < statements.length; i++) {
      const stmt = statements[i].trim();
      
      // Pular comentários e linhas vazias
      if (stmt.startsWith('--') || stmt.length === 0) {
        continue;
      }
      
      console.log(`Executando declaração ${i+1}/${statements.length}...`);
      
      try {
        // Tentar executar usando o pool direto
        await pool.query(stmt);
        console.log(`✓ Declaração ${i+1} executada com sucesso!`);
      } catch (error) {
        console.error(`❌ Erro ao executar declaração ${i+1}:`, error);
        
        // Tentar executar usando Supabase como fallback
        try {
          console.log('Tentando executar via Supabase...');
          const { error: supabaseError } = await supabase.rpc('exec_sql', { sql: stmt });
          
          if (supabaseError) {
            throw supabaseError;
          }
          
          console.log(`✓ Declaração ${i+1} executada com sucesso via Supabase!`);
        } catch (supabaseError) {
          console.error(`❌ Erro ao executar via Supabase:`, supabaseError);
          console.error('Continuando para a próxima declaração...');
        }
      }
    }
    
    console.log('Migração concluída com sucesso!');
    process.exit(0);
  } catch (error) {
    console.error('Erro durante a migração:', error);
    process.exit(1);
  }
}

// Executar a função principal
executeMigration();
